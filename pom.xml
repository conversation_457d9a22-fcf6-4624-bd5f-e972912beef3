<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>net.polyv.operation</groupId>
    <artifactId>clearing-system</artifactId>
    <version>develop-3-docker-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>clearing-system</name>
    <description>polyv结算系统</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>

        <druid.version>1.1.22</druid.version>
        <commons.lang3.version>3.9</commons.lang3.version>
        <commons.io.version>2.6</commons.io.version>
        <fastjson.version>1.2.83</fastjson.version>
        <mysql.connector.java.version>5.1.49</mysql.connector.java.version>
        <hutool.version>5.3.3</hutool.version>
        <httpclient.version>4.5.13</httpclient.version>
        <httpmime.version>4.5.1</httpmime.version>
        <poi.version>3.17</poi.version>
        <spring-jcl>5.2.19.RELEASE</spring-jcl>
        <jedis.version>2.9.0</jedis.version>
        <easyexcel.version>2.2.3</easyexcel.version>
        <health-service.version>0.0.1</health-service.version>
        <third-invoker.version>1.66.3.RELEASE</third-invoker.version>
        <user-service-api.version>1.66.3.RELEASE</user-service-api.version>
        <micro-common-parent.version>1.60.3.RELEASE</micro-common-parent.version>
        <micro-common-ops.version>1.57.3.RELEASE</micro-common-ops.version>
        <aliyun.sdk.core.version>4.1.2</aliyun.sdk.core.version>
        <ip-geo.version>1.0.2.RELEASE</ip-geo.version>
        <aliyun-sdk-oss>3.4.2</aliyun-sdk-oss>
        <clear-system-client.version>1.26.3-SNAPSHOT</clear-system-client.version>
        <support-service-api.version>1.11.2.RELEASE</support-service-api.version>
        <pop-internal-api.version>2.0.1.RELEASE</pop-internal-api.version>
        <alibaba-dingtalk-service-sdk.version>2.0.0</alibaba-dingtalk-service-sdk.version>
        <apollo-client.version>2.1.0</apollo-client.version>
        <skywalking.version>8.5.0</skywalking.version>
        <playback-service-api.version>1.34.3.RELEASE</playback-service-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>net.polyv.modules</groupId>
                <artifactId>micro-common-parent</artifactId>
                <version>${micro-common-parent.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <!-- for mysql 5.x -->
            <dependency>
	            <groupId>mysql</groupId>
	            <artifactId>mysql-connector-java</artifactId>
	            <version>${mysql.connector.java.version}</version>
        	</dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--链路追踪id-->
        <dependency>
            <groupId>net.polyv.modules</groupId>
            <artifactId>playback-service-api</artifactId>
            <version>${playback-service-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>${skywalking.version}</version>
        </dependency>
        <!--监控sql日志-->
        <dependency>
            <groupId>org.bgee.log4jdbc-log4j2</groupId>
            <artifactId>log4jdbc-log4j2-jdbc4.1</artifactId>
            <version>1.16</version>
        </dependency>

        <!-- apollo -->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo-client.version}</version>
        </dependency>
        <!--升级钉钉组件-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>${alibaba-dingtalk-service-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>net.polyv.modules</groupId>
            <artifactId>micro-common-api</artifactId>
            <version>${micro-common-parent.version}</version>
        </dependency>

        <dependency>
            <groupId>net.polyv.modules</groupId>
            <artifactId>micro-common-ops</artifactId>
            <version>${micro-common-ops.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jcl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
		<dependency>
			 <groupId>org.springframework.boot</groupId>
			 <artifactId>spring-boot-configuration-processor</artifactId>
			 <optional>true</optional>
		</dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-zuul</artifactId>
        </dependency>

        <!--阿里的druid数据库连接池-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>net.polyv.modules</groupId>
            <artifactId>clear-system-client</artifactId>
            <version>${clear-system-client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
            <version>3.0.1</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>

        <!-- POI 用于导入导出Excel-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dingtalk.api</groupId>
            <artifactId>taobao-sdk-java</artifactId>
            <version>20200602</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${jedis.version}</version>
        </dependency>

        <dependency>
            <groupId>net.polyv.commons</groupId>
            <artifactId>health-service</artifactId>
            <version>${health-service.version}</version>
        </dependency>
        <dependency>
            <groupId>net.polyv.modules</groupId>
            <artifactId>user-service-api</artifactId>
            <version>${user-service-api.version}</version>
        </dependency>
        <dependency>
            <groupId>net.polyv.modules</groupId>
            <artifactId>support-service-api</artifactId>
            <version>${support-service-api.version}</version>
        </dependency>
        <dependency>
            <groupId>net.polyv.modules</groupId>
            <artifactId>third-invoker</artifactId>
            <version>${third-invoker.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>${aliyun.sdk.core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${aliyun-sdk-oss}</version>
        </dependency>
        <!--pop-->
        <dependency>
            <groupId>net.polyv.modules</groupId>
            <artifactId>pop-internal-api</artifactId>
            <version>${pop-internal-api.version}</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <env>local</env>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>product</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>

    <build>

        <finalName>clearing-system-1.0.0</finalName>
        <resources>
            <resource>
                <directory>src/main/bin</directory>
                <targetPath>${project.build.directory}</targetPath>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <targetPath>${project.build.directory}/classes</targetPath>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <testSource>${java.version}</testSource>
                    <testTarget>${java.version}</testTarget>
                    <compilerVersion>${java.version}</compilerVersion>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                    <skip>true</skip>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <addResources>true</addResources>
                    <excludeDevtools>true</excludeDevtools>
                    <mainClass>net.polyv.PcsApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.3.8.RELEASE</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <!-- 正式仓库 -->
        <repository>
            <!-- 对应 maven conf/settings.xml中的 server的id，server中配置了帐号/密码 -->
            <id>releases</id>
            <name>company nexus repository.hosted repository</name>
            <uniqueVersion>true</uniqueVersion>
            <!-- 远程仓库的地址 -->
            <url>http://repository.polyv.net/repository/releases/</url>
        </repository>

        <!-- 快照仓库 -->
        <snapshotRepository>
            <!-- 对应 maven conf/settings.xml中的 server的id，server中配置了帐号/密码 -->
            <id>snapshots</id>
            <name>company nexus repository.hosted repository</name>
            <uniqueVersion>false</uniqueVersion>
            <!-- 远程仓库的地址 -->
            <url>http://repository.polyv.net/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>snapshotRepo</id>
            <url>http://repository.polyv.net/repository/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>


</project>