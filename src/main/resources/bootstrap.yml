apollo:
  bootstrap:
    # will inject 'application' namespace in bootstrap phase
    enabled: true
    # 引入配置命名空间
    namespaces: application,backend.eureka-cloud,druid.yml,redis.yml,polyv.yml,log.yml,backend.micro-common,backend.pcs-common
    eagerLoad:
      # put apollo initialization before logging system initialization
      enabled: true

spring:
  main:
    allow-bean-definition-overriding: true



