package net.polyv.service.schedule;

import net.polyv.constant.schdule.ScheduleJobTypeConst;
import net.polyv.exception.job.ScheduleJobExecuteException;
import net.polyv.model.entity.primary.CustomerEffectiveScheduleJob;

/**
 * 定时任务执行者
 * <AUTHOR>
 * @since 09/05/2020
 */
public interface ScheduleJobExecutor {

    /**
     * 获取优先级，数值越高优先级越高
     * @return 优先级
     */
    default int getOrder()  {
        return 0;
    }

    /**
     * 获取处理的类型
     * @return 处理的任务类型
     */
    ScheduleJobTypeConst getProcessType();

    /**
     * 处理定时任务
     * 此处对于执行结果，抛出异常和返回false都是表示执行失败了
     * 执行失败的程序会触发重试机制，请确保执行者在成功、失败的时候的数据一致性
     * @param job 输入参数
     * @return 处理结果
     * @throws ScheduleJobExecuteException 执行异常
     */
    boolean process(CustomerEffectiveScheduleJob job) throws ScheduleJobExecuteException;
}
