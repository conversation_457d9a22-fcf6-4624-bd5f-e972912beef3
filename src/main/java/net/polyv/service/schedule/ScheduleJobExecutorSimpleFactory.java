package net.polyv.service.schedule;

import com.alibaba.fastjson.JSON;
import net.polyv.constant.schdule.ScheduleJobTypeConst;
import net.polyv.exception.job.ExecutorNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 定时任务执行器简单工厂
 * <AUTHOR>
 * @since 09/05/2020
 */
@Slf4j
@Service
public class ScheduleJobExecutorSimpleFactory {

    @Autowired
    private ApplicationContext applicationContext;

    private Map<ScheduleJobTypeConst, ScheduleJobExecutor> executorCacheMap;

    /**
     * 根据类型获取任务执行器
     * @param type 类型
     * @return 任务执行器
     * @throws ExecutorNotFoundException 没有该类型的任务执行器
     */
    public ScheduleJobExecutor get(ScheduleJobTypeConst type) throws ExecutorNotFoundException {

        if (type == null) {
            log.error("Type is null.");
            throw new ExecutorNotFoundException();
        }

        ScheduleJobExecutor executor = this.executorCacheMap.get(type);

        if (executor == null) {
            log.error("Can't found executor by type: {}", type);
            throw new ExecutorNotFoundException();
        }

        return executor;
    }

    /**
     * 缓存任务执行器
     * 此处用了同步机制，是为了避免多个spring上下文的场景
     */
    @PostConstruct
    public synchronized void cacheScheduleJobExecutor() {
        if (this.executorCacheMap != null) {
            return ;
        }

        Map<String, ScheduleJobExecutor> executorMap = this.applicationContext.getBeansOfType(ScheduleJobExecutor.class);
        this.executorCacheMap = new HashMap<>(executorMap.size());

        executorMap.forEach((key, value) -> this.executorCacheMap.put(value.getProcessType(), value));

        log.info("load schedule job executor success, current executor list is {}",
                JSON.toJSONString(executorMap.keySet()));
    }
}
