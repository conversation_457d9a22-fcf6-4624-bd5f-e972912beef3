package net.polyv.service.customer;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import net.polyv.dao.primary.business.BusinessPackageSpecificationRepository;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONArray;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.business.BusinessBillingPlanCodeEnum;
import net.polyv.constant.examinationDonate.BusinessTypeEnum;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.examinationDonate.ExaminationDonateResourceSpecificationRepository;
import net.polyv.model.dto.CustomerLastTestAndDonateDTO;
import net.polyv.model.dto.CustomerOrderAvailableDTO;
import net.polyv.modules.pcs.api.stereotype.SalesOpportunitiesStatusConst;
import net.polyv.modules.pcs.api.vo.crm.GetLastRechargeToNowResourceResponse;

/**
 * <AUTHOR>
 * @date 2023/3/8
 */
@Component
@Slf4j
public class CustomerAmountQueryService {
    @Resource
    BusinessPackageSpecificationRepository businessPackageSpecificationRepository ;
    @Resource
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    @Resource
    private ExaminationDonateResourceSpecificationRepository examinationDonateResourceSpecificationRepository;
    
    /**
     * 根据客户id 获取最后一笔充值到现在的 测试、赠送金额
     * 1.查询订单
     * 2.查询赠送、测试金额数据
     */
    public List<GetLastRechargeToNowResourceResponse> getDonateAndTestLastNormalRecharge(List<String> customerIds) {
        //1.获取销售机会表是否存在赠送金额
        List<Map<String, Object>> lastRechargeOrderExt = this.salesOpportunitiesRepository.getLastRechargeOrderExt(
                customerIds, SalesOpportunitiesStatusConst.associate.getStatus(),
                BusinessBillingPlanCodeEnum.AMOUNT.getCode());
        Map<String, Long> orderDonateMap = new HashMap<>();
        List<CustomerOrderAvailableDTO> salesOpportunities = new ArrayList<>();
        if (!CollectionUtils.isEmpty(lastRechargeOrderExt)) {
            String s = JsonUtil.beanToString(lastRechargeOrderExt).orElse("");
            salesOpportunities = JSONArray.parseArray(s, CustomerOrderAvailableDTO.class);
            salesOpportunities.forEach(v -> {
                if (v.getDonate() != null) {
                    orderDonateMap.put(v.getCustomerId(), v.getDonate());
                }
            });
        }
        //2.获取测试赠送管理充值数据
        List<Map<String, Object>> customerTestDonateList = new ArrayList<>();
        salesOpportunities.forEach(v -> {
            List<Map<String, Object>> record =
                    this.examinationDonateResourceSpecificationRepository.getCustomerDonateAndTestRecord(
                    v.getCustomerId(), DateUtil.formatDateTime(v.getTime()), BusinessTypeEnum.AMOUNT.getCode());
            if (CollectionUtils.isEmpty(record)) {
                return;
            }
            customerTestDonateList.addAll(record);
        });
        String s = JsonUtil.beanToString(customerTestDonateList).orElse("");
        List<CustomerLastTestAndDonateDTO> testAndDonateList = JSONArray.parseArray(s,
                CustomerLastTestAndDonateDTO.class);
        Map<String, CustomerLastTestAndDonateDTO> testAndDonateMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(testAndDonateList)) {
            testAndDonateMap = testAndDonateList.stream()
                    .collect(Collectors.toMap(v -> v.getCustomerId() + v.getResourceType(), Function.identity(),(k1,k2)->k2));
        }
        //获取测试+赠送资源数据
        List<GetLastRechargeToNowResourceResponse> responseList = new ArrayList<>();
        Map<String, CustomerLastTestAndDonateDTO> finalTestAndDonateMap = testAndDonateMap;
        salesOpportunities.forEach(v -> {
            CustomerLastTestAndDonateDTO donateDTO =null;
            CustomerLastTestAndDonateDTO testDTO =null;
            try {
                GetLastRechargeToNowResourceResponse response = new GetLastRechargeToNowResourceResponse();
                Integer donate = ResourceTypeEnum.DONATE.getCode();
                Integer test = ResourceTypeEnum.TEST.getCode();
                donateDTO = finalTestAndDonateMap.get(v.getCustomerId() + donate);
                testDTO = finalTestAndDonateMap.get(v.getCustomerId() + test);
                response.setCustomerId(v.getCustomerId());
                response.setLastRechargeValue(v.getRecharge());
                Long orderDonate = orderDonateMap.getOrDefault(v.getCustomerId(), 0l);
                response.setDonateRechargeValue(Objects.nonNull(donateDTO)&&Objects.nonNull(donateDTO.getNum()) ?
                         donateDTO.getNum() + orderDonate: orderDonate );
                response.setTestRechargeValue(Objects.nonNull(testDTO)&&Objects.nonNull(testDTO.getNum()) ? testDTO.getNum():0L );
                responseList.add(response);
            } catch (Exception e) {
                log.error("组装金额异常:test={},donate={},order={}",testDTO,donateDTO,v,e);
            }
        });
        return responseList;
    }

    /**
     * 根据客户id 获取最后一笔充值到现在的 测试、赠送资源点
     * 1.查询订单
     * 2.查询赠送、测试金额数据
     */
    public List<GetLastRechargeToNowResourceResponse> getDonateAndTestLastNormalRechargeResourcePoint(List<String> customerIds) {
        //1.获取销售机会表是否存在赠送金额
        List<SalesOpportunities> lastRechargeSalesOpportunities = this.salesOpportunitiesRepository.getLastRechargeOrder(
                customerIds, SalesOpportunitiesStatusConst.associate.getStatus(),
                BusinessBillingPlanCodeEnum.RESOURCE_POINT.getCode());
        Map<String, Long> orderDonateMap = new HashMap<>();

        // 资源点的数据保存在business_package_spec里面
        List<BusinessPackageSpecification> businessPackageSpecificationList = businessPackageSpecificationRepository.findBySoIdIn(lastRechargeSalesOpportunities.stream().map(SalesOpportunities::getSoId).collect(Collectors.toList())) ;
        if(!CollectionUtils.isEmpty(businessPackageSpecificationList)){
            for(BusinessPackageSpecification businessPackageSpecification : businessPackageSpecificationList){
                orderDonateMap.put(businessPackageSpecification.getCustomerId() , businessPackageSpecification.getDonateResourcePoint()) ;
            }
        }

        //2.获取测试赠送管理充值数据
        List<Map<String, Object>> customerTestDonateList = new ArrayList<>();
        lastRechargeSalesOpportunities.forEach(v -> {
            List<Map<String, Object>> record =
                    this.examinationDonateResourceSpecificationRepository.getCustomerDonateAndTestRecord(
                            v.getCustomerId(), DateUtil.formatDateTime(v.getUpdateTime()), BusinessTypeEnum.RESOURCE_POINT.getCode());
            if (CollectionUtils.isEmpty(record)) {
                return;
            }
            customerTestDonateList.addAll(record);
        });

        String s = JsonUtil.beanToString(customerTestDonateList).orElse("");
        List<CustomerLastTestAndDonateDTO> testAndDonateList = JSONArray.parseArray(s,
                CustomerLastTestAndDonateDTO.class);
        Map<String, CustomerLastTestAndDonateDTO> testAndDonateMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(testAndDonateList)) {
            testAndDonateMap = testAndDonateList.stream()
                    .collect(Collectors.toMap(v -> v.getCustomerId() + v.getResourceType(), Function.identity(),(k1,k2)->k2));
        }
        //获取测试+赠送资源数据
        List<GetLastRechargeToNowResourceResponse> responseList = new ArrayList<>();
        Map<String, CustomerLastTestAndDonateDTO> finalTestAndDonateMap = testAndDonateMap;
        lastRechargeSalesOpportunities.forEach(v -> {
            CustomerLastTestAndDonateDTO donateDTO =null;
            CustomerLastTestAndDonateDTO testDTO =null;
            try {
                GetLastRechargeToNowResourceResponse response = new GetLastRechargeToNowResourceResponse();
                Integer donate = ResourceTypeEnum.DONATE.getCode();
                Integer test = ResourceTypeEnum.TEST.getCode();
                donateDTO = finalTestAndDonateMap.get(v.getCustomerId() + donate);
                testDTO = finalTestAndDonateMap.get(v.getCustomerId() + test);
                response.setCustomerId(v.getCustomerId());
                response.setLastRechargeValue(v.getAmountGained());
                Long orderDonate = orderDonateMap.getOrDefault(v.getCustomerId(), 0l);
                response.setDonateRechargeValue(Objects.nonNull(donateDTO)&&Objects.nonNull(donateDTO.getNum()) ?
                        donateDTO.getNum() + orderDonate: orderDonate );
                response.setTestRechargeValue(Objects.nonNull(testDTO)&&Objects.nonNull(testDTO.getNum()) ? testDTO.getNum():0L );
                responseList.add(response);
            } catch (Exception e) {
                log.error("组装资源点异常:test={},donate={},order={}",testDTO,donateDTO,v,e);
            }
        });
        return responseList;
    }
    
    
}
