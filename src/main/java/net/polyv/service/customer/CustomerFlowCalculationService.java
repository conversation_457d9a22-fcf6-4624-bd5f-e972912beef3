package net.polyv.service.customer;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.config.PcsConfig;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.resource.CustomerPeriodResourceSettingRepository;
import net.polyv.model.entity.primary.resource.CustomerPeriodResourceSetting;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.pcs.api.req.crm.GetUserLast7DayConsumedRequest;
import net.polyv.modules.pcs.api.stereotype.ResourceEnum;
import net.polyv.modules.pcs.api.vo.crm.GetGrayUserResourceByZeroResponse;
import net.polyv.modules.pcs.api.vo.crm.GetUserLast7DayConsumedResponse;
import net.polyv.service.CacheService;
import net.polyv.service.GrayTestService;
import net.polyv.service.impl.resource.available.BaseAvailableCalcServiceImpl;
import net.polyv.service.impl.resource.available.TrafficAvailableCalcServiceImpl;

/**
 * <AUTHOR>
 * @date 2023/3/10
 */
@Component
@Slf4j
public class CustomerFlowCalculationService {
    @Resource
    private TrafficAvailableCalcServiceImpl trafficAvailableCalcService;
    @Resource
    private CacheService cacheService;
    @Resource
    private BaseAvailableCalcServiceImpl baseAvailableCalcService;
    @Resource
    private CustomerPeriodResourceSettingRepository customerPeriodResourceSettingRepository;
    @Resource
    private GrayTestService grayTestService;
    @Resource
    private CustomerLastConsumedService customerLastConsumedService;
    
    
    /**
     * 结算用户点播流量
     */
    public void preCalculationFlowGrayUser() {
        //获取用户从账单查询近七天消耗的数据
        LocalDateTime now = LocalDateTime.now();
        GetUserLast7DayConsumedRequest build = GetUserLast7DayConsumedRequest.builder()
                .queryTimeEnd(LocalDateTimeUtil.format(now, Constant.DATE_FORMAT_yyyy_MM_dd))
                .queryTimeStart(LocalDateTimeUtil.format(now.minusDays(7).plusDays(1), Constant.DATE_FORMAT_yyyy_MM_dd))
                .resourceCode(ResourceCodeConst.traffic.name())
                .build();
        List<GetUserLast7DayConsumedResponse> responseList = this.customerLastConsumedService.listByLastConsumedInCache(
                build);
        if (CollectionUtils.isEmpty(responseList)) {
            log.info("近七天消耗流量客户为空");
            return;
        }
        List<String> customerIds = responseList.stream()
                .map(GetUserLast7DayConsumedResponse::getCustomerId)
                .collect(Collectors.toList());
        this.calculationFlowIsZeroUser(customerIds);
        this.calculationPackageFlowUser(responseList);
    }
    
    /**
     * 流量
     * @param customerIds
     * @return
     */
    public List<GetGrayUserResourceByZeroResponse> cycleQuery(List<String> customerIds) {
        List<String> flowZeroUsers = cacheService.getFlowZeroUsers();
        if (CollectionUtils.isEmpty(flowZeroUsers)) {
            return new ArrayList<>();
        }
        log.info("流量为0 缓存数据：{}", flowZeroUsers);
        return flowZeroUsers.stream()
                .map(v -> GetGrayUserResourceByZeroResponse.builder().customerId(v).available(0L).recharge(0L).build())
                .collect(Collectors.toList());
    }
    
    /**
     * 流量
     * @param customerIds
     * @return
     */
    public List<GetGrayUserResourceByZeroResponse> cycleQueryPackage() {
        String packageList = cacheService.getPackageFlowSize();
        if (StringUtils.isEmpty(packageList)) {
            return new ArrayList<>();
        }
        JSONArray objects = JSONUtil.parseArray(packageList);
        List<GetGrayUserResourceByZeroResponse> responseList = JSONUtil.toList(objects,
                GetGrayUserResourceByZeroResponse.class);
        log.debug("套餐流量 缓存数据：{}", responseList);
        return responseList;
    }
    
    /**
     * 获取客户流量为0 并设置进缓存。时效1h
     * @param customerIds 客户id
     */
    public void calculationFlowIsZeroUser(List<String> customerIds) {
        //获取灰度客户有效期内 空间已经为0数据
        Date date = new Date();
        long l = System.currentTimeMillis();
        log.info("近七天消耗流量用户：{}", customerIds);
        List<String> users = customerIds.stream()
                .filter(v -> this.trafficAvailableCalcService.getAvailable(v, ResourceCodeConst.traffic.name(), date) <=
                        0L)
                .collect(Collectors.toList());
        cacheService.setZeroUsers(users, GlobalConfigConst.FLOW_AVAILABLE_IS_ZERO);
        log.info("缓存已设置达到流量为0,耗时：{}ms,用户数据：{}", System.currentTimeMillis() - l, users);
    }
    
    
    /**
     * 获取客户套餐流量 并设置进缓存。时效1h
     * @param responseList 近七天消耗
     */
    public void calculationPackageFlowUser(List<GetUserLast7DayConsumedResponse> responseList) {
        List<GetGrayUserResourceByZeroResponse> list = new ArrayList<>();
        //获取 临时+流量包为0 数据
        for (GetUserLast7DayConsumedResponse response : responseList) {
            String customerId = response.getCustomerId();
            String resource = ResourceEnum.traffic.name();
            Date date = new Date();
            long tempTotal = trafficAvailableCalcService.getTempAvailable(customerId, resource, date);
            long available = baseAvailableCalcService.getAvailable(customerId, resource, date);
            CustomerPeriodResourceSetting setting =
                    this.customerPeriodResourceSettingRepository.getCurrentEffectivePeriodSetting(
                    customerId, resource);
            if (tempTotal == 0 && available == 0) {
                GetGrayUserResourceByZeroResponse result = GetGrayUserResourceByZeroResponse.builder()
                        .available(trafficAvailableCalcService.getPeriodAvailable(customerId, resource, date))
                        .recharge(Objects.isNull(setting) || Objects.isNull(setting.getPeriodAmount()) ? 0L :
                                setting.getPeriodAmount())
                        .customerId(customerId)
                        .build();
                list.add(result);
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            log.info("设置套餐流量进入缓存，list={}", list);
            this.cacheService.setPackageFlow(list);
        }
        
    }
    
}
