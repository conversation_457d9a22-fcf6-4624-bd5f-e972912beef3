package net.polyv.service.customer;

import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.common.CommonOperateResultVO;

/**
 * 最低消费相关逻辑
 * <AUTHOR>
 * @since 2020/5/28
 */
public interface MinConsumptionService {

    /**
     * 计算最低消费
     * @param inputVO 最低消费入参
     * @return 计算结果
     */
    CommonOperateResultVO calcMinConsumption(CustomerInfoGetInputVO inputVO);
}
