package net.polyv.service.customer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.resource.CustomerPeriodResourceSettingRepository;
import net.polyv.model.entity.primary.resource.CustomerPeriodResourceSetting;
import net.polyv.modules.pcs.api.req.crm.GetUserVodPackageRequest;
import net.polyv.modules.pcs.api.vo.crm.GetUserVodPackageResponse;
import net.polyv.util.DateUtil;

/**
 * 客户点播查询服务
 * <AUTHOR>
 * @date 2023/3/13
 */
@Component
@Slf4j
public class CustomerVodQueryService {
     @Resource
    private CustomerPeriodResourceSettingRepository customerPeriodResourceSettingRepository;
    
    
    public List<GetUserVodPackageResponse> getVodPackageExpireList(GetUserVodPackageRequest request) {
        List<GetUserVodPackageResponse> responses  =new ArrayList<>();
        Date date = new Date();
        String time = DateUtil.formatDate(date);
        List<Map<String, Object>> resultList = this.customerPeriodResourceSettingRepository.getEffectivePeriod(
                Lists.newArrayList(ResourceCodeConst.space.name()), time, request.getDay());
        if (CollectionUtils.isEmpty(resultList)){
            return responses;
        }
        List<CustomerPeriodResourceSetting> effectivePeriod = JSONUtil.toList(JSONUtil.parseArray(resultList),
                CustomerPeriodResourceSetting.class);
        effectivePeriod.forEach(v->{
            GetUserVodPackageResponse getUserVodPackageResponse = new GetUserVodPackageResponse();
            getUserVodPackageResponse.setCustomerId(v.getCustomerId());
            getUserVodPackageResponse.setExpireTime(v.getEndDate());
            getUserVodPackageResponse.setDay(DateUtil.getleftDays(DateUtil.getBeginOfDay(date),v.getEndDate()));
            responses.add(getUserVodPackageResponse);
        });
        return  responses;
    }
}
