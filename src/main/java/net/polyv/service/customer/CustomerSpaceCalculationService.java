package net.polyv.service.customer;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.resource.CustomerPeriodResourceSettingRepository;
import net.polyv.modules.pcs.api.stereotype.ResourceEnum;
import net.polyv.modules.pcs.api.vo.crm.GetGrayUserResourceByZeroResponse;
import net.polyv.service.CacheService;
import net.polyv.service.impl.resource.available.SpaceAvailableCalcServiceImpl;
import net.polyv.web.model.account.item.ResAvailableStateDetailVO;

/**
 * <AUTHOR>
 * @date 2023/3/10
 */
@Component
@Slf4j
public class CustomerSpaceCalculationService {
    @Resource
    private SpaceAvailableCalcServiceImpl spaceAvailableCalcServiceImpl;
    @Resource
    private CacheService cacheService;
    @Resource
    private CustomerPeriodResourceSettingRepository customerPeriodResourceSettingRepository;
    
    /**
     * 循环查询点播空间可用数据
     * @param customerIds
     * @return
     */
    public List<GetGrayUserResourceByZeroResponse> cycleQuery(List<String> customerIds) {
        String packageList = cacheService.getPackageSpaceSize();
        if (StringUtils.isEmpty(packageList)) {
            return new ArrayList<>();
        }
        JSONArray objects = JSONUtil.parseArray(packageList);
        List<GetGrayUserResourceByZeroResponse> responseList = JSONUtil.toList(objects,
                GetGrayUserResourceByZeroResponse.class);
        log.debug("套餐空间 缓存数据：{}", responseList);
        return responseList;
    }
    
    /**
     * 获取客户空间为0 并设置进缓存。时效1h
     * @param customerIds 客户id
     */
    public void calculationSpaceIsZeroUser(List<String> customerIds) {
        //获取灰度客户有效期内 空间已经为0数据
        List<String> effectiveUser;
        if (CollectionUtils.isEmpty(customerIds)) {
            effectiveUser = this.customerPeriodResourceSettingRepository.getCurrentEffectivePeriodSettings(
                    ResourceEnum.space.name());
        } else {
            effectiveUser = this.customerPeriodResourceSettingRepository.getCurrentEffectivePeriodSettings(customerIds,
                    ResourceEnum.space.name());
        }
        if (CollectionUtils.isEmpty(effectiveUser)) {
            return;
        }
        Date date = new Date();
        long l = System.currentTimeMillis();
        log.info("当前点播套餐生效中用户：{}", effectiveUser);
        List<GetGrayUserResourceByZeroResponse> users = effectiveUser.stream().map(v -> {
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                // ignore
            }
            ResAvailableStateDetailVO availableDetail = this.spaceAvailableCalcServiceImpl.getAvailableDetail(v,
                    ResourceCodeConst.space.name(), date);
            if (Objects.nonNull(availableDetail)) {
                //计算超出比例 >=90% 的数据
                Long available = availableDetail.getTempAvailable() + availableDetail.getPeriodAvailable();
                Long total = availableDetail.getPeriodTotal() + availableDetail.getTempTotal();
                if (total == 0) {
                    return null;
                }
                if (available == 0) {
                    return GetGrayUserResourceByZeroResponse.builder()
                            .recharge(total)
                            .available(0L)
                            .customerId(v)
                            .build();
                }
                BigDecimal availableRatio = new BigDecimal(available).divide(new BigDecimal(total),2,
                        RoundingMode.HALF_UP);
                BigDecimal ratio = BigDecimal.ONE.subtract(availableRatio).multiply(new BigDecimal("100"));
                if (ratio.compareTo(new BigDecimal("90")) >= 0) {
                    return GetGrayUserResourceByZeroResponse.builder()
                            .recharge(total)
                            .available(available)
                            .customerId(v)
                            .build();
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        cacheService.setPackageSpace(users);
        log.info("缓存已设置达到>90%空间,耗时：{}ms,用户数据：{}", System.currentTimeMillis() - l, users.size());
    }
}
