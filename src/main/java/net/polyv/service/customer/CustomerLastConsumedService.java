package net.polyv.service.customer;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.ResourceAlterTypeConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.dao.primary.custom.AccountRechargeRecordDao;
import net.polyv.dao.primary.resource.ResourceAlterationRecordRepository;
import net.polyv.model.dto.CustomerLastConsumedDTO;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.pcs.api.req.crm.GetUserLast7DayConsumedRequest;
import net.polyv.modules.pcs.api.stereotype.ResourceEnum;
import net.polyv.modules.pcs.api.vo.crm.GetRechargeOrCreditResponse;
import net.polyv.modules.pcs.api.vo.crm.GetUserLast7DayConsumedResponse;
import net.polyv.service.CacheService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DingWarnRobot;
import net.polyv.web.model.account.item.ResAvailableStateVO;
import net.polyv.web.model.account.item.ResourceAvailableResultVO;
import net.polyv.web.model.account.item.ResourceAvailableSearchVO;

/**
 * <AUTHOR>
 * @date 2023/4/19
 */
@Component
@Slf4j
public class CustomerLastConsumedService {
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private BillingItemRepository billingItemRepository;
    @Resource
    private CacheService cacheService;
    @Resource
    private CustomerResourceService customerResourceService;
    @Resource
    private CustomerBillingDailyRepository customerBillingDailyRepository;
    @Resource
    private AccountRechargeRecordDao accountRechargeRecordDao;
    
    @Resource
    private ResourceAlterationRecordRepository resourceAlterationRecordRepository;
    
    private List<GetUserLast7DayConsumedResponse> getUserConsumedByLastDay7(GetUserLast7DayConsumedRequest request) {
        return getUserConsumedByLastDay7(request, true);
    }
    
    private List<GetUserLast7DayConsumedResponse> getUserConsumedByLastDay7(GetUserLast7DayConsumedRequest request,
            boolean needQueryAvailable) {
        return getResourceLast7DayConsumed(request, needQueryAvailable);
    }
    
    
    private List<GetUserLast7DayConsumedResponse> getResourceLast7DayConsumed(GetUserLast7DayConsumedRequest request,
                                                                              boolean needQueryAvailable) {
        List<GetUserLast7DayConsumedResponse> responseList = calculationUserConsumedByLastDay7(request,
                needQueryAvailable);
        if (responseList == null) {
            return null;
        }
        return responseList;
    }
    
    private List<Integer> getItemIds(String resourceCode) {
        List<BillingItem> billItems = this.billingItemRepository.findByResourceCode(resourceCode);
        if (CollectionUtils.isEmpty(billItems)) {
            log.error("无法获取资源编码对应的计费项：resourceCode={}", resourceCode);
            return null;
        }
        return billItems.stream().map(BillingItem::getId).collect(Collectors.toList());
    }
    
    private Map<String, Long> getAvailableMap(boolean isAmount,
                                              boolean isResourcePoint ,
                                              List<String> userIds,
                                              String resourceCode) {
        
        Map<String, Long> map = new HashMap<>();
        if (isAmount) {
            List<GetRechargeOrCreditResponse> responses = this.accountRechargeRecordDao.sumBalanceAmountByCustomerIds(
                    userIds);
            if (CollectionUtils.isEmpty(responses)) {
                return map;
            }
            return responses.stream()
                    .collect(Collectors.toMap(GetRechargeOrCreditResponse::getCustomerId,
                            GetRechargeOrCreditResponse::getRecharge));
        }

        if(isResourcePoint){
            List<GetRechargeOrCreditResponse> responses = this.accountRechargeRecordDao.sumBalanceAmountByCustomerIdsResourcePoint(
                    userIds);
            if (CollectionUtils.isEmpty(responses)) {
                return map;
            }
            return responses.stream()
                    .collect(Collectors.toMap(GetRechargeOrCreditResponse::getCustomerId,
                            GetRechargeOrCreditResponse::getRecharge));
        }

        //获取充值资源可用量
        ResourceAvailableSearchVO vo = new ResourceAvailableSearchVO();
        vo.setCustomerIdList(userIds);
        vo.setResourceCodeList(Lists.newArrayList(resourceCode));
        ResourceAvailableResultVO resultVO = this.customerResourceService.searchCustomerCurrentAvailable(vo);
        Map<String, Map<String, ResAvailableStateVO>> stateMap = resultVO.getStateMap();
        for (String userId : userIds) {
            if (stateMap.containsKey(userId)) {
                Map<String, ResAvailableStateVO> resourceMap = stateMap.get(userId);
                if (resourceMap.containsKey(resourceCode)) {
                    ResAvailableStateVO resAvailableStateVO = resourceMap.get(resourceCode);
                    map.put(userId, resAvailableStateVO.getAvailable());
                }
            }
        }
        return map;
    }
    
    private Map<String, Long> getSumRechargeMap(boolean isAmount, boolean isResourcePoint ,List<String> userIds, String resourceCode) {
        
        Map<String, Long> map = new HashMap<>();
        //金额资源有需要再处理
        if (isAmount) {
            return map;
        }
        if(isResourcePoint){
            return map ;
        }
        //资源包总充值量获取
        if (isPermanentResource(resourceCode)) {
            userIds.forEach(userId -> {
                //累计充值直播流量
                long sumDepositLiveFlow = resourceAlterationRecordRepository.sumByCustomerIdAndResourceCodeAndType(
                        userId, ResourceCodeConst.live_flow.name(), ResourceAlterTypeConst.deposit.name());
                map.put(userId, sumDepositLiveFlow);
            });
        }
        return map;
    }
    
    /**
     * 是否永久资源（需要自己拓展）
     * @param resourceCode
     * @return
     */
    private boolean isPermanentResource(String resourceCode) {
        return ResourceEnum.live_flow.name().equals(resourceCode) || ResourceEnum.duration.name().equals(resourceCode);
    }
    
    private List<GetUserLast7DayConsumedResponse> calculationUserConsumedByLastDay7(
            GetUserLast7DayConsumedRequest request, boolean needQueryAvailable) {
        boolean isAmount = ResourceEnum.amount.name().equals(request.getResourceCode());
        boolean isResourcePoint = ResourceEnum.resource_point.name().equals(request.getResourceCode()) ;
        boolean needQuerySumRecharge =
                Objects.nonNull(request.getNeedQuerySumRecharge()) && request.getNeedQuerySumRecharge();

        List<Integer> itemIds = isAmount ? new ArrayList<>() : getItemIds(request.getResourceCode());
        String start = request.getQueryTimeStart();
        String end = request.getQueryTimeEnd();
        List<Map<String, Object>> result;
        if(isAmount){
            result = this.customerBillingDailyRepository.getCustomerLastDayAmountConsumed(start) ;
        } else if(isResourcePoint){
            result = this.customerBillingDailyRepository.getCustomerLastDayResourcePointConsumed(start) ;
        } else{
            result = this.customerBillingDailyRepository.getCustomerLastDayConsumedByCustomerId(start, end, itemIds,
                    false, null);
        }

        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        String s = JsonUtil.beanToString(result).orElse("");
        List<CustomerLastConsumedDTO> results = JSONArray.parseArray(s, CustomerLastConsumedDTO.class);
        if (CollectionUtils.isEmpty(results)) {
            return null;
        }
        List<String> userIds = results.stream()
                .map(CustomerLastConsumedDTO::getCustomerId)
                .collect(Collectors.toList());
        log.info("近七天消耗客户数据：resource={},size={},userId={}", request.getResourceCode(), userIds.size(),
                userIds);
        long l = System.currentTimeMillis();
        Map<String, Long> availableMap =
                needQueryAvailable ? getAvailableMap(isAmount,isResourcePoint , userIds, request.getResourceCode()) : new HashMap<>();
        Map<String, Long> sumRechargeMap =
                needQuerySumRecharge ? getSumRechargeMap(isAmount, isResourcePoint ,userIds, request.getResourceCode()) :
                        new HashMap<>();
        List<GetUserLast7DayConsumedResponse> responseList = new ArrayList<>();
        for (CustomerLastConsumedDTO dto : results) {
            GetUserLast7DayConsumedResponse response = new GetUserLast7DayConsumedResponse();
            response.setAvailable(availableMap.getOrDefault(dto.getCustomerId(), 0L));
            response.setLast7DayConsumed(dto.getSumConsumed());
            response.setLast7DayConsumedDay(dto.getDayNum());
            response.setCustomerId(dto.getCustomerId());
            response.setSumRecharge(sumRechargeMap.getOrDefault(dto.getCustomerId(), 0L));
            
            responseList.add(response);
        }
        log.info("加载近七天客户数据耗时：resource={},{}ms", request.getResourceCode(), System.currentTimeMillis() - l);
        return responseList;
    }
    
    public List<GetUserLast7DayConsumedResponse> listByLastConsumedInCache(GetUserLast7DayConsumedRequest request) {
        List<GetUserLast7DayConsumedResponse> responseList = cacheService.getLastConsumedBy7(request);
        if (CollectionUtils.isEmpty(responseList)) {
            return null;
        }
        List<GetUserLast7DayConsumedResponse> filterList = new ArrayList<>();
        if (Objects.nonNull(request.getNeedQueryGray()) && request.getNeedQueryGray() &&
                !CollectionUtils.isEmpty(request.getGrayUserIds())) {
            for (GetUserLast7DayConsumedResponse response : responseList) {
                if (request.getGrayUserIds().contains(response.getCustomerId())) {
                    filterList.add(response);
                }
            }
        }
        return CollectionUtils.isEmpty(filterList) ? responseList : filterList;
    }
    
    public void setCacheDataByLastConsumed(GetUserLast7DayConsumedRequest request) {
        List<GetUserLast7DayConsumedResponse> consumed = this.getUserConsumedByLastDay7(request);
        if (CollectionUtils.isEmpty(consumed)) {
            log.info("该资源无近七天消耗量：{}", request);
            return;
        }
        cacheService.setLastConsumedBy7(consumed, request.getResourceCode());
    }
    
    public void preCalculateUserConsumeByLast7Day() {
        //获取灰度用户从账单查询近七天消耗的数据
        LocalDateTime now = LocalDateTime.now();
        List<ResourceEnum> resourceCodeList = Lists.newArrayList(ResourceEnum.traffic,
                ResourceEnum.duration,
                ResourceEnum.mic_duration,
                ResourceEnum.guide_duration,
                ResourceEnum.amount,
                ResourceEnum.live_flow ,
                ResourceEnum.materialspace,
                ResourceEnum.resource_point);
        for (ResourceEnum resourceEnum : resourceCodeList) {
            GetUserLast7DayConsumedRequest build = GetUserLast7DayConsumedRequest.builder()
                    .queryTimeEnd(LocalDateTimeUtil.format(now, Constant.DATE_FORMAT_yyyy_MM_dd))
                    .queryTimeStart(
                            LocalDateTimeUtil.format(now.minusDays(7).plusDays(1), Constant.DATE_FORMAT_yyyy_MM_dd))
                    .build();
            if (ResourceEnum.live_flow.equals(resourceEnum)) {
                build.setNeedQuerySumRecharge(true);
            }
            build.setResourceCode(resourceEnum.name());
            try {
                this.setCacheDataByLastConsumed(build);
            } catch (Exception e) {
                log.error("无法计算消耗量数据：{}", build, e);
                dingWarnRobot.sendWarnMsg("【预计算告警消息失败】",
                        Lists.newArrayList("参数：", JsonUtil.beanToString(build).get()));
            }
        }
    }
}
