package net.polyv.service.customer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.modules.pcs.api.vo.crm.*;
import net.polyv.util.JsonMapper;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.dao.primary.AccountRechargeRecordRepository;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.dao.primary.custom.AccountRechargeRecordDao;
import net.polyv.dao.primary.resource.CustomerPeriodResourceSettingRepository;
import net.polyv.dao.primary.resource.CustomerTempResourceSettingRepository;
import net.polyv.dao.primary.resource.ResourceAlterationRecordRepository;
import net.polyv.model.dto.CustomerLastConsumedDTO;
import net.polyv.model.dto.CustomerLastRechargeTimeDTO;
import net.polyv.model.dto.CustomerLastTestAndDonateDTO;
import net.polyv.model.dto.CustomerNumDTO;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.crm.GetCreditUsedRequest;
import net.polyv.modules.pcs.api.req.crm.GetGrayUserResourceByZeroRequest;
import net.polyv.modules.pcs.api.req.crm.GetLastRechargeToNowResourceRequest;
import net.polyv.modules.pcs.api.req.crm.GetRechargeResourceRequest;
import net.polyv.modules.pcs.api.req.crm.GetUnpaidBillsRequest;
import net.polyv.modules.pcs.api.req.crm.GetUserLast7DayConsumedRequest;
import net.polyv.modules.pcs.api.stereotype.ResourceEnum;
import net.polyv.modules.user.api.stereotype.ResourceCodeEnum;
import net.polyv.service.impl.account.UserCreditQueryService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.PageUtils;
import net.polyv.web.model.account.item.ResAvailableStateVO;
import net.polyv.web.model.account.item.ResourceAvailableResultVO;
import net.polyv.web.model.account.item.ResourceAvailableSearchVO;

/**
 * 客户资源查询服务
 * <AUTHOR>
 * @date 2022/11/28 14:47
 */
@Component
@Slf4j
public class CustomerResourceQueryService {
    @Resource
    private ResourceAlterationRecordRepository resourceAlterationRecordRepository;
    @Resource
    private AccountRechargeRecordRepository accountRechargeRecordRepository;
    @Resource
    private AccountRechargeRecordDao accountRechargeRecordDao;
    @Resource
    private CustomerBillingDailyRepository customerBillingDailyRepository;
    @Resource
    private BillingItemRepository billingItemRepository;
    @Resource
    private CustomerResourceService customerResourceService;
    @Resource
    private UserCreditQueryService userCreditQueryService;
    @Resource
    private CustomerSpaceCalculationService customerSpaceCalculationService;
    @Resource
    private CustomerFlowCalculationService customerFlowCalculationService;
    @Resource
    private CustomerAmountQueryService customerAmountQueryService;
    @Resource
    private CustomerLastConsumedService customerLastConsumedService;
    @Resource
    private CustomerPeriodResourceSettingRepository customerPeriodResourceSettingRepository;
    @Resource
    private CustomerTempResourceSettingRepository  customerTempResourceSettingRepository;
    @Resource
    private LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository;
    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;
    
    public Pager<GetRechargeAmountExpireResponse> getRechargeExpireByPage(String expireTime, Integer pageIndex,
            Integer pageSize) {
        Pageable pageable = PageRequest.of(pageIndex - 1, pageSize);
        //余额
        String now = DateUtil.format(new Date(), Constant.DATE_FORMAT_yyyy_MM_dd);
        return this.accountRechargeRecordDao.sumBalanceLessThenExpireTime(now, expireTime, pageable);
    }

    public Pager<GetRechargeAmountExpireResponse> getRechargeResourcePointExpireByPage(String expireTime, Integer pageIndex,
                                                                          Integer pageSize) {
        Pageable pageable = PageRequest.of(pageIndex - 1, pageSize);
        //余额
        String now = DateUtil.format(new Date(), Constant.DATE_FORMAT_yyyy_MM_dd);
        return this.accountRechargeRecordDao.sumBalanceLessThenExpireTimeResourcePoint(now, expireTime, pageable);
    }

    public Pager<GetRechargeAmountExpireResponse> getRechargeResourceExpireByPage(String itemCode,String expireTime, Integer pageIndex,
                                                                                       Integer pageSize) {
        Pageable pageable = PageRequest.of(pageIndex - 1, pageSize);
        //余额
        String now = DateUtil.format(new Date(), Constant.DATE_FORMAT_yyyy_MM_dd);
        Page<BillingCustomerConfig> pager = this.billingCustomerConfigRepository.customerResourceWillExpireByPage(itemCode,now, expireTime, pageable);
        return sumBalanceLessThenExpireTimeResource(pager);
    }

    public Pager<GetRechargeAmountExpireResponse> getRechargeResourceNotExpireByPage(String itemCode, Integer pageIndex,Integer pageSize) {
        Pageable pageable = PageRequest.of(pageIndex - 1, pageSize);
        //余额
        String now = DateUtil.format(new Date(), Constant.DATE_FORMAT_yyyy_MM_dd);
        Page<BillingCustomerConfig> pager = this.billingCustomerConfigRepository.customerResourceNotExpireByPage(itemCode,now, pageable);
        return sumBalanceLessThenExpireTimeResource(pager);
    }

    public Pager<GetRechargeAmountExpireResponse> sumBalanceLessThenExpireTimeResource(Page<BillingCustomerConfig> list) {
        if(Objects.isNull(list) || !list.hasContent()){
            return new Pager<>();
        }
        // 转换对象
        List<ContractAmountVO> responses = null;
        List<GetRechargeAmountExpireResponse> responseList = new ArrayList<>();
        Map<String, List<BillingCustomerConfig>> customerMap = list.getContent().stream()
                .collect(Collectors.groupingBy(BillingCustomerConfig::getUnionId));
        customerMap.forEach((k, v) -> {
            List<ContractAmountVO> amountExpireList = new ArrayList<>();
            for(BillingCustomerConfig config : v){
                ContractAmountVO vo = new ContractAmountVO();
                vo.setExpireTime(config.getExpireTime());
                vo.setCustomerId(config.getUnionId());
                vo.setContractId(config.getContractId());
                vo.setAmount(config.getAvailableValue());
                vo.setResourceType(config.getResourceType());
                amountExpireList.add(vo);
            }
            responseList.add(new GetRechargeAmountExpireResponse(k, amountExpireList));
        });
        return PageUtils.assemblyPage(list, responseList);
    }
    
    /**
     * 该方法主要兼容
     * 空间不可用=0.流量不可用=0
     * 以及套餐流量不足
     **/
    public List<GetGrayUserResourceByZeroResponse> getGrayUserResourceByZero(GetGrayUserResourceByZeroRequest request) {
        
        Boolean queryByZero = request.getQueryByZero();
        //点播空间：获取点播套餐未过期，但空间已经用完 直接接管
        if (ResourceEnum.space.name().equals(request.getResourceCode())) {
            return findPackageActiveButSpace(null);
        }
        //点播流量不足：获取点播套餐未过期，但空间已经用完 直接接管
        if (ResourceEnum.traffic.name().equals(request.getResourceCode()) && queryByZero) {
            return findPackageActiveButFlowIsUnable(null);
        }
        if (ResourceEnum.traffic.name().equals(request.getResourceCode())) {
            //点播流量：套餐流量不足（临时+周期） 过滤 永久资源为0
            return vodPackageFlow();
        }
        return null;
    }
    
    private List<GetGrayUserResourceByZeroResponse> findPackageActiveButFlowIsUnable(List<String> grayUsers) {
        //获取灰度客户有效期内 空间已经为0数据
        List<GetGrayUserResourceByZeroResponse> responseList = new ArrayList<>();
        List<GetGrayUserResourceByZeroResponse> list = this.customerFlowCalculationService.cycleQuery(grayUsers);
        if (!CollectionUtils.isEmpty(list)) {
            responseList.addAll(list);
        }
        return responseList;
    }
    
    private List<GetGrayUserResourceByZeroResponse> vodPackageFlow() {
        return this.customerFlowCalculationService.cycleQueryPackage();
    }
    
    private List<GetGrayUserResourceByZeroResponse> findPackageActiveButSpace(List<String> grayUsers) {
        //获取灰度客户有效期内 空间已经为0数据
        List<GetGrayUserResourceByZeroResponse> responseList = new ArrayList<>();
        List<GetGrayUserResourceByZeroResponse> list = this.customerSpaceCalculationService.cycleQuery(grayUsers);
        if (!CollectionUtils.isEmpty(list)) {
            responseList.addAll(list);
        }
        return responseList;
    }
    
    
    public List<GetCreditUsedResponse> getUserCreditUsed(GetCreditUsedRequest request) {
        //授信近七天消耗 只能从 金额账单触发，因为场景：充值金额不足扣 会扣授信、 单有授信额度的时候也是出金额账单 作为判断的基础数据
        GetUserLast7DayConsumedRequest build = GetUserLast7DayConsumedRequest.builder()
                .resourceCode(ResourceEnum.amount.name())
                .queryTimeEnd(request.getQueryTimeEnd())
                .queryTimeStart(request.getQueryTimeStart())
                .build();
        List<GetUserLast7DayConsumedResponse> userConsumedByLastDay7 =
                this.customerLastConsumedService.listByLastConsumedInCache(build);
        if (CollectionUtils.isEmpty(userConsumedByLastDay7)) {
            return null;
        }
        List<String> customerIds = userConsumedByLastDay7.stream()
                .map(GetUserLast7DayConsumedResponse::getCustomerId)
                .collect(Collectors.toList());
        return userCreditQueryService.getCustomerCreditList(customerIds);
    }
    
    
    public Pager<GetUnpaidBillsResponse> getUserUnpaidBills(GetUnpaidBillsRequest request) {
        Pageable pageable = PageRequest.of(request.getPageNumber() - 1, request.getPageSize());
        //处理连麦 与 普通计费项待支付账单时间
        Page<Map<String, Object>> maps = this.customerBillingDailyRepository.pageUnpaidBillsByTime(
                request.getQueryTimeStart(), request.getQueryTimeEnd(), pageable);
        if (CollectionUtils.isEmpty(maps.getContent())) {
            return null;
        }
        Optional<String> s = JsonUtil.beanToString(maps.getContent());
        List<GetUnpaidBillsResponse> result = JSONArray.parseArray(s.get(), GetUnpaidBillsResponse.class);
        List<String> userIds = result.stream().map(GetUnpaidBillsResponse::getCustomerId).collect(Collectors.toList());
        
        List<GetUnpaidBillsResponse> responses = new ArrayList<>();
        Map<String, Long> availableCreditMap = this.userCreditQueryService.customerAvailableCredit(userIds);
        // 可用授信额度 = 0 过滤授信等于0
        for (GetUnpaidBillsResponse dto : result) {
            // 授信额度
            Long validCredit = availableCreditMap.getOrDefault(dto.getCustomerId(), 0L);
            if (validCredit <= 0) {
                responses.add(dto);
            }
        }
        return PageUtils.assemblyPage(maps, responses);
    }
    
    public List<GetLastRechargeToNowResourceResponse> getLastRechargeToNewResource(
            GetLastRechargeToNowResourceRequest request) {
        //获取最后一笔正式充值记录
        Date date = new Date();
        return getResourceByLastRecharge(request, date);
    }
    
    public List<GetRechargeResourceResponse> getRechargeResourceUsers(GetRechargeResourceRequest request) {
        Date endTime = cn.hutool.core.date.DateUtil.parse(request.getQueryTimeEnd());
        Date startTime = cn.hutool.core.date.DateUtil.parse(request.getQueryTimeStart());
        List<CustomerNumDTO> resourceRecharge = getResourceRecharge(request, endTime, startTime);
        List<CustomerNumDTO> amountRecharge = getAmountRecharge(request, endTime, startTime);
        Map<String, Boolean> resourceMap = new HashMap<>();
        Map<String, Boolean> amountMap = new HashMap<>();
        Set<String> unionIds = Sets.newHashSet();
        if (!CollectionUtils.isEmpty(resourceRecharge)) {
            resourceMap = resourceRecharge.stream()
                    .collect(Collectors.toMap(CustomerNumDTO::getCustomerId, v -> Boolean.TRUE));
            unionIds.addAll(resourceRecharge.stream()
                    .map(CustomerNumDTO::getCustomerId)
                    .distinct()
                    .collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(amountRecharge)) {
            amountMap = amountRecharge.stream()
                    .collect(Collectors.toMap(CustomerNumDTO::getCustomerId, v -> Boolean.TRUE));
            unionIds.addAll(
                    amountRecharge.stream().map(CustomerNumDTO::getCustomerId).distinct().collect(Collectors.toList()));
        }
        List<GetRechargeResourceResponse> responseList = new ArrayList<>();
        for (String customerId : unionIds) {
            GetRechargeResourceResponse response = new GetRechargeResourceResponse();
            response.setCustomerId(customerId);
            response.setHasRechargeResource(resourceMap.containsKey(customerId));
            response.setHasRechargeAmount(amountMap.containsKey(customerId));
            responseList.add(response);
        }
        return responseList;
    }
    
    public List<GetUserLast7DayConsumedResponse> getUserConsumedByLastDay7(GetUserLast7DayConsumedRequest request){
        return getUserConsumedByLastDay7(request,true);
    }
    
    public List<GetUserLast7DayConsumedResponse> getUserConsumedByLastDay7(GetUserLast7DayConsumedRequest request,
            boolean needQueryAvailable) {
        boolean isAmount = ResourceEnum.amount.name().equals(request.getResourceCode());
        return getResourceLast7DayConsumed(request, isAmount,needQueryAvailable);
    }
    
    
    private List<GetUserLast7DayConsumedResponse> getResourceLast7DayConsumed(GetUserLast7DayConsumedRequest request,
            boolean isAmount, boolean needQueryAvailable) {
        List<Integer> itemIds = isAmount ? new ArrayList<>() : getItemIds(request.getResourceCode());
        String start = request.getQueryTimeStart();
        String end = request.getQueryTimeEnd();
        
        List<Map<String, Object>> result =
                isAmount ? this.customerBillingDailyRepository.getCustomerLastDayAmountConsumed(start) :
                        this.customerBillingDailyRepository.getCustomerLastDayConsumedByCustomerId(start, end, itemIds,
                                request.getNeedQueryGray(), request.getGrayUserIds());
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        String s = JsonUtil.beanToString(result).orElse("");
        List<CustomerLastConsumedDTO> results = JSONArray.parseArray(s, CustomerLastConsumedDTO.class);
        if (CollectionUtils.isEmpty(results)) {
            return null;
        }
        List<String> userIds = results.stream()
                .map(CustomerLastConsumedDTO::getCustomerId)
                .collect(Collectors.toList());
        log.info("近七天消耗客户数据：size={},userId={}", userIds.size(), userIds);
        long l = System.currentTimeMillis();
        Map<String, Long> availableMap =
                needQueryAvailable ? getAvailableMap(isAmount, userIds, request.getResourceCode()) : new HashMap<>();
        List<GetUserLast7DayConsumedResponse> responseList = new ArrayList<>();
        for (CustomerLastConsumedDTO dto : results) {
            GetUserLast7DayConsumedResponse response = new GetUserLast7DayConsumedResponse();
            response.setAvailable(availableMap.getOrDefault(dto.getCustomerId(), 0L));
            response.setLast7DayConsumed(dto.getSumConsumed());
            response.setLast7DayConsumedDay(dto.getDayNum());
            response.setCustomerId(dto.getCustomerId());
            responseList.add(response);
        }
        log.info("加载近七天客户数据耗时：{}ms", System.currentTimeMillis() - l);
        return responseList;
    }
    
    private Map<String, Long> getAvailableMap(boolean isAmount, List<String> userIds, String resourceCode) {
        
        Map<String, Long> map = new HashMap<>();
        if (isAmount) {
            List<GetRechargeOrCreditResponse> responses = this.accountRechargeRecordDao.sumBalanceAmountByCustomerIds(
                    userIds);
            if (CollectionUtils.isEmpty(responses)) {
                return map;
            }
            return responses.stream()
                    .collect(Collectors.toMap(GetRechargeOrCreditResponse::getCustomerId,
                            GetRechargeOrCreditResponse::getRecharge));
        }
        //获取充值资源可用量
        ResourceAvailableSearchVO vo = new ResourceAvailableSearchVO();
        vo.setCustomerIdList(userIds);
        vo.setResourceCodeList(Lists.newArrayList(resourceCode));
        ResourceAvailableResultVO resultVO = this.customerResourceService.searchCustomerCurrentAvailable(vo);
        Map<String, Map<String, ResAvailableStateVO>> stateMap = resultVO.getStateMap();
        for (String userId : userIds) {
            if (stateMap.containsKey(userId)) {
                Map<String, ResAvailableStateVO> resourceMap = stateMap.get(userId);
                if (resourceMap.containsKey(resourceCode)) {
                    ResAvailableStateVO resAvailableStateVO = resourceMap.get(resourceCode);
                    map.put(userId, resAvailableStateVO.getAvailable());
                }
            }
        }
        return map;
    }
    
    private List<Integer> getItemIds(String resourceCode) {
        List<BillingItem> billItems = this.billingItemRepository.findByResourceCode(resourceCode);
        if (CollectionUtils.isEmpty(billItems)) {
            log.error("无法获取资源编码对应的计费项：resourceCode={}", resourceCode);
            return null;
        }
        return billItems.stream().map(BillingItem::getId).collect(Collectors.toList());
    }
    
    private List<CustomerNumDTO> getAmountRecharge(GetRechargeResourceRequest request, Date endTime, Date startTime) {
        String customerIds = request.getCustomerIds();
        List<String> unionIds =
                StringUtils.isNotBlank(customerIds) ? Arrays.asList(customerIds.split(Constant.COMMA)) : null;
        DateTime dateTime = DateUtil.endOfDay(endTime);
        //转字符串
        String start = DateUtil.format(startTime, Constant.DATE_TIME_FORMAT_yyyy_MM_dd_HH_mm_ss);
        String end = DateUtil.format(dateTime, Constant.DATE_TIME_FORMAT_yyyy_MM_dd_HH_mm_ss);
        List<Map<String, Object>> amountRecharge;
        if (CollectionUtils.isEmpty(unionIds)) {
            amountRecharge = this.accountRechargeRecordRepository.getCustomerRechargeNum(start, end);
        } else {
            amountRecharge = this.accountRechargeRecordRepository.getCustomerRechargeNum(unionIds, start, end);
        }
        if (CollectionUtils.isEmpty(amountRecharge)) {
            return null;
        }
        String s = JsonUtil.beanToString(amountRecharge).orElse("");
        return JSONArray.parseArray(s, CustomerNumDTO.class);
    }
    
    private List<CustomerNumDTO> getResourceRecharge(GetRechargeResourceRequest request, Date endTime, Date startTime) {
        String customerIds = request.getCustomerIds();
        List<String> unionIds =
                StringUtils.isNotBlank(customerIds) ? Arrays.asList(customerIds.split(Constant.COMMA)) : null;
        List<String> resourceCodes = Arrays.asList(request.getResourceCodes().split(Constant.COMMA));
        String endDate = net.polyv.util.DateUtil.formatDate(endTime);
        String startDate = net.polyv.util.DateUtil.formatDate(startTime);
        List<Map<String, Object>> resourceRecharges = new ArrayList<>();
    
        //并发峰值
        if (resourceCodes.contains(ResourceEnum.peak_concurrence.name())) {
            List<Map<String, Object>> peakConcurrenceList;
            if (CollectionUtils.isEmpty(unionIds)) {
                peakConcurrenceList = this.resourceAlterationRecordRepository.getCustomerRechargeNum(
                        Lists.newArrayList(ResourceCodeEnum.concurrence.name()), startDate, endDate);
            } else {
                peakConcurrenceList = this.resourceAlterationRecordRepository.getCustomerRechargeNum(
                        Lists.newArrayList(ResourceCodeEnum.concurrence.name()), unionIds, startDate, endDate);
            }
            if (!CollectionUtils.isEmpty(peakConcurrenceList)) {
                resourceRecharges.addAll(peakConcurrenceList);
            }
        }
    
        if (resourceCodes.contains(ResourceEnum.concurrence.name())) {
            //查询并发变动记录
            List<Map<String, Object>> concurrentList;
            if (CollectionUtils.isEmpty(unionIds)) {
                concurrentList = this.liveCustomerConcurrenceSettingRepository.queryDepositConcurrentRecord(startDate,
                        endDate);
            } else {
                concurrentList = this.liveCustomerConcurrenceSettingRepository.queryDepositConcurrentRecord(unionIds,
                        startDate, endDate);
            }
            if (!CollectionUtils.isEmpty(concurrentList)) {
                resourceRecharges.addAll(concurrentList);
            }
        }
        //点播: 空间、流量查询套餐数据
        if (resourceCodes.contains(ResourceEnum.space.name()) || resourceCodes.contains(ResourceEnum.traffic.name())) {
            ArrayList<String> vodResources = Lists.newArrayList(ResourceEnum.space.name(), ResourceEnum.traffic.name());
            List<Map<String, Object>> periodList;
            List<Map<String, Object>> tempList;
            if (CollectionUtils.isEmpty(unionIds)) {
                periodList = this.customerPeriodResourceSettingRepository.queryDepositRecord(vodResources, startDate, endDate);
                tempList = this.customerTempResourceSettingRepository.queryDepositRecord(vodResources, startDate, endDate);
            } else {
                periodList = this.customerPeriodResourceSettingRepository.queryDepositRecord(vodResources, unionIds, startDate, endDate);
                tempList = this.customerTempResourceSettingRepository.queryDepositRecord(vodResources, unionIds, startDate, endDate);
            }
            resourceRecharges.addAll(ListUtils.emptyIfNull(periodList));
            resourceRecharges.addAll(ListUtils.emptyIfNull(tempList));
        }

        //素材库空间的套餐查询
        if (resourceCodes.contains(ResourceEnum.materialspace.name())) {
            String configEndDateTime = endDate + " 23:59:59";
            String configStartTime = DateUtil.format(startTime, Constant.DATE_TIME_FORMAT_yyyy_MM_dd_HH_mm_ss);
            log.info("============== resourceCodes {} startTime {} endTime {}" ,resourceCodes,configStartTime,configEndDateTime);
            List<Map<String,Object>> configList = billingCustomerConfigRepository.getCustomerRechargeNum(ResourceEnum.materialspace.name(),configStartTime,configEndDateTime);
            resourceRecharges.addAll(configList);
        }

        //加载其他资源查询
        List<Map<String, Object>> resourceList;
        if (CollectionUtils.isEmpty(unionIds)) {
            resourceList = this.resourceAlterationRecordRepository.getCustomerRechargeNum(resourceCodes, startDate,
                    endDate);
        } else {
            resourceList = this.resourceAlterationRecordRepository.getCustomerRechargeNum(resourceCodes, unionIds,
                    startDate, endDate);
        }
        if (!CollectionUtils.isEmpty(resourceList)) {
            resourceRecharges.addAll(resourceList);
        }
        if (CollectionUtils.isEmpty(resourceRecharges)) {
            return null;
        }
        String s = JsonUtil.beanToString(resourceRecharges).orElse("");
        List<CustomerNumDTO> customerNumDTOS = JSONArray.parseArray(s, CustomerNumDTO.class);
        if (CollectionUtils.isEmpty(customerNumDTOS)) {
            return null;
        }
        Map<String, List<CustomerNumDTO>> collect = customerNumDTOS.stream()
                .collect(Collectors.groupingBy(CustomerNumDTO::getCustomerId));
        List<CustomerNumDTO> resultList  =new ArrayList<>();
        collect.forEach((k,v)->{
            long sum = v.stream().mapToLong(CustomerNumDTO::getNum).sum();
            resultList.add(new CustomerNumDTO(k,sum));
        });
        return resultList;
    }
    
    
    private List<GetLastRechargeToNowResourceResponse> getResourceByLastRecharge(
            GetLastRechargeToNowResourceRequest request, Date date) {
        boolean isAmount = ResourceEnum.amount.name().equals(request.getResourceCode());
        boolean isResourcePoint = ResourceEnum.resource_point.name().equals(request.getResourceCode()) ;

        List<String> unionIds = Arrays.asList(request.getCustomerIds().split(Constant.COMMA));
        if (isAmount) {
            return this.customerAmountQueryService.getDonateAndTestLastNormalRecharge(unionIds);
        }

        if(isResourcePoint){
            return this.customerAmountQueryService.getDonateAndTestLastNormalRechargeResourcePoint(unionIds) ;
        }

        List<CustomerLastRechargeTimeDTO> lastRecharge = getLastRecharge(request);
        if (CollectionUtils.isEmpty(lastRecharge)) {
            return null;
        }
        List<GetLastRechargeToNowResourceResponse> responseList = new ArrayList<>();
        Map<Date, List<CustomerLastRechargeTimeDTO>> collect = lastRecharge.stream()
                .collect(Collectors.groupingBy(CustomerLastRechargeTimeDTO::getDepositTime));
        collect.forEach((rechargeTime, list) -> {
            List<String> customerIds = list.stream()
                    .map(CustomerLastRechargeTimeDTO::getCustomerId)
                    .collect(Collectors.toList());
            List<CustomerLastTestAndDonateDTO> testAndDonateList = getLastTestAndDonate(request, date, rechargeTime,
                    customerIds);
            Map<String, CustomerLastTestAndDonateDTO> testAndDonateMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(testAndDonateList)) {
                testAndDonateMap = testAndDonateList.stream()
                        .collect(Collectors.toMap(v -> v.getCustomerId() + v.getResourceType(), Function.identity()));
            }
            //获取测试+赠送资源数据
            Map<String, CustomerLastTestAndDonateDTO> finalTestAndDonateMap = testAndDonateMap;
            list.forEach(v -> {
                GetLastRechargeToNowResourceResponse response = new GetLastRechargeToNowResourceResponse();
                Integer donate = ResourceTypeEnum.DONATE.getCode();
                Integer test = ResourceTypeEnum.TEST.getCode();
                CustomerLastTestAndDonateDTO donateDTO = finalTestAndDonateMap.get(v.getCustomerId() + donate);
                CustomerLastTestAndDonateDTO testDTO = finalTestAndDonateMap.get(v.getCustomerId() + test);
                response.setCustomerId(v.getCustomerId());
                response.setLastRechargeValue(v.getAlteration());
                response.setDonateRechargeValue(
                        Objects.nonNull(donateDTO) && Objects.nonNull(donateDTO.getNum()) ? donateDTO.getNum() : 0L);
                response.setTestRechargeValue(
                        Objects.nonNull(testDTO) && Objects.nonNull(testDTO.getNum()) ? testDTO.getNum() : 0L);
                responseList.add(response);
            });
        });
        return responseList;
    }
    
    private List<CustomerLastTestAndDonateDTO> getLastTestAndDonate(GetLastRechargeToNowResourceRequest request,
            Date date, Date rechargeTime, List<String> customerIds) {
        List<Map<String, Object>> mapList =
                this.resourceAlterationRecordRepository.getDonateAndTestByLastNormalRecharge(
                request.getResourceCode(), customerIds, rechargeTime, date);
        if (CollectionUtils.isEmpty(mapList)) {
            return null;
        }
        String s = JsonUtil.beanToString(mapList).orElse("");
        return JSONArray.parseArray(s, CustomerLastTestAndDonateDTO.class);
    }
    
    private List<CustomerLastRechargeTimeDTO> getLastRecharge(GetLastRechargeToNowResourceRequest request) {
        List<String> unionIds = Arrays.asList(request.getCustomerIds().split(Constant.COMMA));
        
        List<Map<String, Object>> lastNormalDepositRecord =
                this.resourceAlterationRecordRepository.getLastNormalDepositRecord(
                request.getResourceCode(), unionIds);
        if (CollectionUtils.isEmpty(lastNormalDepositRecord)) {
            return null;
        }
        String s = JsonUtil.beanToString(lastNormalDepositRecord).orElse("");
        return JSONArray.parseArray(s, CustomerLastRechargeTimeDTO.class);
    }
    
}
