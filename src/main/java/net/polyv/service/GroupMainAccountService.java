package net.polyv.service;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.business.BusinessBillingPlanCodeEnum;
import net.polyv.dao.primary.GroupAccountConfigRepository;
import net.polyv.dao.primary.groupAccount.GroupMainAccountRepository;
import net.polyv.model.entity.primary.GroupAccountConfig;
import net.polyv.model.entity.primary.LiveFlowPackageInfo;
import net.polyv.model.entity.primary.groupAccount.GroupMainAccount;
import net.polyv.modules.common.exception.BizException;
import net.polyv.modules.common.util.BeanUtil;
import net.polyv.modules.pcs.api.req.crm.GroupAccountInfoUpdateReq;
import net.polyv.modules.pcs.api.vo.GroupMainAccountVO;
import net.polyv.modules.pcs.api.vo.crm.GetLiveFlowInfoResponse;
import net.polyv.modules.user.api.stereotype.GroupAccountErrorCodeEnum;
import net.polyv.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class GroupMainAccountService {

    @Autowired
    GroupMainAccountRepository groupMainAccountRepository ;

    @Autowired
    private GroupAccountConfigRepository groupAccountConfigRepository;

    public GroupMainAccount getById(String groupId){
        return groupMainAccountRepository.findById(groupId).orElse(null) ;
    }

    public void update(GroupAccountInfoUpdateReq req){
        GroupMainAccount groupMainAccount = getById(req.getGroupId()) ;
        if(groupMainAccount == null) {
            throw new BizException(GroupAccountErrorCodeEnum.group_account_not_found) ;
        }

        if(StringUtils.isNotBlank(req.getCompany())){
            groupMainAccount.setCompany(req.getCompany());
        }

        if(StringUtils.isNotBlank(req.getSaleUserId())){
            groupMainAccount.setSaleUserId(req.getSaleUserId());
        }

        if(StringUtils.isNotBlank(req.getSaleUserName())){
            groupMainAccount.setSaleUserName(req.getSaleUserName());
        }

        groupMainAccountRepository.update(groupMainAccount) ;
    }

    public List<GroupMainAccountVO> listGroupMainAccount(List<String> groupIds) {
        List<GroupMainAccount> groupMainAccountList = groupMainAccountRepository.findAllById(groupIds) ;
        if(CollectionUtils.isEmpty(groupMainAccountList)){
            return new ArrayList<>() ;
        }

        return BeanUtil.copy(groupMainAccountList , GroupMainAccountVO.class) ;
    }

    public List<GetLiveFlowInfoResponse> listGroupAccountLivePackage(Integer limitDays) {
        List<GroupAccountConfig> list = groupAccountConfigRepository.findLivePackageByExpireDate( DateUtil.getCurrentDay().getTime(), DateUtil.getDateAfterDays(limitDays).getTime());
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>() ;
        }

        List<GetLiveFlowInfoResponse> result = new ArrayList<>();
        for(GroupAccountConfig groupAccountConfig : list){
            if(Objects.isNull(groupAccountConfig.getLiveEnd())){
                continue;
            }
            GetLiveFlowInfoResponse info = new GetLiveFlowInfoResponse();
            info.setCustomerId(groupAccountConfig.getGroupId());
            info.setPackageName(groupAccountConfig.getLivePackageName());
            String billingPlanCode = groupAccountConfig.getLiveConcurrenceType();
            info.setBillingPlanCode(billingPlanCode);
            int days = DateUtil.getDaysBetween(DateUtil.getCurrentDay(),new Date(groupAccountConfig.getLiveEnd()));
            info.setDays(days);
            info.setExpiredTime(groupAccountConfig.getLiveEnd());
            result.add(info);
        }

        return result;
    }

    public List<GetLiveFlowInfoResponse> listGroupAccountVodPackage(Integer limitDays) {
        List<GroupAccountConfig> list = groupAccountConfigRepository.findVodPackageByExpireDate( DateUtil.getCurrentDay().getTime(), DateUtil.getDateAfterDays(limitDays).getTime());
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>() ;
        }

        List<GetLiveFlowInfoResponse> result = new ArrayList<>();
        for(GroupAccountConfig groupAccountConfig : list){
            if(Objects.isNull(groupAccountConfig.getVodEnd())){
                continue;
            }
            GetLiveFlowInfoResponse info = new GetLiveFlowInfoResponse();
            info.setCustomerId(groupAccountConfig.getGroupId());
            info.setPackageName(groupAccountConfig.getVodPackageName());
            String billingPlanCode = groupAccountConfig.getLiveConcurrenceType();
            info.setBillingPlanCode(billingPlanCode);
            int days = DateUtil.getDaysBetween(DateUtil.getCurrentDay(),new Date(groupAccountConfig.getVodEnd()));
            info.setDays(days);
            info.setExpiredTime(groupAccountConfig.getVodEnd());
            result.add(info);
        }

        return result;
    }


}
