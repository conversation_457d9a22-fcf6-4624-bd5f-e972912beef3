package net.polyv.service.handler;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.config.VodPackageConfig;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.BusinessBillingPlanCodeEnum;
import net.polyv.constant.business.OnlineBusinessOpenResultEnum;
import net.polyv.constant.finance.ContractOriginEnum;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.examinationDonate.VodDepositPackage;
import net.polyv.model.entity.primary.business.OnlineBusinessOpenLog;
import net.polyv.modules.common.exception.BizException;
import net.polyv.modules.common.util.DateUtil;
import net.polyv.modules.common.util.JacksonUtil;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.finance.FinanceBusinessOrderOpenDTO;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenCoolPackageExtInfo;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenRequest;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenResource;
import net.polyv.modules.pcs.api.stereotype.OnlineBusinessOpenResourceCodeEnum;
import net.polyv.modules.pcs.api.vo.VodPackageState;
import net.polyv.modules.user.api.service.CrmApi;
import net.polyv.modules.user.api.vo.CustomerEntityVO;
import net.polyv.rest.client.vod.user.PolyvApiRestTemplate;
import net.polyv.service.account.CustomerService;
import net.polyv.service.finance.FinancePreOpenContractService;
import net.polyv.service.item.ItemService;
import net.polyv.util.DateFormatUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 在线开通酷播云套餐
 */
@Slf4j
@Service
public class OnlineBusinessCoolPackageOpenHandler extends OnlineBusinessOpenHandler {

    @Autowired
    private FinancePreOpenContractService preOpenContractService;

    @Autowired
    private CrmApi crmApi;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private VodPackageConfig vodPackageConfig;

    @Autowired
    private ItemService itemService;
    
    
    @Override
    public boolean canOpen(OnlineBusinessOpenRequest request) {
        return OnlineBusinessOpenResourceCodeEnum.COOL_PACKAGE.equals(extractOpenResourceCode(request));
    }

    @Override
    public void preValidateOnlineBusinessOpenParam(OnlineBusinessOpenRequest request) {
        super.preValidateOnlineBusinessOpenParam(request);
        String unionId = request.getUnionId();
        String accountType = this.customerService.getAccountType(unionId);
        VodPackageState vodPackage = customerService.getVodPackage(accountType, unionId);
        boolean isFreeCoolPackage = Optional.ofNullable(vodPackage).map(VodPackageState::getId).map(id -> id == 49).orElse(false);
        // 在线购买酷播云套餐, 如果前一个付费套餐还没过期, 不覆盖开通了
        Date vodPackageExpireDate = itemService.getVodPackageExpireDate(unionId);
        if (!isFreeCoolPackage && vodPackageExpireDate != null && vodPackageExpireDate.getTime() >= DateUtil.getDateStart(new Date()).getTime()) {
            saveCoolPackageOnlineBusinessDelayedOpenLog(request, vodPackageExpireDate);
            throw new BizException(40001, "客户当前套餐未到期, 稍后由销售运营在CRM处理开通");
        }
    }
    
    
    @Override
    public CommonResult<String> doOpen(OnlineBusinessOpenRequest request) throws ClearingSystemException {
        FinanceBusinessOrderOpenDTO addSpecDTO = new FinanceBusinessOrderOpenDTO();

        addSpecDTO.setOperatorId(onlineBusinessOpenDefaultOperatorId);
        addSpecDTO.setSaleUserId(onlineBusinessOpenDefaultSaleUserId);
        addSpecDTO.setSaleUserName(onlineBusinessOpenDefaultSaleUserName);
        addSpecDTO.setContractWay(ContractOriginEnum.ORIGIN_PRE_OPEN.getCode());
        addSpecDTO.setIsAlreadyActivated((short) 0);
        addSpecDTO.setContractWay(ContractOriginEnum.ORIGIN_PRE_OPEN.getCode());

        // 补全账号级别信息
        fillPreOpenParamFromAccount(addSpecDTO, request.getUnionId());
        // 补全资源相关信息
        fillPreOpenParamFromRequest(addSpecDTO, request);

        log.info("convert online business open param to pre open param, result: {}", JacksonUtil.writeAsString(addSpecDTO));

        return preOpenContractService.createPreOpenContract(addSpecDTO);
    }

    /**
     * 套餐还没到期就在线购买了新的酷播云套餐, 保存开通日志
     * 目前用于购买之后在酷播云后台展示一条yyyy-MM-dd ~ yyyy-MM-dd的待开通的记录  
     */
    private void saveCoolPackageOnlineBusinessDelayedOpenLog(OnlineBusinessOpenRequest request, Date vodPackageExpireDate) {
        String customerId = request.getUnionId();
        String openResource = OnlineBusinessOpenResourceCodeEnum.COOL_PACKAGE.name();
        // 如果已经有待开通的数据(多次购买), 需要开通队列需要顺延下去
        // 比如套餐过期时间是2025-04-02, 在2025-04-01买了2次套餐, 待开通队列的数据也要有2条, 分别是 2025-04-03 ~ 2026-04-02 和 2027-04-03 ~ 2028-04-02
        List<OnlineBusinessOpenLog> waitOpenRecords = onlineBusinessOpenLogRepository.findWaitOpenRecords(customerId, openResource, DateFormatUtil.formatDateNormal(vodPackageExpireDate));

        Date prevEndDate = waitOpenRecords.stream()
                .map(OnlineBusinessOpenLog::getEndDate)
                .filter(Objects::nonNull)
                .max(Comparator.comparing(Date::getTime))
                .orElse(vodPackageExpireDate);

        OnlineBusinessOpenLog onlineBusinessOpenLog = new OnlineBusinessOpenLog();
        onlineBusinessOpenLog.setCustomerId(customerId);
        onlineBusinessOpenLog.setOpenResource(openResource);
        onlineBusinessOpenLog.setOpenResult(OnlineBusinessOpenResultEnum.DELAYED.getCode());
        onlineBusinessOpenLog.setStartDate(DateUtil.getXDay(prevEndDate, 1));
        onlineBusinessOpenLog.setEndDate(DateUtil.getXYear(prevEndDate, 1));

        Map<String, Object> extMap = new HashMap<>();
        extMap.put("onlineBusinessOpenRequest", request);
        extMap.put("vodPackageExpireDate", DateFormatUtil.formatDateNormal(vodPackageExpireDate));
        onlineBusinessOpenLog.setExt(JSON.toJSONString(extMap));

        onlineBusinessOpenLog.setCreateTime(new Date());
        onlineBusinessOpenLog.setUpdateTime(new Date());

        log.info("save cool package online business delayed open log, customerId: {}, log info: {}", customerId, JSON.toJSONString(onlineBusinessOpenLog));

        onlineBusinessOpenLogRepository.save(onlineBusinessOpenLog);
    }

    private void fillPreOpenParamFromAccount(FinanceBusinessOrderOpenDTO addSpecDTO, String unionId) {
        addSpecDTO.setUnionId(unionId);
        ResponseVO<CustomerEntityVO> customerResponse = crmApi.getCustomerByUnionId(unionId);
        if (!customerResponse.isSuccess() || customerResponse.getData() == null) {
            throw new BizException(400, "查找用户信息失败");
        }
        String email = customerResponse.getData().getEmail();
        String company = customerResponse.getData().getCompany();
        if (StringUtils.isBlank(email)) {
            throw new BizException(400, "找不到用户邮箱");
        }
        addSpecDTO.setEmail(email);
        addSpecDTO.setCompany(company);

        String accountType = customerService.getTotalAccountType(unionId);
        if (StringUtils.isBlank(accountType)) {
            throw new BizException(400, "识别账号类型失败");
        }
        addSpecDTO.setAccountType(accountType);

    }

    private void fillPreOpenParamFromRequest(FinanceBusinessOrderOpenDTO addSpecDTO, OnlineBusinessOpenRequest request) {
        OnlineBusinessOpenResource openResource = request.getOpenResource();
        // 酷播云套餐
        if (OnlineBusinessOpenResourceCodeEnum.COOL_PACKAGE.equals(openResource.getResourceCode())) {
            addSpecDTO.setBusinessType(7);
            addSpecDTO.setBillingPlanCode(BusinessBillingPlanCodeEnum.COOL_PACKAGE.getCode());
            addSpecDTO.setMainAccountPassword("");
            addSpecDTO.setMainAccountMobile("");
            addSpecDTO.setDirectAssociateSoId("");

            String extInfoJson = openResource.getExtInfo();
            OnlineBusinessOpenCoolPackageExtInfo extInfo = JacksonUtil.readValue(extInfoJson, OnlineBusinessOpenCoolPackageExtInfo.class);

            addSpecDTO.setRemark(extInfo.getRemark());
            addSpecDTO.setContractAmount(extInfo.getContractAmount());

            if (!vodPackageConfig.isCoolPackageId(extInfo.getPackageId())) {
                throw new BizException(400, "套餐信息有误");
            }
            VodDepositPackage packageType = PolyvApiRestTemplate.getPackageType(extInfo.getPackageId());
            if (packageType == null) {
                throw new BizException(400, "查询套餐信息失败");
            }
            addSpecDTO.setVodPackageType(extInfo.getPackageId());
            addSpecDTO.setPackageName(packageType.getName());
            addSpecDTO.setPackageDesc(packageType.getTxt());
            addSpecDTO.setVodFlow(String.valueOf(packageType.getFlow() / 1073741824));
            addSpecDTO.setVodSpace(String.valueOf(packageType.getSpace() / 1073741824));
            // 套餐开始和结束时间 (如果没有传就默认当天生效, 酷播云套餐现在默认都是一年)
            Date now = new Date();
            String startDate = StringUtils.isBlank(extInfo.getStartDate())
                    ? DateFormatUtils.format(now, "yyyy-MM-dd")
                    : extInfo.getStartDate();
            String endDate = StringUtils.isBlank(extInfo.getEndDate())
                    ? DateFormatUtil.formatDate(DateUtil.getXYear(DateUtil.getXDay(DateFormatUtil.parseDate(startDate, "yyyy-MM-dd"), -1), 1), "yyyy-MM-dd")
                    : extInfo.getEndDate();
            addSpecDTO.setValidPeriodStartTime(startDate);
            addSpecDTO.setValidPeriodEndTime(endDate);

            // 酷播云现在的套餐默认不是年流量
            addSpecDTO.setVodPackageIsAnnualFlow((short) 0);
            addSpecDTO.setPackageIsCustomize((short) 0);
            addSpecDTO.setIsAlreadyActivated((short) 0);

            if (!AccountTypeEnum.NORMAL.getCode().equals(addSpecDTO.getAccountType())) {
                throw new BizException(400, "当前账号类型不支持此操作");
            }
            return;
        }
    }
}
