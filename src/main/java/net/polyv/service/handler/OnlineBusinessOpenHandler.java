package net.polyv.service.handler;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.dao.primary.business.OnlineBusinessOpenLogRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.modules.common.exception.BizException;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenRequest;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenResource;
import net.polyv.modules.pcs.api.stereotype.OnlineBusinessOpenResourceCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Objects;
import java.util.Optional;

@Slf4j
public abstract class OnlineBusinessOpenHandler {

    @Value("${online-business.open.default-operator-id:1}")
    protected String onlineBusinessOpenDefaultOperatorId;

    @Value("${online-business.open.default-sale-user-id:root}")
    protected String onlineBusinessOpenDefaultSaleUserId;

    @Value("${online-business.open.default-sale-user-name:root}")
    protected String onlineBusinessOpenDefaultSaleUserName;

    @Autowired
    protected OnlineBusinessOpenLogRepository onlineBusinessOpenLogRepository;
    
    protected void preValidateOnlineBusinessOpenParam(OnlineBusinessOpenRequest request) {
        String unionId = request.getUnionId();
        if (StringUtils.isBlank(unionId)) {
            throw new BizException(400, "union不能为空");
        }
        OnlineBusinessOpenResource openResource = request.getOpenResource();
        if (Objects.isNull(openResource)) {
            throw new BizException(400, "openResource不能为空");
        }
        if (openResource.getResourceCode() == null) {
            throw new BizException(400, "resourceCode不能为空");
        }
        if (StringUtils.isBlank(openResource.getExtInfo())) {
            throw new BizException(400, "extInfo不能为空");
        }
    }

    protected void fillOnlineBusinessOpenParam(OnlineBusinessOpenRequest request) {
        
    }

    protected void overrideOnlineBusinessOpenParam(OnlineBusinessOpenRequest request) {
         
    }
    
    protected OnlineBusinessOpenResourceCodeEnum extractOpenResourceCode(OnlineBusinessOpenRequest request) {
        return Optional.ofNullable(request)
                .map(OnlineBusinessOpenRequest::getOpenResource)
                .map(OnlineBusinessOpenResource::getResourceCode)
                .orElse(null);
    }

    public abstract boolean canOpen(OnlineBusinessOpenRequest request);
    
    public CommonResult<String> open(OnlineBusinessOpenRequest request) throws ClearingSystemException {
        fillOnlineBusinessOpenParam(request);
        preValidateOnlineBusinessOpenParam(request);
        overrideOnlineBusinessOpenParam(request);
        return doOpen(request);
    }

    public abstract CommonResult<String> doOpen(OnlineBusinessOpenRequest request) throws ClearingSystemException;
    
}
