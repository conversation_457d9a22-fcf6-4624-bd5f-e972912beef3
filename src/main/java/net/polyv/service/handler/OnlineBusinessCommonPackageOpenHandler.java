package net.polyv.service.handler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.customer.LiveBillingType;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.bilingConfig.OtherBillConfigItemDO;
import net.polyv.model.data.examinationDonate.VodDepositPackage;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.common.exception.BizException;
import net.polyv.modules.common.util.JacksonUtil;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenCommonPackageExtInfo;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenRequest;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenResource;
import net.polyv.modules.pcs.api.stereotype.OnlineBusinessOpenResourceCodeEnum;
import net.polyv.modules.user.api.bo.ContactDescriptionBO;
import net.polyv.modules.user.api.req.CustomerSearchReq;
import net.polyv.modules.user.api.service.CrmApi;
import net.polyv.modules.user.api.vo.CustomerEntityVO;
import net.polyv.modules.third.service.crm.FXiaoKeService;
import net.polyv.modules.third.req.fxiaoke.FXiaoKeCustomerQueryReq;
import net.polyv.modules.third.vo.fxiaoke.FXiaoKeBaseResponse;
import net.polyv.modules.third.vo.fxiaoke.FXiaoKeProductDetailVO;
import net.polyv.modules.third.stereotype.FXiaoKeApiNameEnum;
import net.polyv.modules.third.stereotype.FXiaoKeOperatorEnum;
import net.polyv.modules.pcs.api.stereotype.OnlineBusinessPayAccountEnum;
import net.polyv.model.data.business.BusinessOrderAddSpecDTO;
import net.polyv.rest.client.vod.user.PolyvApiRestTemplate;
import net.polyv.service.so.BusinessOrderService;

import net.polyv.util.converter.UnitConverterUtil;
import org.apache.commons.collections4.CollectionUtils;
import net.polyv.util.DateFormatUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collections;
import java.util.Optional;

/**
 * 在线开通 通用套餐
 */
@Slf4j
@Service
public class OnlineBusinessCommonPackageOpenHandler extends OnlineBusinessOpenHandler {

    @Autowired
    private CrmApi crmApi;

    @Autowired
    private FXiaoKeService fxiaoKeService;

    @Autowired
    private BusinessOrderService businessOrderService;

    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;

    /**
     * 轮询查询crm订单最多重试次数
     */
    @Value("${online-business.open.query-crm-order.retry-times:60}")
    private int queryCrmOrderRetryTimes;


    @Override
    public boolean canOpen(OnlineBusinessOpenRequest request) {
        List<OnlineBusinessOpenResourceCodeEnum> allowCodes = Arrays.asList(
                OnlineBusinessOpenResourceCodeEnum.LIVE_PACKAGE,
                OnlineBusinessOpenResourceCodeEnum.VOD_PACKAGE,
                OnlineBusinessOpenResourceCodeEnum.RESOURCE_PACKAGE
        );
        OnlineBusinessOpenResourceCodeEnum resourceCode = extractOpenResourceCode(request);
        for (OnlineBusinessOpenResourceCodeEnum allowCode : allowCodes) {
            if (allowCode.equals(resourceCode)) {
                return true;
            }
        }
        return false;
    }

    @Override
    protected void fillOnlineBusinessOpenParam(OnlineBusinessOpenRequest request) {
        super.fillOnlineBusinessOpenParam(request);
        OnlineBusinessOpenResource openResource = request.getOpenResource();
        if (openResource == null) {
            return;
        }
        OnlineBusinessOpenResourceCodeEnum openResourceCode = openResource.getResourceCode();
        OnlineBusinessOpenCommonPackageExtInfo extInfo = JacksonUtil.readValue(openResource.getExtInfo(), OnlineBusinessOpenCommonPackageExtInfo.class);
        if (extInfo == null) {
            return;
        }
        if (OnlineBusinessOpenResourceCodeEnum.VOD_PACKAGE.equals(openResourceCode)) {
            if (StringUtils.isBlank(extInfo.getPackageName()) || StringUtils.isBlank(extInfo.getPackageDesc())) {
                VodDepositPackage packageType = PolyvApiRestTemplate.getPackageType(extInfo.getPackageId());
                if (StringUtils.isBlank(extInfo.getPackageName()) && packageType != null) {
                    extInfo.setPackageName(packageType.getName());
                }
                if (StringUtils.isBlank(extInfo.getPackageDesc()) && packageType != null) {
                    extInfo.setPackageDesc(packageType.getTxt());
                }
            }
        }
        String json = JacksonUtil.writeAsString(extInfo);
        log.info("fill online business open param, result: {}", json);
        openResource.setExtInfo(json);
    }

    @Override
    protected void overrideOnlineBusinessOpenParam(OnlineBusinessOpenRequest request) {
        super.overrideOnlineBusinessOpenParam(request);
        OnlineBusinessOpenResource openResource = request.getOpenResource();
        OnlineBusinessOpenCommonPackageExtInfo extInfo = JacksonUtil.readValue(openResource.getExtInfo(), OnlineBusinessOpenCommonPackageExtInfo.class);
        
        // 1. 处理隔天支付的情况, 如果隔天才支付订单, 并且套餐开始时间小于当前时间, 把开始时间设置为今天, 结束时间也对应延长
        Date originalStartDate = DateFormatUtil.parseDateNormal(extInfo.getStartDate());
        Date originalEndDate = DateFormatUtil.parseDateNormal(extInfo.getEndDate());
        Date currentDate = DateFormatUtil.parseDateNormal(DateFormatUtil.formatDateNormal(new Date()));
        if (originalStartDate.before(currentDate)) {
            long validityDays = (originalEndDate.getTime() - originalStartDate.getTime()) / (24 * 60 * 60 * 1000);
            String newStartDate = DateFormatUtil.formatDateNormal(currentDate);
            extInfo.setStartDate(newStartDate);
            Date newEndDate = DateUtil.offsetDay(currentDate, (int) validityDays);
            String newEndDateStr = DateFormatUtil.formatDateNormal(newEndDate);
            extInfo.setEndDate(newEndDateStr);
        }
        
        String json = JacksonUtil.writeAsString(extInfo);
        log.info("override online business open param, result: {}", json);
        openResource.setExtInfo(json);
    }

    @Override
    public void preValidateOnlineBusinessOpenParam(OnlineBusinessOpenRequest request) {
        super.preValidateOnlineBusinessOpenParam(request);
        OnlineBusinessOpenCommonPackageExtInfo extInfo = JacksonUtil.readValue(request.getOpenResource().getExtInfo(), OnlineBusinessOpenCommonPackageExtInfo.class);
        String amount = extInfo.getAmount();
        String productCode = extInfo.getProductCode();
        OnlineBusinessOpenResourceCodeEnum openResourceCode = request.getOpenResource().getResourceCode();

        if (StringUtils.isBlank(amount)) {
            throw new BizException(400, "合同金额不能为空");
        }
        if (StringUtils.isBlank(productCode)) {
            throw new BizException(400, "产品编码不能为空");
        }
        if (StringUtils.isBlank(extInfo.getPayedAccountCode())) {
            throw new BizException(400, "收款账户代码不能为空");
        }
        if (OnlineBusinessOpenResourceCodeEnum.VOD_PACKAGE.equals(openResourceCode)) {
            if (StringUtils.isBlank(extInfo.getPackageId())) {
                throw new BizException(400, "套餐ID不能为空");
            }
            if (StringUtils.isBlank(extInfo.getPackageName())) {
                throw new BizException(400, "套餐名称不能为空");
            }
            if (extInfo.getVodSpace() == null) {
                throw new BizException(400, "点播空间不能为空");
            }
            if (extInfo.getVodFlow() == null) {
                throw new BizException(400, "点播流量不能为空");
            }
            if (extInfo.getVodPackageIsAnnualFlow() == null) {
                throw new BizException(400, "是否年流量套餐不能为空");
            }
            if (extInfo.getVodPackageIsAnnualFlow() == 1 && StringUtils.isBlank(extInfo.getVodPackageFlowMonthsOfYear())) {
                throw new BizException(400, "点播套餐年流量月数不能为空");
            }
        }
        if (StringUtils.isBlank(extInfo.getStartDate())) {
            throw new BizException(400, "套餐开始时间不能为空");
        }
        //资源包有永久有效的，不需要设置结束时间
        if (!OnlineBusinessOpenResourceCodeEnum.RESOURCE_PACKAGE.equals(openResourceCode)
                && StringUtils.isBlank(extInfo.getEndDate())) {
            throw new BizException(400, "套餐结束时间不能为空");
        }
        if (extInfo.getValidityUnit() == null) {
            throw new BizException(400, "有效期单位不能为空");
        }
        OnlineBusinessPayAccountEnum payedAccountEnum = OnlineBusinessPayAccountEnum.getByName(extInfo.getPayedAccountCode());
        if (payedAccountEnum == null) {
            throw new BizException(400, "无效的收款账户代码: " + extInfo.getPayedAccountCode());
        }
    }


    @Override
    public CommonResult<String> doOpen(OnlineBusinessOpenRequest request) throws ClearingSystemException {
        OnlineBusinessOpenCommonPackageExtInfo extInfo = JacksonUtil.readValue(request.getOpenResource().getExtInfo(), OnlineBusinessOpenCommonPackageExtInfo.class);
        // 第一步：获取客户信息
        CustomerEntityVO customerInfo = getUserInfo(request.getUnionId());
        String company = customerInfo.getCompany();

        // 第二步：获取负责人和销售运营人员信息
        ContactDescriptionBO contactDescription = JacksonUtil.readValue(customerInfo.getContactDescription(), ContactDescriptionBO.class);
        String customerAccountId = contactDescription.getAccount_id();

        // 获取负责人信息
        String ownerUserId = getFxkUserId(contactDescription.getOwner());
        if (StringUtils.isBlank(ownerUserId)) {
            throw new BizException(400, "客户负责人[owner]信息不存在");
        }

        // 获取销售运营人员信息
        String salesOperationsUserId = getFxkUserId(contactDescription.getSales_operations_personnel__c());
        if (StringUtils.isBlank(salesOperationsUserId)) {
            throw new BizException(400, "销售运营人员[sales_operations_personnel__c]信息不存在");
        }
    
        // 查询商品信息
        String productCode = extInfo.getProductCode();
        FXiaoKeProductDetailVO productDetail = getProductDetail(productCode);

        // 第三步：创建销售商机
        String opportunityId = createOpportunity(request, customerAccountId, ownerUserId);

        // 第四步：创建合同
        String contractDetailId = createContractDetail(extInfo, opportunityId, ownerUserId, salesOperationsUserId,
                customerAccountId, company, productDetail);

        // 第五步：查询订单
        String orderId = queryOrderWithPolling(contractDetailId);

        // 第六步：创建到款
        String financialReceiptId = createFinancialReceipt(extInfo, customerAccountId, ownerUserId, salesOperationsUserId);

        // 第七步：创建回款
        createReimbursement(extInfo, customerAccountId, ownerUserId, salesOperationsUserId, financialReceiptId, orderId);

        // 第八步：配置订单
        configureOrder(request, orderId, productDetail);

        return CommonResult.ok();
    }

    /**
     * 获取客户信息
     */
    private CustomerEntityVO getUserInfo(String unionId) {
        // 构造查询请求，按unionId查询客户信息
        CustomerSearchReq searchReq = new CustomerSearchReq();
        searchReq.setUnionId(unionId);

        // 调用CRM API获取客户信息
        ResponseVO<List<CustomerEntityVO>> response = crmApi.getCustomerBySearchReq(searchReq);

        log.info("get crm customer info, req: {}, response: {}", JacksonUtil.writeAsString(searchReq), JacksonUtil.writeAsString(response));

        if (!response.isSuccess()) {
            throw new BizException(response.getError().getCode(), response.getError().getDesc());
        }

        List<CustomerEntityVO> customerList = response.getData();
        if (CollectionUtils.isEmpty(customerList)) {
            throw new BizException(400, "CRM客户信息不存在");
        }

        // 取第一个客户信息
        CustomerEntityVO customerEntityVO = customerList.get(0);
        ContactDescriptionBO contactDescriptionBO = JacksonUtil.readValue(customerEntityVO.getContactDescription(), ContactDescriptionBO.class);
        if (contactDescriptionBO == null) {
            throw new BizException(400, "CRM客户信息缺失, 字段[contactDescription]");
        }
        if (StringUtils.isBlank(contactDescriptionBO.getAccount_id())) {
            throw new BizException(400, "CRM客户信息缺失, 字段[account_id]");
        }
        if (StringUtils.isBlank(customerEntityVO.getCompany())) {
            throw new BizException(400, "CRM客户信息缺失, 字段[company]");
        }
        if (CollectionUtils.isEmpty(contactDescriptionBO.getOwner())) {
            throw new BizException(400, "CRM客户信息缺失, 字段[owner]");
        }
        if (CollectionUtils.isEmpty(contactDescriptionBO.getSales_operations_personnel__c())) {
            throw new BizException(400, "CRM客户信息缺失, 字段[sales_operations_personnel__c]");
        }
        return customerEntityVO;
    }

    /**
     * 获取分享销客的用户id
     */
    private String getFxkUserId(List<String> personnelIdList) {
        if (CollectionUtils.isEmpty(personnelIdList)) {
            return null;
        }

        // 取第一个人员ID进行查询
        String personnelId = personnelIdList.get(0);

        // 构造查询请求
        FXiaoKeCustomerQueryReq queryReq = new FXiaoKeCustomerQueryReq();

        // 设置查询条件：按_id字段查询
        Map<String, Object> filter = new HashMap<>();
        filter.put("operator", FXiaoKeOperatorEnum.EQ.getValue());
        filter.put("field_name", "_id");
        filter.put("field_values", Collections.singletonList(personnelId));

        // 设置查询对象
        Map<String, Object> searchQueryInfoMap = new HashMap<>();
        searchQueryInfoMap.put("offset", 0);
        searchQueryInfoMap.put("limit", 1);
        searchQueryInfoMap.put("filters", Collections.singletonList(filter));
        searchQueryInfoMap.put("fieldProjection", Arrays.asList("name", "user_id", "phone", "owner", "out_owner", "_id"));

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("search_query_info", searchQueryInfoMap);
        dataMap.put("dataObjectApiName", FXiaoKeApiNameEnum.PERSONAL.getValue());
        queryReq.setData(dataMap);

        ResponseVO<FXiaoKeBaseResponse> response = fxiaoKeService.queryCustomer(queryReq);
        
        log.info("query fxk personnel info, req: {}, response: {}", JacksonUtil.writeAsString(queryReq), JacksonUtil.writeAsString(response));
        
        if (!response.isSuccess()) {
            return null;
        }

        FXiaoKeBaseResponse fxkResponse = response.getData();
        if (fxkResponse == null || !fxkResponse.isSuccess()) {
            return null;
        }

        Map<String, Object> resultData = fxkResponse.getData();
        if (resultData == null) {
            return null;
        }

        List<Map<String, Object>> dataList = (List<Map<String, Object>>) resultData.get("dataList");
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }

        Map<String, Object> personnelInfo = dataList.get(0);

        return (String) personnelInfo.get("user_id");
    }

    /**
     * 创建销售商机
     * @return 商机ID (dataId)
     */
    private String createOpportunity(OnlineBusinessOpenRequest request, String customerAccountId, String salesOperationsUserId) {

        // 解析扩展信息获取金额
        OnlineBusinessOpenCommonPackageExtInfo extInfo = JacksonUtil.readValue(request.getOpenResource().getExtInfo(), OnlineBusinessOpenCommonPackageExtInfo.class);

        String amount = extInfo.getAmount();

        // 构造商机名称：年份-业务类别-账号（商城订单）
        String now = DateFormatUtil.formatDateTimeShort(new Date());
        String opportunityName = String.format("%s-%s-%s（商城订单）", now, request.getOpenResource().getResourceCode().getSystemName(), request.getUnionId());

        // 构造创建商机的请求参数
        Map<String, Object> createOpportunityParam = buildCreateOpportunityParam(opportunityName, customerAccountId, salesOperationsUserId, amount);

        // 调用纷享销客API创建商机
        ResponseVO<FXiaoKeBaseResponse> response = fxiaoKeService.createPresetsObjCustomizeField(createOpportunityParam);

        log.info("create opportunity, req: {}, response: {}", JacksonUtil.writeAsString(createOpportunityParam), JacksonUtil.writeAsString(response));

        if (!response.isSuccess()) {
            throw new BizException(response.getError().getCode(), response.getError().getDesc());
        }

        FXiaoKeBaseResponse fxkResponse = response.getData();
        if (fxkResponse == null || !fxkResponse.isSuccess()) {
            throw new BizException(500, String.format("创建销售商机失败. %s", formatFxkApiErrorMsg(fxkResponse)));
        }

        String opportunityId = fxkResponse.getDataId();
        if (StringUtils.isBlank(opportunityId)) {
            throw new BizException(500, "创建销售商机成功但未返回商机ID");
        }

        return opportunityId;
    }

    /**
     * 构造创建商机的请求参数
     */
    private Map<String, Object> buildCreateOpportunityParam(String opportunityName, String accountId,
                                                            String ownerUserId, String amount) {
        // 构造商机对象数据
        Map<String, Object> objectData = new HashMap<>();
        objectData.put("owner", Collections.singletonList(ownerUserId));
        objectData.put("sales_process_id", "64a8d89bc0f54000012bb736"); // 商机推进器（不要编辑）
        objectData.put("sales_stage", "5"); // 商机阶段：赢单
        objectData.put("sales_status", "2"); // 阶段状态 进行中:1,赢单:2,输单:4,作废:3
        objectData.put("field_4AbWd__c", "0"); // 商机需求 公有云:0,私有云:1,现场:2,硬件:3,运营服务:4,定制开发:5
        objectData.put("field_89f34__c", Collections.singletonList("1")); // 客户关注点 稳定性:0,安全性:1,接口能力:2,功能:3,服务:4,价格:5
        objectData.put("close_date", System.currentTimeMillis()); // 预计成交日期 (先写死今天, 后面看下要不要当成参数)
        objectData.put("account_id", accountId); // 客户名称
        objectData.put("field_7hO2a__c", "100.0"); // 自评赢单率
        objectData.put("field_JmEG4__c", amount); // 预计合作金额
        objectData.put("name", opportunityName); // 商机名称
        objectData.put("dataObjectApiName", "NewOpportunityObj"); // 对象名称，此处固定
        objectData.put("record_type", "record_A7aMC__c"); // 业务类型：续费
        objectData.put("field_2G26s__c", "1"); // 商机阶段（用于编辑） 需求确认:1,演示测试:2,商务沟通:3,其他:other
        objectData.put("field_2bXOf__c", "1"); // 商机阶段（用于编辑.） 需求确认:1,商务沟通:3,其他:other
        objectData.put("field_1Wm0i__c", "赢单"); // 商机阶段（用于筛选）

        // 构造data参数
        Map<String, Object> data = new HashMap<>();
        data.put("object_data", objectData);

        // 构造完整请求参数
        Map<String, Object> param = new HashMap<>();
        param.put("hasSpecifyTime", true); // 是否可传创建时间
        param.put("triggerApprovalFlow", false); // 是否触发审批流程
        param.put("data", data);

        return param;
    }

    /**
     * 创建合同明细
     * @param extInfo       在线业务开通请求
     * @param opportunityId 商机ID
     * @param ownerUserId   负责人用户ID
     * @param salesOperationsUserId 销售运营用户ID
     * @param customerAccountId 客户账户ID
     * @param company 客户公司名称
     * @param productDetail 产品详情
     * @return 合同明细ID
     */
    private String createContractDetail(OnlineBusinessOpenCommonPackageExtInfo extInfo, String opportunityId,
            String ownerUserId, String salesOperationsUserId, String customerAccountId,
            String company, FXiaoKeProductDetailVO productDetail) {

        // 构造合同详情
        Map<String, Object> contractParam = buildContractParam(extInfo, opportunityId, ownerUserId, productDetail, salesOperationsUserId, customerAccountId, company);

        // 创建合同明细
        ResponseVO<FXiaoKeBaseResponse> response = fxiaoKeService.createCustomObjCustomizeField(contractParam);

        log.info("create contract detail, req: {}, response: {}", JacksonUtil.writeAsString(contractParam), JacksonUtil.writeAsString(response));

        if (!response.isSuccess()) {
            throw new BizException(response.getError().getCode(), response.getError().getDesc());
        }

        FXiaoKeBaseResponse fxkResponse = response.getData();
        if (fxkResponse == null || !fxkResponse.isSuccess()) {
            throw new BizException(500, "创建合同明细失败. " + formatFxkApiErrorMsg(fxkResponse));
        }

        // 从响应的data中获取object_b6MrX__c字段，提取contract_detail_id
        Map<String, Object> responseData = fxkResponse.getData();
        if (responseData == null) {
            throw new BizException(500, "创建合同明细成功但未返回数据");
        }

        Object objectB6MrX = responseData.get("object_b6MrX__c");
        String contractDetailId = null;

        if (objectB6MrX != null && objectB6MrX instanceof List) {
            List<?> contractDetailIdList = (List<?>) objectB6MrX;
            if (!contractDetailIdList.isEmpty()) {
                contractDetailId = String.valueOf(contractDetailIdList.get(0));
            }
        }

        if (StringUtils.isBlank(contractDetailId)) {
            throw new BizException(500, "创建合同明细成功但未返回合同明细ID");
        }

        return contractDetailId;
    }

    /**
     * 查询订单
     * @param contractDetailId 合同明细ID
     * @return 订单ID
     */
    private String queryOrderWithPolling(String contractDetailId) {
        for (int i = 1; i <= queryCrmOrderRetryTimes; i++) {
            try {
                String orderId = queryOrder(contractDetailId);
                if (StringUtils.isNotBlank(orderId)) {
                    return orderId;
                }
            } catch (Exception e) {
                if (i == queryCrmOrderRetryTimes) {
                    throw e;
                }
            }
            ThreadUtil.sleep(2000);
        }
        throw new BizException(500, "查询CRM订单超时, 订单ID: " + contractDetailId);
    }
    
    /**
     * 查询订单
     * @param contractDetailId 合同明细ID
     * @return 订单ID
     */
    private String queryOrder(String contractDetailId) {
        // 构造查询订单的请求参数
        Map<String, Object> queryParam = buildQueryOrderParam(contractDetailId);

        // 调用纷享销客API查询订单
        ResponseVO<FXiaoKeBaseResponse> response = fxiaoKeService.queryPresetsObjCustomizeField(queryParam);

        log.info("query crm order, req: {}, response: {}", JacksonUtil.writeAsString(queryParam), JacksonUtil.writeAsString(response));

        if (!response.isSuccess()) {
            throw new BizException(response.getError().getCode(), response.getError().getDesc());
        }

        FXiaoKeBaseResponse fxkResponse = response.getData();
        if (fxkResponse == null || !fxkResponse.isSuccess()) {
            throw new BizException(500, "查询订单失败");
        }

        // 从响应数据中提取订单列表
        Map<String, Object> responseData = fxkResponse.getData();
        if (responseData == null) {
            throw new BizException(500, "查询订单成功但未返回数据");
        }

        List<Map<String, Object>> dataList = (List<Map<String, Object>>) responseData.get("dataList");
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BizException(400, "未找到对应的订单信息");
        }

        // 取第一个订单的order_id
        Map<String, Object> orderInfo = dataList.get(0);
        String orderId = (String) orderInfo.get("order_id");

        if (StringUtils.isBlank(orderId)) {
            throw new BizException(500, "查询到订单信息但未返回订单ID");
        }
        return orderId;
    }

    /**
     * 构造查询订单的请求参数
     * @param contractDetailId 合同明细ID
     * @return 查询订单的请求参数
     */
    private Map<String, Object> buildQueryOrderParam(String contractDetailId) {
        // 构造过滤条件
        Map<String, Object> filter = new HashMap<>();
        filter.put("operator", FXiaoKeOperatorEnum.EQ.getValue());
        filter.put("field_name", "field_Gdi20__c"); // 合同明细ID字段
        filter.put("field_values", Collections.singletonList(contractDetailId));

        // 构造查询条件
        Map<String, Object> searchQueryInfo = new HashMap<>();
        searchQueryInfo.put("offset", 0);
        searchQueryInfo.put("limit", 1);
        searchQueryInfo.put("fieldProjection", Collections.singletonList("order_id")); // 只查询order_id字段
        searchQueryInfo.put("filters", Collections.singletonList(filter));

        // 构造data参数
        Map<String, Object> data = new HashMap<>();
        data.put("search_query_info", searchQueryInfo);
        data.put("dataObjectApiName", "SalesOrderProductObj"); // 销售订单产品对象

        // 构造完整请求参数
        Map<String, Object> param = new HashMap<>();
        param.put("data", data);

        return param;
    }

    /**
     * 创建财务到款
     * @param extInfo               在线业务开通请求
     * @param customerAccountId     客户账户ID
     * @param ownerUserId           负责人用户ID
     * @param salesOperationsUserId 销售运营用户ID
     * @return 财务到款ID
     */
    private String createFinancialReceipt(OnlineBusinessOpenCommonPackageExtInfo extInfo, String customerAccountId,
                                          String ownerUserId, String salesOperationsUserId) {
        String amount = extInfo.getAmount();
        String payedAccountCode = extInfo.getPayedAccountCode();

        // 根据枚举名称获取对应的账户代码
        OnlineBusinessPayAccountEnum payedAccountEnum = OnlineBusinessPayAccountEnum.getByName(payedAccountCode);

        // 构造创建财务到款的请求参数
        Map<String, Object> financialReceiptParam = buildCreateFinancialReceiptParam(customerAccountId, ownerUserId, salesOperationsUserId, amount, payedAccountEnum.getReceiptCode());

        // 调用纷享销客API创建财务到款
        ResponseVO<FXiaoKeBaseResponse> response = fxiaoKeService.createCustomObjCustomizeField(financialReceiptParam);

        if (!response.isSuccess()) {
            throw new BizException(response.getError().getCode(), response.getError().getDesc());
        }

        log.info("create financial receipt, req: {}, response: {}", JacksonUtil.writeAsString(financialReceiptParam), JacksonUtil.writeAsString(response));

        if (!response.isSuccess()) {
            throw new BizException(response.getError().getCode(), response.getError().getDesc());
        }

        FXiaoKeBaseResponse fxkResponse = response.getData();
        if (fxkResponse == null || !fxkResponse.isSuccess()) {
            throw new BizException(500, "创建财务到款失败. " + formatFxkApiErrorMsg(fxkResponse));
        }

        String financialReceiptId = fxkResponse.getDataId();
        if (StringUtils.isBlank(financialReceiptId)) {
            throw new BizException(500, "创建财务到款成功但未返回财务到款ID");
        }
        return financialReceiptId;
    }

    /**
     * 构造创建财务到款的请求参数
     * @param customerAccountId     客户账户ID
     * @param ownerUserId           负责人用户ID
     * @param salesOperationsUserId 销售运营用户ID
     * @param amount                到款金额
     * @param accountCode           收款账户代码
     * @return 创建财务到款的请求参数
     */
    private Map<String, Object> buildCreateFinancialReceiptParam(String customerAccountId, String ownerUserId,
                                                                 String salesOperationsUserId, String amount, String accountCode) {
        // 构造财务到款对象数据
        Map<String, Object> objectData = new HashMap<>();
        objectData.put("dataObjectApiName", "object_a8JLy__c"); // 财务到款对象
        objectData.put("field_2SgBy__c", amount); // 到款金额
        objectData.put("field_8Hzjc__c", System.currentTimeMillis()); // 到款日期（当前日期）
        objectData.put("owner", Collections.singletonList(ownerUserId)); // 负责人
        objectData.put("field_4Q1A2__c", "option1"); // 是否为个人代付：option1否，Z7Sv7RRcS是
        objectData.put("field_WbFis__c", "option1"); // 是否为关联方打款：option1否
        objectData.put("field_2jv3a__c", Collections.singletonList(salesOperationsUserId)); // 销售运营人员
        objectData.put("field_B02ph__c", ""); // 个人代付名称（暂时为空）
        objectData.put("field_E0o2v__c", customerAccountId); // 客户名称
        objectData.put("field_Iu1dC__c", accountCode); // 收款账户

        // 构造data参数
        Map<String, Object> data = new HashMap<>();
        data.put("object_data", objectData);

        // 构造完整请求参数
        Map<String, Object> param = new HashMap<>();
        param.put("includeDetailIds", true); // 指示API响应中需返回已创建明细的ID
        param.put("data", data);

        return param;
    }

    /**
     * 查询商品详情信息
     * @param productCode 产品编码
     * @return 商品详情VO对象
     */
    private FXiaoKeProductDetailVO getProductDetail(String productCode) {
        ResponseVO<FXiaoKeBaseResponse> response = fxiaoKeService.getProductDetailByCode(productCode);

        log.info("get crm product detail, productCode: {}, response: {}", productCode, JacksonUtil.writeAsString(response));

        if (!response.isSuccess()) {
            throw new BizException(response.getError().getCode(), response.getError().getDesc());
        }

        FXiaoKeBaseResponse fxkResponse = response.getData();
        if (fxkResponse == null || !fxkResponse.isSuccess()) {
            throw new BizException(500, String.format("查询CRM商品信息失败, 商品编码: %s, %s", productCode, formatFxkApiErrorMsg(fxkResponse)));
        }

        Map<String, Object> resultData = fxkResponse.getData();
        if (resultData == null) {
            throw new BizException(400, "商品信息不存在");
        }

        String jsonString = JacksonUtil.writeAsString(resultData);
        return JacksonUtil.readValue(jsonString, FXiaoKeProductDetailVO.class);
    }

    /**
     * 构造合同明细数据
     * @param opportunityId 商机ID
     * @param ownerUserId   负责人用户ID
     * @param productDetail 商品详情VO对象
     * @return 合同明细数据
     */
    private Map<String, Object> buildContractParam(OnlineBusinessOpenCommonPackageExtInfo extInfo, String opportunityId,
                                                   String ownerUserId, FXiaoKeProductDetailVO productDetail,
                                                   String salesOperationsUserId, String customerAccountId, String company) {
        String name = productDetail.getName();
        String productCategory = productDetail.getCategory();
        String productName = productDetail.getProductCode();
        String productDetailItem = productDetail.getFieldC5CyV();
        String specificationUnit = productDetail.getUnit();

        Map<String, Object> objectData = new HashMap<>();
        objectData.put("dataObjectApiName", FXiaoKeApiNameEnum.CONTRACT_ADD.getValue());
        objectData.put("field_6pbia__c", extInfo.getProductCode()); // 产品编码
        objectData.put("field_S1Hc7__c", "2"); // 采购分类：0:新购， 1:增购， 2:续费
        objectData.put("field_20UVx__c", extInfo.getQuantity()); // 数量
        objectData.put("owner", Collections.singletonList(ownerUserId)); // 人员
        objectData.put("field_2x9u6__c", extInfo.getValidityUnit()); // 有效期(时间单位) 
        objectData.put("field_1HpoK__c", Optional.ofNullable(extInfo.getValidity()).map(String::valueOf).orElse("1")); // 有效期单位  4:/    3:天     2:年     1:个月
        objectData.put("field_kpaO1__c", opportunityId); // 商机id
        objectData.put("field_11H96__c", productCategory); // 产品分类
        objectData.put("field_ua5M4__c", productName); // 产品名称
        objectData.put("field_1j8lg__c", productDetailItem); // 产品明细项
        objectData.put("field_F3Dg3__c", specificationUnit); // 规格单位
        objectData.put("field_7kckS__c", "100"); // 折扣比例
        objectData.put("field_h20F2__c", "业务合同"); // 合同大类
        objectData.put("record_type", "default__c"); // 预设业务类型
        objectData.put("field_G1o1w__c", extInfo.getAmount()); // 标准总价（元）
        objectData.put("field_ej95F__c", extInfo.getAmount()); // 折扣总价(元)
        objectData.put("field_I9usk__c", extInfo.getAmount()); // 售价
        objectData.put("contract_details_source__c", "2"); // 合同明细来源：2自助下单

        // 针对点播产品的特殊处理
        if ("cool_package".equals(name) || "vod_package".equals(name)) {
            objectData.put("n_playback_traffic_type__c", extInfo.getVodPackageIsAnnualFlow() == 1 ? "CbRAjPdsG" : "option1");
            objectData.put("field_if17U__c", UnitConverterUtil.bytes2GBRoundUp(extInfo.getVodSpace())); // 套餐空间(G/年)
            objectData.put("field_21ZZz__c", UnitConverterUtil.bytes2GBRoundUp(extInfo.getVodFlow())); // 套餐流量(G/月)
        }

        // 业务类别
        objectData.put("field_GKqLr__c", "vod_package".equals(name) ? "点播" : "cool_package".equals(name) ? "酷播" : "直播");

        // 创建合同的请求参数
        Map<String, Object> param = new HashMap<>();
        Map<String, Object> dataParam = new HashMap<>();
        Map<String, Object> objectDataParam = new HashMap<>();
        param.put("includeDetailIds", true);
        param.put("data", dataParam);
        dataParam.put("object_data", objectDataParam);
        dataParam.put("details", ImmutableMap.of("object_b6MrX__c", Collections.singletonList(objectData)));

        objectDataParam.put("dataObjectApiName", "object_dLsRA__c"); // 固定
        objectDataParam.put("contract_amount", extInfo.getAmount()); // 合同金额
        objectDataParam.put("owner", Collections.singletonList(ownerUserId));
        objectDataParam.put("field_Yh114__c", customerAccountId); // 用户在分销客的id
        objectDataParam.put("field_SkHv8__c", Collections.singletonList(opportunityId)); // 商机id
        objectDataParam.put("field_oWU2W__c", "0");
        objectDataParam.put("field_X9ozy__c", "0");
        objectDataParam.put("field_8ffAS__c", extInfo.getCooperationSubject()); // 合作主体
        objectDataParam.put("field_o6D0s__c", "1"); // 是否需要扫描件 1:不需要
        objectDataParam.put("field_46nzH__c", "1"); // 合同是否邮寄 1：不需要
        objectDataParam.put("field_2kXbJ__c", "0"); // 签约主体与开票主体是否一致 0：是
        objectDataParam.put("field_01ey2__c", Collections.singletonList(salesOperationsUserId)); // 销售运营
        objectDataParam.put("N_standard_contract_templa__c", ""); // 合同模版，传空表示无合同模板
        objectDataParam.put("field_4896a__c", company); // 公司名称
        objectDataParam.put("field_ym54W__c", "5"); // 合同签署方式

        return param;
    }

    /**
     * 创建回款
     * @param extInfo               开通请求参数
     * @param customerAccountId     客户ID
     * @param ownerUserId           负责人用户ID
     * @param salesOperationsUserId 销售运营用户ID
     * @param financialReceiptId    财务到款ID
     * @param orderId               订单ID
     */
    private void createReimbursement(OnlineBusinessOpenCommonPackageExtInfo extInfo,
                                     String customerAccountId,
                                     String ownerUserId,
                                     String salesOperationsUserId,
                                     String financialReceiptId,
                                     String orderId) {
        String amount = extInfo.getAmount();
        String payedAccountCode = extInfo.getPayedAccountCode();
        OnlineBusinessPayAccountEnum payedAccountEnum = OnlineBusinessPayAccountEnum.getByName(payedAccountCode);

        // 构造创建回款的请求参数
        Map<String, Object> reimbursementParam = buildCreateReimbursementParam(
                customerAccountId, ownerUserId, salesOperationsUserId,
                financialReceiptId, orderId, amount, payedAccountEnum.getReimbursementCode()
        );

        // 调用纷享销客API创建回款
        ResponseVO<FXiaoKeBaseResponse> response = fxiaoKeService.createCustomObjCustomizeField(reimbursementParam);

        log.info("create reimbursement, req: {}, response: {}", JacksonUtil.writeAsString(reimbursementParam), JacksonUtil.writeAsString(response));

        if (!response.isSuccess()) {
            throw new BizException(response.getError().getCode(), response.getError().getDesc());
        }

        FXiaoKeBaseResponse fxkResponse = response.getData();
        if (fxkResponse == null || !fxkResponse.isSuccess()) {
            throw new BizException(500, "创建回款失败. " + formatFxkApiErrorMsg(fxkResponse));
        }
    }

    /**
     * 构造创建回款的请求参数
     * @param customerAccountId     客户ID
     * @param ownerUserId           负责人用户ID
     * @param salesOperationsUserId 销售运营用户ID
     * @param financialReceiptId    财务到款ID
     * @param orderId               订单ID
     * @param amount                回款金额
     * @param accountCode           收款账户代码
     * @return 请求参数Map
     */
    private Map<String, Object> buildCreateReimbursementParam(String customerAccountId,
                                                              String ownerUserId,
                                                              String salesOperationsUserId,
                                                              String financialReceiptId,
                                                              String orderId,
                                                              String amount,
                                                              String accountCode) {
        long nowTs = System.currentTimeMillis();
        
        Map<String, Object> param = new HashMap<>();
        param.put("includeDetailIds", true);

        Map<String, Object> objectData = new HashMap<>();
        objectData.put("dataObjectApiName", "PaymentObj"); // 回款对象
        objectData.put("account_id", customerAccountId); // 客户名称
        objectData.put("owner", Collections.singletonList(ownerUserId)); // 负责人
        objectData.put("field_TZ0si__c", Collections.singletonList(salesOperationsUserId)); // 销售运营人员
        objectData.put("amount", amount); // 回款金额
        objectData.put("life_status", "审核中"); // 状态：未生效;审核中;已回款;变更中;已作废
        objectData.put("field_Ep234__c", financialReceiptId); // 财务到款明细编号
        objectData.put("field_CI29v__c", "0"); // 是否拆分业绩 0否
        objectData.put("field_eS1m5__c", accountCode); // 收款账户
        objectData.put("payment_time", nowTs); // 回款日期
        objectData.put("field_11kiF__c", "option1"); // 是否为关联打款方
        objectData.put("field_nzrEd__c", "option1"); // 是否为个人代付 option1否，Z7Sv7RRcS是
        objectData.put("field_19y67__c", ""); // 个人代付名称

        // 构造回款明细
        Map<String, Object> reimbursementDetail = new HashMap<>();
        reimbursementDetail.put("dataObjectApiName", "OrderPaymentObj"); // 回款明细对象
        reimbursementDetail.put("payment_amount", amount); // 金额
        reimbursementDetail.put("order_id", orderId); // 订单编号
        reimbursementDetail.put("owner", Collections.singletonList(ownerUserId)); // 负责人
        reimbursementDetail.put("life_status", "审核中"); // 状态
        reimbursementDetail.put("field_lVUUh__c", "0"); // 业绩/提成计算方式
        reimbursementDetail.put("field_B2Qgs__c", nowTs); // 回款日期
        reimbursementDetail.put("colloect_money_detail_collect_account__c", "中国银行"); // 收款账户

        Map<String, Object> details = new HashMap<>();
        details.put("OrderPaymentObj", Collections.singletonList(reimbursementDetail));

        Map<String, Object> data = new HashMap<>();
        data.put("object_data", objectData);
        data.put("details", details);

        param.put("data", data);

        return param;
    }

    /**
     * 配置订单
     * @param request 开通请求参数
     * @param orderId 订单ID
     * @param productDetail 商品详情VO对象
     */
    @SuppressWarnings("rawtypes")
    private void configureOrder(OnlineBusinessOpenRequest request, String orderId, FXiaoKeProductDetailVO productDetail) throws ClearingSystemException {
        // 这里可能分销客还没有把数据同步回来, 我们需要等待数据同步回来再添加订单规格
        for (int i = 1; i <= queryCrmOrderRetryTimes; i++) {
            if (salesOpportunitiesRepository.findBySoId(orderId) != null) {
                break;
            }
            ThreadUtil.sleep(2000);
            log.info("wait sales opportunities data, soId: {}, retry: {}", orderId, i);
        }
        
        // 构造配置订单的请求参数
        BusinessOrderAddSpecDTO specDTO = buildBusinessOrderAddSpecDTO(request, orderId, productDetail);

        CommonResult result = businessOrderService.addSpec(specDTO);

        log.info("add business order spec, dto: {}, result: {}", JacksonUtil.writeAsString(specDTO), JacksonUtil.writeAsString(result));

        if (!CommonResult.isOk(result)) {
            throw new BizException(500, "保存POP商机套餐规格信息失败, 错误信息: " + result.getMsg());
        }
    }

    /**
     * 构造配置订单的请求参数
     * @param request 开通请求参数
     * @param orderId 订单ID
     * @return BusinessOrderAddSpecDTO
     */
    private BusinessOrderAddSpecDTO buildBusinessOrderAddSpecDTO(OnlineBusinessOpenRequest request, String orderId, FXiaoKeProductDetailVO productDetail) {
        OnlineBusinessOpenCommonPackageExtInfo extInfo = JacksonUtil.readValue(request.getOpenResource().getExtInfo(), OnlineBusinessOpenCommonPackageExtInfo.class);

        BusinessOrderAddSpecDTO specDTO = new BusinessOrderAddSpecDTO();

        // 基础参数
        specDTO.setSoId(orderId);

        specDTO.setAccountType(AccountTypeEnum.NORMAL.getCode()); // 暂时写死普通账号
        specDTO.setUnionId(request.getUnionId());

        // 套餐基础信息
        specDTO.setVodPackageType(extInfo.getPackageId());
        specDTO.setPackageName(extInfo.getPackageName());
        specDTO.setPackageDesc(extInfo.getPackageDesc());
        specDTO.setVodPackageIsAnnualFlow(extInfo.getVodPackageIsAnnualFlow());
        specDTO.setVodPackageFlowMonthsOfYear(extInfo.getVodPackageFlowMonthsOfYear());
        specDTO.setValidPeriodStartTime(extInfo.getStartDate());
        specDTO.setValidPeriodEndTime(extInfo.getEndDate());
        specDTO.setPackageIsCustomize(extInfo.getPackageIsCustomize() != null ? extInfo.getPackageIsCustomize().shortValue(): null);
        specDTO.setVodSpace(extInfo.getVodSpace() != null ? String.valueOf(UnitConverterUtil.bytes2GBRoundUp(extInfo.getVodSpace())) : null);
        specDTO.setVodFlow(extInfo.getVodFlow() != null ? String.valueOf(UnitConverterUtil.bytes2GBRoundUp(extInfo.getVodFlow())) : null);
        specDTO.setRemark(extInfo.getRemark());
        specDTO.setFunctionList(extInfo.getFunctionList());
        
        // 其他计费项
        List<OtherBillConfigItemDO> otherBillConfigItemDOS = new ArrayList<>();
        
        String billingPlanCode = productDetail.getName();

        // 直播分钟数
        if ("live_duration".equals(billingPlanCode)) {
            specDTO.setLivePackageType(extInfo.getPackageId());
            specDTO.setLivePackageMinutesQuantity(String.valueOf(extInfo.getQuantity()));
        }

        // 直播并发
        if ("live_concurrent".equals(billingPlanCode)
                || "live_prtc_concurrent".equals(billingPlanCode)) {
            // 目前只有并发包月可以在线续, 先写死
            String liveConcurrentType = "live_concurrent".equals(extInfo.getBillingPlanCode())
                    ? LiveBillingType.Monthly.getValue()
                    : LiveBillingType.Prtc_Monthly.getValue();
            specDTO.setLiveConcurrentType(liveConcurrentType);
            specDTO.setLiveConcurrentDurationUnit("月"); // 先写死
            specDTO.setLiveConcurrentQuantity(String.valueOf(extInfo.getQuantity()));
            specDTO.setLiveConcurrentDuration(String.valueOf(extInfo.getValidityUnit()));
            specDTO.setLiveConcurrentIsLimit(extInfo.getLiveConcurrentIsLimit());
        }
        
        // 点播空间
        if ("vod_space".equals(billingPlanCode)) {
            specDTO.setVodSpace(extInfo.getQuantity() != null ? String.valueOf(extInfo.getQuantity()) : null);
        }
        
        //点播流量
        if ("vod_flow".equals(billingPlanCode)) {
            specDTO.setVodFlow(extInfo.getQuantity() != null ? String.valueOf(extInfo.getQuantity()) : null);
            specDTO.setVodFlowType("package_spec_flow_pack");
        }
        
        //连麦分钟数
        if ("live_mic_duration".equals(billingPlanCode)) {
            specDTO.setLiveMicMinutesQuantity(String.valueOf(extInfo.getQuantity()));
        }
        
        //智能制课
        if ("aiPPTVideoDigitalHumanEnabled".equals(billingPlanCode)) {
            //"[{\"code\":\"aiPPTVideoDigitalHumanEnabled\",\"name\":\"智能制课（数字人）\",\"value\":1000,
            // \"expireTimeStart\":\"2025-08-25\",\"expireTimeEnd\":\"2025-08-30\",\"sys\":\"Live\",\"type\":0,
            // \"unit\":\"分钟\"}]"
            OtherBillConfigItemDO otherBillConfigItemDO = OtherBillConfigItemDO.builder().build();
            otherBillConfigItemDO.setCode("aiPPTVideoDigitalHumanEnabled");
            otherBillConfigItemDO.setName("智能制课（数字人）");
            otherBillConfigItemDO.setValue(String.valueOf(extInfo.getQuantity()));
            otherBillConfigItemDO.setExpireTimeStart(DateFormatUtil.parseDate(extInfo.getStartDate(), Constant.DATE_FORMAT_yyyy_MM_dd));
            otherBillConfigItemDO.setExpireTimeEnd(DateFormatUtil.parseDate(extInfo.getEndDate(), Constant.DATE_FORMAT_yyyy_MM_dd));
            otherBillConfigItemDO.setSys("Live");
            otherBillConfigItemDO.setType(0);
            otherBillConfigItemDO.setUnit("分钟");
            otherBillConfigItemDOS.add(otherBillConfigItemDO);
        }
        if (CollectionUtils.isNotEmpty(otherBillConfigItemDOS)) {
            specDTO.setOtherBillItems(JacksonUtil.writeAsString(otherBillConfigItemDOS));
        }
        
        
        ResponseVO<CustomerEntityVO> customerResponse = crmApi.getCustomerByUnionId(request.getUnionId());
        if (!customerResponse.isSuccess() || customerResponse.getData() == null) {
            throw new BizException(400, "查找用户信息失败");
        }
        specDTO.setEmail(customerResponse.getData().getEmail());
        specDTO.setCompany(customerResponse.getData().getCompany());

        // 默认值参数
        specDTO.setIsAlreadyActivated((short) 0);
        specDTO.setAutoOpenOrder(1);
        specDTO.setShowUnivalent(true);

        return specDTO;
    }
    
    private String formatFxkApiErrorMsg(FXiaoKeBaseResponse response) {
        if (response == null || response.isSuccess()) {
            return "";
        }
        int errorCode = response.getErrorCode();
        String errorMessage = response.getErrorMessage();
        return String.format("errorCode: [%s]. errorMessage: [%s]", errorCode, errorMessage);
    }

}
