package net.polyv.service;

import java.util.List;
import java.util.Map;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.salesopportunities.BillingItemDO;
import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.itemsetting.data.CustomerBillingItemManualVO;
import net.polyv.web.model.itemsetting.input.CustomerBillingItemRatioRequest;
import net.polyv.web.model.itemsetting.input.CustomerBillingItemSettingSaveInputVO;

/**
 * @author: chens<PERSON><PERSON>
 * @since: 2020/5/13
 * @description: 用户-计费项-设置表操作的service接口
 */
public interface CustomerBillingItemSettingService {

    /**
     * 保存顾客计费项
     * @param inputVO 保存信息入参
     */
    void saveCustomerBillingItemSetting(CustomerBillingItemSettingSaveInputVO inputVO);
    
    
    /**
     * 设置用户计费项比率
     * @param cbirRequest 请求对象
     * @return 修改结果
     * @throws ClearingSystemException 异常
     */
    CommonResult updateCustomerBillingItemRatio(CustomerBillingItemRatioRequest cbirRequest)
            throws ClearingSystemException;
    
    
    /**
     * 保存计费项目，不跟销售机会走，不调账
     * @param inputVO
     */
    CommonOperateResultVO saveCustomerBillingItemManualSetting(CustomerBillingItemManualVO inputVO);
    
    /**
     * 获取超码率倍率map
     * @return
     */
    Map<String, Integer> getOverCodeRateItemRatio();
    
    /**
     * 更新客户超码率倍率
     * @param unionIds
     */
    void updateOverCodeRateItemRatio(List<String> unionIds, Map<String, Integer> itemRatios)
            throws ClearingSystemException;

    /**
     * 更新客户的连麦自定义配置
     * @param unionIds
     */
    void updateMicPdItemSetting(List<String> unionIds)
            throws ClearingSystemException;

    void updateMicPdItemSettingByResolution(String customerId,String resolution) ;

    /**
     * 更新客户的自定义配置
     * @param customerId
     * @param resourceCode
     * @param billingItemDO
     */
    void updateCustomerItemSetting(String customerId, BillingItemDO billingItemDO,String resourceCode) ;
    
}
