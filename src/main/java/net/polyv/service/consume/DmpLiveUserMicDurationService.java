package net.polyv.service.consume;

import java.util.Date;

import org.springframework.data.domain.Page;

import net.polyv.constant.item.MicItemScaleGradientConst;
import net.polyv.model.entity.dmp.LiveDbUserMicDurationDaily;

/**
 * 直播连麦分钟数服务
 * <AUTHOR>
 * @since 2020/6/21
 */
public interface DmpLiveUserMicDurationService {
    
    Page<LiveDbUserMicDurationDaily> pageUserMicDuration(Date statAt, Integer pageIndex, Integer pageSize,
            MicItemScaleGradientConst micScaleGradient);
}
