package net.polyv.service.consume;

import net.polyv.model.entity.primary.ItemConsumeDaily;
import net.polyv.modules.pcs.api.req.GroupMicDurationGetRequest;
import net.polyv.modules.pcs.api.vo.GroupMicDurationResultVO;
import net.polyv.web.model.consume.input.UserConsumeInfoGetVO;
import net.polyv.web.model.consume.result.UserConsumeInfoVO;
import net.polyv.web.model.resource.CustomerMaxConcurrenceInputVO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 计费项用量服务
 *
 */
public interface ItemConsumeService {

    /**
     * 分页查询
     * @param getVO
     * @return
     */
    Page<ItemConsumeDaily> getGroupConsumeInfo(UserConsumeInfoGetVO getVO);

    /**
     * 获取用户指定时间范围使用最大并发
     * @param inputVO
     * @return
     */
    long getCustomerMaxConcurrence(CustomerMaxConcurrenceInputVO inputVO);

    /**
     * 获取集团主账号连麦计量数据
     * @param request
     * @return
     */
    List<GroupMicDurationResultVO> getGroupMicDuration(GroupMicDurationGetRequest request);
}
