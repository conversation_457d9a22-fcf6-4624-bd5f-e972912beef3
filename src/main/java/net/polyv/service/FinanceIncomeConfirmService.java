package net.polyv.service;

import com.alibaba.excel.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.billingdaily.CustomerBillingDailyTradeTypeConst;
import net.polyv.constant.finance.ContractAssociationStatusEnum;
import net.polyv.constant.finance.ContractOperationCodeEnum;
import net.polyv.constant.finance.ContractOriginEnum;
import net.polyv.constant.finance.FinanceIncomeDetailTagEnum;
import net.polyv.constant.finance.FinanceTaxRateConst;
import net.polyv.constant.finance.StatusValueEnum;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.constant.resource.ResourceSourceConst;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.dao.primary.GroupAccountConfigRepository;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.billingConfig.BillingItemExtRepository;
import net.polyv.dao.primary.business.BusinessPackageSpecificationRepository;
import net.polyv.dao.primary.finance.FinanceContractRepository;
import net.polyv.dao.primary.finance.FinanceContractSpecificationRepository;
import net.polyv.dao.primary.finance.FinanceIncomeDetailRepository;
import net.polyv.dao.primary.finance.FinanceIncomeRepository;
import net.polyv.dao.primary.finance.FinanceRefundRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.GroupAccountConfig;
import net.polyv.model.entity.primary.billingconfig.BillingItemExt;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.model.entity.primary.finance.FinanceContractSpecificationEntity;
import net.polyv.model.entity.primary.finance.FinanceIncomeDetailEntity;
import net.polyv.model.entity.primary.finance.FinanceIncomeEntity;
import net.polyv.rest.client.dmp.LiveUserDurationClient;
import net.polyv.rest.client.group.GroupOneAccountClient;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.client.vod.user.UnionUserClient;
import net.polyv.rest.model.finance.Bills4FinanceDTO;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.service.account.CustomerService;
import net.polyv.service.bill.BillingConsumeDataService;
import net.polyv.service.bill.BillingDataService;
import net.polyv.service.bill.BillingService;
import net.polyv.service.finance.FinanceContractService;
import net.polyv.service.finance.FinanceIncomeService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.MoneyUtil;
import net.polyv.util.converter.UnitConverterUtil;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.consume.input.AggByUserInputVO;
import net.polyv.web.model.consume.input.UserConsumeInfoGetVO;
import net.polyv.web.model.consume.result.AggUserConsumeVO;
import net.polyv.web.model.consume.result.UserConsumeInfoVO;
import net.polyv.web.model.finance.data.BillingItemAttributeDTO;
import net.polyv.web.model.finance.data.CurrentFinanceIncomeDTO;
import net.polyv.web.model.finance.input.Bills4FinanceQueryDO;
import net.polyv.web.model.finance.input.ContractSpecQueryDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_EVEN;

/**
 * @description: 财务系统收入确认服务
 * @author: Neo
 * @date: 2022-09-07
 */
@Slf4j
@Component
public class FinanceIncomeConfirmService {

    @Autowired
    private FinanceContractRepository contractRepository;
    @Autowired
    private FinanceIncomeRepository incomeRepository;
    @Autowired
    private FinanceIncomeDetailRepository incomeDetailRepository;
    @Autowired
    private FinanceContractSpecificationRepository contractSpecificationRepository;
    @Autowired
    private BusinessPackageSpecificationRepository businessSpecificationRepository;
    @Autowired
    private FinanceRefundRepository refundRepository;
    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    @Autowired
    private BillingConsumeDataService billingConsumeDataService;
    @Autowired
    private CustomerBillingDailyRepository customerBillingDailyRepository;
    @Autowired
    private BillingDataService billingDataService;
    @Autowired
    private BillingService billingService;
    @Autowired
    private FinanceIncomeService incomeService;
    @Autowired
    private FinanceContractService contractService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private GroupOneAccountClient groupOneAccountClient;
    @Autowired
    private UnionUserClient unionUserClient;
    @Autowired
    private LiveUserDurationClient liveUserDurationClient;
    @Autowired
    private GroupAccountConfigRepository groupAccountConfigRepository;
    @Resource
    private BillingItemExtRepository billingItemExtRepository;
    
    /**
     * 处理退费账单、退费记录的合同设置为未完成确认收入状态
     * @param specifyDate 指定日期
     */
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void adjustUnfinishedContractStatus(Date specifyDate) {
        // 查询存在调账退费的账单的合同ID
        Date billStartDate = DateUtil.getSecondDayOfMonth(specifyDate);
        Date billEndDate = DateUtil.getDateEnd(DateUtil.getFirstDayOfXMonth(specifyDate, 1));
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        List<String> refundBillsContractIdList = this.billingService.getRefundBillsContractId(billStartDate, billEndDate, CustomerBillingDailyTradeTypeConst.refund_adjust_bill.getTradeType());
        // 查询退款合同ID集合
        Date refundStartDate = DateUtil.getDateStart(DateUtil.getFirstDayOfMonth(specifyDate));
        Date refundEndDate = DateUtil.getDateEnd(DateUtil.getLastDayOfMonth(specifyDate));
        List<String> refundContractIdList = this.refundRepository.getContractRefundByDate(refundStartDate, refundEndDate);
        List<String> combinedContractIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(refundBillsContractIdList)) {
            combinedContractIdList.addAll(refundBillsContractIdList);
        }
        if (!CollectionUtils.isEmpty(refundContractIdList)) {
            combinedContractIdList.addAll(refundContractIdList);
        }
        if (CollectionUtils.isEmpty(combinedContractIdList)) {
            return;
        }
        combinedContractIdList = combinedContractIdList.stream().distinct().collect(Collectors.toList());
        this.contractRepository.updateContractIncomeStatus(combinedContractIdList, currentMonth);
    }

    public void confirmAssociatedContractIncome(Date specifyDate) {
        // 查询本月关联合同
        Date associatedStartDate = DateUtil.getFirstDayOfMonth(specifyDate);
        Date associatedEndDate = DateUtil.getFirstDayOfXMonth(specifyDate, 1);
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        // 查询已经确认收入，当前关联的合同
        List<FinanceContractEntity> associatedContractIdList = this.contractRepository.getFinishedIncomeCurrentAssociatedContract(currentMonth, associatedStartDate, associatedEndDate,
                ContractAssociationStatusEnum.ASSOCIATED.getCode());
        if (!CollectionUtils.isEmpty(associatedContractIdList)) {
            for (FinanceContractEntity contractEntity : associatedContractIdList) {
                try {
                    String operationCode = contractEntity.getOperationCode();
                    // 处理金额合同历史关联
                    if (StringUtils.isNotEmpty(operationCode) && (ContractOperationCodeEnum.DEPOSIT_AMOUNT.getCode().equals(operationCode) ||
                            ContractOperationCodeEnum.OVER_AMOUNT_PAY.getCode().equals(operationCode))) {
                        continue;
                    }
                    String contractId = contractEntity.getContractId();
                    // 执行收入流程，使其生成关联后的收入
                    CurrentFinanceIncomeDTO currentFinanceIncome = this.incomeService.getCurrentFinanceIncome(contractEntity, specifyDate);
                    // 关联差额 = (新单价-旧单价)*充值量/1.06
                    // BigDecimal otherAmount = (contractUnivalentWithTax.subtract(beforeContractUnivalent)).multiply(cumulativeConsumed).divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0, RoundingMode.HALF_EVEN);
                    this.incomeService.setCurrentFinanceIncome(contractEntity, currentFinanceIncome, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                } catch (Exception e) {
                    log.error(String.format("confirmAssociatedContractIncome occurred.contractId %s exception %s", contractEntity.getContractId(), e.getMessage()), e);
                }
            }
        }
    }


    public void confirmRefundContractIncome(Date specifyDate) {
        // 处理那些业务合同已经不会再进入流程的退费
        // 查询退款合同ID集合
        Date refundStartDate = DateUtil.getDateStart(DateUtil.getFirstDayOfMonth(specifyDate));
        Date refundEndDate = DateUtil.getDateEnd(DateUtil.getLastDayOfMonth(specifyDate));
        List<String> refundContractIdList = this.refundRepository.getContractRefundByDate(refundStartDate, refundEndDate);
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        if (!CollectionUtils.isEmpty(refundContractIdList)) {
            List<FinanceContractEntity> refundContractList = this.contractRepository.findByContractIdIn(refundContractIdList);
            if (!CollectionUtils.isEmpty(refundContractList)) {
                for (FinanceContractEntity contractEntity : refundContractList) {
                    try {
                        String contractId = contractEntity.getContractId();
                        // 执行收入流程，使其生成退费的收入
                        CurrentFinanceIncomeDTO currentFinanceIncome = this.incomeService.getCurrentFinanceIncome(contractEntity, specifyDate);
                        this.incomeService.setCurrentFinanceIncome(contractEntity, currentFinanceIncome, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                    } catch (Exception e) {
                        log.error(String.format("confirmRefundContractIncome occurred.contractId %s exception %s", contractEntity.getContractId(), e.getMessage()), e);
                    }
                }
            }
        }
    }

    /**
     * 确认充值量合同收入流程
     *
     * @param operationCode 合同类别编码
     * @param unionId       用户ID
     * @param currentMonth  当前月份
     */
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void confirmQuantityContractIncome(String operationCode, String currentMonth, Date specifyDate, String unionId) {
        // 当月最后一天（now）
        Date monthEndDate = DateUtil.getCurrentMonthEnd(specifyDate);
        // 下个月第一天
        Date nextMonthFirstDate = DateUtil.getFirstDayOfXMonth(specifyDate, 1);
        // 下个月第一天字符串类型
        String nextMonthFirstDateChar = DateFormatUtil.formatDateTimeNormal(nextMonthFirstDate);
        // 当月第二天
        Date consumedStartDate = DateUtil.getSecondDayOfMonth(specifyDate);
        // 下个月第一天
        Date consumedEndDate = DateUtil.getFirstDayOfXMonth(specifyDate, 1);

        ContractSpecQueryDO searchDO = ContractSpecQueryDO.builder()
                .unionId(unionId)
                .operationCode(operationCode)
                .businessEndDate(nextMonthFirstDateChar)
                .currentMonth(currentMonth).build();
        // 查询合同数据
        List<FinanceContractEntity> contractList = this.contractRepository.getContractBySearchDO(searchDO);
        if (CollectionUtils.isEmpty(contractList)) return;

        /*
            获取使用量账单
         */
        BigDecimal totalConsumed = this.getBusinessConsumed(operationCode, currentMonth, specifyDate, unionId);
        log.info(String.format("getBusinessConsumed-unionId{%s},operationCode{%s},consumedStartDate{%s},consumedEndDate{%s}.totalConsumed{%s}",
                unionId, operationCode, consumedStartDate, consumedEndDate, totalConsumed.toPlainString()));
        // 是否需要继续扣减
        boolean isNextDeduction = true;

        innerLoop:
        for (FinanceContractEntity contract : contractList) {
            // 跳过大于当前时间的合同
            if (contract.getCreateTime().getTime() > monthEndDate.getTime()) {
                continue innerLoop;
            }
            String contractWay = contract.getContractWay();
            if (StringUtils.isNotEmpty(contractWay) && ContractOriginEnum.ORIGIN_MONTHLY_SETTLE.getCode().equals(contractWay)) {
                // 月结不在这里结算
                continue;
            }
            // 获取合同当前可用金额
            CurrentFinanceIncomeDTO currentIncomeDTO = this.incomeService.getCurrentFinanceIncome(contract, monthEndDate);
            BigDecimal currentAvailable = currentIncomeDTO.getContractAvailable();
            log.info(String.format("合同{%s}: 可用金额：{%s}", currentIncomeDTO.getContractId(), currentAvailable.toPlainString()));
            if (currentAvailable.compareTo(BigDecimal.ZERO) == 0) {
                // 合同金额等于0，本合同当月不参与分钟数计算
                this.incomeService.setCurrentFinanceIncome(contract, currentIncomeDTO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                continue innerLoop;
            }
            // 获取该合同规格配置
            CommonResult<BusinessPackageSpecification> contractSpecResult = this.contractService.getContractSpec(contract, specifyDate);
            if (CommonResult.isNotOk(contractSpecResult)) {
                continue innerLoop;
            }
            BusinessPackageSpecification contractSpec = contractSpecResult.getData();
            // 消耗收入
            BigDecimal usageIncomeAmount = null;
            // 消耗用量
            BigDecimal usageIncomeQuantity = null;
            // 非消耗收入
            BigDecimal otherIncomeAmount = null;
            // 非消耗用量
            BigDecimal otherIncomeQuantity = null;

            // 检查该合同是否当前充值量的单位和计算单价的时候单位不同
            Boolean isGbConverted = this.contractService.isGbConvertedContract(contract);

            // 合同当前可用充值量
            Long currentContractTotalQuantity = currentIncomeDTO.getContractQuantity();
            if (Objects.isNull(currentContractTotalQuantity) || currentContractTotalQuantity <= 0) {
                continue innerLoop;
            }
            // 点播空间、流量会转换成GB
            String currentContractId = contract.getContractId();
            log.info(String.format("合同%s当前充值量 %s，需要扣除用量 %s", currentContractId, currentContractTotalQuantity, totalConsumed.toPlainString()));
            // 单价（不含税）
            BigDecimal currentUnitPriceWithTax = currentIncomeDTO.getContractUnivalentWithTax();

            Date businessEndDate = null;
            if (Objects.nonNull(contractSpec)) {
                businessEndDate = contractSpec.getValidPeriodEndDate();
            }
            // 当前合同剩余充值量
            BigDecimal deferredQuantity = currentIncomeDTO.getDeferredQuantity();
            // BigDecimal deferredIncome = currentIncomeDTO.getDeferredIncome();
            // 每个合同都需要执行的流程（isNextDeduction不会影响流程，只会影响数据的扣减）
            if (Objects.nonNull(businessEndDate) && currentMonth.equals(DateFormatUtil.formatDateMonth(businessEndDate))) {
                if (totalConsumed.compareTo(BigDecimal.ZERO) > 0) { // 消耗大于0
                    // 需要扣除的用量
                    if (deferredQuantity.compareTo(totalConsumed) > 0) {
                        usageIncomeQuantity = totalConsumed;
                        otherIncomeQuantity = deferredQuantity.subtract(totalConsumed);
                    } else {
                        usageIncomeQuantity = deferredQuantity;
                        // usageIncomeAmount = deferredIncome;
                        otherIncomeQuantity = BigDecimal.ZERO;
                    }
                } else {
                    // 消耗小于等于0
                    usageIncomeQuantity = BigDecimal.ZERO;
                    usageIncomeAmount = BigDecimal.ZERO;
                    otherIncomeQuantity = deferredQuantity;
                }
                totalConsumed = totalConsumed.subtract(usageIncomeQuantity);
                if (totalConsumed.compareTo(BigDecimal.ZERO) <= 0) {
                    isNextDeduction = false;
                }
            } else {
                if (isNextDeduction) {
                    // 没有合同到期的情况
                    if (deferredQuantity.compareTo(totalConsumed) >= 0) {
                        usageIncomeQuantity = totalConsumed;
                        isNextDeduction = false;
                    } else {
                        usageIncomeQuantity = deferredQuantity;
                        // usageIncomeAmount = deferredIncome;
                    }
                    otherIncomeQuantity = BigDecimal.ZERO;
                    totalConsumed = totalConsumed.subtract(usageIncomeQuantity);
                } else {
                    usageIncomeQuantity = BigDecimal.ZERO;
                    otherIncomeQuantity = BigDecimal.ZERO;
                    usageIncomeAmount = BigDecimal.ZERO;
                    otherIncomeAmount = BigDecimal.ZERO;
                }
            }

            // 本次业务确认收入金额（ROUND_HALF_EVEN进制）
            if (isGbConverted) {
                // 单位转换（单位为GB/元，但消耗量单位是byte）
                if (Objects.isNull(usageIncomeAmount)) {
                    usageIncomeAmount = currentUnitPriceWithTax.multiply(usageIncomeQuantity)
                            .divide(new BigDecimal("1024").pow(3), 2, RoundingMode.HALF_UP)
                            .divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0, RoundingMode.DOWN);
                }
                if (Objects.isNull(otherIncomeAmount)) {
                    otherIncomeAmount = currentUnitPriceWithTax.multiply(otherIncomeQuantity)
                            .divide(new BigDecimal("1024").pow(3), 2, RoundingMode.HALF_UP)
                            .divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0, RoundingMode.DOWN);
                }
            } else {
                if (Objects.isNull(usageIncomeAmount)) {
                    usageIncomeAmount = currentUnitPriceWithTax.multiply(usageIncomeQuantity).divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0, RoundingMode.HALF_EVEN);
                }
                if (Objects.isNull(otherIncomeAmount)) {
                    otherIncomeAmount = currentUnitPriceWithTax.multiply(otherIncomeQuantity)
                            .divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0, RoundingMode.HALF_EVEN);
                }
            }
            log.info(String.format("合同%s当前单价（不含税） %s，当前合同金额 %s 需要确认消耗收入金额 %s ，非消耗金额 %s", currentContractId,
                    currentUnitPriceWithTax.toPlainString(), currentAvailable.toPlainString(),
                    usageIncomeAmount.toPlainString(), otherIncomeAmount.toPlainString()));
            this.incomeService.setCurrentFinanceIncome(contract, currentIncomeDTO, usageIncomeAmount, otherIncomeAmount,
                    usageIncomeQuantity, otherIncomeQuantity);
        }
    }
    
    public BigDecimal getBusinessConsumed(String operationCode, String currentMonth, Date specifyDate, String unionId) {
        return getBusinessConsumed(operationCode, currentMonth, specifyDate, unionId, "");
    }
    
    
    /**
     * 获取业务消耗用量
     * 普通账号通过用户unionId查询账单
     * 集团账号1.0通过主账号获取子账号列表查询账单
     * 集团账号2.0通过主账号groupId查询账单
     */
    public BigDecimal getBusinessConsumed(String operationCode, String currentMonth, Date specifyDate, String unionId,
            String contractId) {
        // 当月第一天
        Date currentMonthFirstDate = DateUtil.getFirstDayOfMonth(specifyDate);
        // String currentMonthFirstDateChar = DateFormatUtil.formatDateNormal(currentMonthFirstDate);
        // 当月最后一天
        Date currentMonthLastDate = DateUtil.getLastDayOfMonth(specifyDate);
        // String currentMonthLastDateChar = DateFormatUtil.formatDateNormal(currentMonthLastDate);
        // 当月第二天
        Date currentMonthSecondDate = DateUtil.getSecondDayOfMonth(specifyDate);
        String currentMonthSecondDateChar = DateFormatUtil.formatDateNormal(currentMonthSecondDate);
        // 下个月第一天
        Date nextMonthFirstDate = DateUtil.getFirstDayOfXMonth(specifyDate, 1);
        String nextMonthFirstDateChar = DateFormatUtil.formatDateNormal(nextMonthFirstDate);

        // 当月第三天
        Date currentMonthThirdDate = DateUtil.getDateAfterDays(1, currentMonthSecondDate);
        String currentMonthThirdDateChar = DateFormatUtil.formatDateNormal(currentMonthThirdDate);
        // 下个月第二天
        Date nextMonthSecondDate = DateUtil.getSecondDayOfMonth(nextMonthFirstDate);
        String nextMonthSecondDateChar = DateFormatUtil.formatDateNormal(nextMonthSecondDate);

        // 判断当前业务类型
        List<String> groupOneOperationCodeList = Lists.newArrayList(
                ContractOperationCodeEnum.VOD_PACKAGE_V1.getCode(),
                ContractOperationCodeEnum.VOD_FLOW_V1.getCode(),
                ContractOperationCodeEnum.LIVE_MIC_DURATION_V1.getCode(),
                ContractOperationCodeEnum.LIVE_DURATION_V1.getCode());
        List<String> groupOneSubUnionIdList = new ArrayList<>();
        String groupOneSubUnionIds = "";
        // 是否集团账号1.0的业务合同
        GroupAccountConfig groupAccountConfig = groupAccountConfigRepository.findByGroupId(unionId).orElse(null);
        if (groupOneOperationCodeList.contains(operationCode)) {
            // 查询集团账号1.0下面的子账号
            WrappedResponse<List<String>> groupSubEmailsResponse = this.groupOneAccountClient.getGroupSubEmails(unionId);
            List<String> groupSubEmailList = groupSubEmailsResponse.getData();
            if (!CollectionUtils.isEmpty(groupSubEmailList)) {
                List<UserMsgVO> unionUserList = UserClient.getUsersByEmailsRepaired(String.join(",", groupSubEmailList));
                if (!CollectionUtils.isEmpty(unionUserList)) {
                    groupOneSubUnionIdList = unionUserList.stream().map(UserMsgVO::getUnionId).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
                    groupOneSubUnionIds = unionUserList.stream().map(UserMsgVO::getUnionId).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(","));
                }
            }
        }

        String currentWithRate = null;
        BigDecimal consumed = BigDecimal.ZERO;
        boolean billingQuery = false;   // 是否查询账单
        String billingQueryStartDate = currentMonthSecondDateChar;
        String billingQueryEndDate = nextMonthFirstDateChar;

        AggByUserInputVO getConsumedQuery = new AggByUserInputVO();
        // 根据用户、计费项id、业务时间获取账单
        if (ContractOperationCodeEnum.LIVE_DURATION.getCode().equals(operationCode)) {
            billingQuery = true;
            currentWithRate = GlobalConfigConst.Y_FLAG;
            getConsumedQuery.setResourceCode(ResourceCodeConst.duration.name());
        } else if (ContractOperationCodeEnum.LIVE_GUIDE_DURATION.getCode().equals(operationCode)) {
            billingQuery = true;
            getConsumedQuery.setResourceCode(ResourceCodeConst.guide_duration.name());
        } else if (ContractOperationCodeEnum.LIVE_MIC_DURATION.getCode().equals(operationCode)) {
            billingQuery = true;
            // 连麦出账+1天
            billingQueryStartDate = currentMonthThirdDateChar;
            billingQueryEndDate = nextMonthSecondDateChar;
            getConsumedQuery.setResourceCode(ResourceCodeConst.mic_duration.name());
        } else if (ContractOperationCodeEnum.LIVE_CONCURRENT_PEAK.getCode().equals(operationCode)) {
            billingQuery = true;
            getConsumedQuery.setResourceCode(ResourceCodeConst.concurrence.name());
        } else if (ContractOperationCodeEnum.VOD_FLOW_V1.getCode().equals(operationCode) ||
                ContractOperationCodeEnum.VOD_PACKAGE_V1.getCode().equals(operationCode)) {
            UserConsumeInfoGetVO getVO = new UserConsumeInfoGetVO();
            getVO.setResourceCode(ResourceCodeConst.traffic.name());
            getVO.setStartDate(currentMonthFirstDate);
            getVO.setEndDate(currentMonthLastDate);
            getVO.setResourceSourceList(Lists.newArrayList(ResourceSourceConst.PERMANENT));
            getVO.setQueryNoContractId(true);
            getVO.setCustomerIdList(groupOneSubUnionIdList);
            UserConsumeInfoVO userConsumeInfo = this.billingConsumeDataService.getUserConsumeInfo(getVO);
            consumed = Objects.nonNull(userConsumeInfo) && Objects.nonNull(userConsumeInfo.getItemConsumed()) ? new BigDecimal(userConsumeInfo.getItemConsumed()) : BigDecimal.ZERO;
        } else if (ContractOperationCodeEnum.VOD_PACK_FLOW.getCode().equals(operationCode)) {
            UserConsumeInfoGetVO getVO = new UserConsumeInfoGetVO();
            getVO.setResourceCode(ResourceCodeConst.traffic.name());
            getVO.setStartDate(currentMonthFirstDate);
            getVO.setEndDate(currentMonthLastDate);
            getVO.setResourceSourceList(Lists.newArrayList(ResourceSourceConst.PERMANENT));
            getVO.setQueryNoContractId(true);
            getVO.setCustomerId(unionId);
            UserConsumeInfoVO userConsumeInfo = this.billingConsumeDataService.getUserConsumeInfo(getVO);
            consumed = Objects.nonNull(userConsumeInfo) && Objects.nonNull(userConsumeInfo.getItemConsumed()) ? new BigDecimal(userConsumeInfo.getItemConsumed()) : BigDecimal.ZERO;
        } else if (ContractOperationCodeEnum.LIVE_DURATION_V1.getCode().equals(operationCode)) {
            billingQuery = true;
            currentWithRate = GlobalConfigConst.Y_FLAG;
            getConsumedQuery.setResourceCode(ResourceCodeConst.duration.name());
        } else if (ContractOperationCodeEnum.LIVE_MIC_DURATION_V1.getCode().equals(operationCode)) {
            billingQuery = true;
            // 连麦出账+1天
            billingQueryStartDate = currentMonthThirdDateChar;
            billingQueryEndDate = nextMonthSecondDateChar;
            getConsumedQuery.setResourceCode(ResourceCodeConst.mic_duration.name());
        } else if (ContractOperationCodeEnum.LIVE_FLOW.getCode().equals(operationCode)) {
            billingQuery = true;
            currentWithRate = GlobalConfigConst.Y_FLAG;
            getConsumedQuery.setResourceCode(ResourceCodeConst.live_flow.name());
        }
        BillingItemExt billingItemExt = this.billingItemExtRepository.findByItemCode(operationCode);
        if (Objects.nonNull(billingItemExt)) {
            //自定义计费项使用itemCode
            getConsumedQuery.setItemCode(billingItemExt.getItemCode());
            billingQuery = true;
            if (StringUtils.isNotEmpty(contractId)) {
                getConsumedQuery.setContractId(contractId);
            }
        }
        if (billingQuery) {
            if (StringUtils.isNotEmpty(groupOneSubUnionIds)) {
                getConsumedQuery.setUnionIds(groupOneSubUnionIds);
            } else {
                getConsumedQuery.setUnionIds(unionId);
            }
            /*
                修改：之前使用consume_start_date、consume_end_date查询
                现在改为：stat_at查询，则应该startDate、endDate都加一天
             */
            getConsumedQuery.setStartDate(billingQueryStartDate);
            getConsumedQuery.setEndDate(billingQueryEndDate);
            getConsumedQuery.setWithRate(currentWithRate);
            List<AggUserConsumeVO> userConsumeList = this.billingConsumeDataService.aggUserConsumeDataByStatAt(
                    getConsumedQuery);
            if (!CollectionUtils.isEmpty(userConsumeList)) {
                BigDecimal sumConsumed = userConsumeList.stream()
                        .map(AggUserConsumeVO::getSumConsumed)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
                if (StringUtils.isNotEmpty(currentWithRate)) {
                    consumed = sumConsumed.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                } else {
                    consumed = sumConsumed;
                }
            }
        }
        //直播流量 ||素材库空间 || 素材库流量是返回gb，需要转换成byte
        if (ContractOperationCodeEnum.LIVE_FLOW.getCode().equals(operationCode) ||
                ContractOperationCodeEnum.MATERIAL_SPACE.getCode().equals(operationCode) ||
                ContractOperationCodeEnum.MATERIAL_TRAFFIC.getCode().equals(operationCode)) {
            consumed = BigDecimal.valueOf(UnitConverterUtil.GB2bytes(consumed));
        }
        return consumed;
    }

    /**
     * 处理直播流量包确认收入流程
     * @param specifyDate 指定日期
     */
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void confirmLiveFlowIncome(Date specifyDate, FinanceContractEntity currentContract) {
        String contractId = currentContract.getContractId();
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        Date consumeStartDate = DateUtil.getBeginOfMonth(specifyDate);
        Date consumeEndDate = DateUtil.getEndOfMonth(specifyDate);
        // 获取消耗
        UserConsumeInfoGetVO ucig = new UserConsumeInfoGetVO();

        String operationCode = currentContract.getOperationCode();
        // 获取当前合同收入信息
        CurrentFinanceIncomeDTO currentFinanceIncome = this.incomeService.getCurrentFinanceIncome(currentContract,
                specifyDate);
        // 获取合同规格
        BusinessPackageSpecification contractSpec = null;
        CommonResult<BusinessPackageSpecification> contractSpecResult = this.contractService.getContractSpec(currentContract, specifyDate);
        if (CommonResult.isOk(contractSpecResult)) {
            contractSpec = contractSpecResult.getData();
        }
        if (Objects.isNull(contractSpec)) {
            log.error(String.format("confirmVodYearlyIncome - 合同 %s 获取不到对应规格", contractId));
            return;
        }
        // 套餐开始结束时间
        Date validPeriodStartDate = contractSpec.getValidPeriodStartDate();
        Date validPeriodEndDate = contractSpec.getValidPeriodEndDate();
        // 计算套餐总量（Byte）
        BigDecimal contractTotalQuantity = new BigDecimal(currentFinanceIncome.getContractQuantity());

        // 消耗用量
        BigDecimal usageConsumed = BigDecimal.ZERO;
        // 非消耗用量
        BigDecimal otherConsumed = BigDecimal.ZERO;
        // 单价（含税）
        BigDecimal unitPriceWithTax = currentFinanceIncome.getContractUnivalentWithTax();

        // 当月使用量（G*单价）
        BigDecimal taxRate = currentFinanceIncome.getTaxRate();
        // 消耗用量金额
        BigDecimal usageIncome = BigDecimal.ZERO;
        // 非消耗用量金额
        BigDecimal otherIncome = BigDecimal.ZERO;
        ucig.setContractId(contractId);
        ucig.setResourceCode(ResourceCodeConst.live_flow.name());
        ucig.setStartDate(consumeStartDate);
        ucig.setEndDate(consumeEndDate);
        UserConsumeInfoVO userConsume = this.billingConsumeDataService.getUserConsumeInfo(ucig);
        // 本月消耗量（byte）
        Long currentMonthConsumed = userConsume.getItemConsumed();
        // 处理合同是否到期
        boolean isPackageExpire = Objects.nonNull(validPeriodEndDate) && DateFormatUtil.formatDateMonth(validPeriodEndDate).equals(currentMonth);
        if (Objects.isNull(currentMonthConsumed) || currentMonthConsumed <= 0) {
            // 账单为空，直接入本月收入
//            this.incomeService.setCurrentFinanceIncome(currentContract, currentFinanceIncome, BigDecimal.ZERO,
//                    BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
//            return;
            currentMonthConsumed = 0L;
        }
        // 消耗用量
        usageConsumed = new BigDecimal(currentMonthConsumed);
        // 当月使用（需要转换单位）
        BigDecimal currentMonthConsumedGb = UnitConverterUtil.bytes2GB(currentMonthConsumed);
        // 消耗用量金额
        usageIncome = unitPriceWithTax.multiply(currentMonthConsumedGb).divide(taxRate, 0, ROUND_HALF_EVEN);

        // 累计消耗量
        FinanceIncomeDetailEntity historyIncomeDetail =
                this.incomeDetailRepository.findNearestByContractIdAndIncomeMonth(
                contractId, currentMonth);
        BigDecimal totalConsumed = BigDecimal.ZERO;
        if (Objects.nonNull(historyIncomeDetail)) {
            totalConsumed = Objects.nonNull(historyIncomeDetail.getCumulativeConsume()) ?
                    new BigDecimal(historyIncomeDetail.getCumulativeConsume()) : BigDecimal.ZERO;
        }
        boolean isUsedOut = usageConsumed.add(totalConsumed).compareTo(contractTotalQuantity) >= 0;
        // 合同最后一月,或者用量已经使用完，将所有流量结入最后一月
        if (isPackageExpire) {
            otherConsumed = currentFinanceIncome.getDeferredQuantity().subtract(usageConsumed);
            BigDecimal otherConsumedGb = new BigDecimal(MoneyUtil.convertBytes2GB(otherConsumed.longValue()));
            otherIncome = unitPriceWithTax.multiply(otherConsumedGb).divide(taxRate, 0, ROUND_HALF_EVEN);
        }
        if (isUsedOut) {
            // 用完无需额外处理
        }
        this.incomeService.setCurrentFinanceIncome(currentContract, currentFinanceIncome, usageIncome, otherIncome,
                usageConsumed, otherConsumed);

    }

    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void confirmVodFlowIncome(Date specifyDate, FinanceContractEntity currentContract) {
        String contractId = currentContract.getContractId();
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        Date consumeStartDate = DateUtil.getBeginOfMonth(specifyDate);
        Date consumeEndDate = DateUtil.getEndOfMonth(specifyDate);
        // 获取消耗
        UserConsumeInfoGetVO ucig = new UserConsumeInfoGetVO();

        String operationCode = currentContract.getOperationCode();
        // 获取当前合同收入信息
        CurrentFinanceIncomeDTO currentFinanceIncome = this.incomeService.getCurrentFinanceIncome(currentContract,
                specifyDate);
        // 获取合同规格
        BusinessPackageSpecification contractSpec = null;
        CommonResult<BusinessPackageSpecification> contractSpecResult = this.contractService.getContractSpec(
                currentContract, specifyDate);
        if (CommonResult.isOk(contractSpecResult)) {
            contractSpec = contractSpecResult.getData();
        }
        if (Objects.isNull(contractSpec)) {
            log.error(String.format("confirmVodYearlyIncome - 合同 %s 获取不到对应规格", contractId));
            return;
        }
        // 套餐开始结束时间
        Date validPeriodStartDate = contractSpec.getValidPeriodStartDate();
        Date validPeriodEndDate = contractSpec.getValidPeriodEndDate();
        // 计算套餐总量（Byte）
        BigDecimal contractTotalQuantity = new BigDecimal(currentFinanceIncome.getContractQuantity());
        // 年流量月数
        Integer vodPackageFlowMonthsOfYear = null;
        // 周期数
        Integer periodNum = null;
        // 消耗用量
        BigDecimal usageConsumed = BigDecimal.ZERO;
        // 非消耗用量
        BigDecimal otherConsumed = BigDecimal.ZERO;
        // 单价（含税）
        BigDecimal unitPriceWithTax = currentFinanceIncome.getContractUnivalentWithTax();

        // 当月使用量（G*单价）
        BigDecimal taxRate = currentFinanceIncome.getTaxRate();
        // 消耗用量金额
        BigDecimal usageIncome = BigDecimal.ZERO;
        // 非消耗用量金额
        BigDecimal otherIncome = BigDecimal.ZERO;
        ucig.setContractId(contractId);
        ucig.setResourceCode(ResourceCodeConst.traffic.name());
        ucig.setStartDate(consumeStartDate);
        ucig.setEndDate(consumeEndDate);
        UserConsumeInfoVO userConsume = this.billingConsumeDataService.getUserConsumeInfo(ucig);
        // 本月消耗量（byte）
        Long currentMonthConsumed = userConsume.getItemConsumed();
        // 处理合同是否到期
        boolean isPackageExpire = Objects.nonNull(validPeriodEndDate) &&
                DateFormatUtil.formatDateMonth(validPeriodEndDate).equals(currentMonth);
        if (Objects.isNull(currentMonthConsumed) || currentMonthConsumed <= 0) {
            // 账单为空，直接入本月收入
            // this.incomeService.setCurrentFinanceIncome(currentContract, currentFinanceIncome, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            // return;
            currentMonthConsumed = 0L;
        }
        // 消耗用量
        usageConsumed = new BigDecimal(currentMonthConsumed);
        // 当月使用（需要转换单位）
        BigDecimal currentMonthConsumedGb = UnitConverterUtil.bytes2GB(currentMonthConsumed);
        // 消耗用量金额
        usageIncome = unitPriceWithTax.multiply(currentMonthConsumedGb).divide(taxRate, 0, ROUND_HALF_EVEN);
        if (ContractOperationCodeEnum.VOD_PACK_FLOW.getCode().equals(operationCode) || ContractOperationCodeEnum.COOL_PACK_FLOW.getCode().equals(operationCode)) {
            // 累计消耗量
            FinanceIncomeDetailEntity historyIncomeDetail = this.incomeDetailRepository.findNearestByContractIdAndIncomeMonth(contractId, currentMonth);
            BigDecimal totalConsumed = BigDecimal.ZERO;
            if (Objects.nonNull(historyIncomeDetail)) {
                totalConsumed = Objects.nonNull(historyIncomeDetail.getCumulativeConsume()) ? new BigDecimal(historyIncomeDetail.getCumulativeConsume()) : BigDecimal.ZERO;
            }
            boolean isUsedOut = usageConsumed.add(totalConsumed).compareTo(contractTotalQuantity) >= 0;
            // 合同最后一月,或者用量已经使用完，将所有流量结入最后一月
            if (isPackageExpire) {
                otherConsumed = currentFinanceIncome.getDeferredQuantity().subtract(usageConsumed);
                BigDecimal otherConsumedGb = new BigDecimal(MoneyUtil.convertBytes2GB(otherConsumed.longValue()));
                otherIncome = unitPriceWithTax.multiply(otherConsumedGb).divide(taxRate, 0, ROUND_HALF_EVEN);
            }
            if (isUsedOut) {
                // 用完无需额外处理
            }
            this.incomeService.setCurrentFinanceIncome(currentContract, currentFinanceIncome, usageIncome, otherIncome, usageConsumed, otherConsumed);
        } else if (ContractOperationCodeEnum.VOD_YEARLY_PACKAGE.getCode().equals(operationCode) ||
                ContractOperationCodeEnum.COOL_YEARLY_PACKAGE.getCode().equals(operationCode)) {
            // 年流量数
            vodPackageFlowMonthsOfYear = contractSpec.getVodPackageFlowMonthsOfYear();
            if (Objects.isNull(vodPackageFlowMonthsOfYear) || vodPackageFlowMonthsOfYear == 0) {
                vodPackageFlowMonthsOfYear = 12;
            }
            // 充值流量数
            BigDecimal currentPackageVodFlow = Objects.nonNull(contractSpec.getVodFlow()) ? new BigDecimal(contractSpec.getVodFlow() * vodPackageFlowMonthsOfYear) : BigDecimal.ZERO;
            // 新版年流量计算周期：根据套餐时间段来确认周期数
            periodNum = this.contractService.calcYearlyPackagePeriod(validPeriodStartDate, validPeriodEndDate, vodPackageFlowMonthsOfYear);
            // 判断本月是否已经到一个周期了
            // 列出每个周期结束的月份
            List<String> periodEndMonthList = new ArrayList<>();
            // 列出每个周期开始的日期
            List<Date> judgePeriodStartDateList = new ArrayList<>();
            // 列出每个周期结束的日期
            List<Date> judgePeriodEndDateList = new ArrayList<>();
            boolean afterPeriodIsExpire = false;
            Date calcCurrentPeriodStartDate = validPeriodStartDate;
            Date calcCurrentPeriodEndDate = null;
            for (int i = 1; i <= periodNum; i++) {  // 该循环是用于将每个周期的结束时间存放到集合中，用作下面判断当月是否是某个周期的结束，需要对应将该周期内的剩余流量（一个周期的流量减去已用）结入非消耗收入
                // 结束时间 = 开始时间+周期数-1天
                calcCurrentPeriodEndDate = DateUtil.getDateAfterDays(-1, DateUtil.getAddMonthDate(calcCurrentPeriodStartDate, vodPackageFlowMonthsOfYear));
                // 开始时间 = 结束时间+1天
                calcCurrentPeriodStartDate = DateUtil.getDateAfterDays(1, calcCurrentPeriodEndDate);
                if (calcCurrentPeriodEndDate.after(validPeriodEndDate)) {
                    calcCurrentPeriodEndDate = validPeriodEndDate;
                    afterPeriodIsExpire = true;
                }
                String currentPeriodEndMonth = DateFormatUtil.formatDateMonth(calcCurrentPeriodEndDate);
                if (!periodEndMonthList.contains(currentPeriodEndMonth)) {
                    periodEndMonthList.add(currentPeriodEndMonth);
                    judgePeriodEndDateList.add(calcCurrentPeriodEndDate);
                }
                if (afterPeriodIsExpire) {
                    // 计算结束时间时，以套餐的结束时间为终点
                    break;
                }
            }
            if (isPackageExpire) {
                // 过期的时候把剩余整个合同剩余结入非消耗
                // 去掉，用周期过期足够了 10.13
                // 不够，nnd ，23.08.10
                otherIncome = currentFinanceIncome.getDeferredIncome().subtract(usageIncome);
                otherConsumed = currentFinanceIncome.getDeferredQuantity().subtract(usageConsumed);
                this.incomeService.setCurrentFinanceIncome(currentContract, currentFinanceIncome, usageIncome, otherIncome, usageConsumed, otherConsumed);
                return;
            }
            // 年流量的处理相对会比较复杂一点，需要确定每个周期结束时间为界限的前后消耗，作为非消耗量的切分点
            if (periodEndMonthList.contains(currentMonth)) {    // 是某一个周期的结束月份
                // 采用该周期的金额减去使用金额来计算清零金额
                BigDecimal contractAmountExcludingTax = currentFinanceIncome.getContractAmountExcludingTax();
                // 取出当月的周期结束时间
                int index = periodEndMonthList.indexOf(currentMonth);
                // 获取本周期在本月的使用量
                Date currentPeriodEndDate = judgePeriodEndDateList.get(index);
                // 本周期的本月使用量查询条件构建
                ucig.setContractId(contractId);
                ucig.setResourceCode(ResourceCodeConst.traffic.name());
                ucig.setStartDate(consumeStartDate);
                ucig.setEndDate(currentPeriodEndDate);
                UserConsumeInfoVO currentPeriodCurrentMonthConsumedVO = this.billingConsumeDataService.getUserConsumeInfo(ucig);
                // 本周期的本月消耗量（byte）
                Long currentPeriodCurrentMonthConsumed = currentPeriodCurrentMonthConsumedVO.getItemConsumed();
                // 计算本周期的历史消耗量
                // 计算本月之前的本周期消耗用量 ： 该合同的 历史消耗+历史非消耗-周期量*周期数 = 包含了本周期的消耗量
                Long periodHistoryConsumed = 0L;
                Long periodHistoryOtherConsumed = 0L;
                FinanceIncomeDetailEntity periodHistoryConsumedDetail = this.incomeDetailRepository.getNearestMonthNullItemIdDetail(contractId, currentMonth);
                if (Objects.nonNull(periodHistoryConsumedDetail)) {
                    periodHistoryConsumed = periodHistoryConsumedDetail.getCumulativeConsume();
                }
                FinanceIncomeDetailEntity periodHistoryOtherConsumeDetail = this.incomeDetailRepository.getNearestMonthSpecifyItemIdDetail(contractId, currentMonth,
                        FinanceIncomeDetailTagEnum.PERIOD_EXPIRED_INCOME_DETAIL_TAG.getCode());
                if (Objects.nonNull(periodHistoryOtherConsumeDetail)) {
                    periodHistoryOtherConsumed = periodHistoryOtherConsumeDetail.getCumulativeConsume();
                }
                // 本周期历史用量
                BigDecimal currentPeriodHistoryQuantity = BigDecimal.ZERO;
                // 如果还没有经历过完整周期，则用量不需要减去周期量*周期数
                currentPeriodHistoryQuantity = new BigDecimal(periodHistoryConsumed).add(new BigDecimal(periodHistoryOtherConsumed))
                        .subtract(new BigDecimal(index).multiply(currentPackageVodFlow));
                // 本周期总用量 = 本周期历史用量+本月本周期用量
                BigDecimal currentPeriodTotalQuantity = currentPeriodHistoryQuantity.add(new BigDecimal(currentPeriodCurrentMonthConsumed));
                // 本周期过期的金额 = 周期价格 - 本周期的历史使用金额 - 本月周期内的消耗金额
                BigDecimal currentPeriodHistoryQuantityGb = new BigDecimal(MoneyUtil.convertBytes2GB(currentPeriodTotalQuantity.longValue()));
                // 本周期历史消耗金额
                BigDecimal currentPeriodHistoryAmount = unitPriceWithTax.multiply(currentPeriodHistoryQuantityGb).divide(taxRate, 0, ROUND_HALF_EVEN);
                otherConsumed = currentPackageVodFlow.subtract(currentPeriodTotalQuantity);
                otherIncome = unitPriceWithTax.multiply(UnitConverterUtil.bytes2GB(otherConsumed)).divide(taxRate, 0, ROUND_HALF_EVEN);
                // otherIncome = contractAmountExcludingTax.divide(new BigDecimal(periodNum), 0, BigDecimal.ROUND_HALF_EVEN).subtract(currentPeriodHistoryAmount);
            }
            this.incomeService.setCurrentFinanceIncome(currentContract, currentFinanceIncome, usageIncome, otherIncome, usageConsumed, otherConsumed);
        }
    }

    /**
     * 确认一次性收入
     * 包括点播临时流量
     *
     * @param specifyDate 指定日期
     */
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void confirmOnetimeIncome(Date specifyDate) {
        // 查询出本月创建的，还没有确认收入的，指定一次性收入类型的业务类别
        String startDay = DateFormatUtil.formatDateNormal(DateUtil.getDateAfterDays(-1, DateUtil.getFirstDayOfMonth(specifyDate)));
        String endDay = DateFormatUtil.formatDateNormal(DateUtil.getDateEnd(DateUtil.getLastDayOfMonth(specifyDate)));
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        ContractSpecQueryDO searchDO = ContractSpecQueryDO.builder()
                .currentMonth(currentMonth)
                .contractWay(ContractOriginEnum.ORIGIN_MONTHLY_SETTLE.getCode())    // 月结
                .businessStartDate(startDay)
                .businessEndDate(endDay)
                .operationCodeList(ContractOperationCodeEnum.getOnceStatsOperationCodeList()) // 或者指定业务类别
                .build();
        List<FinanceContractEntity> onetimeConfirmContractList = this.contractRepository.getCurrentMonthOnetimeConfirmContract(searchDO);
        if (CollectionUtils.isEmpty(onetimeConfirmContractList)) {
            return;
        }
        for (FinanceContractEntity contractEntity : onetimeConfirmContractList) {
            log.info(String.format("====业务合同ID：{%s}确认一次性收入====", contractEntity.getContractId()));
            try {
                String contractId = contractEntity.getContractId();
                String contractWay = contractEntity.getContractWay();
                CurrentFinanceIncomeDTO currentFinanceInfoDTO = this.incomeService.getCurrentFinanceIncome(contractEntity, specifyDate);
                if (ContractOriginEnum.ORIGIN_MONTHLY_SETTLE.getCode().equals(contractWay)) {
                    String monthlyMonth = currentFinanceInfoDTO.getCurrentMonth();
                    boolean isDiff = false; // 账期是否不等于当月
                    // 月结需要使用账期确认收入
                    Long contractSpecificationId = contractEntity.getContractSpecificationId();
                    if (Objects.nonNull(contractSpecificationId)) {
                        FinanceContractSpecificationEntity spec = this.contractSpecificationRepository.getById(contractSpecificationId);
                        if (Objects.nonNull(spec) && StringUtils.isNotEmpty(spec.getAccountPeriod())) {
                            monthlyMonth = spec.getAccountPeriod();
                            isDiff = !monthlyMonth.equals(currentMonth);
                        }
                    }
                    currentFinanceInfoDTO.setCurrentMonth(monthlyMonth);
                    if (isDiff) {
                        // 删除创建合同时创建的收入
                        List<FinanceIncomeEntity> notItMonthIncomeList = this.incomeRepository.findByContractIdAndNotInMonth(contractId, monthlyMonth);
                        if (!CollectionUtils.isEmpty(notItMonthIncomeList)) {
                            for (FinanceIncomeEntity dfi : notItMonthIncomeList) {
                                if (Objects.nonNull(dfi.getOtherIncome()) && dfi.getOtherIncome() != 0) {
                                    continue;
                                }
                                this.incomeRepository.delete(dfi);
                            }
                        }
                        List<FinanceIncomeDetailEntity> notInMonthIncomeDetailList = this.incomeDetailRepository.findByContractIdAndNotInMonth(contractId, monthlyMonth);
                        if (!CollectionUtils.isEmpty(notInMonthIncomeDetailList)) {
                            for (FinanceIncomeDetailEntity dfid : notInMonthIncomeDetailList) {
                                // 可能是发生在未来的特殊计费项
                                if (Objects.nonNull(dfid.getBillingItemId()) && dfid.getBillingItemId() < 0) {
                                    continue;
                                }
                                this.incomeDetailRepository.delete(dfid);
                            }
                        }
                    }
                }
                // 将当前剩余全部结入
                this.incomeService.setCurrentFinanceIncome(contractEntity, currentFinanceInfoDTO, currentFinanceInfoDTO.getContractAvailable(), BigDecimal.ZERO,
                        new BigDecimal(currentFinanceInfoDTO.getContractQuantity()), BigDecimal.ZERO);
            } catch (Exception e) {
                log.error(String.format("confirmOnetimeIncome occurred.contractId %s exception %s", contractEntity.getContractId(), e.getMessage()), e);
            }
        }
    }

    /**
     * 按天确认收入
     *
     * @param contract     合同实体
     * @param currentMonth 当前月份
     * @param specifyDate  指定日期
     * @param startDate    套餐开始时间
     * @param endDate      套餐结束时间
     */
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void confirmDailyIncome(FinanceContractEntity contract, String currentMonth,
                                   Date specifyDate, Date startDate, Date endDate) {
        log.info(String.format("====业务合同ID：{%s}财务统计使用按天分摊收入====", contract.getContractId()));
        if (null != startDate && null != endDate
                && DateUtil.getDateStart(startDate).getTime() <= endDate.getTime()) {
            String contractId = contract.getContractId();
            // 当月的第一天和下一个月的第一天
            Date currentFirstDate = DateUtil.getFirstDayOfMonth(specifyDate);
            Date nextMonthFirstDate = DateUtil.getFirstDayOfXMonth(specifyDate, 1);
            // 获取当前收入信息
            CurrentFinanceIncomeDTO currentFinanceIncomeDTO = this.incomeService.getCurrentFinanceIncome(contract, specifyDate);
            // 合同天数（业务开通可用天数）
            Integer contractQuantity = this.getOperationDays(contract, specifyDate);
            if (Objects.isNull(contractQuantity) || contractQuantity == 0) {
                return;
            }
            // 计算单价（不含税）
            BigDecimal unitPriceWithTax = currentFinanceIncomeDTO.getContractUnivalentWithTax();
            BigDecimal currentTaxRate = currentFinanceIncomeDTO.getTaxRate();
            if (unitPriceWithTax.compareTo(BigDecimal.ZERO) <= 0) {
                this.incomeService.setCurrentFinanceIncome(contract, currentFinanceIncomeDTO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                return;
            }
            if (startDate.getTime() >= nextMonthFirstDate.getTime()) {
                // 业务开始时间大于等于下个月开始时间 && 合同创建时间小于下个月开始时间
                if (contract.getCreateTime().getTime() < nextMonthFirstDate.getTime()) {
                    this.incomeService.setCurrentFinanceIncome(contract, currentFinanceIncomeDTO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                }
                return;
            }
            // 上个月之后减去的可用金额（不含税）
            BigDecimal currentAvailable = currentFinanceIncomeDTO.getContractAvailable();
            if (endDate.getTime() < currentFirstDate.getTime()) {
                // 到期合同仍有剩余
                boolean hasLeft = currentAvailable.compareTo(BigDecimal.ZERO) > 0;
                // 到期合同仍有剩余且当月差额为0则钉钉报警
                if (hasLeft) {
                    this.incomeService.setCurrentFinanceIncome(contract, currentFinanceIncomeDTO, BigDecimal.ZERO, currentAvailable,
                            BigDecimal.ZERO, currentFinanceIncomeDTO.getDeferredQuantity());
                }
                return;
            }
            // 当月使用金额（未包括关联差额）
            BigDecimal usageIncomeAmount = null;
            BigDecimal usageIncomeQuantity = null;

            boolean isEnd = false;
            // 套餐结束月份
            String endMonth = DateFormatUtil.formatDateMonth(endDate);
            // 合同当前剩余可用天数
            BigDecimal contractCanUsesDays = currentFinanceIncomeDTO.getDeferredQuantity();
            // BigDecimal contractCanUsesDays = new BigDecimal(currentAvailable.divide(unitPriceWithTax, 2, BigDecimal.ROUND_HALF_UP).longValue());
            if (endMonth.equals(currentMonth)) {
                usageIncomeAmount = unitPriceWithTax.multiply(contractCanUsesDays).divide(currentTaxRate, 0, ROUND_HALF_EVEN);
                usageIncomeQuantity = contractCanUsesDays;
            } else {
                // 有效天数
                if (startDate.getTime() >= currentFirstDate.getTime()) {
                    usageIncomeQuantity = new BigDecimal(DateUtil.getDiffDays(DateUtil.getDateStart(startDate), nextMonthFirstDate));
                } else {
                    usageIncomeQuantity = new BigDecimal(DateUtil.getDiffDays(currentFirstDate, nextMonthFirstDate));
                }
                // 当月可使用天数跟套餐剩余可用天数相比
                if (usageIncomeQuantity.compareTo(contractCanUsesDays) > 0) {
                    isEnd = true;
                    usageIncomeQuantity = contractCanUsesDays;
                }
                log.info(String.format("合同 {%s} 当月可使用天数：{%s}, isEnd={%b}", contractId, usageIncomeQuantity.toPlainString(), isEnd));
                usageIncomeAmount = isEnd ? currentAvailable : unitPriceWithTax.multiply(usageIncomeQuantity).divide(currentTaxRate, 0, ROUND_HALF_EVEN);
            }
            // 保存收入数据
            this.incomeService.setCurrentFinanceIncome(contract, currentFinanceIncomeDTO, usageIncomeAmount, BigDecimal.ZERO, usageIncomeQuantity, BigDecimal.ZERO);
        }
    }

    /**
     * 并发套餐按天确认收入
     *
     * @param contract     合同对象
     * @param currentMonth 当前月份
     * @param specifyDate  指定日期
     * @param startDate    套餐开始时间
     * @param endDate      套餐结束时间
     */
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void confirmConcurrentDailyIncome(FinanceContractEntity contract, String currentMonth,
                                             Date specifyDate, Date startDate, Date endDate) {
        log.info(String.format("====业务合同ID：{%s}直播并发按天*并发数分摊收入====", contract.getContractId()));
        Date specifyDateStart = DateUtil.getDateStart(DateUtil.getFirstDayOfMonth(specifyDate));
        if (null != startDate && null != endDate
                && DateUtil.getDateStart(startDate).getTime() <= endDate.getTime()
                && DateUtil.getDateEnd(endDate).getTime() >= specifyDateStart.getTime()) {
            String contractId = contract.getContractId();
            // 当月的第一天和下一个月的第一天
            Date currentFirstDate = DateUtil.getFirstDayOfMonth(specifyDate);
            Date nextMonthFirstDate = DateUtil.getFirstDayOfXMonth(specifyDate, 1);
            // 获取当前收入信息
            CurrentFinanceIncomeDTO currentFinanceIncomeDTO = this.incomeService.getCurrentFinanceIncome(contract, specifyDate);
            // 合同天数（业务开通可用天数）
            Integer contractQuantity = this.getOperationDays(contract, specifyDate);
            if (Objects.isNull(contractQuantity) || contractQuantity == 0) {
                return;
            }
            // 计算单价（含税）
            BigDecimal unitPriceWithTax = currentFinanceIncomeDTO.getContractUnivalentWithTax();
            BigDecimal currentTaxRate = currentFinanceIncomeDTO.getTaxRate();
            if (unitPriceWithTax.compareTo(BigDecimal.ZERO) <= 0) {
                this.incomeService.setCurrentFinanceIncome(contract, currentFinanceIncomeDTO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                return;
            }
            if (startDate.getTime() >= nextMonthFirstDate.getTime()) {
                // 业务开始时间大于等于下个月开始时间 && 合同创建时间小于下个月开始时间
                if (contract.getCreateTime().getTime() < nextMonthFirstDate.getTime()) {
                    this.incomeService.setCurrentFinanceIncome(contract, currentFinanceIncomeDTO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                }
                return;
            }
            // 上个月之后减去的可用金额（不含税）
            BigDecimal currentAvailable = currentFinanceIncomeDTO.getContractAvailable();
            if (endDate.getTime() < currentFirstDate.getTime()) {
                // 到期合同仍有剩余
                boolean hasLeft = currentAvailable.compareTo(BigDecimal.ZERO) > 0;
                // 到期合同仍有剩余且当月差额为0则钉钉报警
                if (hasLeft) {
                    this.incomeService.setCurrentFinanceIncome(contract, currentFinanceIncomeDTO, BigDecimal.ZERO, currentAvailable,
                            BigDecimal.ZERO, currentFinanceIncomeDTO.getDeferredQuantity());
                }
                return;
            }
            // 当月使用金额（未包括关联差额）
            BigDecimal usageIncomeAmount = null;
            BigDecimal usageIncomeQuantity = null;
            CommonResult<BusinessPackageSpecification> contractSpecResult = this.contractService.getContractSpec(contract, specifyDate);
            BigDecimal rechargeQuantity = BigDecimal.ZERO;
            if (CommonResult.isOk(contractSpecResult)) {
                BusinessPackageSpecification spec = contractSpecResult.getData();
                if (Objects.nonNull(spec.getLiveConcurrentQuantity())) {
                    rechargeQuantity = new BigDecimal(spec.getLiveConcurrentQuantity());
                }
            }
            boolean isEnd = false;
            // 套餐结束月份
            String endMonth = DateFormatUtil.formatDateMonth(endDate);
            // 合同当前剩余可用天数（计算天数的时候采取截取，那么可能产生尾差，但不能让用量超出）
            BigDecimal contractCanUsesDays = currentFinanceIncomeDTO.getDeferredQuantity();
            if (endMonth.equals(currentMonth)) {
                // 到期
                usageIncomeAmount = unitPriceWithTax.multiply(contractCanUsesDays).divide(currentTaxRate, 0, ROUND_HALF_EVEN);
                usageIncomeQuantity = contractCanUsesDays;
            } else {
                // 有效天数
                if (startDate.getTime() >= currentFirstDate.getTime()) {
                    usageIncomeQuantity = new BigDecimal(DateUtil.getDiffDays(DateUtil.getDateStart(startDate), nextMonthFirstDate));
                } else {
                    usageIncomeQuantity = new BigDecimal(DateUtil.getDiffDays(currentFirstDate, nextMonthFirstDate));
                }
                // 使用量应该再乘以充值并发量
                usageIncomeQuantity = usageIncomeQuantity.multiply(rechargeQuantity);
                // 当月可使用天数跟套餐剩余可用天数相比
                if (usageIncomeQuantity.compareTo(contractCanUsesDays) > 0) {
                    isEnd = true;
                    usageIncomeQuantity = contractCanUsesDays;
                }
                log.info(String.format("合同 {%s} 当月可使用天数：{%s}, isEnd={%b}", contractId, usageIncomeQuantity.toPlainString(), isEnd));
                usageIncomeAmount = isEnd ? currentAvailable : unitPriceWithTax.multiply(usageIncomeQuantity).divide(currentTaxRate, 0, ROUND_HALF_EVEN);
            }
            // 保存收入数据
            this.incomeService.setCurrentFinanceIncome(contract, currentFinanceIncomeDTO, usageIncomeAmount, BigDecimal.ZERO, usageIncomeQuantity, BigDecimal.ZERO);
        }
    }

    /**
     * 确认金额账单收入
     * 1.不能取到金额合同对应的退费账单，因为退费的退收入已经在收入明细侧完成
     * 判断的条件就是：item_id为空
     *
     * @param contract     合同实体
     * @param specifyDate  指定日期
     * @param currentMonth 当前月份
     */
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void confirmDepositIncome(FinanceContractEntity contract, Date specifyDate, String currentMonth) {
        log.info(String.format("====业务合同ID：{%s}财务统计进行充值金额收入确认====", contract.getContractId()));
        // 待支付不在这里处理
        if (StatusValueEnum.YES.getValue().equals(contract.getWaitPayStatus())) {
            return;
        }

        String unionId = contract.getUnionId();
        String contractId = contract.getContractId();
        String operationCode = contract.getOperationCode();
        Map<Integer, BigDecimal> contractItemAmountMap = new HashMap<>();
        Map<Integer, BigDecimal> contractItemConsumedMap = new HashMap<>();
        Map<Integer, BillingItemAttributeDTO> billingItemAttributeMap = new HashMap<>();

        // 如果合同金额已经为0了，则直接退收入就行
        if (contract.getCurrentAmount() == 0) {
            Date queryRefundStartDate = DateUtil.getDateStart(DateUtil.getFirstDayOfMonth(DateFormatUtil.parseDateMonthNormal(currentMonth)));
            Date queryRefundEndDate = DateUtil.getDateEnd(DateUtil.getLastDayOfMonth(DateFormatUtil.parseDateMonthNormal(currentMonth)));
            Integer contractExistsCurrentMonthRefund = this.refundRepository.getContractExistsRefund(contractId, queryRefundStartDate, queryRefundEndDate);
            if (Objects.nonNull(contractExistsCurrentMonthRefund) && contractExistsCurrentMonthRefund == 1) {
                // 这个情况是待支付合同的最后一笔偿还为赠送金额的情况下，合同金额会减到0
                String lastMonth = DateFormatUtil.formatDateMonth(DateUtil.getAddMonthDate(DateFormatUtil.parseDateMonthNormal(currentMonth), -1));
                // 查询上个月的收入明细
                List<FinanceIncomeDetailEntity> lastMonthIncomeDetail = this.incomeDetailRepository.findContractIncomeDetail(contractId, lastMonth);
                if (!CollectionUtils.isEmpty(lastMonthIncomeDetail)) {
                    for (FinanceIncomeDetailEntity last : lastMonthIncomeDetail) {
                        if (last.getBillingItemId() <= 0) {
                            // 特殊计费项
                            continue;
                        }
                        contractItemAmountMap.put(last.getBillingItemId(), new BigDecimal(last.getCumulativeIncome()).negate());
                        contractItemConsumedMap.put(last.getBillingItemId(), new BigDecimal(last.getCumulativeConsume()).negate());
                        BillingItemAttributeDTO itemAttr = new BillingItemAttributeDTO();
                        itemAttr.setUnivalent(last.getUnivalent());
                        itemAttr.setConsumeUnit(last.getConsumeUnit());
                        itemAttr.setUnivalentUnit(last.getUnivalentUnit());
                        billingItemAttributeMap.putIfAbsent(last.getBillingItemId(), itemAttr);
                    }
                    if (contractItemAmountMap.isEmpty()) {
                        contract.setIncomeMonth(currentMonth);
                        contract.setIncomeStatus(StatusValueEnum.YES.getValue());
                        this.contractService.saveContract(contract);
                    } else {
                        this.incomeService.setCurrentAmountFinanceIncome(contract, currentMonth, contractItemAmountMap, contractItemConsumedMap, billingItemAttributeMap);
                    }
                } else {
                    // 没有历史账单的赠送金额完全偿还待支付，直接确认收入
                    contract.setIncomeMonth(currentMonth);
                    contract.setIncomeStatus(StatusValueEnum.YES.getValue());
                    this.contractService.saveContract(contract);
                }
                return;
            }
        }
        Date currentMonthSecondDay = DateUtil.getSecondDayOfMonth(specifyDate);
        Date currentMonthFirstDay = DateUtil.getFirstDayOfMonth(specifyDate);
        Date currentMonthLastDay = DateUtil.getLastDayOfMonth(specifyDate);
        Date nextMonthFirstDay = DateUtil.getFirstDayOfXMonth(specifyDate, 1);
        Date nextMonthSecondDay = DateUtil.getDateAfterDays(1, nextMonthFirstDay);
        Date billStartDate = DateUtil.getDateStart(currentMonthSecondDay);
        Date billEndDate = DateUtil.getDateEnd(nextMonthFirstDay);
        Date billReplyStartDate = DateUtil.getDateStart(currentMonthFirstDay);
        Date billReplyEndDate = DateUtil.getDateEnd(currentMonthLastDay);

        // 连麦分钟数的billId，需要特殊处理
        List<Integer> micItemIdList = Lists.newArrayList(4, 5, 45, 46, 47, 48, 49);
        String billStartDateChar = DateFormatUtil.formatDateNormal(billStartDate);
        Bills4FinanceQueryDO billQueryDO = new Bills4FinanceQueryDO();
        billQueryDO.setStatAtStartDate(DateFormatUtil.formatDateNormal(billStartDate));
        billQueryDO.setStatAtEndDate(DateFormatUtil.formatDateNormal(billEndDate));
        billQueryDO.setRepayOperationStartTime(DateFormatUtil.formatDateNormal(billReplyStartDate));
        billQueryDO.setRepayOperationEndTime(DateFormatUtil.formatDateNormal(billReplyEndDate));
        billQueryDO.setContractId(contractId);
        billQueryDO.setCustomerId(unionId);
        int cubPageNum = 0; // 从0开始
        int cubPageSize = 500;
        int cubTotalPage = 0;
        int loopCount = 0;
        int maxLoopCount = 1000;
        Page<Map<String, Object>> unpaidBillingPagingResult;
        // 计费项与费率Map
        Map<Integer, BigDecimal> itemRatioMap = new HashMap<>();
        do {
            unpaidBillingPagingResult =
                    this.customerBillingDailyRepository.queryContractBills4Finance(billQueryDO, PageRequest.of(cubPageNum++, cubPageSize));
            if (cubTotalPage == 0) {
                cubTotalPage = unpaidBillingPagingResult.getTotalPages();
            }
            List<Map<String, Object>> billMapList = unpaidBillingPagingResult.getContent();
            // 结构转换
            List<Bills4FinanceDTO> billList = new ArrayList<>();
            innerLoop:
            for (Map<String, Object> fe : billMapList) {
                Bills4FinanceDTO b4f = new Bills4FinanceDTO();
                if (Objects.nonNull(fe.get("customerId"))) {
                    b4f.setCustomerId(String.valueOf(fe.get("customerId")));
                }
                String curStatAt = null;
                if (Objects.nonNull(fe.get("statAt"))) {
                    curStatAt = String.valueOf(fe.get("statAt"));
                    b4f.setStatAt(curStatAt);
                }
                if (Objects.nonNull(fe.get("cost"))) {
                    b4f.setCost(String.valueOf(fe.get("cost")));
                }
                Integer curItemId = null;
                if (Objects.nonNull(fe.get("itemId"))) {
                    curItemId = Integer.parseInt(String.valueOf(fe.get("itemId")));
                    b4f.setItemId(curItemId);
                } else {
                    // itemId为空的跳过，不确认这个账单的收入
                    continue innerLoop;
                }
                if (Objects.nonNull(fe.get("ratio"))) {
                    itemRatioMap.putIfAbsent(curItemId, new BigDecimal(String.valueOf(fe.get("ratio"))));
                }
                // 连麦分钟数特殊处理（连麦分钟数是隔两天出账，查询的时间范围是隔一天，时间范围的第一天会包含了连麦的上个月最后一天的数据，需要扣除）
                if (StringUtils.isNotEmpty(curStatAt) && curStatAt.equals(billStartDateChar) && micItemIdList.contains(curItemId)) {
                    // 跳过
                    continue;
                }
                if (Objects.nonNull(fe.get("itemConsumed"))) {
                    b4f.setItemConsumed(new BigDecimal(String.valueOf(fe.get("itemConsumed"))));
                }
                BillingItemAttributeDTO itemAttr = new BillingItemAttributeDTO();
                if (Objects.nonNull(fe.get("univalence"))) {
                    itemAttr.setUnivalent(Long.parseLong(String.valueOf(fe.get("univalence"))));
                }
                if (Objects.nonNull(fe.get("consumedUnit"))) {
                    itemAttr.setConsumeUnit(String.valueOf(fe.get("consumedUnit")));
                }
                if (Objects.nonNull(fe.get("univalenceUnit"))) {
                    itemAttr.setUnivalentUnit(String.valueOf(fe.get("univalenceUnit")));
                }
                billingItemAttributeMap.putIfAbsent(curItemId, itemAttr);
                billList.add(b4f);
            }

            // 处理账单数据
            Map<Integer, List<Bills4FinanceDTO>> billingItemIdGroup = billList.stream()
                    .filter(f -> StringUtils.isNotEmpty(f.getCustomerId())).collect(Collectors.groupingBy(Bills4FinanceDTO::getItemId));
            for (Map.Entry<Integer, List<Bills4FinanceDTO>> billingItemEntry : billingItemIdGroup.entrySet()) {
                Integer billingItemId = billingItemEntry.getKey();
                // 添加用户、待支付合同和计费项的映射
                // 计费明细
                List<Bills4FinanceDTO> billingItemList = billingItemEntry.getValue();
                // 费用总额
                BigDecimal totalAmount = billingItemList.stream()
                        .map(Bills4FinanceDTO::getCost)
                        .map(BigDecimal::new)
                        .reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
                // 用量
                BigDecimal totalItemConsumed = billingItemList.stream()
                        .map(Bills4FinanceDTO::getItemConsumed)
                        .reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
                // 用量倍率
                //2023-09-23屏蔽掉这段逻辑，因为sql查出来的itemConsumed已经包含了倍率的了，不需要再处理了
//                BigDecimal itemRatio = itemRatioMap.get(billingItemId);
//                if (Objects.nonNull(itemRatio) && itemRatio.compareTo(new BigDecimal(100)) > 0) {
//                    // 倍率大于1的用量需要除
//                    totalItemConsumed = totalItemConsumed.divide(itemRatio.divide(new BigDecimal(100), 2,
//                    BigDecimal.ROUND_DOWN), 0, BigDecimal.ROUND_HALF_UP);
//                }
                // 当月应结算金额
                BigDecimal currentAmount = totalAmount.divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0,
                        ROUND_HALF_EVEN);
                // 累计使用金额
                contractItemAmountMap.merge(billingItemId, currentAmount, BigDecimal::add);
                contractItemConsumedMap.merge(billingItemId, totalItemConsumed, BigDecimal::add);
            }
        } while (cubPageNum < cubTotalPage && loopCount++ < maxLoopCount);

        // 组装一个连麦账单的Map<计费项:cost>
        Map<Integer, BigDecimal> micExtraCostMap = new HashMap<>();
        Map<Integer, BigDecimal> micExtraConsumedMap = new HashMap<>();
        // 连麦额外
        // 补统计下个月2号的连麦数据
        Bills4FinanceQueryDO micUnpaidBillQuery = new Bills4FinanceQueryDO();
        micUnpaidBillQuery.setStatAtStartDate(DateFormatUtil.formatDateNormal(nextMonthSecondDay));
        micUnpaidBillQuery.setCustomerId(unionId);
        micUnpaidBillQuery.setContractId(contractId);
        micUnpaidBillQuery.setItemIdList(micItemIdList);
        List<Map<String, Object>> micUnpaidBillList = this.customerBillingDailyRepository.queryMicBillsByContractId(micUnpaidBillQuery);
        if (!CollectionUtils.isEmpty(micUnpaidBillList)) {
            for (Map<String, Object> curMap : micUnpaidBillList) {
                String mecCustomerId = null;
                if (Objects.nonNull(curMap.get("customerId"))) {
                    mecCustomerId = String.valueOf(curMap.get("customerId"));
                }
                BigDecimal mecCost = null;
                if (Objects.nonNull(curMap.get("cost"))) {
                    mecCost = new BigDecimal(String.valueOf(curMap.get("cost")));
                }
                Integer mecItemId = null;
                if (Objects.nonNull(curMap.get("itemId"))) {
                    mecItemId = Integer.parseInt(String.valueOf(curMap.get("itemId")));
                }
                if (Objects.isNull(mecItemId)) {
                    continue;
                }
                BigDecimal mecConsumed = null;
                if (Objects.nonNull(curMap.get("itemConsumed"))) {
                    mecConsumed = new BigDecimal(String.valueOf(curMap.get("itemConsumed")));
                }
                if (Objects.nonNull(curMap.get("ratio"))) {
                    itemRatioMap.putIfAbsent(mecItemId, new BigDecimal(String.valueOf(curMap.get("ratio"))));
                }
                // 金额
                micExtraCostMap.merge(mecItemId, mecCost, BigDecimal::add);
                micExtraConsumedMap.merge(mecItemId, mecConsumed, BigDecimal::add);
                // 组装计费项属性
                BillingItemAttributeDTO itemAttr = new BillingItemAttributeDTO();
                if (Objects.nonNull(curMap.get("univalence"))) {
                    itemAttr.setUnivalent(Long.parseLong(String.valueOf(curMap.get("univalence"))));
                }
                if (Objects.nonNull(curMap.get("consumedUnit"))) {
                    itemAttr.setConsumeUnit(String.valueOf(curMap.get("consumedUnit")));
                }
                if (Objects.nonNull(curMap.get("univalenceUnit"))) {
                    itemAttr.setUnivalentUnit(String.valueOf(curMap.get("univalenceUnit")));
                }
                billingItemAttributeMap.putIfAbsent(mecItemId, itemAttr);
            }
        }
        for (Map.Entry<Integer, BigDecimal> entry : micExtraCostMap.entrySet()) {
            Integer billItemId = entry.getKey();
            BigDecimal micExtraCost = entry.getValue();
            BigDecimal micExtraAmount = micExtraCost.divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0,
                    ROUND_HALF_EVEN);
            BigDecimal micExtraConsumed = micExtraConsumedMap.getOrDefault(billItemId, BigDecimal.ZERO);
            // 用量倍率
            //2023-09-23屏蔽掉这段逻辑，因为sql查出来的itemConsumed已经包含了倍率的了，不需要再处理了
//            BigDecimal itemRatio = itemRatioMap.get(billItemId);
//            if (Objects.nonNull(itemRatio) && itemRatio.compareTo(new BigDecimal(100)) > 0) {
//                // 倍率大于1的用量需要除
//                micExtraConsumed = micExtraConsumed.divide(itemRatio.divide(new BigDecimal(100), 2, BigDecimal
//                .ROUND_DOWN), 0, BigDecimal.ROUND_HALF_UP);
//            }
            contractItemAmountMap.merge(billItemId, micExtraAmount, BigDecimal::add);
            contractItemConsumedMap.merge(billItemId, micExtraConsumed, BigDecimal::add);
        }
        this.incomeService.setCurrentAmountFinanceIncome(contract, currentMonth, contractItemAmountMap, contractItemConsumedMap, billingItemAttributeMap);
    }


    /**
     * 计算套餐有效时间间隔
     *
     * @param contract 合同实体
     * @return 天数
     */
    public Integer getOperationDays(FinanceContractEntity contract, Date specifyDate) {
        CommonResult<BusinessPackageSpecification> contractSpecResult = this.contractService.getContractSpec(contract, specifyDate);
        if (CommonResult.isNotOk(contractSpecResult)) {
            return 0;
        }
        BusinessPackageSpecification contractSpec = contractSpecResult.getData();
        if (Objects.nonNull(contractSpec)) {
            Date validPeriodStartDate = contractSpec.getValidPeriodStartDate();
            Date validPeriodEndDate = contractSpec.getValidPeriodEndDate();
            return DateUtil.getOverDaysFromTwoDate(validPeriodStartDate, validPeriodEndDate);
        }
        return 0;
    }

    /**
     *  生成本月0元收入，收入主表存在数据收入明细缺失的
     */
    public void generateMissingIncomeDetail(Date specifyDate) {
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        List<String> missingDetailContractIdList = this.incomeRepository.listMissingDetailContractId(currentMonth);
        if (Objects.isNull(missingDetailContractIdList)) {
            missingDetailContractIdList = new ArrayList<>();
        }
        Date current = new Date();
        List<FinanceIncomeDetailEntity> saveList = new ArrayList<>();
        log.info("execute generateMissingIncomeDetail...rows count: " + missingDetailContractIdList.size());
        if (!CollectionUtils.isEmpty(missingDetailContractIdList)) {
            // 执行生成特殊计费项数据
            for (String contractId : missingDetailContractIdList) {
                FinanceIncomeDetailEntity addFid = new FinanceIncomeDetailEntity();
                addFid.setContractId(contractId);
                addFid.setIncomeMonth(currentMonth);
                addFid.setBillingItemId(FinanceIncomeDetailTagEnum.MISSING_DEFAULT_INCOME_DETAIL_TAG.getCode());
                addFid.setTaxRate(FinanceTaxRateConst.NORMAL.getTaxRate().multiply(new BigDecimal("100")).longValue());
                addFid.setCurrentIncome(0L);
                addFid.setCumulativeIncome(0L);
                addFid.setCurrentConsume(0L);
                addFid.setCumulativeConsume(0L);
                addFid.setUnivalent(1L);
                addFid.setCreateTime(current);
                addFid.setUpdateTime(current);
                addFid.setStatus(StatusValueEnum.YES.getValue());
                saveList.add(addFid);
            }
        }
        if (!CollectionUtils.isEmpty(saveList)) {
            this.incomeDetailRepository.saveAll(saveList);
        }
    }
}
