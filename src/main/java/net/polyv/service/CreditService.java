package net.polyv.service;

import net.polyv.model.data.balance.BalanceInsufficientNotifyResult;
import net.polyv.model.data.credit.CreditInsufficientNotifyResult;
import net.polyv.model.data.deposit.BalanceChangeAmountDO;
import net.polyv.web.model.concurrence.AccountInsufficientRequest;

/**
 * 授信额度相关服务
 *
 * <AUTHOR>
 * @since 2020/5/14
 */
public interface CreditService {

    /**
     * 扣除、冻结顾客的授信额度
     *
     * @param amountDO 扣除参数
     * @return 实际扣除金额
     */
    long reduceCredit(BalanceChangeAmountDO amountDO);

    /**
     * 解冻顾客的授信额度
     *
     * @param amountDO 解冻参数
     * @return 实际解冻金额
     */
    long unfreezeCredit(BalanceChangeAmountDO amountDO);

    /**
     * 扣除冻结金额
     *
     * @param amountDO 扣除参数
     * @return 实际扣除金额
     */
    long reduceFreezeCredit(BalanceChangeAmountDO amountDO);


    /**
     * POP-1695 提供授信额度不足的客户列表
     * 当用户授信额度不足10,000 元、1,000 元、500 元、100 元、0 元时触发提醒
     * 过线提醒，仅触发一次
     * 剩余可用天数 ≤15，≤7，≤5，≤3，≤2，≤1
     * 剩余可用天数 = 剩余授信额度 / 日均用量
     *
     * @param ciq 请求实体
     * @return 授信不足用户通知信息
     */
    CreditInsufficientNotifyResult creditInsufficientRemind(AccountInsufficientRequest ciq);


    /**
     * 用户余额不足提醒
     *
     * @param biq 请求实体
     * @return 余额不足用户通知信息
     */
    BalanceInsufficientNotifyResult balanceInsufficientRemind(AccountInsufficientRequest biq);
}
