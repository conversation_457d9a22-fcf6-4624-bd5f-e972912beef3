package net.polyv.service;

import lombok.extern.slf4j.Slf4j;
import net.polyv.model.dto.GroupAccount;
import net.polyv.rest.client.group.GroupOneAccountClient;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.model.dmp.Paginator;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.rest.util.ResponseUtil;
import net.polyv.web.model.WrappedResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.alibaba.excel.util.CollectionUtils;
import com.google.common.collect.Lists;

/**
 * 集团账号1.0 服务
 * <AUTHOR>
 * @date 2022/8/16 14:21
 */
@Slf4j
@Component
public class GroupAccountOneService {
    @Resource
    private GroupOneAccountClient groupOneAccountClient;
    /**
     * 默认unionId
     */
    private static final String DEFAULT_UNION_ID = "000";
    
    public String findGroupIdByMainEmail(String email) {
        GroupAccount byMainEmail = this.findByMainEmail(email);
        if (Objects.isNull(byMainEmail)) {
            log.error("无法根据主账号邮箱查询集团账号id,email:{}", email);
        } else {
            return byMainEmail.getGroupId();
        }
        return DEFAULT_UNION_ID;
    }
    
    public GroupAccount findByMainEmail(String email) {
        WrappedResponse<Paginator<GroupAccount>> result = this.groupOneAccountClient.getUserListByEmail(email, null,
                null, 1, 1);
        if (result.isSuccess() && Objects.nonNull(result.getData())) {
            Optional<GroupAccount> first = result.getData().getContents().stream().findFirst();
            return first.orElse(null);
        }
        return null;
    }
    
    public GroupAccount findByGroupId(String groupId) {
        WrappedResponse<GroupAccount> result = this.groupOneAccountClient.getByGroupId(groupId);
        if (result.isSuccess() && Objects.nonNull(result.getData())) {
            return result.getData();
        }
        return null;
    }
    
    /**
     * 获取主账号下所有分账号unionId列表
     * @param groupId
     * @return
     */
    public List<String> getUnionIdsByGroupId(String groupId) {
        WrappedResponse<List<String>> groupSubEmailsResponse = this.groupOneAccountClient.getGroupSubEmails(groupId);
        List<String> result = Lists.newArrayList();
        if (!ResponseUtil.isErrorResponse(groupSubEmailsResponse)) {
            List<String> groupSubEmailList = groupSubEmailsResponse.getData();
            if (!CollectionUtils.isEmpty(groupSubEmailList)) {
                List<List<String>> partitionList = Lists.partition(groupSubEmailList, 20);
                for (List<String> partition : partitionList) {
                    List<UserMsgVO> unionUserList = UserClient.getUsersByEmailsRepaired(String.join(",", partition));
                    if (!CollectionUtils.isEmpty(unionUserList)) {
                        List<String> groupOneSubUnionIdList = unionUserList.stream()
                                .map(UserMsgVO::getUnionId)
                                .filter(StringUtils::isNotEmpty)
                                .distinct()
                                .collect(Collectors.toList());
                        result.addAll(groupOneSubUnionIdList);
                    }
                }
            }
        }
        return result;
    }
    
}
