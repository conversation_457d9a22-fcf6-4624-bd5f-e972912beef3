package net.polyv.service.export;

import net.polyv.constant.export.ExportLimitConst;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/6/9
 */
public interface ExportDataHandler<SearchInput, Data> {

    /**
     * 获取检索入参
     * @return 检索入参
     */
    SearchInput getSearchInput();

    /**
     * 获取泛型的Class类
     * @return 泛型class类
     */
    Class<Data> getDataClass();

    /**
     * 列表数据
     * @param input 检索入参
     * @param page 当前页
     * @param pageSize  每页数量
     * @return 数据
     */
    List<Data> listData(SearchInput input, int page, int pageSize);

    /**
     * 列表数据
     * @param input 检索入参
     * @param page 当前页
     * @return 数据
     */
    default List<Data> listData(SearchInput input, int page) {
        return this.listData(input, page, ExportLimitConst.DEFAULT_NUMBER_OF_EXPORTS_PER_TIME);
    }
}
