package net.polyv.service.export;

import java.util.List;
import java.util.Map;

import net.polyv.constant.export.ExportLimitConst;

/**
 * <AUTHOR>
 * @since 2020/6/9
 */
public interface ExportMoreDataHandler<SearchInput, Data> {

    /**
     * 获取检索入参
     * @return 检索入参
     */
    SearchInput getSearchInput();
    /**
     * 设置检索入参用于初始化参数
     * @return 检索入参
     */
    void setSearchInput(SearchInput input);
    
    /**
     * 列表数据
     * @param input 检索入参
     * @param page 当前页
     * @param pageSize  每页数量
     * @return 数据
     */
    Map<String, List<?>>listData(SearchInput input, int page, int pageSize);

    /**
     * 列表数据
     * @param input 检索入参
     * @param page 当前页
     * @return 数据
     */
    default Map<String, List<?>> listData(SearchInput input, int page) {
        return this.listData(input, page, ExportLimitConst.DEFAULT_NUMBER_OF_EXPORTS_PER_TIME);
    }
}
