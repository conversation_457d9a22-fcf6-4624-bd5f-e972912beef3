package net.polyv.service.export;

import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.BillDetailListRequest;
import net.polyv.modules.pcs.api.req.GroupUserDataExportRequest;
import net.polyv.modules.pcs.api.vo.BillDetailListVO;
import net.polyv.modules.pcs.api.vo.GroupUserDataExportVO;


/**
 * 导出服务
 * <AUTHOR>
 * @since 2022/09/02
 */
public interface ExportService {
    
    /**
     * 获取集团主账号下所有分账号的数据
     * @param request
     * @return
     */
    Pager<GroupUserDataExportVO> getGroupUserExportData(GroupUserDataExportRequest request);
    
    Pager<BillDetailListVO> billDetailList(BillDetailListRequest request);
}
