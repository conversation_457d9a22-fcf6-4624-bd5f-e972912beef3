package net.polyv.service;


import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.ResourceDepositListGetRequest;
import net.polyv.modules.pcs.api.vo.ResourceDepositListResultVO;
import java.util.List;
import java.util.Map;

/**
 * 资源充值详情记录
 *
 * <AUTHOR>
 * @since 2022/08/09
 */
public interface CustomizedAccountDepositDetailService {

    ResponseVO<Map<String,List<ResourceDepositListResultVO>>> depositList(ResourceDepositListGetRequest request);
}
