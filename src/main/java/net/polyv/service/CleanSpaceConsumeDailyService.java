package net.polyv.service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;


import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.cache.CacheConst;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.dao.primary.BackupItemConsumeDailyRepository;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.ItemConsumeDailyRepository;
import net.polyv.model.entity.primary.BackupItemConsumeDaily;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.ItemConsumeDaily;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;

/**
 * 清理空间消耗用量记录表 以及用量记录备份表数据service
 * <AUTHOR>
 */
@Slf4j
@Service
public class CleanSpaceConsumeDailyService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Resource(name = "redisTemplate")
    private ValueOperations<String, String> valueOperations;
    
    @Resource
    private ItemConsumeDailyRepository itemConsumeDailyRepository;
    
    @Resource
    private BackupItemConsumeDailyRepository backupItemConsumeDailyRepository;
    
    /**
     * 每天清理多少个月的数据
     */
    @Value("${pcs.space-consume-daily.clean-months:1}")
    private int cleanMonths;
    
    /**
     * 普通账号空间消耗保留什么时候开始的数据，则日期之前的都要清理
     */
    @Value("${pcs.space-consume-daily.keep-start-date:2023-01-01}")
    private String keepStartDate;
    
    /**
     * 所有计费项开始清理日期
     */
    @Value("${pcs.space-consume-daily.clean-start-date:2021-08-01}")
    private String spaceCleanStartDate;
    
    /**
     * 空间消耗保留多少天的数据
     */
    @Value("${pcs.space-consume-daily.keep-days:7}")
    private int keepDays;
    
    /**
     * 是否迁移用量记录表数据到备份表
     */
    @Value("${pcs.space-consume-daily.migrate:true}")
    private boolean migrateFlag;
    
    @Resource
    private DingWarnRobot dingWarnRobot;
    
    @Resource
    private BillingItemRepository billingItemRepository;
    
    /**
     * 清除备份表空间数据
     */
    public void cleanSpaceBackupConsumeDaily() {
        long startTime = System.currentTimeMillis();
        log.info("开始清理用量记录表数据");
        
        try {
            // 获取清理日期范围
            Date cleanStartDate = getCleanStartDate();
            Date cleanEndDate = DateUtil.getDateAfterDays(-1, DateUtil.getAddMonthDate(cleanStartDate, cleanMonths));
            log.info("清理用量记录表数据开始时间：{}，结束时间：{}", cleanStartDate, cleanEndDate);
            
            // 查找计费项
            BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.video_space.getCode());
            if (Objects.isNull(billingItem)) {
                log.error("计费项不存在，code：{}", ItemCodeConst.video_space.getCode());
                return;
            }
            
            // 循环删除空间数据
            while (cn.hutool.core.date.DateUtil.compare(cleanStartDate, DateFormatUtil.parseDateNormal(keepStartDate)) <
                    0 && cn.hutool.core.date.DateUtil.compare(cleanEndDate, cleanStartDate) >= 0) {
                backupItemConsumeDailyRepository.deleteByItemIdAndStatAt(billingItem.getId(), cleanStartDate);
                cleanStartDate = DateUtil.getDateAfterDays(1, cleanStartDate);
            }
            
            // 设置下一次清理开始时间
            valueOperations.set(CacheConst.CLEAN_ITEM_CONSUME_DAILY_START_DATE,
                    DateFormatUtil.formatDateNormal(cleanStartDate));
            log.info("设置下一次清理开始时间：{}到redis", cleanStartDate);
            log.info("清理用量记录表数据完成，花费：{}毫秒", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("cleanSpaceConsumeDaily异常", e);
            dingWarnRobot.sendWarnMsg("清理空间用量记录表数据异常", e.getMessage());
        }
    }
    
    private Date getCleanStartDate() {
        // 获取清理开始日期
        Object maxDate = valueOperations.get(CacheConst.CLEAN_ITEM_CONSUME_DAILY_START_DATE);
        // 如果缓存中没有开始清理日期，则使用默认开始日期
        Date cleanStartDate = Objects.nonNull(maxDate) ? DateFormatUtil.parseDateNormal(maxDate.toString()) :
                DateFormatUtil.parseDateNormal(spaceCleanStartDate);
        return cleanStartDate;
    }
    
    /**
     * 每周迁移一次
     * 迁移14天用量记录表数据到备份表
     * item_consume_daily -> backup_item_consume_daily
     */
    public void migrateItemConsumeDailyToBackup() {
        long startTime = System.currentTimeMillis();
        log.info("开始迁移用量记录表数据到备份表");
        
        try {
            // 获取迁移日期范围
            Date migrateEndDate = DateUtil.getDateAfterDays(-1);
            Date migrateStartDate = DateUtil.getDateAfterDays(-13, migrateEndDate);
            log.info("迁移用量记录表数据到备份表开始时间：{}，结束时间：{}", migrateStartDate, migrateEndDate);
            
            // 查找计费项
            BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.video_space.getCode());
            if (Objects.isNull(billingItem)) {
                log.error("计费项不存在，code：{}", ItemCodeConst.video_space.getCode());
                return;
            }
            
            // 循环迁移空间数据
            while (cn.hutool.core.date.DateUtil.compare(migrateEndDate, migrateStartDate) >= 0) {
                this.migrateItemConsumeDailyToBackup(billingItem.getId(), migrateStartDate);
                migrateStartDate = DateUtil.getDateAfterDays(1, migrateStartDate);
            }
            
            log.info("迁移用量记录表数据到备份表完成，花费：{}毫秒", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("migrateItemConsumeDailyToBackup异常", e);
            dingWarnRobot.sendWarnMsg("迁移用量记录表数据到备份表异常", e.getMessage());
        }
    }
    
    private void migrateItemConsumeDailyToBackup(Integer billingItemId, Date migrateStartDate) {
        if (!migrateFlag) {
            return;
        }
        // 查询普通账号空间消耗记录
        List<ItemConsumeDaily> list = itemConsumeDailyRepository.findNormalSpaceItemConsumeDaily(billingItemId,
                migrateStartDate);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
    
        List<BackupItemConsumeDaily> backupItemConsumeDailyList =
                backupItemConsumeDailyRepository.findNormalSpaceItemConsumeDaily(
                billingItemId, migrateStartDate);
        //已经迁移过的数据不再迁移
        if (!CollectionUtils.isEmpty(backupItemConsumeDailyList)) {
            return;
        }
        // 批量插入到备份表,并清除用量记录表数据
        List<List<ItemConsumeDaily>> partitionList = Lists.partition(list, 1000);
        for (List<ItemConsumeDaily> partition : partitionList) {
            List<BackupItemConsumeDaily> backupList = partition.stream().map(item -> {
                BackupItemConsumeDaily backupItem = new BackupItemConsumeDaily();
                BeanUtils.copyProperties(item, backupItem);
                return backupItem;
            }).collect(Collectors.toList());
            //插入到备份表
            backupItemConsumeDailyRepository.saveAll(backupList);
        }
    }
    
    
    /**
     * 每周清理一次
     * 清理item_consume_daily表7天前的数据
     */
    public void cleanBeforeDaysItemConsumeDaily() {
        long startTime = System.currentTimeMillis();
        log.info("开始清理item_consume_daily表7天前数据完成");
        
        try {
            // 获取清理日期范围
            Date cleanEndDate = DateUtil.getDateAfterDays(-keepDays - 1);
            Date cleanStartDate = DateUtil.getDateAfterDays(-keepDays + 1, cleanEndDate);
            log.info("清理item_consume_daily表7天前数据开始时间：{}，结束时间：{}", cleanStartDate, cleanEndDate);
            
            // 查找计费项
            BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.video_space.getCode());
            if (Objects.isNull(billingItem)) {
                log.error("计费项不存在，code：{}", ItemCodeConst.video_space.getCode());
                return;
            }
            
            // 清理空间数据
            while (cn.hutool.core.date.DateUtil.compare(cleanEndDate, cleanStartDate) >= 0) {
                itemConsumeDailyRepository.deleteNormalSpaceItemConsumeDaily(billingItem.getId(), cleanStartDate);
                cleanStartDate = DateUtil.getDateAfterDays(1, cleanStartDate);
            }
            
            log.info("清理item_consume_daily表7天前数据完成，花费：{}毫秒", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("cleanBeforeDaysItemConsumeDaily", e);
            dingWarnRobot.sendWarnMsg("清理item_consume_daily表7天前数据异常", e.getMessage());
        }
    }
}

