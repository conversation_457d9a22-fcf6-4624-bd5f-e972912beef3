package net.polyv.service.resource;

import net.polyv.model.data.resource.period.PeriodTimeDO;
import net.polyv.model.entity.primary.resource.CustomerPeriodResourceSetting;

import java.util.Date;

/**
 * 周期计算逻辑
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/5/27
 */
public interface PeriodCalcService {

    /**
     * 根据周期设置表 和 基准日期 计算当前周期
     * @param baseDate 基准日期，一般可以设置为 new Date()
     * @param setting 周期资源设置表
     * @return 当前周期的开始、结束时间
     */
    PeriodTimeDO calcPeriod(Date baseDate, CustomerPeriodResourceSetting setting);
}
