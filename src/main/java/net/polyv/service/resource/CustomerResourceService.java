package net.polyv.service.resource;

import net.polyv.model.data.deposit.DepositResourceDTO;
import net.polyv.model.data.resource.ContractResourceAvailableDO;
import net.polyv.model.entity.primary.AvailableCustomerResourceDaily;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.*;
import net.polyv.modules.pcs.api.req.testdonate.DonateAIResourceRequest;
import net.polyv.modules.pcs.api.req.testdonate.DonateBillingItemResourceRequest;
import net.polyv.modules.pcs.api.vo.AvailableRuleUseTimeVO;
import net.polyv.modules.pcs.api.vo.BatchGetResourceResultVO;
import net.polyv.modules.pcs.api.vo.BillingCustomerConfigVO;
import net.polyv.modules.pcs.api.vo.MaterialResourceInfoVO;
import net.polyv.web.model.account.item.CustomerItemInfoGetInputVO;
import net.polyv.web.model.account.item.ResAvailableStateDetailVO;
import net.polyv.web.model.account.item.ResAvailableStateVO;
import net.polyv.web.model.account.item.ResourceAvailableResultVO;
import net.polyv.web.model.account.item.ResourceAvailableSearchVO;
import net.polyv.web.model.resource.AddAlterationVO;
import net.polyv.web.model.resource.AvailableCustomerResourceDailyVO;
import net.polyv.web.model.resource.CleanResourceVO;
import net.polyv.web.model.resource.CustomerMaxConcurrenceInputVO;
import net.polyv.web.model.resource.CustomerMaxConcurrenceVO;
import net.polyv.web.model.resource.CustomerSumNumberVO;
import net.polyv.web.model.resource.RecalculateResourceAvailDailyVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 客户资源服务，包括资源变动，每日剩余计算
 * <AUTHOR>
 * @since 2020/8/10
 */
public interface CustomerResourceService {
    
    ResponseVO<Object> donateAIResource(DonateAIResourceRequest request);
    
    boolean isCustomizedGroupId(String groupId);

    boolean isCustomizedGroupIdByUnionId(String unionId);
    /**
     * 添加资源变动值
     * @param addAlterationVO 输入参数
     */
    void addAlteration(AddAlterationVO addAlterationVO);
    
    /**
     * 复算资源可用量快照
     * @param inputVO 输入参数
     */
    void recalculateAvailDaily(RecalculateResourceAvailDailyVO inputVO);

    /**
     * 通过当天出账的账单，将对应的客户资源可用量写回直播后台中
     * !此方法依赖于账单数据，仅限用于结算之后的写回!
     * @param resourceCode 资源编码
     */
    void writePcsResourceAvailable2LiveDbAfterClearing(String resourceCode);
    
    /**
     * 将客户指定资源 最近一次可用量快照+ 快照之后的变动 同步到直播后台中
     * 此方法不局限于结算之后写回
     * @param customerId 客户id
     * @param resourceCode 资源编码
     */
    void writeLatestPcsResourceAvailable2LiveDb(String customerId, String resourceCode);

    /**
     * 获取顾客的某项资源的可用情况
     * @param inputVO 查询入参
     * @return 顾客的某项资源的可用情况
     */
    List<ResAvailableStateVO> getCustomerCurrentAvailable(CustomerItemInfoGetInputVO inputVO);

    /**
     * 获取用户当前可用量详情
     */
    ResAvailableStateDetailVO getCustomerCurrentAvailableDetail(CustomerItemInfoGetInputVO inputVO);
    
    /**
     * 清零资源
     */
    void cleanItemResource(CleanResourceVO cleanResourceVO);

    /**
     * 检索顾客当前可用量
     * @param searchVO 检索条件
     * @return 可用量结果
     */
    ResourceAvailableResultVO searchCustomerCurrentAvailable(ResourceAvailableSearchVO searchVO);

    /**
     * 查特定计费项特定客户一段时间范围内的可用量快照
     * @param customerId:客户id
     * @param resourceCode:资源编码
     * @param startDate:开始日期
     * @param endDate:结束日期
     * @return
     */

    List<AvailableCustomerResourceDailyVO> findByCustomerIdAndResourceCodeAndStatAt(String customerId,
            String resourceCode, String startDate, String endDate);
    
    /**
     * 列表用户当前的所有合同资源列表
     * @param customerId 用户id
     * @param resourceCode 资源编码
     * @param baseDate 基准日期 查询某日的资源可用情况
     * @return 用户当前的所有合同资源列表
     */
    List<ContractResourceAvailableDO> listContractResourceAvailable(String customerId, String resourceCode,
            Date baseDate);
    
    /**
     * 点播无限版流量套餐扣款资源顺序列表
     * @param businessPackageSpecification
     * @param resourceCode
     * @param baseDate
     * @param reduceAmount :本次需扣除用量
     * @return
     */
    List<ContractResourceAvailableDO> listContractResourceAvailableWithVodUnlimitedPackage(
            BusinessPackageSpecification businessPackageSpecification, String resourceCode, Date baseDate,
            long reduceAmount);
    
    /**
     * 查询指定资源预计可用量不足的客户列表
     * @param resourceCode:资源编码
     * @param threshold:分钟数
     * @return 客户资源剩余情况和可用天数列表
     */
    List<AvailableCustomerResourceDaily> findAvailableInsufficient(String resourceCode, Long threshold);
    
    /**
     * 查询指定资源可使用天数不足的客户列表
     * @param resourceCode:资源编码
     * @param leftDays:剩余使用天数
     * @return 客户资源剩余情况和可用天数列表
     */
    List<AvailableCustomerResourceDailyVO> findDaysInsufficient(String resourceCode,Integer leftDays);

    CustomerMaxConcurrenceVO getCustomerMaxConcurrence(CustomerMaxConcurrenceInputVO inputVO);
    
    /**
     * 查询次数类资源剩余
     * @param unionId 客户id
     * @param resourceCode 资源编码
     * @return {@link CustomerSumNumberVO}
     * <AUTHOR>
     * @date 2022/8/3
     */
    CustomerSumNumberVO getSumAvailableNumber(String unionId, String resourceCode, Date baseDate);
    
    /**
     * 根据编码获取限定时长类的可用信息
     * @param unionId 客户id
     * @param itemCode 计费项编码
     * @return {@link AvailableRuleUseTimeVO}
     * <AUTHOR>
     * @date 2022/8/3
     */
    AvailableRuleUseTimeVO getRuleUseTimeAvailable(String unionId, String itemCode);
    
    /**
     * 获取素材库总流量（有效期内充值资源之和）
     * @param unionId
     * @return
     */
    long getMaterialTrafficTotal(String unionId);
    
    
    /**
     * 批量充值
     * @return
     */
    ResponseVO<Object> batchDeposit(DepositResourceRequest request);
    
    void rightNowOpen(DepositResourceDTO dto);

    /**
     * 同步华为云分账号历史资源数据到新表
     * 包括并发、分钟数、流量包、增容空间、连麦和导播台
     */
    void syncHuaweiSubAccountResource(String groupId);

    /**
     * 清理定制化客户过期资源
     */
    void cleanCustomizedExpireResource(String expireDate);

    ResponseVO<Map<String,BatchGetResourceResultVO>> getResourceAvailableList(BatchGetResourceRequest request);

    /**
     * 添加过期清零日志记录
     *
     * @param customerId        用户ID
     * @param resourcesCode     资源编码
     * @param testAlteration    测试资源
     * @param donateAlteration  赠送资源
     * @param regularAlteration 常规资源
     * @return 结果
     */
    boolean generateGroupUserAlterationRecord(String customerId, String resourcesCode, long testAlteration,
            long donateAlteration, long regularAlteration);
    
    
    /**
     * 是否有送过赠送|测试资源
     * @param unionId
     * @param itemCode
     * @return
     */
    boolean isDonateAiResource(String unionId, String itemCode);
    
    /**
     * 修改点播套餐 过期时间
     * @param request
     * @return
     */
    ResponseVO<Object> updateVodPackageExpireDate(UpdateVodPackageExpireDateRequest request);
    
    /**
     * 获取用户某一天空间变动
     * @param unionId
     * @param date
     * @return
     */
    long getDailySpaceAlterationByUnionId(String unionId, Date date);
    
    /**
     * 获取素材库资源信息
     * @param liveUserId
     * @return
     */
    ResponseVO<MaterialResourceInfoVO> getMaterialResourceInfo(String liveUserId);

    ResponseVO<MaterialResourceInfoVO> getMaterialTrafficItemState(String liveUserId);
    
    /**
     * 获取素材库已使用空间
     * @param liveUserId
     * @return
     */
    long getMaterialUsedSpace(String liveUserId);
    
    /**
     * 赠送计费项资源
     * @param request
     * @return
     */
    ResponseVO<Object> donateBillingItemResource(DonateBillingItemResourceRequest request);

    /**
     *
     * @param req
     * @return
     */
    Pager<BillingCustomerConfigVO> pageBillingCustomerConfig(BillingCustomerConfigPageReq req);

    ResponseVO<Object> donateBillingItemResourceForGroup(DonateBillingItemResourceRequest request);

    long getGroupMaterialUsedSpace(String groupId);

    List<String> geExpiredtCustomerIdListByItemCodeAndStatAt(ListExpiredCustomerRequest req);
}
