package net.polyv.service.resource;

import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.oldpackage.ResourceDepositInputVO;

/**
 * 资源储值逻辑
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/6/2
 */
public interface ResourceDepositService {

    /**
     * 储值资源
     * @param inputVO 储值资源入参
     * @return 储值资源操作结果
     */
    CommonOperateResultVO depositResource(ResourceDepositInputVO inputVO);
}
