package net.polyv.service.resource.available;

import java.util.Date;

import net.polyv.web.model.account.item.ResAvailableStateDetailVO;

/**
 * 资源可用量计算服务
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 *         created by 2021/5/24
 */
public interface ResourceAvailableCalcService {
    
    /**
     * 是否能计算该资源编码可用量
     * @param resourceCode 资源编码
     * @return true表示可以，false表示不适用
     */
    boolean canCalc(String resourceCode);

    /**
     * 优先级
     * @return 当前计算器优先级
     */
    default Integer getOrder() {
        return 5;
    }

    /**
     * 获取给定时间的可用量
     * @param customerId 顾客id
     * @param resourceCode 资源编码
     * @param baseDate 基准日期
     * @return 可用量
     */
    long getAvailable(String customerId, String resourceCode, Date baseDate);

    /**
     * 获取给定时间的可用量详情
     * @param customerId 顾客id
     * @param resourceCode 资源编码
     * @param baseDate 基准日期
     * @return 可用量
     */
    ResAvailableStateDetailVO getAvailableDetail(String customerId, String resourceCode, Date baseDate);
}
