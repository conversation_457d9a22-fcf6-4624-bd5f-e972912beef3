package net.polyv.service.resource.available;

import java.util.Date;

/**
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/5/24
 */
public interface DefaultAvailableService {

    /**
     * 获取顾客默认可用量
     * @param customerId
     * @param resourceCode
     * @param statAt
     * @return
     */
    Long getDefaultAvailable(String customerId, String resourceCode, Date statAt);

    /**
     * 获取顾客默认可用量
     * @param customerId
     * @param resourceCode
     * @return
     */
    Long getDefaultAvailable(String customerId, String resourceCode);
}
