package net.polyv.service.resource;

import java.util.List;
import java.util.Map;

import net.polyv.model.entity.primary.resource.ResourceChangeHistory;
import net.polyv.rest.model.vod.VodPackageVo;
import net.polyv.web.model.account.item.ResAvailableStateDetailVO;

/**
 * 资源变更历史服务
 * <AUTHOR>
 * @date 2023/2/24
 */
public interface ResourceChangeHistoryOperationService {
    
    
    /**
     * 执行激活数据——》即：将newJson内对象进行插入到对应表
     * @param ids
     */
    void doActiveData(String ids);
    
    /**
     * 计算变动差异
     */
    void calculationAlterationDiff(String ids);
    
    
    /**
     * 执行计算变动数据
     * @param recordList 获取数据存在的变动数据
     * @param classMap   类名map 存储变动记录的类型
     * @param vodSyncData  点播历史数据=没有为null，会加载。有就直接使用
     */
    void doCalculationAlterationDiff(List<ResourceChangeHistory> recordList, Map<String, Class<?>> classMap, List<VodPackageVo> vodSyncData);
    
    
    /**
     * 回填用户点播空间、流量
     * @param customerSpaceResourceMap 空间可用
     * @param customerTrafficResourceMap 流量可用
     * @param unionIds  用户id
     */
    void fillAvailableStateMap(Map<String, ResAvailableStateDetailVO> customerSpaceResourceMap,
            Map<String, ResAvailableStateDetailVO> customerTrafficResourceMap, List<String> unionIds);
}
