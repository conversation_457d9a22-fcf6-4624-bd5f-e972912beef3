package net.polyv.service.resource;

import net.polyv.web.model.account.item.ResAvailableStateDetailVO;

import java.util.Date;

/**
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/28
 */
public interface CustomerResourceCacheService {

    /**
     * 获取当前可用资源
     * @param customerId
     * @param resourceCode
     * @return
     */
    Long getResourceAvailable(String customerId, String resourceCode);

    /**
     * 获取当前可用资源
     * @param customerId
     * @param resourceCode
     * @param baseDate
     * @return
     */
    Long getResourceAvailable(String customerId, String resourceCode, Date baseDate);

    /**
     * 获取当前可用资源详情
     * @param customerId
     * @param resourceCode
     * @param baseDate
     * @return
     */
    ResAvailableStateDetailVO getResourceAvailableDetail(String customerId, String resourceCode, Date baseDate);
}
