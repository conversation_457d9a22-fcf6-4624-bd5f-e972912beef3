package net.polyv.service.resource;

import java.util.List;

import net.polyv.constant.resource.ResourceChangeWay;
import net.polyv.model.entity.primary.resource.ResourceChangeHistory;

/**
 * 资源变更历史服务
 * <AUTHOR>
 * @date 2023/2/24
 */
public interface ResourceChangeHistoryService {
    
    /**
     * 构建资源变更记录历史
     * @param resourceCode 资源编码  {@link net.polyv.constant.item.ResourceCodeConst}
     * @param changeWay 变更方式  {@link  ResourceChangeWay}
     * @param oldObj 变更前对象
     * @param newObj 变更后对象
     * @param diff 差异值  eg:新增方式=0
     * @param unionId 客户id
     * @param isException 是否为异常数据
     * @param sourceConst 资源类型 {@link net.polyv.constant.resource.ResourceSourceConst}
     * @return
     */
    ResourceChangeHistory buildResourceChangeHistory(String resourceCode, String changeWay, Object oldObj,
            Object newObj, Long diff, String unionId, boolean isException,Class<?> calzz,Integer sourceConst);
    
    
    /**
     * 批量插入变更历史
     * @param histories
     */
    void saveByBatch(List<ResourceChangeHistory> histories);
    
    
    /**
     * 获取套餐信息根据客户id列表
     * @param unionIds 客户id
     * @return 未插入套餐记录
     */
    List<ResourceChangeHistory> listUnInsertPackageChangeHistoryByUnionIds(List<String> unionIds);
    
    /**
     * 删除某批客户未处理过的差异数据
     */
    
    void deleteUnInsertPackageChangeHistoryByUnionIds(List<String> unionIds);
    
}
