package net.polyv.service;

import com.alibaba.excel.util.CollectionUtils;
import com.aliyuncs.utils.StringUtils;
import com.google.common.collect.Lists;
import net.polyv.constant.cache.CacheConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static net.polyv.constant.GlobalConfigConst.LOCAL_ENV;
import static net.polyv.constant.GlobalConfigConst.PROD_ENV;

/**
 * 系统环境服务
 *
 * <AUTHOR>
 * @since 2020/10/15
 */
@Service
public class SystemEnvService {

    @Value("${spring.profiles.active}")
    private String env;
    @Value("${polyv.pcs.test.allow-list}")
    private List<String> testAllowList;
    @Value("${polyv.pcs.test.open-all-allow-user:false}")
    private boolean openAllTestAllowUser;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Resource(name = "redisTemplate")
    private ValueOperations<String, String> valueOperations;


    /**
     * 判断当前是否为生产环境
     */
    public boolean isProdEnv() {
        return PROD_ENV.equals(env);
    }
    
    /**
     * 是否为本地环境
     * @return
     */
    public boolean isLocalEnv() {
        return LOCAL_ENV.equals(env);
    }

    public boolean isTestAllowUser(String customerId) {
        return openAllTestAllowUser || getTestAllowList().contains(customerId);
    }
    
    /**
     * 测试账号列表
     *
     * @return 返回union_id列表
     */
    public List<String> getTestAllowList() {
        List<String> resultList = new ArrayList<>();
        String redisVal = valueOperations.get(CacheConst.TEST_ALLOW_USER);
        if (StringUtils.isEmpty(redisVal)) {
            return resultList;
        }
        resultList = Lists.newArrayList(redisVal.split(","));
        return resultList;
    }

    public void addTestAllow(String unionIdList) {
        if (StringUtils.isEmpty(unionIdList)) {
            return;
        }
        String existsTestAllow = this.valueOperations.get(CacheConst.TEST_ALLOW_USER);
        String existsTestAllowArr = null;
        if (StringUtils.isEmpty(existsTestAllow)) {
            existsTestAllowArr = unionIdList;
        } else {
            existsTestAllowArr = String.format("%s,%s", existsTestAllow, unionIdList);
        }
        this.valueOperations.set(CacheConst.TEST_ALLOW_USER, existsTestAllowArr);
    }


    public void removeTestAllow(String unionId) {
        if (StringUtils.isEmpty(unionId)) {
            return;
        }
        String existsTestAllow = this.valueOperations.get(CacheConst.TEST_ALLOW_USER);
        if (StringUtils.isEmpty(existsTestAllow)) {
            return;
        }
        List<String> existsList = Lists.newArrayList(existsTestAllow.split(","));
        if (existsList.contains(unionId.trim())) {
            existsList.removeIf(f -> f.equals(unionId.trim()));
            if (CollectionUtils.isEmpty(existsList)) {
                this.valueOperations.set(CacheConst.TEST_ALLOW_USER, "");
                return;
            }
            StringBuilder afterRemove = existsList.stream().reduce(new StringBuilder(), (v1, v2) -> v1.append(v2).append(","), StringBuilder::append);
            this.valueOperations.set(CacheConst.TEST_ALLOW_USER, afterRemove.substring(0, afterRemove.length() - 1));
        }
        return;
    }
}
