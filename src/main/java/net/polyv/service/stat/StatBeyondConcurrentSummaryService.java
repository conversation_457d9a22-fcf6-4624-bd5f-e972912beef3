package net.polyv.service.stat;

import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.stat.StatBeyondConcurrentRequest;
import net.polyv.modules.pcs.api.vo.stat.StatBeyondConcurrentVO;

/**
 * 合同汇总服务
 * <AUTHOR>
 * @date 2022/8/29 16:33
 */
public interface StatBeyondConcurrentSummaryService {
    
    /**
    * 超并发分页列表
    * @param request 请求参数
    * @return {@link Pager< StatBeyondConcurrentVO>}
    * <AUTHOR>
    * @date 2022/9/6
    */
    Pager<StatBeyondConcurrentVO> beyondConcurrent(StatBeyondConcurrentRequest request);
    
}
