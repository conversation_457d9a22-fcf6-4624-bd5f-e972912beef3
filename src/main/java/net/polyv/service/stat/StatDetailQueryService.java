package net.polyv.service.stat;

import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.stat.StatIncomeDetailRequest;
import net.polyv.modules.pcs.api.req.stat.StatPaymentDetailRequest;
import net.polyv.modules.pcs.api.req.stat.StatRefundDetailRequest;
import net.polyv.modules.pcs.api.vo.stat.StatIncomeDetailVO;
import net.polyv.modules.pcs.api.vo.stat.StatPaymentDetailVO;
import net.polyv.modules.pcs.api.vo.stat.StatRefundDetailVO;

/**
 * 统计明细查询服务
 * <AUTHOR>
 * @date 2022/9/6 16:49
 */
public interface StatDetailQueryService {
    
    
    Pager<StatPaymentDetailVO> paymentPage(StatPaymentDetailRequest statPaymentDetailRequest);
    
    Pager<StatRefundDetailVO> refundList(StatRefundDetailRequest statRefundDetailRequest);
    
    Pager<StatIncomeDetailVO> incomeDetailList(StatIncomeDetailRequest statIncomeDetailRequest);
}
