package net.polyv.service.stat;

import java.util.List;

import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.stat.StatBusinessSummaryRequest;
import net.polyv.modules.pcs.api.req.stat.StatSummaryIncomeRequest;
import net.polyv.modules.pcs.api.vo.stat.StatBusinessSummaryVO;
import net.polyv.modules.pcs.api.vo.stat.StatSummaryIncomeVO;

/**
 * 合同汇总服务
 * <AUTHOR>
 * @date 2022/8/29 16:33
 */
public interface StatContractSummaryService {
    /**
     * 合同汇总
     * @param statBusinessSummaryRequest 请求参数
     * @return 业务汇总列表
     * <AUTHOR>
     * @date 2022/9/2
     */
    Pager<StatBusinessSummaryVO> contractSummary(StatBusinessSummaryRequest statBusinessSummaryRequest);
    
    /** 收入汇总
     * @param statSummaryIncomeRequest 请求参数
     * @return 收入汇总列表
     * <AUTHOR>
     * @date 2022/9/2
     */
    Pager<StatSummaryIncomeVO> incomeSummary(StatSummaryIncomeRequest statSummaryIncomeRequest);
    
    
    /**
     * 填充收入参数字段用于导出时回显
     */
    void fillIncomeParams(List<StatSummaryIncomeVO> vos);

    /**
     * 业务汇总
     * @param statBusinessSummaryRequest 请求参数
     * @return 业务汇总列表
     * <AUTHOR>
     * @date 2022/9/2
     */
    Pager<StatBusinessSummaryVO> businessSummary(StatBusinessSummaryRequest statBusinessSummaryRequest);
    
}
