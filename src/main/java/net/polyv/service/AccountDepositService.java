package net.polyv.service;

import java.util.List;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.exception.deposit.AccountDepositRunTimeException;
import net.polyv.model.data.deposit.DepositApproachingExpireMessageDTO;
import net.polyv.modules.pcs.api.vo.packages.RechargeMaturityDateVO;
import net.polyv.web.model.account.AccountDepositExpireRequest;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.account.DepositInputVO;
import net.polyv.web.model.account.GetBalanceStatusResultVO;
import net.polyv.web.model.account.GetRechargeStatsInputVO;
import net.polyv.web.model.account.GetRechargeStatsResultVO;
import net.polyv.web.model.account.GetResourcePointStatusResultVO;
import net.polyv.web.model.account.deposit.AccountDepositWithValidRequest;
import net.polyv.web.model.account.recharge.AccountRegistrationGiftRequest;
import net.polyv.web.model.account.recharge.GetRechargeRecordResultVO;
import net.polyv.web.model.account.recharge.RechargeRecordSearchResultVO;
import net.polyv.web.model.account.recharge.RechargeRecordSearchVO;
import net.polyv.web.model.common.OperaResultVO;
import net.polyv.web.model.common.PageDataVO;

/**
 * 顾客账户余额相关
 *
 * <AUTHOR>
 * @since 07/05/2020
 */
public interface AccountDepositService {

    /**
     * 储值
     *
     * @param inputVO 储值入参
     * @return 储值成功返回参数
     * @throws AccountDepositRunTimeException 账户储值失败
     */
    OperaResultVO deposit(DepositInputVO inputVO) throws AccountDepositRunTimeException;

    /**
     * 资源带你储值
     *
     * @param inputVO 储值入参
     * @return 储值成功返回参数
     * @throws AccountDepositRunTimeException 账户储值失败
     */
    OperaResultVO depositResourcePoint(DepositInputVO inputVO) throws AccountDepositRunTimeException;
    
    /**
     * 集团账号储值
     *
     * @param inputVO 储值入参
     * @return 储值成功返回参数
     * @throws AccountDepositRunTimeException 账户储值失败
     */
    OperaResultVO groupUserDeposit(DepositInputVO inputVO) throws AccountDepositRunTimeException;

    /**
     * 充值测试赠送金额
     * @param inputVO
     * @return
     * @throws AccountDepositRunTimeException
     */
    OperaResultVO depositTestAndDonateAmount(DepositInputVO inputVO) throws AccountDepositRunTimeException;

    /**
     * 充值测试赠送资源点
     * @param inputVO
     * @return
     * @throws AccountDepositRunTimeException
     */
    OperaResultVO depositTestAndDonateAmountResourcePoint(DepositInputVO inputVO) throws AccountDepositRunTimeException;
    
    
    /**
     * 获取顾客充值余额数据
     *
     * @param inputVO 检索入参
     * @return 顾客充值余额数据
     */
    GetRechargeStatsResultVO getRechargeStats(GetRechargeStatsInputVO inputVO);


    /**
     * 列表顾客的充值记录
     *
     * @param inputVO 检索入参
     * @return 充值记录分页结果
     */
    PageDataVO<GetRechargeRecordResultVO> listRechargeRecord(CustomerInfoGetInputVO inputVO);

    /**
     * 获取顾客的余额状况
     * 返回顾客的可用余额，冻结金额，授信额度可用余额，授信额度总额，待支付金额
     *
     * @param inputVO 检索入参
     * @return 顾客的余额状况
     */
    GetBalanceStatusResultVO getBalanceStatus(CustomerInfoGetInputVO inputVO);

    /**
     * 计算顾客的可用授信额度
     *
     * @param inputVO 顾客信息
     * @return 顾客的可用授信额度
     */
    long calcCustomerValidCredit(CustomerInfoGetInputVO inputVO);

    /**
     * 计算顾客的可用授信额度
     * 忽略tatolCredit为0的情况：返回-1
     *
     * @param inputVO 顾客信息
     * @return 顾客的可用授信额度
     */
    long calcCustomerValidCreditWithoutTotalZero(CustomerInfoGetInputVO inputVO);

    /**
     * 计算客户已经使用的授信额度
     *
     * @param inputVO 客户信息
     * @return 已使用的授信额度
     */
    long calcCustomerUsedCredit(CustomerInfoGetInputVO inputVO);

    /**
     * 检索储值记录
     *
     * @param searchVO 检索条件
     * @return 储值记录结果
     */
    RechargeRecordSearchResultVO searchRechargeRecord(RechargeRecordSearchVO searchVO);


    /**
     * 通过客户id查询充值到期时间展示数据
     *
     * @param customerId 客户id
     * @return 到期时间展示
     */
    RechargeMaturityDateVO accountRechargeExpireDateExhibit(String customerId);
    
    
    /**
     * 通过客户id查询资源点充值到期时间展示数据
     *
     * @param customerId 客户id
     * @return 到期时间展示
     */
    RechargeMaturityDateVO accountRechargeResourcePointExpireDateExhibit(String customerId);


    /**
     * 执行过期充值清零
     *
     * @param inputSpecifyDate 指定日期：清除指定这一天的过期充值账单
     * @throws ClearingSystemException 异常
     */
    void executeExpiredCleanup(String inputSpecifyDate) throws ClearingSystemException;

    /**
     * 执行过期充值清零
     *
     * @param inputSpecifyDate 指定日期：清除指定这一天的过期充值账单
     * @throws ClearingSystemException 异常
     */
    void executeExpiredCleanupResourcePoint(String inputSpecifyDate) throws ClearingSystemException;

    /**
     * 金额有效期临近过期提醒
     *
     * @param inputVo 查询参数
     * @return 提醒实体
     */
    List<DepositApproachingExpireMessageDTO> depositApproachingExpireNotify(AccountDepositExpireRequest inputVo);


    /**
     * 注册赠送金额接口
     *
     * @param adwvrList 请求参数集合
     * @return 充值结果
     */
    CommonResult depositRegistrationGift(List<AccountDepositWithValidRequest> adwvrList) throws ClearingSystemException;

    /**
     * 注册赠送资源点接口
     *
     * @param adwvrList 请求参数集合
     * @return 充值结果
     */
    CommonResult depositRegistrationGiftResourcePoint(List<AccountDepositWithValidRequest> adwvrList) throws ClearingSystemException;


    /**
     * 用户赠送金额并且偿还待支付
     *
     * @param adwvr 请求参数
     * @return 结果
     * @throws ClearingSystemException 异常
     */
    CommonResult donateWithRepay(AccountDepositWithValidRequest adwvr) throws ClearingSystemException;
    
    
    /**
     * 获取顾客的资源点状况
     * 返回顾客的可用资源点，冻结资源点，授信额度可用资源点，授信额度资源点，待支付资源点
     *
     * @param inputVO 检索入参
     * @return 顾客的资源点状况
     */
    GetResourcePointStatusResultVO getResourcePointStatus(CustomerInfoGetInputVO inputVO);


    /**
     * 注册赠送接口
     *
     * @param params 请求参数集合
     * @return 结果
     */
    CommonResult<String> registrationGift(AccountRegistrationGiftRequest params) throws ClearingSystemException;

}
