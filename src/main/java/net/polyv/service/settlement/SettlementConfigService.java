package net.polyv.service.settlement;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import net.polyv.dao.primary.CustomerBillingItemSettingRepository;
import net.polyv.model.entity.primary.CustomerBillingItemSetting;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import net.polyv.common.CommonResult;
import net.polyv.dao.primary.CustomerFunctionRepository;
import net.polyv.model.entity.primary.CustomerFunction;
import net.polyv.model.entity.primary.CustomerFunction.CustomerFunctionKey;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.modules.pcs.api.req.SettlementConfigUpdateRequest;
import net.polyv.modules.pcs.api.stereotype.CustomerFunctionEnum;
import net.polyv.modules.pcs.api.vo.SettlementConfigVO;
import net.polyv.service.vod.VodAmountBillListService;
import org.springframework.util.CollectionUtils;

/**
 * 结算配置服务
 * @author: luoyuanbin
 * @date: 2024-07-04 14:01:22
 */
@Component
@Slf4j
public class SettlementConfigService {

    @Resource
    private VodAmountBillListService vodAmountBillListService;

    @Resource
    private CustomerFunctionRepository customerFunctionRepository;

    @Resource
    private CustomerBillingItemSettingRepository customerBillingItemSettingRepository;

    /**
     * 查询结算配置
     * @param customerId
     * @return
     */
    public SettlementConfigVO querySettlementConfig(String customerId) {

        SettlementConfigVO result = new SettlementConfigVO();

        boolean useAmountBill = this.vodAmountBillListService.isUseAmountBill(customerId);
        result.setVodUserAmount(useAmountBill ? SwitchEnum.Y.getCode() : SwitchEnum.N.getCode());

        CustomerFunctionKey key = new CustomerFunctionKey();
        key.setCustomerId(customerId);
        key.setFuncKey(CustomerFunctionEnum.RESOURCE_POINT_SETTLEMENT.getCode());
        
        Optional<CustomerFunction> resourcePointSettlementFunction = customerFunctionRepository.findById(key);
        result.setResourcePointSettlement(SwitchEnum.N.getCode());
        if (resourcePointSettlementFunction.isPresent()){
            result.setResourcePointSettlement(resourcePointSettlementFunction.get().getFuncValue());
        }
        
        return result;
    }

    /**
     * 更新结算配置
     * @param params
     * @return
     */
    public CommonResult<String> saveOrUpdate(SettlementConfigUpdateRequest params) {
        String customerId = params.getCustomerId();

        CommonResult<String> result = null;
        if (StringUtils.isNotEmpty(params.getVodUserAmount())){
            result = this.vodAmountBillListService.saveOrUpdate(customerId, SwitchEnum.isY(params.getVodUserAmount()));
            if (CommonResult.isNotOk(result)){
                return result;
            }
        }

        return saveOrUpdateCustomerFunction(params);
    }

    /**
     * 保存用户的功能
     * @param params
     * @return
     */
    private CommonResult<String> saveOrUpdateCustomerFunction(SettlementConfigUpdateRequest params){

        if (StringUtils.isEmpty(params.getResourcePointSettlement())){
            return CommonResult.ok();
        }

        String customerId = params.getCustomerId();

        return this.saveOrUpdateCustomerFunction(customerId, CustomerFunctionEnum.RESOURCE_POINT_SETTLEMENT, params.getResourcePointSettlement());
    }

    /**
     * 保存或修改客户功能
     * @param customerId
     * @param value
     * @return
     */
    public CommonResult<String> saveOrUpdateCustomerFunction(String customerId, CustomerFunctionEnum functionEnum, String value){

        CustomerFunctionKey key = new CustomerFunctionKey();
        key.setCustomerId(customerId);
        key.setFuncKey(functionEnum.getCode());
        Optional<CustomerFunction> resourcePointSettlementFunctionOptional = customerFunctionRepository.findById(key);
        
        CustomerFunction resourcePointSettlementFunction = resourcePointSettlementFunctionOptional.orElse(null);
        if (Objects.isNull(resourcePointSettlementFunction)){
            resourcePointSettlementFunction = new CustomerFunction();
            resourcePointSettlementFunction.setCustomerId(customerId);
            resourcePointSettlementFunction.setFuncKey(functionEnum.getCode());
            resourcePointSettlementFunction.setCreateTime(new Date());
        }
        
        resourcePointSettlementFunction.setFuncValue(value);
        resourcePointSettlementFunction.setUpdateTime(new Date());

        customerFunctionRepository.save(resourcePointSettlementFunction);
        //修改自定义计费项表生效状态
        List<CustomerBillingItemSetting> list = customerBillingItemSettingRepository.queryByCustomerIdAndIsResourcePoint(customerId,1);
        log.info("-------------Customer {} list {} value {}", customerId, list.size(),value);
        if(CollectionUtils.isEmpty(list)){
            return CommonResult.ok();
        }
        for(CustomerBillingItemSetting setting : list){
            if("Y".equals(value)){
                CustomerBillingItemSetting pointItemSetting = customerBillingItemSettingRepository.findSettingByCustomerIdAndItemIdAndIsResourcePoint(customerId,
                        setting.getItemId(),1);
                log.info("-===========Customer {}  pointItemSetting {}", customerId, pointItemSetting);
                pointItemSetting.setIsActive(1);
                pointItemSetting.setUpdateTime(new Date());
                customerBillingItemSettingRepository.save(pointItemSetting);

                CustomerBillingItemSetting amountItemSetting = customerBillingItemSettingRepository.findSettingByCustomerIdAndItemIdAndIsResourcePoint(customerId,
                        setting.getItemId(),0);
                if(null != amountItemSetting){
                    amountItemSetting.setIsActive(0);
                    amountItemSetting.setUpdateTime(new Date());
                    customerBillingItemSettingRepository.save(amountItemSetting);
                }
            }
            if("N".equals(value)){
                CustomerBillingItemSetting pointItemSetting = customerBillingItemSettingRepository.findSettingByCustomerIdAndItemIdAndIsResourcePoint(customerId,
                        setting.getItemId(),1);
                pointItemSetting.setIsActive(0);
                pointItemSetting.setUpdateTime(new Date());
                customerBillingItemSettingRepository.save(pointItemSetting);

                CustomerBillingItemSetting amountItemSetting = customerBillingItemSettingRepository.findSettingByCustomerIdAndItemIdAndIsResourcePoint(customerId,
                        setting.getItemId(),0);
                if(null != amountItemSetting){
                    amountItemSetting.setIsActive(1);
                    amountItemSetting.setUpdateTime(new Date());
                    customerBillingItemSettingRepository.save(amountItemSetting);
                }
            }
        }
        return CommonResult.ok();
    }
    
}
