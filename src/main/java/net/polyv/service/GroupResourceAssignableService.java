package net.polyv.service;

import net.polyv.modules.pcs.api.req.GroupResourceRecalculateReq;

import java.util.List;

/**
 * 集团主账号可分配资源service
 * <AUTHOR>
 */
public interface GroupResourceAssignableService {
    /**
     * 重算主账号剩余可分配资源
     * @param groupId：集团主账号id，可不传
     * @param businessType：（可不传）业务类型 1:并发 2：分钟数  3：增容空间 4：流量包  5：连麦分钟数 6：导播台分钟数 7：金额
     */
    void recalculateGroupResource(String groupId, Integer businessType);

    /**
     * 重算主账号剩余可分配资源
     */
    void recalculateGroupResource(GroupResourceRecalculateReq resourceRecalculateReq);
    
    /**
     * 获取主账号某个计费项的剩余可分配
     * @param groupId
     * @param businessType
     * @return
     */
    long getGroupResourceByBusinessType(String groupId, Integer businessType);
    
    /**
     * 获取所有分账号某个计费项的资源之和
     * @param customerIds
     * @param businessType
     * @return
     */
    long sumGroupUsersByBusinessType(List<String> customerIds, Integer businessType);
    
    /**
     * 添加剩余可分配
     * @param groupId
     * @param businessType
     * @param amount
     */
    void addResourceAssignable(String groupId, Integer businessType, long amount);
}
