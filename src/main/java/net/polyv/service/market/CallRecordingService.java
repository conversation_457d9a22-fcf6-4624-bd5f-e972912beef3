package net.polyv.service.market;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.market.CallRecordingSeller;
import net.polyv.web.model.market.CallRecordsEntity;
import net.polyv.web.model.market.CallRecordsOssTokenRequest;
import net.polyv.web.model.market.CallRecordsSellerEntity;
import net.polyv.web.model.market.CallRecordsUploadCallbackRequest;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @description: 通话录音上传服务接口
 * @author: Neo
 * @date: 2022-04-22
 */
public interface CallRecordingService {

    /*
        1.销售登录；

        2.APP端获取通话记录，调用后端接口校验通话记录的号码是否归属客户；

            2.1. 确认接口参数数据量

            2.2 确认纷享销客接口查询是否归属客户请求量（后端）

        3.后端返回归属客户的电话号码列表，APP端请求后端接口，参数为客户电话的通话记录内容，
            参数字段：
            startTime 通话开始时间
            otherNumber 拨打电话
            selfNumber 自己电话
            callDuration 通话时长
            callType 通话类型(呼出、呼入、未接)
            fileId 文件ID
        4.后端保存通话记录内容（去重），并返回已经上传过的文件ID

            4.1理论上保存过的通话记录，如果有录音文件应该也是上传过oss了，所以无需后端再返回哪些通话记录是重复的。

        5.APP端根据后端返回的已经上传录音文件ID，过滤后上传录音文件到oss

        6.oss回调，补充录音文件url信息（后端）

        纷享销客跟进记录：
          记录内容：【电话录音】

          通话时间：2022/2/22 2:22:22

          通话时长：2分钟30秒

          录音链接：https://www.luyin.com/asfw

          关联业务模块：销售线索增加对应联系人
          跟进类型：电话联系
          跟进人：根据手机录音对应的上传账号（姓名+手机号）所对应的纷享销客账号匹配
     */

    // 录音用户登录校验
    CommonResult<CallRecordingSeller> checkLogin(String mobile, String password);

    // 添加销售账号
    CommonResult addSeller(List<CallRecordsSellerEntity> crseList);

    // 匹配号码
    CommonResult<Set<String>> matchRecords(List<String> numberList);

    // 保存通话记录
    CommonResult addCallRecords(List<CallRecordsEntity> creList);


    // 录音上传回调更新
    CommonResult updateRecordsOssUrl(CallRecordsUploadCallbackRequest callback);

    // 获取授权访问文件的url
    CommonResult<String> generateRecordAccessRightsUrl(String id, String selfNumber, String dialNumber, String callTime);

    // 访问企客宝迁移过来的录音文件授权url
    CommonResult<String> generateQiKeBaoRecordAccessRightsUrl(String filePath);

    /**
     * 上传通话记录至纷享销客的销售记录
     * <p>
     * fxk_upload_id: 如果含有ID，且fxk_upload_status=0，为更新
     * fxk_upload_status：如果没有fxk_upload_id且为0，则新增
     */
    void upload2Crm();


    // 补充号码匹配的类型：含有线索或联系人
    void complementMatchIds();

    /**
     * 获取oss上传录音文件授权并设置回调
     */
    List<Map<String, Object>> generateCallRecordingOssToken(List<CallRecordsOssTokenRequest> crotrList) throws ClearingSystemException;

}
