package net.polyv.service.test;

import net.polyv.constant.SysTypeConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.resource.CustomerPeriodResourceSettingRepository;
import net.polyv.model.data.resource.period.PeriodTimeDO;
import net.polyv.model.data.salesopportunities.ExtObjectDO;
import net.polyv.model.data.salesopportunities.UserFunctionDO;
import net.polyv.model.entity.primary.CustomerBillingDaily;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.resource.CustomerPeriodResourceSetting;
import net.polyv.service.resource.PeriodCalcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/20
 */
@Service
public class TestService {

    private final SalesOpportunitiesRepository salesOpportunitiesRepository;
    private final CustomerBillingDailyRepository customerBillingDailyRepository;
    private final PeriodCalcService periodCalcService;
    private final CustomerPeriodResourceSettingRepository customerPeriodResourceSettingRepository;

    @Autowired
    public TestService(SalesOpportunitiesRepository salesOpportunitiesRepository,
                       CustomerBillingDailyRepository customerBillingDailyRepository,
                       PeriodCalcService periodCalcService, CustomerPeriodResourceSettingRepository customerPeriodResourceSettingRepository) {
        this.salesOpportunitiesRepository = salesOpportunitiesRepository;
        this.customerBillingDailyRepository = customerBillingDailyRepository;
        this.periodCalcService = periodCalcService;
        this.customerPeriodResourceSettingRepository = customerPeriodResourceSettingRepository;
    }

    public void updateExtField() {

        Pageable pageable = PageRequest.of(0, 500);
        Page<SalesOpportunities> salesOpportunitiesPage = salesOpportunitiesRepository.findAll(pageable);


        while(!salesOpportunitiesPage.isEmpty()) {

            List<SalesOpportunities> updateRecordList = new ArrayList<>();
            salesOpportunitiesPage.forEach(item -> {

                ExtObjectDO extObjectDO = item.getExtObjectDO();
                if (extObjectDO == null) {
                    return ;
                }

                if (extObjectDO.getLinkMicLimit() != null) {

                    if (extObjectDO.getFunctionList() != null &&
                            extObjectDO.getFunctionList().stream().anyMatch(functionItem -> "linkMicLimit".equals(item.getCode()))) {
                        return;
                    }

                    UserFunctionDO userFunctionDO = new UserFunctionDO();
                    userFunctionDO.setSys(SysTypeConst.Live);
                    userFunctionDO.setCode("linkMicLimit");
                    userFunctionDO.setCodeName("连麦人数");
                    userFunctionDO.setValue(extObjectDO.getLinkMicLimit());

                    if (CollectionUtils.isEmpty(extObjectDO.getFunctionList())) {
                        extObjectDO.setFunctionList(new ArrayList<>());
                    }
                    extObjectDO.getFunctionList().add(userFunctionDO);
                    item.setExtObjectDO(extObjectDO);
                    updateRecordList.add(item);
                }
            });

            if (!updateRecordList.isEmpty()) {
                salesOpportunitiesRepository.saveAll(updateRecordList);
            }

            pageable = PageRequest.of(pageable.getPageNumber() + 1, pageable.getPageSize());
            salesOpportunitiesPage = salesOpportunitiesRepository.findAll(pageable);
        }
    }

    /**
     * 处理点播账单数据
     * @param statAt 统计日期
     * @param itemId 项目id，只允许 41,42
     */
    public void processBillingDaily(Date statAt, Integer itemId) {

        if( !itemId.equals(41) && !itemId.equals(42)) {
            return;
        }
        String resourceCode = itemId.equals(41) ? ResourceCodeConst.traffic.name() : ResourceCodeConst.space.name();
        Date current = new Date();

        Pageable pageable = PageRequest.of(0, 200);
        List<CustomerBillingDaily> dailyList = customerBillingDailyRepository.findByStatAtAndItemId(statAt, itemId, pageable);
        while(!CollectionUtils.isEmpty(dailyList)) {

            for (CustomerBillingDaily daily : dailyList) {

                CustomerPeriodResourceSetting setting = customerPeriodResourceSettingRepository.
                        getCurrentEffectivePeriodSetting(daily.getCustomerId(), resourceCode);
                if (setting == null) {
                    continue;
                }

                PeriodTimeDO periodTimeDO = periodCalcService.calcPeriod(current, setting);
                daily.setConsumeStartDate(periodTimeDO.getPeriodStart());
            }
            customerBillingDailyRepository.saveAll(dailyList);

            pageable = pageable.next();
            dailyList = customerBillingDailyRepository.findByStatAtAndItemId(statAt, itemId, pageable);
        }
    }
}
