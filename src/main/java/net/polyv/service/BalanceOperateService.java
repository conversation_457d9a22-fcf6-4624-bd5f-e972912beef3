package net.polyv.service;

import net.polyv.model.data.deposit.BalanceChangeAmountDO;
import net.polyv.model.data.deposit.BalanceChangeResultDO;
import net.polyv.model.data.deposit.ResourcePointChangeResultDO;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 余额操作服务
 * <AUTHOR>
 * @since 2020/5/13
 */
public interface BalanceOperateService {

    /**
     * 减少额度
     * 注意该接口会按照余额、授信额度依次冻结
     * @param balanceChangeAmountDO 入参
     * @return 结果明细
     */
    BalanceChangeResultDO reduce(BalanceChangeAmountDO balanceChangeAmountDO);

    ResourcePointChangeResultDO reduceResourcePoint(BalanceChangeAmountDO balanceChangeAmountDO);

    /**
     * 增加额度
     * @param balanceChangeAmountDO 入参
     * @return 结果明细
     */
    BalanceChangeResultDO unFreeze(BalanceChangeAmountDO balanceChangeAmountDO);

    /**
     * 扣除冻结额度
     * @param balanceChangeAmountDO 入参
     * @return 结果明细
     */
    BalanceChangeResultDO reduceFreeze(BalanceChangeAmountDO balanceChangeAmountDO);


    /**
     * 检查用户余额是否足够扣除
     * @param balanceChangeAmountDO 扣除入参
     * @return true表示足够，false表示不足
     */
    boolean checkCurrentBalanceEnoughReduce(BalanceChangeAmountDO balanceChangeAmountDO);

    boolean checkCurrentBalanceEnoughReduceResourcePoint(BalanceChangeAmountDO balanceChangeAmountDO);
}
