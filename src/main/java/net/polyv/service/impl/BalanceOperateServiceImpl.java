package net.polyv.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.LockKeyConst;
import net.polyv.constant.balance.BalanceChangeTypeConst;
import net.polyv.constant.balance.BalanceTypeConst;
import net.polyv.constant.deposit.AmountDepositTypeEnum;
import net.polyv.constant.deposit.IsDonateConst;
import net.polyv.dao.primary.AccountRechargeRecordRepository;
import net.polyv.dao.primary.AccountRechargeRecordResourcePointRepository;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.exception.balance.BalanceException;
import net.polyv.model.data.concurrence.BalanceChangeState;
import net.polyv.model.data.deposit.*;
import net.polyv.model.entity.primary.AccountRechargeRecord;
import net.polyv.model.entity.primary.AccountRechargeRecordResourcePoint;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.modules.common.util.JacksonUtil;
import net.polyv.service.AccountDepositService;
import net.polyv.service.BalanceOperateService;
import net.polyv.service.CreditService;
import net.polyv.util.LockUtil;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;

/**
 * 余额相关操作（充值金额+赠送金额+授信额度）
 * <AUTHOR>
 * @since 2020/5/13
 */
@Slf4j
@Service
public class BalanceOperateServiceImpl implements BalanceOperateService {

    @Autowired
    private AccountRechargeRecordRepository accountRechargeRecordRepository;
    @Autowired
    private AccountDepositService accountDepositService;
    @Autowired
    private CreditService creditService;
    @Autowired
    private LockUtil lockUtil;
    @Autowired
    private LiveCustomerConcurrenceSettingRepository concurrenceSettingRepository;
    @Autowired
    private AccountRechargeRecordResourcePointRepository resourcePointRepository ;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BalanceChangeResultDO reduce(@Validated BalanceChangeAmountDO balanceChangeAmountDO) {

        // 余额操作加锁
        boolean lockSuccess = this.balanceOperateLock(balanceChangeAmountDO.getCustomerId());
       if (!lockSuccess) {
           throw new BalanceException("系统繁忙，请稍后再试");
       }

        // 检查余额是否足够扣除
        boolean enough = this.checkCurrentBalanceEnoughReduce(balanceChangeAmountDO);
        if (!enough) {
            this.releaseBalanceOperateLock(balanceChangeAmountDO.getCustomerId());
            throw new BalanceException("额度不足，请先充值");
        }

        // 执行操作
        BalanceChangeResultDO resultDO = this.changeBalanceAmount(balanceChangeAmountDO);
        this.releaseBalanceOperateLock(balanceChangeAmountDO.getCustomerId());
        return resultDO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResourcePointChangeResultDO reduceResourcePoint(@Validated BalanceChangeAmountDO balanceChangeAmountDO) {

        // 余额操作加锁
        boolean lockSuccess = this.balanceOperateLockResourcePoint(balanceChangeAmountDO.getCustomerId());
        if (!lockSuccess) {
            throw new BalanceException("系统繁忙，请稍后再试");
        }

//        // 检查余额是否足够扣除,不检查，如果扣除结果为负值，则走金额
//        boolean enough = this.checkCurrentBalanceEnoughReduceResourcePoint(balanceChangeAmountDO);
//        if (!enough) {
//            this.releaseBalanceOperateLockResourcePoint(balanceChangeAmountDO.getCustomerId());
//            throw new BalanceException("资源点不足，请先充值");
//        }

        // 执行操作
        ResourcePointChangeResultDO resultDO = this.changeBalanceAmountResourcePoint(balanceChangeAmountDO);
        this.releaseBalanceOperateLockResourcePoint(balanceChangeAmountDO.getCustomerId());
        return resultDO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BalanceChangeResultDO unFreeze(BalanceChangeAmountDO balanceChangeAmountDO) {

        // 余额操作加锁
        boolean lockSuccess = this.balanceOperateLock(balanceChangeAmountDO.getCustomerId());
        if (!lockSuccess) {
            throw new BalanceException("系统繁忙，请稍后再试");
        }

        // 执行操作
        BalanceChangeResultDO resultDO = this.changeBalanceAmount(balanceChangeAmountDO);
        this.releaseBalanceOperateLock(balanceChangeAmountDO.getCustomerId());
        accountRechargeRecordRepository.findValidRecordByCustomerId(balanceChangeAmountDO.getCustomerId(), 0).forEach(item->log.info("可用充值{}", item));
        return resultDO;
    }

    @Override
    public BalanceChangeResultDO reduceFreeze(BalanceChangeAmountDO balanceChangeAmountDO) {

        // 余额操作加锁
        boolean lockSuccess = this.balanceOperateLock(balanceChangeAmountDO.getCustomerId());
        if (!lockSuccess) {
            throw new BalanceException("系统繁忙，请稍后再试");
        }

        // 执行操作
        BalanceChangeResultDO resultDO = this.changeBalanceAmount(balanceChangeAmountDO);
        this.releaseBalanceOperateLock(balanceChangeAmountDO.getCustomerId());
        return resultDO;
    }

    /**
     * 检查当前的余额能否扣除
     * @param balanceChangeAmountDO 扣费入参
     * @return true表示足够，false表示余额不足
     */
    @Override
    public boolean checkCurrentBalanceEnoughReduce(BalanceChangeAmountDO balanceChangeAmountDO) {

        // 授信额度充足
        long validCredit = accountDepositService.calcCustomerValidCredit(
                CustomerInfoGetInputVO.builder().customerId(balanceChangeAmountDO.getCustomerId()).build());
        if (validCredit >= balanceChangeAmountDO.getAmount()) {
            return true;
        }

        // 可用充值额度+赠送额度+测试额度
        Long validDeposit = accountRechargeRecordRepository.sumUserValidBalanceAmount(balanceChangeAmountDO.getCustomerId());
        if (validDeposit >= balanceChangeAmountDO.getAmount()) {
            return true;
        }

        // 可用额度+赠送+测试授信额度之和足够抵扣
        return validCredit + validDeposit >= balanceChangeAmountDO.getAmount();
    }

    /**
     * 检查当前的余额能否扣除
     * @param balanceChangeAmountDO 扣费入参
     * @return true表示足够，false表示余额不足
     */
    @Override
    public boolean checkCurrentBalanceEnoughReduceResourcePoint(BalanceChangeAmountDO balanceChangeAmountDO) {

        // 可用充值额度+赠送额度+测试额度
        Long validDeposit = resourcePointRepository.sumUserValidBalanceAmount(balanceChangeAmountDO.getCustomerId());

        // 可用额度+赠送+测试授信额度之和足够抵扣
        return  validDeposit >= balanceChangeAmountDO.getAmount();
    }

    /**
     * 执行增减余额操作
     * @param amountDO 扣除入参
     * @return 执行结果
     */
    private BalanceChangeResultDO changeBalanceAmount(BalanceChangeAmountDO amountDO) {

        BalanceChangeResultDO resultDO = BalanceChangeResultDO.success("");

        long changeAmount;
        if (BalanceChangeTypeConst.FREEZE.equals(amountDO.getReduceType())
            || BalanceChangeTypeConst.REDUCE.equals(amountDO.getReduceType())) {
            // 冻结、扣费
            changeAmount = this.reduceBalance(amountDO, resultDO);
        } else if (BalanceChangeTypeConst.UNFREEZE.equals(amountDO.getReduceType())){
            // 解冻
            changeAmount = this.unFreezeBalance(amountDO);
        } else {
            // 扣除冻结金额
            changeAmount = this.doReduceFreeze(amountDO, resultDO);
        }

        // 此处应该变化到0，如果不为0则是异常
        if (changeAmount != 0) {
            if (log.isWarnEnabled()) {
                log.warn("增减余额后，结果不为0，changeAmount={}, 参数={}", changeAmount, JSON.toJSONString(amountDO));
            }
            throw new BalanceException("增减余额后，结果不为0");
        }

        // 操作完成
        return resultDO;
    }

    /**
     * 执行资源点增减余额操作
     * @param amountDO 扣除入参
     * @return 执行结果
     */
    private ResourcePointChangeResultDO changeBalanceAmountResourcePoint(BalanceChangeAmountDO amountDO) {

        ResourcePointChangeResultDO resultDO = ResourcePointChangeResultDO.success("");

        long changeAmount = this.reduceBalanceResourcePoint(amountDO, resultDO);

        resultDO.setLessAmount(changeAmount);

        // 操作完成
        return resultDO;
    }

    /**
     * 扣减冻结金额
     * @param amountDO 改变常数
     * @param resultDO 扣除结果
     * @return 剩余未处理金额，这里一直为0
     */
    private long doReduceFreeze(BalanceChangeAmountDO amountDO, BalanceChangeResultDO resultDO) {
        long changeAmount = amountDO.getAmount();
        
        long reduceFreezeDeposit = 0;
        long reduceFreezeDonate = 0;
        long reduceFreezeCredit = 0;

        // 充值金额
        if (amountDO.getBalanceChangeState().getDeposit() > 0L) {
            reduceFreezeDeposit = this.doReduceFreezeDepositBatch(amountDO, resultDO);
            changeAmount -= reduceFreezeDeposit;
        }

        // 赠送金额
        if (amountDO.getBalanceChangeState().getDonate() > 0L) {
            reduceFreezeDonate = this.doReduceFreezeDonate(amountDO, resultDO);
            changeAmount -= reduceFreezeDonate;
        }

        // 授信额度 只需要做解冻
        if (amountDO.getBalanceChangeState().getCredit() > 0L) {
            reduceFreezeCredit = this.doUnFreezeCredit(amountDO);
            changeAmount -= reduceFreezeCredit;
        }
    
        // 更新并发时段的已使用冻结金额
        LiveCustomerConcurrenceSetting concurrenceSetting = amountDO.getLiveCustomerConcurrenceSetting();
        BalanceChangeState balanceChangeState = concurrenceSetting.getFreezeState();
        balanceChangeState.setAvailableDeposit(balanceChangeState.getAvailableDeposit() - reduceFreezeDeposit);
        balanceChangeState.setAvailableDonate(balanceChangeState.getAvailableDonate() - reduceFreezeDonate);
        balanceChangeState.setAvailableCredit(balanceChangeState.getAvailableCredit() - reduceFreezeCredit);
        concurrenceSetting.setUpdateTime(new Date());
        concurrenceSettingRepository.save(concurrenceSetting);
        return changeAmount;
    }

    private long doReduceFreezeCredit(BalanceChangeAmountDO amountDO, BalanceChangeResultDO resultDO) {
        BalanceChangeAmountDO unFreezeCreditAmount = new BalanceChangeAmountDO();
        unFreezeCreditAmount.setCustomerId(amountDO.getCustomerId());
        unFreezeCreditAmount.setReduceType(amountDO.getReduceType());
        unFreezeCreditAmount.setAmount(amountDO.getBalanceChangeState().getCredit());

        // 保存改变明细
        this.saveChangeDetail(unFreezeCreditAmount.getAmount(), null, resultDO);

        return creditService.reduceFreezeCredit(unFreezeCreditAmount);
    }

    private long doReduceFreezeDonate(BalanceChangeAmountDO amountDO, BalanceChangeResultDO resultDO) {
        List<AccountRechargeRecord> recordList = accountRechargeRecordRepository
                .findValidFreezeRecordByCustomerId(amountDO.getCustomerId(), IsDonateConst.YES, PageRequest.of(0, 1));
        AccountRechargeRecord donateRecord = recordList.get(0);

        long actualReduceFreezeDonate = Math.min(amountDO.getBalanceChangeState().getDonate(), donateRecord.getFreezeAmount());
        int effectiveRows =
                accountRechargeRecordRepository.reduceFreezeAmount(donateRecord.getCustomerId(), donateRecord.getOperaId(), donateRecord.getIsDonate(), actualReduceFreezeDonate);

        if (effectiveRows != 1) {
            throw new BalanceException("余额并发更新冲突，请稍后重试");
        }
    
        donateRecord.setFreezeAmount(donateRecord.getFreezeAmount() - actualReduceFreezeDonate);
        // 保存明细
        this.saveChangeDetail(actualReduceFreezeDonate, donateRecord, resultDO);

        return actualReduceFreezeDonate;
    }

    private long doReduceFreezeDepositBatch(BalanceChangeAmountDO amountDO, BalanceChangeResultDO resultDO) {
        long totalActualReduce = 0L;
        long depositAmount = amountDO.getBalanceChangeState().getDeposit();

        for (int i = 0; ; i++) {
            List<AccountRechargeRecord> recordList = accountRechargeRecordRepository
                    .findValidFreezeRecordByCustomerId(amountDO.getCustomerId(), IsDonateConst.NO, PageRequest.of(i, 10));

            if (CollectionUtils.isEmpty(recordList)) {
                break;
            }

            for (AccountRechargeRecord record : recordList) {
                long actualUnFreeze = this.doReduceFreezeDeposit(depositAmount, record, resultDO);
                totalActualReduce += actualUnFreeze;
                depositAmount -= actualUnFreeze;

                // 解冻完成
                if (depositAmount == 0) {
                    break;
                }
            }

            // 解冻完成
            if (depositAmount == 0) {
                break;
            }
        }

        return totalActualReduce;
    }

    private long doReduceFreezeDeposit(long depositAmount, AccountRechargeRecord record, BalanceChangeResultDO resultDO) {
        long actualFreeze = Math.min(depositAmount, record.getFreezeAmount());
        int effectiveRows =
                accountRechargeRecordRepository.reduceFreezeAmount(record.getCustomerId(), record.getOperaId(), record.getIsDonate(), actualFreeze);

        if (effectiveRows != 1) {
            throw new BalanceException("余额并发更新冲突，请稍后重试");
        }
    
        record.setFreezeAmount(record.getFreezeAmount() - actualFreeze);
        this.saveChangeDetail(actualFreeze, record, resultDO);

        return actualFreeze;
    }

    /**
     * 解冻余额
     * @param amountDO  更改入参
     * @return 剩余补回款
     */
    private long unFreezeBalance(BalanceChangeAmountDO amountDO) {

        // 当前需解冻金额
        long changeAmount = amountDO.getAmount();
        
        
        long unFreezeDeposit = 0L;
        long unFreezeDonate = 0L;
        long unFreezeCredit = 0L;
        
        // 执行充值金额解冻
        if (changeAmount > 0L && amountDO.getBalanceChangeState().getDeposit() > 0L) {
            unFreezeDeposit = this.doUnFreezeDepositBatch(amountDO);
            changeAmount = changeAmount - unFreezeDeposit;
        }

        // 执行赠送金额解冻
        if (changeAmount > 0L && amountDO.getBalanceChangeState().getDonate() > 0L) {
            unFreezeDonate = this.doUnFreezeDonate(amountDO);
            changeAmount = changeAmount - unFreezeDonate;
        }

        // 执行授信额度解冻
        if (changeAmount > 0L && amountDO.getBalanceChangeState().getCredit() > 0L) {
            unFreezeCredit = this.doUnFreezeCredit(amountDO);
            changeAmount = changeAmount - unFreezeCredit;
        }
        
        // 更新并发时段的已使用冻结金额
        LiveCustomerConcurrenceSetting concurrenceSetting = amountDO.getLiveCustomerConcurrenceSetting();
        BalanceChangeState balanceChangeState = concurrenceSetting.getFreezeState();
        balanceChangeState.setAvailableDeposit(balanceChangeState.getAvailableDeposit() - unFreezeDeposit);
        balanceChangeState.setAvailableDonate(balanceChangeState.getAvailableDonate() - unFreezeDonate);
        balanceChangeState.setAvailableCredit(balanceChangeState.getAvailableCredit() - unFreezeCredit);
        concurrenceSetting.setUpdateTime(new Date());
        concurrenceSettingRepository.save(concurrenceSetting);
        
        // 返回剩下需解冻金额
        return changeAmount;
    }

    /**
     * 解冻授信额度
     * @param amountDO 解冻参数
     * @return 实际解冻金额
     */
    private long doUnFreezeCredit(BalanceChangeAmountDO amountDO) {

        BalanceChangeAmountDO unFreezeCreditAmount = new BalanceChangeAmountDO();
        unFreezeCreditAmount.setCustomerId(amountDO.getCustomerId());
        unFreezeCreditAmount.setReduceType(amountDO.getReduceType());
        unFreezeCreditAmount.setAmount(amountDO.getBalanceChangeState().getCredit());

        return creditService.unfreezeCredit(unFreezeCreditAmount);
    }

    /**
     * 解冻赠送额度
     * @param amountDO 解冻参数
     * @return 实际解冻金额
     */
    private long doUnFreezeDonate(BalanceChangeAmountDO amountDO) {

        List<AccountRechargeRecord> recordList = accountRechargeRecordRepository
                .findValidFreezeRecordByCustomerId(amountDO.getCustomerId(), IsDonateConst.YES, PageRequest.of(0, 1));
        AccountRechargeRecord donateRecord = recordList.get(0);
    
        long actualUnfreeze = Math.min(amountDO.getBalanceChangeState().getDonate(), donateRecord.getFreezeAmount());
        int effectiveRows =
                accountRechargeRecordRepository.unFreezeAvailableAmount(donateRecord.getCustomerId(), donateRecord.getOperaId(), donateRecord.getIsDonate(), actualUnfreeze);
    
        if (effectiveRows != 1) {
            throw new BalanceException("余额并发更新冲突，请稍后重试");
        }
        donateRecord.setFreezeAmount(donateRecord.getFreezeAmount() - actualUnfreeze);
        donateRecord.setAvailable(donateRecord.getAvailable() + actualUnfreeze);
        amountDO.getUnfreezeRechargeRecordList().add(donateRecord);

        return actualUnfreeze;
    }

    /**
     * 解冻充值金额（批量）
     * @param amountDO 解冻参数
     * @return 实际解冻金额
     */
    private long doUnFreezeDepositBatch(BalanceChangeAmountDO amountDO) {

        long totalActualUnFreeze = 0L;
        long depositAmount = amountDO.getBalanceChangeState().getDeposit();

        for (int i = 0; ; i++) {
            List<AccountRechargeRecord> recordList = accountRechargeRecordRepository
                    .findValidFreezeRecordByCustomerId(amountDO.getCustomerId(), IsDonateConst.NO, PageRequest.of(i, 10));

            if (CollectionUtils.isEmpty(recordList)) {
                break;
            }

            for (AccountRechargeRecord record : recordList) {
                long actualUnFreeze = this.unFreezeDeposit(depositAmount, record);
                record.setAvailable(record.getAvailable() + actualUnFreeze);
                record.setFreezeAmount(record.getFreezeAmount() - actualUnFreeze);
                amountDO.getUnfreezeRechargeRecordList().add(record);
                totalActualUnFreeze += actualUnFreeze;
                depositAmount -= actualUnFreeze;

                // 解冻完成
                if (depositAmount == 0) {
                    break;
                }
            }

            // 解冻完成
            if (depositAmount == 0) {
                break;
            }
        }

        return totalActualUnFreeze;
    }

    /**
     * 解冻充值金额（单条记录）
     * @param depositAmount 需解冻金额
     * @param record 充值记录
     * @return 实际解冻金额
     */
    private long unFreezeDeposit(long depositAmount, AccountRechargeRecord record) {
        long actualFreeze = Math.min(depositAmount, record.getFreezeAmount());
        int effectiveRows =
                accountRechargeRecordRepository.unFreezeAvailableAmount(record.getCustomerId(), record.getOperaId(), record.getIsDonate(), actualFreeze);

        if (effectiveRows != 1) {
            throw new BalanceException("余额并发更新冲突，请稍后重试");
        }

        return actualFreeze;
    }

    /**
     * 减少余额（扣除、冻结）
     * @param amountDO  更改入参
     * @param resultDO  更改结果
     * @return 剩余待扣款
     */
    private long reduceBalanceResourcePoint(BalanceChangeAmountDO amountDO, ResourcePointChangeResultDO resultDO) {

        long changeAmount = amountDO.getAmount();

        // 扣储值+赠送
        changeAmount = doReduceDepositBatchResourcePoint(amountDO, changeAmount, resultDO);

        return changeAmount;

    }

    /**
     * 减少余额（扣除、冻结）
     * @param amountDO  更改入参
     * @param resultDO  更改结果
     * @return 剩余待扣款
     */
    private long reduceBalance(BalanceChangeAmountDO amountDO, BalanceChangeResultDO resultDO) {

        long changeAmount = amountDO.getAmount();

        // 扣储值+赠送
        changeAmount = doReduceDepositBatch(amountDO, changeAmount, resultDO);
        if (changeAmount == 0) {
            // 余额已经足够扣除
            return changeAmount;
        }

        // 扣授信额度
        return doReduceCredit(amountDO, changeAmount, resultDO);
    }

    /**
     * 执行扣除授信额度
     * @param amountDO 扣除参数
     * @param reduceAmount 本次需要扣除的金额
     * @param resultDO 扣除结果
     * @return 实际扣除的金额
     */
    private long doReduceCredit(BalanceChangeAmountDO amountDO, long reduceAmount, BalanceChangeResultDO resultDO) {
    
        long actualReduce = 0;
        if (BalanceChangeTypeConst.FREEZE.equals(amountDO.getReduceType())
            || BalanceChangeTypeConst.UNFREEZE.equals(amountDO.getReduceType())) {
            BalanceChangeAmountDO creditReduceAmount = new BalanceChangeAmountDO();
            creditReduceAmount.setCustomerId(amountDO.getCustomerId());
            creditReduceAmount.setAmount(reduceAmount);
            creditReduceAmount.setReduceType(amountDO.getReduceType());
            actualReduce = creditService.reduceCredit(creditReduceAmount);
        }

//        long actualReduce = creditService.reduceCredit(creditReduceAmount);
        resultDO.setCreditAmount(resultDO.getCreditAmount() + reduceAmount);
    
        // 保存改变明细
//        this.saveChangeDetail(reduceAmount, null, resultDO);
    
        return reduceAmount - actualReduce;
    }

    /**
     * 执行扣除储值的操作
     * @param amountDO 扣除参数
     * @param reduceAmount 本次需要扣除的金额
     * @param resultDO 扣除结果
     * @return 实际扣除金额
     */
    private long doReduceDepositBatch(BalanceChangeAmountDO amountDO, long reduceAmount, BalanceChangeResultDO resultDO) {
        boolean isFreeze = BalanceChangeTypeConst.FREEZE.equals(amountDO.getReduceType());
        int currentPage = 0;
        Page<AccountRechargeRecord> recordList =
                accountRechargeRecordRepository.findValidRecordByCustomerId(amountDO.getCustomerId(), PageRequest.of(currentPage, 10));
        // 扣储值模块
        while(recordList != null && recordList.getContent().size() > 0 && reduceAmount > 0) {
            for (AccountRechargeRecord record : recordList.getContent()) {
                long actualReduce = this.doReduceDeposit(record, reduceAmount, isFreeze);

                // 扣款
                reduceAmount -= actualReduce;

                // 扣款记录
                if (AmountDepositTypeEnum.DONATE.getCode().equals(record.getIsDonate())) {
                    resultDO.setDonateAmount(resultDO.getDonateAmount() + actualReduce);
                }else if (AmountDepositTypeEnum.DEPOSIT.getCode().equals(record.getIsDonate())) {
                    resultDO.setDepositAmount(resultDO.getDepositAmount() + actualReduce);
                }else if (AmountDepositTypeEnum.TEST.getCode().equals(record.getIsDonate())) {
                    resultDO.setTestAmount(resultDO.getTestAmount() + actualReduce);
                }

                // 记录扣款情况
                this.saveChangeDetail(actualReduce, record, resultDO);

                if (reduceAmount <= 0) {
                    break;
                }
            }
            //注意这里的currentPage不能+1,因为上一个批次数据的available已经更新为0了，会导致查不出数据
            recordList = accountRechargeRecordRepository.findValidRecordByCustomerId(amountDO.getCustomerId(), PageRequest.of(currentPage, 10));
        }
        return reduceAmount;
    }

    /**
     * 执行扣除储值的操作
     * @param amountDO 扣除参数
     * @param reduceAmount 本次需要扣除的金额
     * @param resultDO 扣除结果
     * @return 实际扣除金额
     */
    private long doReduceDepositBatchResourcePoint(BalanceChangeAmountDO amountDO,
                                                   long reduceAmount,
                                                   ResourcePointChangeResultDO resultDO) {
        int currentPage = 0;
        Page<AccountRechargeRecordResourcePoint> recordList =
                resourcePointRepository.findValidRecordByCustomerId(amountDO.getCustomerId(), PageRequest.of(currentPage, 10));

        // 扣储值模块
        while(recordList != null && recordList.getContent().size() > 0 && reduceAmount > 0) {
            for (AccountRechargeRecordResourcePoint record : recordList.getContent()) {

                long actualReduce = this.doReduceDepositResourcePoint(record, reduceAmount);

                log.info("record {} actualReduce {}" , JacksonUtil.writeAsString(record) , actualReduce);

                // 扣款
                reduceAmount -= actualReduce;

                // 扣款记录
                if (AmountDepositTypeEnum.DONATE.getCode().equals(record.getIsDonate())) {
                    resultDO.setDonateAmount(resultDO.getDonateAmount() + actualReduce);
                }else if (AmountDepositTypeEnum.DEPOSIT.getCode().equals(record.getIsDonate())) {
                    resultDO.setDepositAmount(resultDO.getDepositAmount() + actualReduce);
                }else if (AmountDepositTypeEnum.TEST.getCode().equals(record.getIsDonate())) {
                    resultDO.setTestAmount(resultDO.getTestAmount() + actualReduce);
                }

                // 记录扣款情况
                this.saveChangeDetailResourcePoint(actualReduce, record, resultDO);

                if (reduceAmount <= 0) {
                    break;
                }
            }
            //注意这里的currentPage不能+1,因为上一个批次数据的available已经更新为0了，会导致查不出数据
            recordList = resourcePointRepository.findValidRecordByCustomerId(amountDO.getCustomerId(), PageRequest.of(currentPage, 10));
        }
        return reduceAmount;
    }

    /**
     * 保存改变明细
     * @param changeAmount 实际改变金额
     * @param record 充值记录/赠送记录/null（授信额度）
     * @param resultDO 改变的结果
     */
    private void saveChangeDetail(long changeAmount, AccountRechargeRecord record, BalanceChangeResultDO resultDO) {

        // 授信额度
        if (record == null) {
            resultDO.getChangeDetailDOList().add(
                    BalanceChangeDetailDO
                        .builder()
                        .balanceType(BalanceTypeConst.CREDIT)
                        .amount(changeAmount).build()
            );
            return;
        }

        // 充值金额、赠送金额或者测试金额
        BalanceTypeConst balanceType = BalanceTypeConst.DEPOSIT;
        if(AmountDepositTypeEnum.DONATE.getCode().equals(record.getIsDonate())){
            balanceType = BalanceTypeConst.DONATE;
        }
        else if(AmountDepositTypeEnum.DEPOSIT.getCode().equals(record.getIsDonate())){
            balanceType = BalanceTypeConst.DEPOSIT;
        }
        else if(AmountDepositTypeEnum.TEST.getCode().equals(record.getIsDonate())){
            balanceType = BalanceTypeConst.TEST;
        }
        resultDO.getChangeDetailDOList().add(BalanceChangeDetailDO.builder()
                .balanceType(balanceType)
                .amount(changeAmount)
                .rechargeRecord(record)
                .build());
    }

    /**
     * 保存改变明细
     * @param changeAmount 实际改变金额
     * @param record 充值记录/赠送记录/null（授信额度）
     * @param resultDO 改变的结果
     */
    private void saveChangeDetailResourcePoint(long changeAmount, AccountRechargeRecordResourcePoint record, ResourcePointChangeResultDO resultDO) {

        // 充值金额、赠送金额或者测试金额
        BalanceTypeConst balanceType = BalanceTypeConst.DEPOSIT;
        if(AmountDepositTypeEnum.DONATE.getCode().equals(record.getIsDonate())){
            balanceType = BalanceTypeConst.DONATE;
        }
        else if(AmountDepositTypeEnum.DEPOSIT.getCode().equals(record.getIsDonate())){
            balanceType = BalanceTypeConst.DEPOSIT;
        }
        else if(AmountDepositTypeEnum.TEST.getCode().equals(record.getIsDonate())){
            balanceType = BalanceTypeConst.TEST;
        }
        resultDO.getChangeDetailDOList().add(ResourcePointChangeDetailDO.builder()
                .balanceType(balanceType)
                .amount(changeAmount)
                .rechargeRecord(record)
                .build());
    }

    /**
     * 执行扣除储值
     * @param record 储值记录
     * @param reduceAmount 本次扣除金额
     * @param isFreeze 是否冻结
     * @return 实际扣除金额
     */
    private long doReduceDeposit(AccountRechargeRecord record, long reduceAmount, boolean isFreeze) {
        long currentReduce = Math.min(reduceAmount, record.getAvailable());

        int effectiveRows;
        if (isFreeze) {
            effectiveRows = accountRechargeRecordRepository.freezeAvailableAmount(
                    record.getCustomerId(), record.getOperaId(), record.getIsDonate(), currentReduce);
        } else {
            // 扣除金额
            effectiveRows = accountRechargeRecordRepository.reduceAvailableAmount(
                    record.getCustomerId(), record.getOperaId(), record.getIsDonate(), currentReduce);
            
        }

        if (effectiveRows != 1) {
            log.warn("扣费失败，参数={}", JSON.toJSONString(record));
            throw new BalanceException("扣费金额影响行数不等于1，请稍后重试");
        }

        return currentReduce;
    }

    /**
     * 执行扣除储值
     * @param record 储值记录
     * @param reduceAmount 本次扣除金额
     * @return 实际扣除金额
     */
    private long doReduceDepositResourcePoint(AccountRechargeRecordResourcePoint record, long reduceAmount) {
        long currentReduce = Math.min(reduceAmount, record.getAvailable());

        // 扣除金额
        int effectiveRows = resourcePointRepository.reduceAvailableAmount(
                    record.getId(), currentReduce);

        if (effectiveRows != 1) {
            log.warn("扣除资源点失败，参数={}", JSON.toJSONString(record));
            throw new BalanceException("扣除资源点影响行数不等于1，请稍后重试");
        }

        return currentReduce;
    }

    /**
     * 余额操作加锁
     * @param customerId 顾客id
     * @return true表示成功，false表示加锁失败
     */
    private boolean balanceOperateLock(String customerId) {
        String lockKey = String.format(LockKeyConst.CUSTOMER_BALANCE_OPERATE, customerId);
        return lockUtil.lock(lockKey);
    }

    /**
     * 释放余额操作锁
     * @param customerId 顾客id
     */
    private void releaseBalanceOperateLock(String customerId) {
        String lockKey = String.format(LockKeyConst.CUSTOMER_BALANCE_OPERATE, customerId);
        lockUtil.releaseLock(lockKey);
    }

    /**
     * 资源点操作加锁
     * @param customerId 顾客id
     * @return true表示成功，false表示加锁失败
     */
    private boolean balanceOperateLockResourcePoint(String customerId) {
        String lockKey = String.format(LockKeyConst.CUSTOMER_BALANCE_OPERATE + ":resource-point", customerId);
        return lockUtil.lock(lockKey);
    }

    /**
     * 资源点释放操作锁
     * @param customerId 顾客id
     */
    private void releaseBalanceOperateLockResourcePoint(String customerId) {
        String lockKey = String.format(LockKeyConst.CUSTOMER_BALANCE_OPERATE + ":resource-point", customerId);
        lockUtil.releaseLock(lockKey);
    }
}
