package net.polyv.service.impl;

import static net.polyv.constant.credit.CreditAlterationTypeConst.FREEZE;
import static net.polyv.constant.credit.CreditAlterationTypeConst.UNFREEZE;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import net.polyv.constant.deposit.AmountDepositTypeEnum;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import net.polyv.constant.balance.AccountAvailableDailyTypeConst;
import net.polyv.constant.deposit.IsDonateConst;
import net.polyv.dao.primary.AccountAvailableDailyRepository;
import net.polyv.dao.primary.AccountRechargeRecordRepository;
import net.polyv.dao.primary.CreditAlterationRecordRepository;
import net.polyv.dao.primary.CustomerConfigRepository;
import net.polyv.dao.primary.custom.CustomerConfigDao;
import net.polyv.model.entity.primary.AccountAvailableDaily;
import net.polyv.model.entity.primary.AccountRechargeRecord;
import net.polyv.model.entity.primary.CreditAlterationRecord;
import net.polyv.model.entity.primary.CustomerConfig;
import net.polyv.service.AccountAvailableDailyService;

/**
 * 账户余额快照服务
 * <AUTHOR>
 * @since 2020/6/28
 */
@Service
public class AccountAvailableDailyServiceImpl implements AccountAvailableDailyService {
    
    @Autowired
    private AccountRechargeRecordRepository accountRechargeRecordRepository;
    
    @Autowired
    private AccountAvailableDailyRepository accountAvailableDailyRepository;
    
    @Autowired
    private CreditAlterationRecordRepository creditAlterationRecordRepository;
    
    @Autowired
    private CustomerConfigDao customerConfigDao;
    
    @Autowired
    private CustomerConfigRepository customerConfigRepository;
    
    @Override
    public void generateAllSnapshot(Date date) {
        //生成金额 、测试金额、赠送金额快照
        generateAmountAndDonateSnapshot(date);
        
        //生成授信额度快照
        generateCreditSnapshot(date);
    }
    
    @Override
    public void generateAmountAndDonateSnapshot(Date date) {
        Page<AccountRechargeRecord> page = accountRechargeRecordRepository.findAll(PageRequest.of(0, 500));
        //保存进快照表
        List<AccountAvailableDaily> list = buildDailyList(page.getContent(), date);
        accountAvailableDailyRepository.saveAll(list);
        for (int index = 1; index < page.getTotalPages(); index++) {
            page = accountRechargeRecordRepository.findAll(PageRequest.of(index, 500));
            list = buildDailyList(page.getContent(), date);
            accountAvailableDailyRepository.saveAll(list);
        }
    }
    
    @Override
    public void generateCreditSnapshot(Date date) {
        //前一日的快照+当日的变动=当日的快照
        Page<CustomerConfig> page = customerConfigRepository.findAll(PageRequest.of(0, 500));
        List<AccountAvailableDaily> list = page.getContent()
                .stream()
                .map(item -> this.getCreditAvailable(item.getCustomerId(), date))
                .collect(Collectors.toList());
        accountAvailableDailyRepository.saveAll(list);
        for (int index = 1; index < page.getTotalPages(); index++) {
            page = customerConfigRepository.findAll(PageRequest.of(index, 500));
            list = page.getContent()
                    .stream()
                    .map(item -> this.getCreditAvailable(item.getCustomerId(), date))
                    .collect(Collectors.toList());
            accountAvailableDailyRepository.saveAll(list);
        }
    }
    
    private AccountAvailableDaily getCreditAvailable(String customerId, Date date) {
        //1.从customer_config获取总的授信额度--->amount
        //2.从快照表获取前一日已用的授信额度--->history_consumed(amount-available-freeze_amount), history_freeze(freeze_amount)
        //3.从今日变动表获取当天的变动(type:1 扣款， 2 充值补还，3 冻结，4 补扣调账，5 退费调账)--->today_consumed(包含冻结), today_freeze
        //4.amount->amount, available-> amount-history_consumed-today_consumed,
        // freeze_amount->history_freeze+today_freeze
        Date dayBefore1 = DateUtils.addDays(date, -1);
        AccountAvailableDaily daily = accountAvailableDailyRepository.findFirstByCustomerIdAndStatAtAndType(customerId,
                dayBefore1, AccountAvailableDailyTypeConst.CREDIT.getValue());
        
        CustomerConfig config = customerConfigDao.getOrCreateCustomerConfig(customerId);
        long amount = config.getCredit();
        long historyConumed = 0;
        long historyFreeze = 0;
        if(daily!=null){
            historyConumed = daily.getAmount() - daily.getAvailable() - daily.getFreezeAmount();
            historyFreeze = daily.getFreezeAmount();
        }
        
        long todayConsumed = creditAlterationRecordRepository.sumUsedCreditFromStatStartAtDateToNow(customerId, date);
        List<CreditAlterationRecord> crediteAlterList =
                creditAlterationRecordRepository.findByCustomerIdAndStatAtAndTypeIn(
                customerId, date, Arrays.asList(FREEZE, UNFREEZE));
        long todayFreeze = crediteAlterList.stream()
                .mapToLong(record -> (FREEZE == record.getType() ? 1 : -1) * record.getAmount())
                .sum();
        
        AccountAvailableDaily snapshot = new AccountAvailableDaily();
        snapshot.setStatAt(date);
        snapshot.setAmount(amount);
        snapshot.setType(AccountAvailableDailyTypeConst.CREDIT.getValue());
        snapshot.setAvailable(amount - historyConumed - todayConsumed);
        snapshot.setOperaId(0L); //授信额度的操作没有记录operaid，固定写0
        snapshot.setFreezeAmount(historyFreeze + todayFreeze);
        snapshot.setCustomerId(customerId);
        snapshot.setCreateTime(new Date());
        snapshot.setUpdateTime(new Date());
        return snapshot;
    }
    
    private List<AccountAvailableDaily> buildDailyList(List<AccountRechargeRecord> list, Date date) {
        return list.stream().map(record -> buildDailyVo(record, date)).collect(Collectors.toList());
    }
    
    private AccountAvailableDaily buildDailyVo(AccountRechargeRecord record, Date date) {
        AccountAvailableDaily vo = new AccountAvailableDaily();
        BeanUtils.copyProperties(record, vo);
        if(AmountDepositTypeEnum.DEPOSIT.getCode().equals(record.getIsDonate())){
            vo.setType(AccountAvailableDailyTypeConst.DEPOSIT.getValue());
        }else if(AmountDepositTypeEnum.DONATE.getCode().equals(record.getIsDonate())){
            vo.setType(AccountAvailableDailyTypeConst.DONATE.getValue());
        }else if(AmountDepositTypeEnum.TEST.getCode().equals(record.getIsDonate())){
            vo.setType(AccountAvailableDailyTypeConst.TEST.getValue());
        }
        vo.setAmount(record.getDeposit());
        vo.setStatAt(date);
        vo.setCreateTime(new Date());
        vo.setUpdateTime(new Date());
        return vo;
    }
}
