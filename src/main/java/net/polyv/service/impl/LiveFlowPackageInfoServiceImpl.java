package net.polyv.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.LiveFlowPackageInfoRepository;
import net.polyv.model.entity.primary.LiveFlowPackageInfo;
import net.polyv.service.LiveFlowPackageInfoService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateUtil;
import net.polyv.web.model.resource.CleanResourceVO;

@Slf4j
@Service
public class LiveFlowPackageInfoServiceImpl implements LiveFlowPackageInfoService {
    
    @Resource
    private LiveFlowPackageInfoRepository liveFlowPackageInfoRepository;
    @Autowired
    private CustomerResourceService customerResourceService;
    
    
    @Override
    public Boolean isLiveFlowBilling(String customerId) {
        List<LiveFlowPackageInfo> list = liveFlowPackageInfoRepository.findByCustomerIdAndIsLiveFlowBilling(customerId,
                1);
        return CollectionUtils.isNotEmpty(list);
    }
    
    @Override
    public void clearLiveFlowPackageExpiredResource() {
        log.info("LiveFlowCleanScheduleRunner start....");
        Date now = DateUtil.getDateStart(new Date());
        Date pre7Days = DateUtil.getXDay(now, -7);
        List<LiveFlowPackageInfo> list = liveFlowPackageInfoRepository.findAll();
        List<String> customerIdList = new ArrayList<>();
        for (LiveFlowPackageInfo obj : list) {
            //判断是否为直播流量计费，并且过期时间在7天内的
            Long endDateLong = obj.getEndDate().getTime();
            if (Integer.valueOf(1).equals(obj.getIsLiveFlowBilling())) {
                if (null != endDateLong && now.getTime() > endDateLong && endDateLong > pre7Days.getTime()) {
                    customerIdList.add(obj.getCustomerId());
                }
            }
        }
        log.info("过期账号可用直播流量源清理, customerIds={}, resourceCode={} and {}", customerIdList,
                ResourceCodeConst.live_flow.toString());
        customerIdList.forEach(customerId -> {
            CleanResourceVO flowCleanResourceVO = CleanResourceVO.builder()
                    .resourceCode(ResourceCodeConst.live_flow.toString())
                    .customerId(customerId)
                    .build();
            customerResourceService.cleanItemResource(flowCleanResourceVO);
        });
        log.info("LiveFlowCleanScheduleRunner end....");
    }
    
    
    /**
     * 修改是否直播流量计费
     * @param customerId
     * @param isLiveFlowBilling 0：改为不是直播流量计费，即需要查直播流量计费的记录  1则相反
     */
    @Override
    public void updateIsLiveFlowBilling(String customerId, int isLiveFlowBilling) {
        log.info("updateIsLiveFlowBilling, customerId:{},isLiveFlowBilling:{}", customerId, isLiveFlowBilling);
        
        List<LiveFlowPackageInfo> list = liveFlowPackageInfoRepository.findByCustomerIdAndIsLiveFlowBilling(customerId,
                isLiveFlowBilling == 1 ? 0 : 1);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(liveFlowPackageInfo -> {
                liveFlowPackageInfo.setIsLiveFlowBilling(isLiveFlowBilling);
                liveFlowPackageInfo.setUpdateTime(new Date());
                liveFlowPackageInfoRepository.save(liveFlowPackageInfo);
            });
        }
    }
}
