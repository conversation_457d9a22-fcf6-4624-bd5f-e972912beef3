package net.polyv.service.impl;

import static net.polyv.constant.GlobalConfigConst.PCS_RESOURCE_AVAILABLE;
import static net.polyv.constant.GlobalConfigConst.RESOURCE_CUSTOMER_INIT;
import static net.polyv.constant.GlobalConfigConst.RESTRICT_PCS_RESULT;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.BusinessBillingPlanCodeEnum;
import net.polyv.constant.cache.CacheConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.constant.schdule.ScheduleJobTypeConst;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.dao.primary.billingConfig.BillingItemExtRepository;
import net.polyv.dao.primary.business.SystemDictionaryRepository;
import net.polyv.dao.primary.examinationDonate.SaleDepartmentBaseAmountRepository;
import net.polyv.event.CleanCacheEvent;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.business.BusinessOperationCheckResult;
import net.polyv.model.data.deposit.DepositResourceDTO;
import net.polyv.model.data.resource.CustomerWaitOpenPackageVO;
import net.polyv.model.entity.primary.CustomerEffectiveScheduleJob;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.billingconfig.BillingItemExt;
import net.polyv.model.entity.primary.business.SystemDictionary;
import net.polyv.model.entity.primary.examinationDonate.SaleDepartmentBaseAmount;
import net.polyv.model.entity.primary.finance.FinanceIncomeDetailEntity;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.DepositResourceRequest;
import net.polyv.modules.pcs.api.req.crm.GetUserLast7DayConsumedRequest;
import net.polyv.modules.pcs.api.stereotype.PackageCategoryConst;
import net.polyv.modules.pcs.api.vo.crm.GetGrayUserResourceByZeroResponse;
import net.polyv.modules.pcs.api.vo.crm.GetUserLast7DayConsumedResponse;
import net.polyv.modules.support.api.req.DelCacheKeysReq;
import net.polyv.modules.support.api.service.CacheApi;
import net.polyv.modules.user.api.service.CustomerAttributesApi;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.client.vod.resource.VodResourceService;
import net.polyv.rest.client.vod.user.UnionUserClient;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.rest.model.vod.VodPackageVo;
import net.polyv.rest.model.vod.user.CustomerSearchDTO;
import net.polyv.rest.model.vod.user.UserDetail;
import net.polyv.rest.model.vod.user.VodUserCacheCleanDTO;
import net.polyv.rest.util.ResponseUtil;
import net.polyv.service.CacheService;
import net.polyv.service.SystemEnvService;
import net.polyv.service.account.UserAccountService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.HttpClientUtil;
import net.polyv.util.JsonMapper;
import net.polyv.util.LiveRedisUtils;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.cache.CleanLiveResourceCacheInputVO;
import net.polyv.web.model.salesopportunities.input.BusinessOperationInputVO;

@Service
@Slf4j
public class CacheServiceImpl implements CacheService {
    
    @Resource
    private VodResourceService vodResourceService;
    @Autowired
    private SaleDepartmentBaseAmountRepository saleDepartmentBaseAmountRepository;
    @Autowired
    private DingWarnRobot dingWarnRobot;
    
    @Resource
    private CustomerAttributesApi customerAttributesApi;
    
    /**
     * ！ 注意，该工具类连接的是 直播的redis，与PCS的redis不同
     */
    @Autowired
    private LiveRedisUtils liveRedisUtils;
    
    @Autowired
    private LiveCustomerConcurrenceSettingRepository concurrenceSettingRepository;
    
    @Autowired
    private UnionUserClient unionUserClient;
    
    @Autowired
    private SystemEnvService systemEnvService;
    
    @Autowired
    private UserAccountService userAccountService;
    /**
     *
     */
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    
    @Autowired
    private CacheApi cacheApi;
    @Resource
    private SystemDictionaryRepository systemDictionaryRepository;
    
    @Resource
    private BillingItemExtRepository billingItemExtRepository;

    @Value("${redis.key.expire.SPACE_AVAILABLE_PACKAGE:18000}")
    private long spaceAvailablePackageExpireSecond;
    
    
    //TODO 与下面的方法重复了，需要优化
    @Override
    public void cleanLiveRestrictPcsResult(String customerId) {
        cleanLiveRestrictPcsResult(customerId, false);
    }
    
    /**
     * @param customerId:unionId
     * @param isBillingItem:是否清理自定义计费项缓存
     */
    public void cleanLiveRestrictPcsResult(String customerId, boolean isBillingItem) {
        try {
            UserMsgVO userMsgVO = UserClient.getUserByUnionId(customerId);
            if (userMsgVO == null) {
                return;
            }
            List<String> liveCacheKeysList = new ArrayList<>(ResourceCodeConst.values().length);
            List<String> liveClearKeyList = new ArrayList<>(ResourceCodeConst.values().length);
            List<String> pcsClearKeyList = new ArrayList<>(ResourceCodeConst.values().length);
            
            List<String> resultList = new ArrayList<>();
            if (isBillingItem) {
                List<BillingItemExt> billingItemExtRepositoryList = billingItemExtRepository.findAll();
                if (!CollectionUtils.isEmpty(billingItemExtRepositoryList)) {
                    resultList.addAll(billingItemExtRepositoryList.stream()
                            .map(BillingItemExt::getItemCode)
                            .collect(Collectors.toList()));
                }
            } else {
                ResourceCodeConst[] resourceCodeConsts = ResourceCodeConst.values();
                if (resourceCodeConsts != null && resourceCodeConsts.length > 0) {
                    //lambda遍历枚举,提取name，添加到resultList
                    Arrays.stream(resourceCodeConsts)
                            .forEach(resourceCodeConst -> resultList.add(resourceCodeConst.name()));
                }
            }
            for (String code : resultList) {
                liveClearKeyList.add(String.format(RESTRICT_PCS_RESULT, userMsgVO.getLiveUserId(), code));
                liveClearKeyList.add(String.format(RESOURCE_CUSTOMER_INIT, code, customerId));
                pcsClearKeyList.add(CacheConst.getCustomerAvailableCacheKey(customerId, code));
                pcsClearKeyList.add(CacheConst.getCustomerAvailableDetailCacheKey(customerId, code));
                liveCacheKeysList.add(String.format(PCS_RESOURCE_AVAILABLE, userMsgVO.getLiveUserId(), code));
            }
            liveRedisUtils.del(liveClearKeyList.toArray(new String[]{}));
            log.info("delete liveRedisUtils key：{}", liveClearKeyList);
            long count = redisTemplate.delete(pcsClearKeyList);
            log.info("delete pcs key：{}，count：{}", pcsClearKeyList, count);
            //批量删除直播缓存
            if (!CollectionUtils.isEmpty(liveCacheKeysList)) {
                DelCacheKeysReq delCacheKeysReq = new DelCacheKeysReq();
                delCacheKeysReq.setKeys(liveCacheKeysList);
                ResponseVO<Object> deleteResponseVO = cacheApi.delete(delCacheKeysReq);
                log.info("delete live cache key：{}，result：{}", delCacheKeysReq, deleteResponseVO);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            dingWarnRobot.sendWarnMsg("【告警，清理使用限制缓存异常】",
                    String.format("customerId=%s, exception=%s", customerId, e.getMessage()));
        }
    }
    
    @Override
    public void cleanBillingItemCache(String unionId) {
        cleanLiveRestrictPcsResult(unionId, true);
    }
    
    @Override
    public void cleanLiveRestrictPcsResult(CleanLiveResourceCacheInputVO inputVO) {
        
        
        try {
            UserMsgVO userMsgVO = UserClient.getUserByUnionId(inputVO.getCustomerId());
            if (userMsgVO == null) {
                return;
            }
            List<String> liveCacheKeysList = new ArrayList<>();
            List<String> list = new ArrayList<>();
            List<String> pcsClearKeyList = new ArrayList<>();
            if (CollectionUtils.isEmpty(inputVO.getResourceCodeList())) { // 全部资源清理
                for (ResourceCodeConst codeConst : ResourceCodeConst.values()) {
                    String code = codeConst.name();
                    list.add(String.format(RESTRICT_PCS_RESULT, userMsgVO.getLiveUserId(), code));
                    list.add(String.format(RESOURCE_CUSTOMER_INIT, code, inputVO.getCustomerId()));
                    pcsClearKeyList.add(CacheConst.getCustomerAvailableCacheKey(inputVO.getCustomerId(), code));
                    pcsClearKeyList.add(CacheConst.getCustomerAvailableDetailCacheKey(inputVO.getCustomerId(), code));
                    liveCacheKeysList.add(String.format(PCS_RESOURCE_AVAILABLE, userMsgVO.getLiveUserId(), code));
                }
            } else { // 清理指定的资源
                for (String code : inputVO.getResourceCodeList()) {
                    list.add(String.format(RESTRICT_PCS_RESULT, userMsgVO.getLiveUserId(), code));
                    list.add(String.format(RESOURCE_CUSTOMER_INIT, code, inputVO.getCustomerId()));
                    pcsClearKeyList.add(CacheConst.getCustomerAvailableCacheKey(inputVO.getCustomerId(), code));
                    pcsClearKeyList.add(CacheConst.getCustomerAvailableDetailCacheKey(inputVO.getCustomerId(), code));
                    liveCacheKeysList.add(String.format(PCS_RESOURCE_AVAILABLE, userMsgVO.getLiveUserId(), code));
                }
            }
            liveRedisUtils.del(list.toArray(new String[]{}));
            redisTemplate.delete(pcsClearKeyList);
            //批量删除直播缓存
            if (!CollectionUtils.isEmpty(liveCacheKeysList)) {
                DelCacheKeysReq delCacheKeysReq = new DelCacheKeysReq();
                delCacheKeysReq.setKeys(liveCacheKeysList);
                cacheApi.delete(delCacheKeysReq);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            dingWarnRobot.sendWarnMsg("【告警，清理资源缓存异常】",
                    String.format("inputVO=%s, exception=%s", inputVO, e.getMessage()));
        }
    }
    
    @Override
    public void cleanPcsCache(String customerId) {
        try {
            
            List<String> pcsClearKeyList = new ArrayList<>(ResourceCodeConst.values().length);
            for (ResourceCodeConst codeConst : ResourceCodeConst.values()) {
                String code = codeConst.name();
                pcsClearKeyList.add(CacheConst.getCustomerAvailableCacheKey(customerId, code));
                pcsClearKeyList.add(CacheConst.getCustomerAvailableDetailCacheKey(customerId, code));
            }
            redisTemplate.delete(pcsClearKeyList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            dingWarnRobot.sendWarnMsg("【告警，清理使用限制缓存异常】",
                    String.format("customerId=%s, exception=%s", customerId, e.getMessage()));
        }
    }
    
    @Override
    public void cleanPcsCache(CleanLiveResourceCacheInputVO inputVO) {
        try {
            List<String> pcsClearKeyList = new ArrayList<>();
            if (CollectionUtils.isEmpty(inputVO.getResourceCodeList())) { // 全部资源清理
                for (ResourceCodeConst codeConst : ResourceCodeConst.values()) {
                    String code = codeConst.name();
                    pcsClearKeyList.add(CacheConst.getCustomerAvailableCacheKey(inputVO.getCustomerId(), code));
                    pcsClearKeyList.add(CacheConst.getCustomerAvailableDetailCacheKey(inputVO.getCustomerId(), code));
                }
            } else { // 清理指定的资源
                for (String code : inputVO.getResourceCodeList()) {
                    pcsClearKeyList.add(CacheConst.getCustomerAvailableCacheKey(inputVO.getCustomerId(), code));
                    pcsClearKeyList.add(CacheConst.getCustomerAvailableDetailCacheKey(inputVO.getCustomerId(), code));
                }
            }
            long count = redisTemplate.delete(pcsClearKeyList);
            log.info("delete pcs key：{}，count：{}", pcsClearKeyList, count);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            dingWarnRobot.sendWarnMsg("【告警，清理资源缓存异常】",
                    String.format("inputVO=%s, exception=%s", inputVO, e.getMessage()));
        }
    }
    
    @Override
    public void cleanAllCacheByCustomerIdAndCode(String customerId, String code) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(code)) {
            return;
        }
        
        CleanLiveResourceCacheInputVO cleanInputVO = CleanLiveResourceCacheInputVO.builder()
                .customerId(customerId)
                .resourceCodeList(Collections.singletonList(code))
                .build();
        this.cleanLiveRestrictPcsResult(cleanInputVO);
        
        //集团主账号需要调一次清理结算缓存接口
        this.cleanPcsCache(cleanInputVO);
        // 清理点播用户缓存
        if (ResourceCodeConst.isVodResource(code)) {
            try {
                WrappedResponse cleanVodCacheResponse = this.cleanVodUserCache(customerId);
                if (ResponseUtil.isErrorResponse(cleanVodCacheResponse)) {
                    log.error("cleanVodUserCache error,customerId = {},result == {}", customerId,
                            cleanVodCacheResponse);
                }
            } catch (Exception e) {
                dingWarnRobot.sendWarnMsg("【告警，清理点播缓存异常】",
                        String.format("customerId=%s, exception=%s", customerId, e.getMessage()));
            }
            
        }
    }
    
    @Override
    public void cleanLiveUserCache(String customerId) {
        try {
            UserMsgVO userMsgVO = UserClient.getUserByUnionId(customerId);
            if (userMsgVO == null) {
                return;
            }
            cleanLiveCache(String.format("liveuser_%s", userMsgVO.getLiveUserId()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            dingWarnRobot.sendWarnMsg("【告警，清理用户缓存异常】",
                    String.format("customerId=%s, exception=%s", customerId, e.getMessage()));
        }
    }
    
    /**
     * 清理直播缓存
     */
    private void cleanLiveCache(String key) {
        try {
            String url = String.format("https://live.polyv.net/front/del-key?key=%s", key);
            String result = HttpClientUtil.getInstance().sendHttpGet(url);
            log.info("cleanLiveCache key:{},result:{}", key, result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            dingWarnRobot.sendWarnMsg("【告警，清理缓存异常】", String.format("key=%s, exception=%s", key, e.getMessage()));
        }
    }
    
    /**
     * 清理点播用户缓存
     * @param customerId 用户id, unionId
     */
    @Override
    public WrappedResponse cleanVodUserCache(String customerId) {
        if (!systemEnvService.isProdEnv() && !systemEnvService.isTestAllowUser(customerId)) {
            return WrappedResponse.success();
        }
        String accountType = userAccountService.getAccountType(customerId);
        boolean isGroupV2 = AccountTypeEnum.GROUP2.getCode().equals(accountType);
        //集团主账号直接返回
        if (isGroupV2) {
            return WrappedResponse.success();
        }
        CustomerSearchDTO searchDTO = new CustomerSearchDTO();
        searchDTO.setUnionIds(customerId);
        WrappedResponse<List<UserDetail>> response = unionUserClient.listCustomer(searchDTO);
        if (!response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
            log.error("找不到对应用户信息，customerId=" + customerId);
            return WrappedResponse.success();
        }
    
        VodUserCacheCleanDTO cleanDTO = new VodUserCacheCleanDTO();
        cleanDTO.setUserId(response.getData().get(0).getUserId());
        WrappedResponse<String> wrappedResponse = unionUserClient.cleanVodUserCache(cleanDTO);
        log.info("cleanVodUserCache customerId={},result={}", customerId, wrappedResponse);
        return wrappedResponse;
    }
    
    @Override
    public CommonResult asyncCleanLiveUserCache(String customerId) throws ClearingSystemException {
        if (StringUtils.isEmpty(customerId)) {
            return CommonResult.fail("asyncCleanLiveUserCache fail.customerId is empty");
        }
        CleanCacheEvent event = new CleanCacheEvent(CleanCacheEvent.class, "vod", customerId);
        try {
            applicationEventPublisher.publishEvent(event);
        } catch (Exception e) {
            log.error(this.getClass().getName(), e,
                    String.format("asyncCleanLiveUserCache exception[%s]", e.getMessage()));
            throw new ClearingSystemException(e);
        }
        return CommonResult.ok();
    }
    
    @Override
    public CommonResult asyncCleanVodUserCache(String customerId) throws ClearingSystemException {
        if (StringUtils.isEmpty(customerId)) {
            return CommonResult.fail("asyncCleanVodUserCache fail.customerId is empty");
        }
        CleanCacheEvent event = new CleanCacheEvent(CleanCacheEvent.class, "live", customerId);
        try {
            applicationEventPublisher.publishEvent(event);
        } catch (Exception e) {
            log.error(this.getClass().getName(), e,
                    String.format("asyncCleanVodUserCache exception[%s]", e.getMessage()));
            throw new ClearingSystemException(e);
        }
        return CommonResult.ok();
    }
    
    @Override
    public SaleDepartmentBaseAmount getBySaleUserId(String saleUserId) {
        HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
        Object baseAmount = null;
        try {
            baseAmount = opsForHash.get(GlobalConfigConst.SALE_DEPARTMENT_BASE_AMOUNT_KEY, saleUserId);
        } catch (Exception e) {
            return saleDepartmentBaseAmountRepository.findBySaleUserId(saleUserId);
        }
        //有定时任务单独同步mysql数据到redis，这里以防还没同步到redis的时候去做查询
        if (Objects.isNull(baseAmount)) {
            return saleDepartmentBaseAmountRepository.findBySaleUserId(saleUserId);
        }
        return JsonMapper.objectToBean(baseAmount, SaleDepartmentBaseAmount.class);
    }
    
    @Override
    public SystemDictionary getValueByDictTypeAndLabel(String dictType, String label) {
        String key = CacheConst.SYSTEM_DICT_LIST + dictType;
        List<SystemDictionary> sysDict = null;
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(key))) {
            String s = this.redisTemplate.opsForValue().get(key);
            if (StringUtils.isNotEmpty(s)) {
                sysDict = JSONUtil.toList(s, SystemDictionary.class);
            }
        } else {
            sysDict = systemDictionaryRepository.getByDictTypeCode(dictType);
            this.redisTemplate.opsForValue()
                    .set(key, JsonUtil.beanToString(sysDict).orElse(""), CacheConst.DEFAULT_CACHE_SECONDS,
                            TimeUnit.SECONDS);
        }
        if (CollectionUtils.isEmpty(sysDict)) {
            return null;
        }
        Optional<SystemDictionary> first = sysDict.stream()
                .filter(systemDictionary -> label.equals(systemDictionary.getLabel()))
                .findFirst();
        return first.orElse(null);
    }
    
    @Override
    public FinanceIncomeDetailEntity getCacheBeforeMonthIncomeByContractId(String contractId, Long itemId) {
        String key = String.format(GlobalConfigConst.INCOME_BEFORE_MONTH_KEY, contractId, itemId);
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            String json = redisTemplate.opsForValue().get(key);
            return JsonUtil.stringToBean(json, FinanceIncomeDetailEntity.class).orElse(null);
        }
        return null;
    }
    
    @Override
    public boolean setCacheBeforeMonthIncomeByContractId(FinanceIncomeDetailEntity entity) {
        String key = String.format(GlobalConfigConst.INCOME_BEFORE_MONTH_KEY, entity.getContractId(),
                entity.getBillingItemId().longValue());
        FinanceIncomeDetailEntity exist = getCacheBeforeMonthIncomeByContractId(entity.getContractId(),
                entity.getBillingItemId().longValue());
        if (Objects.isNull(exist) || exist.getIncomeMonth().compareTo(entity.getIncomeMonth()) < 0) {
            String s = JsonUtil.beanToString(entity).orElse("");
            redisTemplate.opsForValue().set(key, s, CacheConst.DEFAULT_CACHE_SECONDS, TimeUnit.SECONDS);
            return true;
        }
        return false;
    }
    
    @Override
    public void publishMessage(String topic, String message) {
        redisTemplate.opsForList().leftPush(topic, message);
    }
    
    @Override
    public void cacheGroupSubUser(String customerId) {
        String key = String.format(GlobalConfigConst.GROUP_SUB_USER_KEY, customerId);
        redisTemplate.opsForValue().set(key, customerId, CacheConst.DEFAULT_CACHE_SECONDS, TimeUnit.SECONDS);
    }
    
    @Override
    public boolean isGroupSubUser(String customerId) {
        String key = String.format(GlobalConfigConst.GROUP_SUB_USER_KEY, customerId);
        return StringUtils.isNotBlank(redisTemplate.opsForValue().get(key));
    }
    
    
    @Override
    public void setZeroUsers(List<String> users,String key) {
        if (!CollectionUtils.isEmpty(users)) {
            String join = StringUtils.join(users, Constant.COMMA);
            this.redisTemplate.opsForValue().set(key, join,CacheConst.DEFAULT_CACHE_SECONDS, TimeUnit.SECONDS);
        }
    }
    
    @Override
    public List<String> getSpaceZeroUsers() {
        String s = this.redisTemplate.opsForValue().get(GlobalConfigConst.SPACE_AVAILABLE_IS_ZERO);
        if (StringUtils.isNotEmpty(s)) {
            String[] split = s.split(Constant.COMMA);
            return Arrays.asList(split);
        }
        return new ArrayList<>();
    }
    @Override
    public List<String> getFlowZeroUsers() {
        String s = this.redisTemplate.opsForValue().get(GlobalConfigConst.FLOW_AVAILABLE_IS_ZERO);
        if (StringUtils.isNotEmpty(s)) {
            String[] split = s.split(Constant.COMMA);
            return Arrays.asList(split);
        }
        return new ArrayList<>();
    }
    
    @Override
    public String getPackageFlowSize() {
        String s = this.redisTemplate.opsForValue().get(GlobalConfigConst.FLOW_PACKAGE_AVAILABLE);
        return StringUtils.isEmpty(s) ? null : s;
    }
    
    @Override
    public void setPackageFlow(List<GetGrayUserResourceByZeroResponse> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String json = JsonUtil.beanToString(list).orElse("");
        this.redisTemplate.opsForValue()
                .set(GlobalConfigConst.FLOW_PACKAGE_AVAILABLE, json, CacheConst.DEFAULT_CACHE_SECONDS,
                        TimeUnit.SECONDS);
    }
    
    @Override
    public List<GetUserLast7DayConsumedResponse> getLastConsumedBy7(GetUserLast7DayConsumedRequest request) {
        String key = GlobalConfigConst.LAST_CONSUMED_DAY_7 + request.getResourceCode();
        String s = this.redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(s)) {
            return new ArrayList<>();
        }
        JSONArray objects = JSONUtil.parseArray(s);
        return JSONUtil.toList(objects, GetUserLast7DayConsumedResponse.class);
        
    }
    
    @Override
    public void setLastConsumedBy7(List<GetUserLast7DayConsumedResponse> consumed, String resourceCode) {
        if (CollectionUtils.isEmpty(consumed)) {
            return;
        }
        String key = GlobalConfigConst.LAST_CONSUMED_DAY_7 + resourceCode;
        String json = JsonUtil.beanToString(consumed).orElse("");
        log.info("key={},{} ={}",key,resourceCode,json);
        this.redisTemplate.opsForValue().set(key, json, CacheConst.DEFAULT_CACHE_SECONDS*3,
                TimeUnit.SECONDS);
    }
    
    @Override
    public String getPackageSpaceSize() {
        String s = this.redisTemplate.opsForValue().get(GlobalConfigConst.SPACE_AVAILABLE_PACKAGE);
        return StringUtils.isEmpty(s) ? null : s;
    }
    
    @Override
    public void setPackageSpace(List<GetGrayUserResourceByZeroResponse> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String json = JsonUtil.beanToString(list).orElse("");
        this.redisTemplate.opsForValue().set(GlobalConfigConst.SPACE_AVAILABLE_PACKAGE, json, spaceAvailablePackageExpireSecond, TimeUnit.SECONDS);
    }
    
    
    @Override
    public void setLiveBillingType(String unionId, String billingType) {
        if (StringUtils.isBlank(unionId) || StringUtils.isBlank(billingType)) {
            return;
        }
        String key = String.format(GlobalConfigConst.LIVE_BILLING_TYPE_KEY, unionId);
        this.redisTemplate.opsForValue().set(key, billingType, 60, TimeUnit.SECONDS);
    }
    
    @Override
    public String getLiveBillingType(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            return null;
        }
        String key = String.format(GlobalConfigConst.LIVE_BILLING_TYPE_KEY, unionId);
        return this.redisTemplate.opsForValue().get(key);
    }
    
    @Override
    public Map<String, Date> getPackageEarliestOpenTime(List<String> unionIds, String type) {
        if (CollectionUtils.isEmpty(unionIds) || StringUtils.isBlank(type)) {
            return null;
        }
        HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
        
        Map<String, Date> packageMap = new HashMap<>();
        unionIds.forEach(unionId -> {
            String key = getWaitOpenPackageKey(unionId, type);
            Object openTime = opsForHash.get(GlobalConfigConst.PACKAGE_EARLIEST_OPEN_TIME, key);
            if (Objects.nonNull(openTime)) {
                packageMap.put(unionId, DateFormatUtil.parseDateNormal(openTime.toString()));
            }
        });
        return packageMap;
    }
    
    @Override
    public void setPackageEarliestOpenTime(List<CustomerEffectiveScheduleJob> list) {
        //先删除原来hash数据
        redisTemplate.delete(GlobalConfigConst.PACKAGE_EARLIEST_OPEN_TIME);
    
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //lambda 遍历list，调用getPackage方法，转成List<CustomerWaitOpenPackageVO>
        List<CustomerWaitOpenPackageVO> packageVOList = list.stream()
                .map(this::getPackage)
                .collect(Collectors.toList());
        //遍历packageVOList，过滤掉type或openTime为空的数据，根据customerId，type两个参数分组,
        // 取openTime最小的数据，并转成List<CustomerWaitOpenPackageVO>
        List<CustomerWaitOpenPackageVO> collect = packageVOList.stream()
                .filter(vo -> StringUtils.isNotBlank(vo.getType()) && Objects.nonNull(vo.getOpenTime()))
                .collect(Collectors.groupingBy(vo -> vo.getCustomerId() + vo.getType(),
                        Collectors.minBy((o1, o2) -> o1.getOpenTime().compareTo(o2.getOpenTime()))))
                .values()
                .stream()
                .map(Optional::get)
                .collect(Collectors.toList());
    
        //对collect进行非空判断，遍历collect，customerId和type作为redis的key，openTime为value，存入redis
        if (!CollectionUtils.isEmpty(collect)) {
            HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
            //遍历collect，customerId和type作为redis的key，openTime作为value，存入redis
            Map<String, String> map = collect.stream()
                    .collect(Collectors.toMap(vo -> getWaitOpenPackageKey(vo.getCustomerId(), vo.getType()),
                            CustomerWaitOpenPackageVO::getOpenTime));
            opsForHash.putAll(GlobalConfigConst.PACKAGE_EARLIEST_OPEN_TIME, map);
        
        }
    
    
    }
    
    
    /**
     * 组装待开通套餐redis key
     * @param customerId : unionId
     * @param type :套餐类型
     * @return
     */
    private String getWaitOpenPackageKey(String customerId, String type) {
        return customerId + ":" + type;
    }
    
    public CustomerWaitOpenPackageVO getPackage(CustomerEffectiveScheduleJob job) {
        Integer type = job.getType();
        CustomerWaitOpenPackageVO packageVO = CustomerWaitOpenPackageVO.builder().build();
        packageVO.setCustomerId(job.getCustomerId());
        packageVO.setOpenTime(DateFormatUtil.formatDateNormal(job.getEffectiveTime()));
        //金额
        if (ScheduleJobTypeConst.deposit.getType() == type) {
            packageVO.setType(PackageCategoryConst.amount.name());
            
        }
        //纷享销客业务开通
        if (ScheduleJobTypeConst.business_open.getType() == type) {
            setBusinessOpenPackageCategory(packageVO, job);
        }
        //客户后台金额购买并发套餐
        if (ScheduleJobTypeConst.concurrence_deposit_with_amount.getType() == type) {
            packageVO.setType(PackageCategoryConst.live.name());
        }
        //定制化集团分账号套餐开通
        if (ScheduleJobTypeConst.customized_account_deposit.getType() == type) {
            setCustomizedAccountDepositPackageCategory(packageVO, job);
        }
        
        return packageVO;
        
    }
    
    //定制化集团分账号套餐开通
    void setCustomizedAccountDepositPackageCategory(CustomerWaitOpenPackageVO packageVO,
            CustomerEffectiveScheduleJob job) {
        DepositResourceDTO inputVO = JSON.parseObject(job.getExt(), DepositResourceDTO.class);
        DepositResourceRequest depositResourceRequest = inputVO.getDepositResourceRequest();
        if (Objects.nonNull(depositResourceRequest.getLiveConcurrenceRequest()) ||
                Objects.nonNull(depositResourceRequest.getLiveDurationRequest())) {
            packageVO.setType(PackageCategoryConst.live.name());
        }
        if (Objects.nonNull(depositResourceRequest.getAmountRequest())) {
            packageVO.setType(PackageCategoryConst.amount.name());
        }
    }
    
    //纷享销客业务开通
    void setBusinessOpenPackageCategory(CustomerWaitOpenPackageVO packageVO, CustomerEffectiveScheduleJob job) {
        BusinessOperationInputVO inputVO = JSON.parseObject(job.getExt(), BusinessOperationInputVO.class);
        BusinessOperationCheckResult checkResult = inputVO.getCheckResult();
        SalesOpportunities salesOpportunities = checkResult.getSalesOpportunities();
        if (Objects.nonNull(salesOpportunities)) {
            String billingPlanCode = salesOpportunities.getBillingPlanCode();
            if (BusinessBillingPlanCodeEnum.AMOUNT.getCode().equals(billingPlanCode)) {
                packageVO.setType(PackageCategoryConst.amount.name());
            }
    
            if (BusinessBillingPlanCodeEnum.LIVE_DURATION.getCode().equals(billingPlanCode) ||
                    BusinessBillingPlanCodeEnum.LIVE_CONCURRENT.getCode().equals(billingPlanCode)) {
                packageVO.setType(PackageCategoryConst.live.name());
            }
            
            //点播套餐
            if (BusinessBillingPlanCodeEnum.VOD_PACKAGE.getCode().equals(billingPlanCode)) {
                packageVO.setType(PackageCategoryConst.vod.name());
            }
        }
    }
    
    @Override
    public void setVodSpaceTrafficAvailable(List<VodPackageVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //构造Map<String, String>对象，key为String.format(GlobalConfigConst.VOD_SPACE_TRAFFIC_AVAILABLE, vodPackageVo
        // .getUnionId()))，value为JsonUtil.stringToBean(jsonString, VodPackageVo.class)
        Map<String, String> map = list.stream()
                .collect(Collectors.toMap(vodPackageVo -> String.format(GlobalConfigConst.VOD_SPACE_TRAFFIC_AVAILABLE,
                        vodPackageVo.getUnionId()), vodPackageVo -> JsonUtil.beanToString(vodPackageVo).orElse("")));
        //遍历map，将map中的key和value存入redis，设置过期时间为12小时 + 随机秒数
        map.forEach((key, value) -> {
            this.redisTemplate.opsForValue()
                    .set(key, value, 12 * CacheConst.DEFAULT_CACHE_SECONDS + CacheConst.getRandomSeconds(),
                            TimeUnit.SECONDS);
        });
    }
    
    
    @Override
    public List<VodPackageVo> getVodSpaceTrafficAvailable(List<String> unionIds) {
        if (CollectionUtils.isEmpty(unionIds)) {
            return null;
        }
        List<VodPackageVo> list = new ArrayList<>();
        List<String> notExistsUnionIds = new ArrayList<>();
        //遍历unionIds，组装key，然后从缓存获取数据，将数据转换为VodPackageVo对象，添加到list中
        unionIds.forEach(unionId -> {
            String key = String.format(GlobalConfigConst.VOD_SPACE_TRAFFIC_AVAILABLE, unionId);
            String jsonString = this.redisTemplate.opsForValue().get(key);
            if (StringUtils.isNotBlank(jsonString)) {
                list.add(JsonUtil.stringToBean(jsonString, VodPackageVo.class).orElse(null));
            }
            //从缓存查不到数据则从点播接口获取
            else {
                log.info("从缓存查询不到点播资源可用量的用户：{}", unionId);
                notExistsUnionIds.add(unionId);
            }
        });
        
        if (!CollectionUtils.isEmpty(notExistsUnionIds)) {
            List<VodPackageVo> vodList = vodResourceService.getVodSyncData(
                    String.join(Constant.COMMA, notExistsUnionIds));
            if (!CollectionUtils.isEmpty(vodList)) {
                list.addAll(vodList);
            }
        }
        
        return list;
    }
    
    @Override
    public void setConcurrenceLimit(String liveUserId, Integer limit) {
        if (StringUtils.isBlank(liveUserId) || Objects.isNull(limit)) {
            return;
        }
        String key = String.format(GlobalConfigConst.LIVE_CONCURRENCE_LIMIT_KEY, liveUserId);
        this.redisTemplate.opsForValue().set(key, limit.toString(), 60, TimeUnit.SECONDS);
        
    }
    
    @Override
    public Integer getConcurrenceLimit(String liveUserId) {
        if (StringUtils.isBlank(liveUserId)) {
            return null;
        }
        
        String key = String.format(GlobalConfigConst.LIVE_CONCURRENCE_LIMIT_KEY, liveUserId);
        String limitStr = this.redisTemplate.opsForValue().get(key);
        
        if (StringUtils.isBlank(limitStr)) {
            return null;
        }
    
        try {
            return Integer.parseInt(limitStr);
        } catch (NumberFormatException e) {
            // Handle parsing error gracefully
            return null;
        }
    }
    
    
    @Override
    public boolean isCustomerDeal(String unionId) {
        String key = String.format(GlobalConfigConst.IS_CUSTOMER_DEAL_KEY, unionId);
        Object value = redisTemplate.opsForValue().get(key);
        if (Objects.nonNull(value)) {
            Integer dealStatus = Integer.parseInt(value.toString());
            return dealStatus > 1;
        } else {
            ResponseVO<String> responseVO = customerAttributesApi.getCustomerDealStatus(unionId);
            if (responseVO.isSuccess() && StringUtils.isNotBlank(responseVO.getData())) {
                Integer dealStatus = Integer.parseInt(responseVO.getData());
                redisTemplate.opsForValue().set(key, responseVO.getData(), 300, TimeUnit.SECONDS);
                return dealStatus > 1;
            }
        }
        return false;
    }
}
