package net.polyv.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.polyv.dao.primary.ResourceDepositExpireRecordRepository;
import net.polyv.model.entity.primary.ResourceDepositExpireRecord;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.ResourceExpireRecordAddRequest;
import net.polyv.modules.pcs.api.req.ResourceExpireRecordGetRequest;
import net.polyv.modules.pcs.api.stereotype.PCSErrorCodeEnum;
import net.polyv.modules.pcs.api.vo.ResourceExpireRecordAddVO;
import net.polyv.modules.pcs.api.vo.ResourceExpireRecordResultVO;
import net.polyv.service.ResourceDepositExpireRecordService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.JsonMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ResourceDepositExpireRecordServiceImpl implements ResourceDepositExpireRecordService {

    @Autowired
    private ResourceDepositExpireRecordRepository resourceDepositExpireRecordRepository;


    @Override
    public boolean add(ResourceExpireRecordAddRequest request) {
        try {

            List<ResourceExpireRecordAddVO> list = request.getResourceExpireRecordAddVOList();
            List<ResourceDepositExpireRecord> result = Lists.newArrayList();
            for(ResourceExpireRecordAddVO vo : list){
                ResourceDepositExpireRecord expireRecord = new ResourceDepositExpireRecord();
                expireRecord.setCustomerId(request.getCustomerId());
                expireRecord.setGroupId(request.getGroupId());
                expireRecord.setCreateTime(new Date());
                expireRecord.setEffectiveDate(DateFormatUtil.parseDateNormal(vo.getEffectiveDate()));
                expireRecord.setBusinessType(vo.getBusinessType());
                expireRecord.setExpireDate(DateFormatUtil.parseDateNormal(vo.getExpireDate()));
                expireRecord.setAmount(vo.getAmount());
                result.add(expireRecord);
            }
            if(!CollectionUtils.isEmpty(result)){
                resourceDepositExpireRecordRepository.saveAll(result);
            }
        }
        catch (Exception e){
            log.error("ResourceDepositExpireRecordServiceImpl add error",e);
            return false;
        }
        return true;
    }

    @Override
    public ResponseVO<Map<String, List<ResourceExpireRecordResultVO>>> getExpireRecord(ResourceExpireRecordGetRequest request) {
        Map<String, List<ResourceExpireRecordResultVO>> result = Maps.newHashMap();
        if(StringUtils.isNotBlank(request.getCustomerIds())){
            String[] customerIdArray = request.getCustomerIds().split(",");
            if(customerIdArray.length > 10){
                return ResponseVO.failure(PCSErrorCodeEnum.CUSTOMER_COUNT_OVER_10);
            }
            for(String customerId : customerIdArray){
                List<ResourceExpireRecordResultVO> expireRecords = resourceDepositExpireRecordRepository.getExpireRecords(customerId);
                result.put(customerId,expireRecords);
            }

        }
        return ResponseVO.success(result);
    }
}
