package net.polyv.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.balance.BalanceChangeTypeConst;
import net.polyv.constant.billingdaily.CustomerBillingDailyStatusEnum;
import net.polyv.constant.billingdaily.CustomerBillingDailyTradeTypeConst;
import net.polyv.constant.credit.CreditAlterationTypeConst;
import net.polyv.dao.primary.AccountRechargeRecordRepository;
import net.polyv.dao.primary.CreditAlterationRecordRepository;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.model.data.balance.BalanceInsufficientMessageDTO;
import net.polyv.model.data.balance.BalanceInsufficientNotifyResult;
import net.polyv.model.data.credit.CreditInsufficientMessageDTO;
import net.polyv.model.data.credit.CreditInsufficientNotifyResult;
import net.polyv.model.data.deposit.BalanceChangeAmountDO;
import net.polyv.model.entity.primary.CreditAlterationRecord;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.service.AccountDepositService;
import net.polyv.service.CreditService;
import net.polyv.util.MoneyUtil;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.concurrence.AccountInsufficientRequest;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 授信额度相关操作
 *
 * <AUTHOR>
 * @since 2020/5/14
 */
@Slf4j
@Service
public class CreditServiceImpl implements CreditService {

    @Autowired
    private AccountDepositService accountDepositService;

    @Autowired
    private CreditAlterationRecordRepository creditAlterationRecordRepository;

    /**
     * 用户每日账单DAO
     */
    @Autowired
    private CustomerBillingDailyRepository customerBillingDailyRepository;

    /**
     * 账户充值记录DAO
     */
    @Autowired
    private AccountRechargeRecordRepository accountRechargeRecordRepository;

    /**
     * 定额阈值
     */
    public static final String QUOTA_THRESHOLD = "quotaThreshold";

    /**
     * 天数阈值
     */
    public static final String DAYS_THRESHOLD = "daysThreshold";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public long reduceCredit(BalanceChangeAmountDO amountDO) {
        // 计算实际扣除金额
        long availableCredit = accountDepositService.calcCustomerValidCredit(
                CustomerInfoGetInputVO.builder().customerId(amountDO.getCustomerId()).build());
        long currentReduce = Math.min(amountDO.getAmount(), availableCredit);

        // 写入授信额度扣除记录
        CreditAlterationRecord creditAlterationRecord = new CreditAlterationRecord();
        creditAlterationRecord.setCustomerId(amountDO.getCustomerId());
        creditAlterationRecord.setAmount(currentReduce);
        creditAlterationRecord.setStatAt(new Date());
        creditAlterationRecord.setType(BalanceChangeTypeConst.FREEZE.equals(amountDO.getReduceType())
                ? CreditAlterationTypeConst.FREEZE : CreditAlterationTypeConst.DEDUCTION);
        creditAlterationRecordRepository.save(creditAlterationRecord);

        return currentReduce;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public long unfreezeCredit(BalanceChangeAmountDO amountDO) {
        // 写入授信额度解冻记录
        CreditAlterationRecord creditAlterationRecord = new CreditAlterationRecord();
        creditAlterationRecord.setCustomerId(amountDO.getCustomerId());
        creditAlterationRecord.setAmount(amountDO.getAmount());
        creditAlterationRecord.setStatAt(new Date());
        creditAlterationRecord.setType(CreditAlterationTypeConst.UNFREEZE);
        creditAlterationRecordRepository.save(creditAlterationRecord);

        return amountDO.getAmount();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public long reduceFreezeCredit(BalanceChangeAmountDO amountDO) {

        // 解冻
        this.unfreezeCredit(amountDO);

        // 扣除
//        BalanceChangeAmountDO reduceChangeAmountDO = new BalanceChangeAmountDO();
//        BeanUtils.copyProperties(amountDO, reduceChangeAmountDO);
//        reduceChangeAmountDO.setReduceType(BalanceChangeTypeConst.REDUCE);
//        this.reduceCredit(amountDO);

        return amountDO.getAmount();
    }

    @Override
    public CreditInsufficientNotifyResult creditInsufficientRemind(AccountInsufficientRequest ciq) {
        CreditInsufficientNotifyResult cinr = new CreditInsufficientNotifyResult();
        List<CreditInsufficientMessageDTO> quotaResultList = new ArrayList<>();
        List<CreditInsufficientMessageDTO> daysResultList = new ArrayList<>();
        BigDecimal amountThreshold = Objects.nonNull(ciq.getAmountThreshold()) ? new BigDecimal(ciq.getAmountThreshold()) : null;
        Integer daysThreshold = ciq.getDaysThreshold();
        int cbdcPageNum = 0; // 从0开始
        int cbdcPageSize = 100;
        int cbdcTotalPage = 0;
        int loopCount = 0;
        int maxLoopCount = 1000;
        Page<Map<String, Object>> dbdcPagingResult;
        do {
            // 查询近7日账单，交易类型【金额结算】，账单状态为【已出账】的数据
            dbdcPagingResult = this.customerBillingDailyRepository.queryCbdPagingBySpecifyDate(
                    CustomerBillingDailyTradeTypeConst.amount_clearing.getTradeType(),
                    CustomerBillingDailyStatusEnum.BILLED.getStatus(),
                    PageRequest.of(cbdcPageNum++, cbdcPageSize, Sort.by("customerId").ascending()));
            if (cbdcTotalPage == 0) {
                cbdcTotalPage = dbdcPagingResult.getTotalPages();
            }
            List<Map<String, Object>> cbdcResultList = dbdcPagingResult.getContent();
            if (cbdcResultList.size() > 0) {
                for (Map<String, Object> cbdc : cbdcResultList) {
                    CreditInsufficientMessageDTO messageDTO = assembleCreditMessageData(cbdc, amountThreshold, daysThreshold);
                    if (Objects.nonNull(messageDTO)) {
                        if (messageDTO.getThresholdType().equals(QUOTA_THRESHOLD)) {
                            quotaResultList.add(messageDTO);
                        } else if (messageDTO.getThresholdType().equals(DAYS_THRESHOLD)) {
                            daysResultList.add(messageDTO);
                        }
                    }
                }
            }
        } while (cbdcPageNum < cbdcTotalPage && loopCount++ < maxLoopCount);
        // 如果集合不为空就组合额外信息并返回
        if (quotaResultList.size() > 0) {
            cinr.setQuotaThresholdList(formatCreditInsufficientMessageResult(quotaResultList));
        }
        if (daysResultList.size() > 0) {
            cinr.setDaysThresholdList(formatCreditInsufficientMessageResult(daysResultList));
        }
        return cinr;
    }

    /**
     * 组织实体数据
     *
     * @param cbdc            账单对象
     * @param amountThreshold 金额阈值
     * @param daysThreshold   天数阈值
     * @return 结果
     */
    private CreditInsufficientMessageDTO assembleCreditMessageData(Map<String, Object> cbdc, BigDecimal amountThreshold, Integer daysThreshold) {
        String customerId = null;
        BigDecimal consumeQuantity = null;
        Long consumeDays = null;
        try {
            customerId = String.valueOf(cbdc.get("customerId"));
            consumeQuantity = (BigDecimal) cbdc.get("consumeQuantity"); // 近7日用量
            consumeDays = Long.parseLong(String.valueOf(cbdc.get("consumeDays"))); // 近7日使用天数
        } catch (ClassCastException cce) {
            log.error(this.getClass().getName(), cce, String.format("assembleMessageData-ClassCastException|" +
                    "(String)customerId|(BigDecimal)consumeQuantity|(Long)consumeDays.actual[%s]", JSON.toJSON(cbdc)));
        }
        if (Objects.isNull(consumeQuantity) || Objects.isNull(consumeDays) || consumeDays < 1) {
            log.error(this.getClass().getName(), "assembleMessageData-consumeQuantity|consumeDays|consumeDays is null");
            return null;
        }
        // 日均用量 = 近 7 天用量/有用量的天数
        BigDecimal dailyAverage = consumeQuantity.divide(new BigDecimal(consumeDays), 2, BigDecimal.ROUND_HALF_UP);
        // 查询可用授信额度

        long availableCredit = this.accountDepositService.calcCustomerValidCreditWithoutTotalZero(new CustomerInfoGetInputVO(customerId));
        if (availableCredit == -1) {
            return null;
        }
        BigDecimal availableCreditBd = new BigDecimal(availableCredit);
        // 剩余阈值判断
        BigDecimal creditBelowThreshold = creditThresholdJudge(availableCredit, amountThreshold);
        CreditInsufficientMessageDTO cim = new CreditInsufficientMessageDTO();
        cim.setCustomerId(customerId);
        boolean validFlag = false;
        if (Objects.nonNull(creditBelowThreshold)) {
            cim.setQuotaThreshold(creditBelowThreshold.toPlainString());
            cim.setThresholdType(QUOTA_THRESHOLD);
            validFlag = true;
        }
        // 剩余可用天数 = 剩余授信额度 / 日均用量
        BigDecimal surplusDays = availableCreditBd.divide(dailyAverage, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal surplusDaysBelowThreshold = surplusDaysThresholdJudge(surplusDays, daysThreshold);
        if (Objects.nonNull(surplusDaysBelowThreshold)) {
            cim.setQuotaThreshold(MoneyUtil.getHumanMoney(availableCredit, 2));
            cim.setDaysThreshold(String.valueOf(surplusDaysBelowThreshold.intValue()));
            cim.setThresholdType(DAYS_THRESHOLD);
            validFlag = true;
        }
        if (validFlag) {
            return cim;
        }
        return null;
    }

    /**
     * 组织实体数据
     *
     * @param cbdc                       账单对象
     * @param customerAvailableAmountMap 用户余额map
     * @param amountThreshold            金额阈值
     * @param daysThreshold              天数阈值
     * @return 结果
     */
    private BalanceInsufficientMessageDTO assembleBalanceMessageData(Map<String, Object> cbdc,
                                                                     Map<String, Long> customerAvailableAmountMap,
                                                                     BigDecimal amountThreshold, Integer daysThreshold) {
        String customerId = null;
        BigDecimal consumeQuantity = null;
        Long consumeDays = null;
        try {
            customerId = String.valueOf(cbdc.get("customerId"));
            consumeQuantity = (BigDecimal) cbdc.get("consumeQuantity"); // 近7日用量
            consumeDays = Long.parseLong(String.valueOf(cbdc.get("consumeDays"))); // 近7日使用天数
        } catch (ClassCastException cce) {
            log.error(this.getClass().getName(), cce, String.format("assembleBalanceMessageData-ClassCastException|" +
                    "(String)customerId|(BigDecimal)consumeQuantity|(Long)consumeDays.actual[%s]", JSON.toJSON(cbdc)));
        }
        if (Objects.isNull(consumeQuantity) || Objects.isNull(consumeDays) || consumeDays < 1) {
            log.error(this.getClass().getName(), "assembleMessageData-consumeQuantity|consumeDays|consumeDays is null");
            return null;
        }
        // 日均用量 = 近 7 天用量/有用量的天数
        BigDecimal dailyAverage = consumeQuantity.divide(new BigDecimal(consumeDays), 2, BigDecimal.ROUND_HALF_UP);
        // 查询可用余额额度
        Long availableBalance = customerAvailableAmountMap.get(customerId);
        if (Objects.isNull(availableBalance)) {
            return null;
        }
        BigDecimal availableBalanceBd = new BigDecimal(availableBalance);
        // 剩余阈值判断
        BigDecimal balanceBelowThreshold = creditThresholdJudge(availableBalance, amountThreshold);
        BalanceInsufficientMessageDTO bim = new BalanceInsufficientMessageDTO();
        bim.setCustomerId(customerId);
        boolean validFlag = false;
        if (Objects.nonNull(balanceBelowThreshold)) {
            bim.setBalanceThreshold(balanceBelowThreshold.toPlainString());
            bim.setThresholdType(QUOTA_THRESHOLD);
            validFlag = true;
        }
        // 剩余可用天数 = 余额 / 日均用量
        BigDecimal surplusDays = availableBalanceBd.divide(dailyAverage, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal surplusDaysBelowThreshold = surplusDaysThresholdJudge(surplusDays, daysThreshold);
        if (Objects.nonNull(surplusDaysBelowThreshold)) {
            bim.setBalanceThreshold(MoneyUtil.getHumanMoney(availableBalance, 2));
            bim.setDaysThreshold(String.valueOf(surplusDaysBelowThreshold.intValue()));
            bim.setThresholdType(DAYS_THRESHOLD);
            validFlag = true;
        }
        if (validFlag) {
            return bim;
        }
        return null;
    }

    /**
     * 组装结果集合的额外信息
     *
     * @param cimList cimList
     * @return 携带额外信息的结果集
     */
    private List<CreditInsufficientMessageDTO> formatCreditInsufficientMessageResult(List<CreditInsufficientMessageDTO> cimList) {
        // 查询公司信息
        Map<String, UserMsgVO> userMsgMap = new HashMap<>();
        List<String> insufficientCustomerIdList = cimList.stream().map(CreditInsufficientMessageDTO::getCustomerId)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (insufficientCustomerIdList.size() > 0) {
            List<UserMsgVO> overstepUserList = UserClient.getUsersByUnionIds(StringUtils.join(insufficientCustomerIdList, ","));
            userMsgMap = overstepUserList.stream().filter(f -> StringUtils.isNotEmpty(f.getUnionId()))
                    .collect(Collectors.toMap(UserMsgVO::getUnionId, Function.identity(), (o, n) -> n, HashMap::new));
        }
        Iterator<CreditInsufficientMessageDTO> cimIt = cimList.iterator();
        while (cimIt.hasNext()) {
            CreditInsufficientMessageDTO cim = cimIt.next();
            String currentCustomerId = cim.getCustomerId();
            UserMsgVO trUserMsg = userMsgMap.get(currentCustomerId);
            // 产品要求 要过滤 isGroup = Y 的
            if (Objects.nonNull(trUserMsg) && StringUtils.isNotEmpty(trUserMsg.getIsGroup())
                    && trUserMsg.getIsGroup().trim().equals("Y")) {
                cimIt.remove();
            }
            if (Objects.nonNull(trUserMsg)) {
                cim.setCompany(trUserMsg.getCompanyName());
                cim.setAccount(trUserMsg.getEmail());
            }
            StringBuilder contentSb = new StringBuilder();
            contentSb.append("【账户授信额度不足】\n");
            if (StringUtils.isNotEmpty(cim.getCompany())) {
                contentSb.append(String.format("公司名：%s \n", cim.getCompany()));
            }
            if (StringUtils.isNotEmpty(cim.getAccount())) {
                contentSb.append(String.format("账号：%s \n", cim.getAccount()));
            }
            contentSb.append("说明：账户");
            if (Objects.nonNull(cim.getQuotaThreshold())) {
                contentSb.append(String.format("授信已不足 %s 元，", cim.getQuotaThreshold()));
            }
            if (Objects.nonNull(cim.getDaysThreshold())) {
                contentSb.append(String.format("预计还够使用 %s 天，", cim.getDaysThreshold()));
            }
            contentSb.append("请及时联系客户充值");
            cim.setContent(contentSb.toString());
        }
        return cimList;
    }

    /**
     * 组装结果集合的额外信息
     *
     * @param bimList bimList
     * @return 携带额外信息的结果集
     */
    private List<BalanceInsufficientMessageDTO> formatBalanceInsufficientMessageResult(List<BalanceInsufficientMessageDTO> bimList) {
        // 查询公司信息
        Map<String, UserMsgVO> userMsgMap = new HashMap<>();
        List<String> insufficientCustomerIdList = bimList.stream().map(BalanceInsufficientMessageDTO::getCustomerId)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (insufficientCustomerIdList.size() > 0) {
            List<UserMsgVO> overstepUserList = UserClient.getUsersByUnionIds(StringUtils.join(insufficientCustomerIdList, ","));
            userMsgMap = overstepUserList.stream().filter(f -> StringUtils.isNotEmpty(f.getUnionId()))
                    .collect(Collectors.toMap(UserMsgVO::getUnionId, Function.identity(), (o, n) -> n, HashMap::new));
        }
        Iterator<BalanceInsufficientMessageDTO> bimIt = bimList.iterator();
        while (bimIt.hasNext()) {
            BalanceInsufficientMessageDTO bim = bimIt.next();
            String currentCustomerId = bim.getCustomerId();
            UserMsgVO trUserMsg = userMsgMap.get(currentCustomerId);
            // 产品要求 要过滤 isGroup = Y 的
            if (Objects.nonNull(trUserMsg) && StringUtils.isNotEmpty(trUserMsg.getIsGroup())
                    && trUserMsg.getIsGroup().trim().equals("Y")) {
                bimIt.remove();
            }
            if (Objects.nonNull(trUserMsg)) {
                bim.setCompany(trUserMsg.getCompanyName());
                bim.setAccount(trUserMsg.getEmail());
            }
            StringBuilder contentSb = new StringBuilder();
            contentSb.append("【账户余额不足】\n");
            if (StringUtils.isNotEmpty(bim.getCompany())) {
                contentSb.append(String.format("公司名：%s \n", bim.getCompany()));
            }
            if (StringUtils.isNotEmpty(bim.getAccount())) {
                contentSb.append(String.format("账号：%s \n", bim.getAccount()));
            }
            contentSb.append("说明：账户");
            if (Objects.nonNull(bim.getBalanceThreshold())) {
                contentSb.append(String.format("余额已不足 %s 元，", bim.getBalanceThreshold()));
            }
            if (Objects.nonNull(bim.getDaysThreshold())) {
                contentSb.append(String.format("预计还够使用 %s 天。", bim.getDaysThreshold()));
            }
            bim.setContent(contentSb.toString());
        }
        return bimList;
    }

    /**
     * 授信额度阈值判断
     * 授信额度不足10,000元、1,000元、500元、100元、0元时触发提醒
     *
     * @param input 输入
     * @return 阈值
     */
    private BigDecimal creditThresholdJudge(Long input, BigDecimal amountThreshold) {
        if (Objects.isNull(input) || Objects.isNull(amountThreshold)) {
            return null;
        }
        BigDecimal inputBd = new BigDecimal(MoneyUtil.getHumanMoney(input, 2));
        // 转换
        if (inputBd.compareTo(amountThreshold) <= 0) {
            return inputBd;
        }
        return null;
    }

    /**
     * 剩余可用天数判断
     * 剩余可用天数 ≤15，≤7，≤5，≤3，≤2，≤1
     *
     * @param input 输入
     * @return 阈值
     */
    private BigDecimal surplusDaysThresholdJudge(BigDecimal input, Integer daysThreshold) {
        if (Objects.isNull(input) || Objects.isNull(daysThreshold)) {
            return null;
        }
        BigDecimal daysThresholdBd = new BigDecimal(daysThreshold);
        if (input.compareTo(daysThresholdBd) <= 0) {
            return input;
        }
        return null;
    }


    @Override
    public BalanceInsufficientNotifyResult balanceInsufficientRemind(AccountInsufficientRequest biq) {
        BalanceInsufficientNotifyResult binr = new BalanceInsufficientNotifyResult();
        List<BalanceInsufficientMessageDTO> quotaResultList = new ArrayList<>();
        List<BalanceInsufficientMessageDTO> daysResultList = new ArrayList<>();
        BigDecimal amountThreshold = Objects.nonNull(biq.getAmountThreshold()) ? new BigDecimal(biq.getAmountThreshold()) : null;
        Integer daysThreshold = biq.getDaysThreshold();
        int cbdcPageNum = 0; // 从0开始
        int cbdcPageSize = 100;
        int cbdcTotalPage = 0;
        int loopCount = 0;
        int maxLoopCount = 1000;
        Page<Map<String, Object>> dbdcPagingResult;
        do {
            // 查询近7日账单，交易类型【金额结算】，账单状态为【已出账】的数据
            dbdcPagingResult = this.customerBillingDailyRepository.queryCbdPagingBySpecifyDate(
                    CustomerBillingDailyTradeTypeConst.amount_clearing.getTradeType(),
                    CustomerBillingDailyStatusEnum.BILLED.getStatus(),
                    PageRequest.of(cbdcPageNum++, cbdcPageSize, Sort.by("customerId").ascending()));
            if (cbdcTotalPage == 0) {
                cbdcTotalPage = dbdcPagingResult.getTotalPages();
            }
            List<Map<String, Object>> cbdcResultList = dbdcPagingResult.getContent();
            if (cbdcResultList.size() > 0) {
                // 获取用户ID集合
                List<String> customerIdList = cbdcResultList.stream().filter(f -> Objects.nonNull(f.get("customerId")))
                        .map(m -> String.valueOf(m.get("customerId"))).distinct().collect(Collectors.toList());
                // 查询用户可用金额
                List<Map<String, Object>> customerAvailableQueryMap =
                        this.accountRechargeRecordRepository.queryCustomerAvailableAmount(customerIdList);
                // 扁平化结果集结构
                Map<String, Long> customerAvailableAmountMap = customerAvailableQueryMap.stream()
                        .filter(f -> Objects.nonNull(f.get("customerId")) && Objects.nonNull(f.get("available")))
                        .collect(Collectors.toMap(c1 -> String.valueOf(c1.get("customerId")),
                                c2 -> (Long) c2.get("available"), (o, n) -> n, HashMap::new));
                for (Map<String, Object> cbdc : cbdcResultList) {
                    BalanceInsufficientMessageDTO messageDTO = assembleBalanceMessageData(cbdc, customerAvailableAmountMap,
                            amountThreshold, daysThreshold);
                    if (Objects.nonNull(messageDTO)) {
                        if (messageDTO.getThresholdType().equals(QUOTA_THRESHOLD)) {
                            quotaResultList.add(messageDTO);
                        } else if (messageDTO.getThresholdType().equals(DAYS_THRESHOLD)) {
                            daysResultList.add(messageDTO);
                        }
                    }
                }
            }
        } while (cbdcPageNum < cbdcTotalPage && loopCount++ < maxLoopCount);
        // 如果集合不为空就组合额外信息并返回
        if (quotaResultList.size() > 0) {
            binr.setQuotaThresholdList(formatBalanceInsufficientMessageResult(quotaResultList));
        }
        if (daysResultList.size() > 0) {
            binr.setDaysThresholdList(formatBalanceInsufficientMessageResult(daysResultList));
        }
        return binr;
    }
}
