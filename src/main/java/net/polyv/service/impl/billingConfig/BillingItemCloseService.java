package net.polyv.service.impl.billingConfig;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.billing.BillingWayConfigCodeEnum;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.dao.primary.billingConfig.BillingWayConfigRepository;
import net.polyv.model.data.salesopportunities.UserFunctionDO;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.model.entity.primary.billingconfig.BillingWayConfig;
import net.polyv.modules.common.constant.Constant;
import net.polyv.rest.client.live.FunctEnableClient;
import net.polyv.service.business.BusinessFunctionService;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;

/**
 * 计费项关闭服务
 * <AUTHOR>
 * @date 2022/11/11 14:56
 */
@Slf4j
@Component
public class BillingItemCloseService {
    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;
    @Resource
    private FunctEnableClient functEnableClient;
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private BusinessFunctionService businessFunctionService;
    @Resource
    private BillingItemRepository billingItemRepository;
    @Resource
    private BillingWayConfigRepository billingWayConfigRepository;
    @Value("${pcs.config.ruleUseTimeWayId}")
    private Integer ruleUseTimeWayId;
    
    /**
     * 只针对按使用时长计费的计费项，过期才需要关闭功能开关
     * @param currentDay
     */
    @Transactional(rollbackFor = Exception.class)
    public void closeFunction(Date currentDay) {
        String currentDayStr = DateUtil.formatDate(currentDay);
        //获取按时长计费的计费项配置
        BillingWayConfig billingWayConfig = billingWayConfigRepository.findByCode(
                BillingWayConfigCodeEnum.BY_DURATION.getCode());
        if (Objects.isNull(billingWayConfig)) {
            return;
        }
        List<BillingCustomerConfig> customerConfigs =
                billingCustomerConfigRepository.findByIsDeletedAndExpireTimeLessThanAndBillingWayId(
                PcsEnumConst.NO.getValue(), currentDay, billingWayConfig.getId());
        if (CollectionUtils.isEmpty(customerConfigs)) {
            return;
        }
        Map<String, List<UserFunctionDO>> maps = buildCloseFunctions(customerConfigs);
        if (CollectionUtils.isEmpty(maps)) {
            return;
        }
        maps.forEach((customerId, list) -> {
            try {
                if (CollectionUtils.isEmpty(list)){
                    return;
                }
                this.functEnableClient.enableFunctAccess(null, list, customerId, null);
            } catch (Exception e) {
                log.error("关闭客户-unionId:{},date={},失败！", customerId, currentDayStr);
                String format = String.format("客户id：%s", customerId);
                String format1 = String.format("关闭日期：%s", currentDayStr);
                String format2 = String.format("关闭参数：%s", list);
                dingWarnRobot.sendMsgToAtPeople("【关闭客户功能失败】", Lists.newArrayList(format, format1, format2), "");
            }
        });
        //当关闭限定时长类功能时 直接isDeleted =1
        List<BillingCustomerConfig> closeTimeList = new ArrayList<>();
        for (BillingCustomerConfig config : customerConfigs) {
            if (ruleUseTimeWayId.equals(config.getBillingWayId())) {
                config.setIsDeleted(PcsEnumConst.YES.getValue());
                closeTimeList.add(config);
            }
        }
        if (CollectionUtils.isEmpty(closeTimeList)) {
            return;
        }
        this.billingCustomerConfigRepository.saveAll(closeTimeList);
    }
    
    public void restConfig(List<BillingCustomerConfig> customerConfigs) {
        for (BillingCustomerConfig v : customerConfigs) {
            v.setIsDeleted(PcsEnumConst.YES.getValue());
            v.setIsReset(PcsEnumConst.YES.getValue());
            v.setAvailableValue((long) Constant.ZERO);
        }
        this.billingCustomerConfigRepository.saveAll(customerConfigs);
    }
    
    
    private Map<String, List<UserFunctionDO>> buildCloseFunctions(List<BillingCustomerConfig> config) {
        if (CollectionUtils.isEmpty(config)) {
            return null;
        }
        List<String> itemCodes = config.stream()
                .map(BillingCustomerConfig::getItemCode)
                .distinct()
                .collect(Collectors.toList());
        List<BillingItem> allByCodeIn = billingItemRepository.findAllByCodeIn(itemCodes);
        Map<String, List<BillingCustomerConfig>> collect = config.stream()
                .collect(Collectors.groupingBy(BillingCustomerConfig::getUnionId));
        Map<String, List<UserFunctionDO>> maps = new HashMap<>(collect.size());
        collect.forEach((customerId, list) -> {
            List<String> codes = list.stream().map(BillingCustomerConfig::getItemCode).collect(Collectors.toList());
            List<BillingItem> items = allByCodeIn.stream()
                    .filter(obj -> codes.contains(obj.getCode()))
                    .collect(Collectors.toList());
            maps.put(customerId, businessFunctionService.functionProcess(items, false));
        });
        return maps;
    }
}
