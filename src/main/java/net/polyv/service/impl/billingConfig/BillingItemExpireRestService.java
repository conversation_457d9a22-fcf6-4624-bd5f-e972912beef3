package net.polyv.service.impl.billingConfig;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.ResourceAlterTypeConst;
import net.polyv.constant.bill.BillStatusConst;
import net.polyv.constant.bill.IsManualChargingConst;
import net.polyv.constant.bill.IsManualConst;
import net.polyv.constant.bill.UnpaidTypeConst;
import net.polyv.constant.billingdaily.CustomerBillingDailyTradeTypeConst;
import net.polyv.constant.resource.ResourceSourceConst;
import net.polyv.dao.primary.AvailableCustomerResourceDailyRepository;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.dao.primary.CustomerBillingItemSettingRepository;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.dao.primary.custom.BillingItemDao;
import net.polyv.dao.primary.custom.ResourceAlterationRecordDao;
import net.polyv.model.entity.primary.AvailableCustomerResourceDaily;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.CustomerBillingDaily;
import net.polyv.model.entity.primary.CustomerBillingItemSetting;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.modules.common.constant.Constant;
import net.polyv.service.CacheService;

import net.polyv.service.impl.bill.BillingClearingConfigService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.polyv.constant.common.PrecisionConst.DEFAULT_BIG_DECIMAL_ROUNDING_MODE;
import static net.polyv.constant.common.PrecisionConst.DEFAULT_PRECISION;

/**
 * 计费项过期清零
 * <AUTHOR>
 * @date 2022/11/16 15:27
 */
@Component
@Slf4j
public class BillingItemExpireRestService {
    @Resource
    private CustomerBillingDailyRepository customerBillingDailyRepository;
    @Resource
    private BillingItemCloseService billingItemCloseService;
    @Resource
    private AvailableCustomerResourceDailyRepository availableCustomerResourceDailyRepository;
    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;
    @Resource
    private ResourceAlterationRecordDao resourceAlterationRecordDao;
    @Resource
    private CustomerBillingItemSettingRepository customerBillingItemSettingRepository;
    @Resource
    private BillingItemDao billingItemDao;
    @Resource
    private BillingClearingConfigService billingClearingConfigService;
    
    @Resource
    private CacheService cacheService;
    
    public void doExpireRest(List<BillingCustomerConfig> oneCustomerConfigs, Date date) {
        DateTime dateTime = DateUtil.endOfDay(date);
        String format = DateUtil.format(dateTime, Constant.DATE_TIME_FORMAT_yyyy_MM_dd_HH_mm_ss);
        log.info("开始执行清零 time:{}", format);
        Map<String, BillingItem> billingItemMap = this.billingItemDao.mapByItemCode(
                oneCustomerConfigs.stream().map(BillingCustomerConfig::getItemCode).collect(Collectors.toSet()));
        for (BillingCustomerConfig config : oneCustomerConfigs) {
            //获取该客户所有到达有效期的充值记录
            log.info("清零客户,customerId={} ,itemCode={}", config.getUnionId(), config.getItemCode());
            List<BillingCustomerConfig> configs = this.billingCustomerConfigRepository.findCustomerExpireConfig(
                    config.getUnionId(), config.getItemCode(), format);
            if (CollectionUtils.isEmpty(configs) || !billingItemMap.containsKey(config.getItemCode())) {
                continue;
            }
            log.info("清零合同id={}",
                    configs.stream().map(BillingCustomerConfig::getContractId).collect(Collectors.toList()));
            BillingItem item = billingItemMap.get(config.getItemCode());
            CustomerBillingItemSetting settingByItemScale = billingClearingConfigService.getCustomerBillingItemSetting(config.getUnionId(),item.getResourceCode(),
                    config.getAvailableValue(),item.getId());

            //生成清零账单
            //createCleanBills(configs, item, settingByItemScale);
            //log.info("清零客户,出清零账单成功,customerId={} ,itemCode={}", config.getUnionId(), config.getItemCode());
            //生成清零变动记录
            createCleanAlation(configs);
            log.info("清零客户,出清零变动记录成功,customerId={} ,itemCode={}", config.getUnionId(),
                    config.getItemCode());
            //生成清零记录
            this.billingItemCloseService.restConfig(configs);
            log.info("清零客户,清零记录成功,customerId={} ,itemCode={}", config.getUnionId(), config.getItemCode());
            //更新清零可用表记录
            //updateAvailable(configs, date);
            log.info("清零客户,更新清零可用表记录成功,customerId={} ,itemCode={}", config.getUnionId(),
                    config.getItemCode());
            
            //清理缓存
            cacheService.cleanLiveRestrictPcsResult(config.getUnionId());
            cacheService.cleanAllCacheByCustomerIdAndCode(config.getUnionId(), config.getItemCode());
        }
    
    
    }
    
    private void updateAvailable(List<BillingCustomerConfig> configs, Date date) {
        for (BillingCustomerConfig config : configs) {
            AvailableCustomerResourceDaily resourceDaily = new AvailableCustomerResourceDaily();
            resourceDaily.setResourceCode(config.getItemCode());
            resourceDaily.setStatAt(date);
            resourceDaily.setCustomerId(config.getUnionId());
            resourceDaily.setContractId(config.getContractId());
            resourceDaily.setAvailable(0L);
            this.availableCustomerResourceDailyRepository.save(resourceDaily);
        }
    }
    
    private void createCleanAlation(List<BillingCustomerConfig> configs) {
        for (BillingCustomerConfig config : configs) {
            resourceAlterationRecordDao.saveResourceAlteration(new Date(), config.getUnionId(), config.getItemCode(),
                    ResourceAlterTypeConst.expire_clean, -config.getAvailableValue(), ResourceSourceConst.PERMANENT,
                    config.getContractId(), config.getResourceType(), null);
            log.info("clean done... customerId={}, resourceCode={},resourceType = {}, alteration={}",
                    config.getUnionId(), config.getItemCode(), config.getResourceType(), -config.getAvailableValue());
            
        }
    }
    
    private void createCleanBills(List<BillingCustomerConfig> configs, BillingItem billingItem,
            CustomerBillingItemSetting unitSetting) {
        List<CustomerBillingDaily> dailyList = new ArrayList<>();
        int ratio = Objects.nonNull(unitSetting) ? unitSetting.getRatio() : billingItem.getDefaultRatio();
        for (BillingCustomerConfig config : configs) {
            Long originalItemConsumed = config.getAvailableValue();
            CustomerBillingDaily billingDaily = this.buildBaseBillingDaily(billingItem, unitSetting);
            billingDaily.setTradeType(CustomerBillingDailyTradeTypeConst.consumed_clearing.getTradeType());
            billingDaily.setOriginalItemConsumed(originalItemConsumed);
            billingDaily.setItemConsumed(this.tranRatioItemConsumed(new BigDecimal(config.getAvailableValue()), ratio));
            billingDaily.setItemConsumedUnit(billingItem.getItemConsumedUnit());
            billingDaily.setUnivalenceUnit(billingItem.getUnivalenceUnit());
            billingDaily.setContractId(config.getContractId());
            // billingDaily.setGroupId(inputVO.getGroupId());
            billingDaily.setConsumeStartDate(config.getExpireTime());
            billingDaily.setConsumeEndDate(config.getExpireTime());
            billingDaily.setCustomerId(config.getUnionId());
            billingDaily.setIsCleanBills(PcsEnumConst.YES.getValue());
            dailyList.add(billingDaily);
        }
        if (!CollectionUtils.isEmpty(dailyList)) {
            this.customerBillingDailyRepository.saveAll(dailyList);
        }
    }
    
    
    private BigDecimal tranRatioItemConsumed(BigDecimal itemConsumed, Integer ratio) {
        return itemConsumed.multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(ratio), DEFAULT_PRECISION, DEFAULT_BIG_DECIMAL_ROUNDING_MODE);
    }
    
    /**
     * 构建基础的账单
     * @param billingItem 计费项
     * @return 结算结果
     */
    private CustomerBillingDaily buildBaseBillingDaily(BillingItem billingItem,
            CustomerBillingItemSetting itemSetting) {
        CustomerBillingDaily billingDaily = new CustomerBillingDaily();
        billingDaily.setStatAt(DateUtil.beginOfDay(new Date()));
        billingDaily.setStatus(BillStatusConst.PAID);
        billingDaily.setIsManual(IsManualConst.NO);
        billingDaily.setIsManualCharging(IsManualChargingConst.NO);
        billingDaily.setItemId(billingItem.getId());
        billingDaily.setItemConsumed(BigDecimal.ZERO);
        billingDaily.setUnivalenceUnit(billingItem.getUnivalenceUnit());
        billingDaily.setItemConsumedUnit(billingItem.getItemConsumedUnit());
        billingDaily.setItemCategory(billingItem.getCategory());
        billingDaily.setOperaId(-1L);
        billingDaily.setUnivalence(0L);
        billingDaily.setCost(0L);
        billingDaily.setUnpaid(0L);
        billingDaily.setUnpaidType(UnpaidTypeConst.NO_UNPAID);
        
        if (Objects.nonNull(itemSetting)) {
            billingDaily.setCusItemId(itemSetting.getId());
            billingDaily.setLastAdjustUnivalence(itemSetting.getUnivalence());
            billingDaily.setLastAdjustRatio(itemSetting.getRatio());
            billingDaily.setUnivalenceUnitConversion(itemSetting.getUnivalenceUnitConversion());
        } else {
            billingDaily.setUnivalenceUnitConversion(billingItem.getUnivalenceUnitConversion());
            billingDaily.setLastAdjustUnivalence(billingItem.getDefaultUnivalence());
            billingDaily.setLastAdjustRatio(billingItem.getDefaultRatio());
        }
        
        return billingDaily;
    }
}
