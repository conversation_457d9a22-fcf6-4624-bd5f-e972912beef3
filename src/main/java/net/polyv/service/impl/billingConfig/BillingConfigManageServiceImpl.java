package net.polyv.service.impl.billingConfig;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.billing.BillingWayConfigCodeEnum;
import net.polyv.constant.cache.CacheConst;
import net.polyv.constant.common.PrecisionConst;
import net.polyv.constant.event.BillingEventEnum;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.dao.primary.billingConfig.BillingItemExtRepository;
import net.polyv.dao.primary.billingConfig.BillingLongestOpenTimeRepository;
import net.polyv.dao.primary.billingConfig.BillingSettlementPeriodConfigRepository;
import net.polyv.dao.primary.billingConfig.BillingWayConfigRepository;
import net.polyv.dao.primary.custom.BillingItemExtDao;
import net.polyv.exception.bill.BillConfigException;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.model.entity.primary.billingconfig.BillingItemExt;
import net.polyv.model.entity.primary.billingconfig.BillingLongestOpenTimeConfig;
import net.polyv.model.entity.primary.billingconfig.BillingSettlementPeriodConfig;
import net.polyv.model.entity.primary.billingconfig.BillingWayConfig;
import net.polyv.model.event.BillingConfigEvent;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.common.exception.BizException;
import net.polyv.modules.common.stereotype.BizErrorCodeEnum;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.billingconfig.GetBillingItemRequest;
import net.polyv.modules.pcs.api.req.billingconfig.PageBillingItemRequest;
import net.polyv.modules.pcs.api.req.billingconfig.SaveBillingItemRequest;
import net.polyv.modules.pcs.api.req.billingconfig.UpdateBillingItemRequest;
import net.polyv.modules.pcs.api.stereotype.PCSErrorCodeEnum;
import net.polyv.modules.pcs.api.vo.ConfigVO;
import net.polyv.modules.pcs.api.vo.billingconfig.BillingConfigVO;
import net.polyv.modules.pcs.api.vo.billingconfig.BillingItemBaseVO;
import net.polyv.modules.pcs.api.vo.billingconfig.BillingItemInfoVO;
import net.polyv.modules.pcs.api.vo.billingconfig.BillingItemPageVO;
import net.polyv.service.billingconfig.BillingConfigManageService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.BeanUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.JsonMapper;
import net.polyv.util.MoneyUtil;
import net.polyv.util.PageUtils;
import net.polyv.util.converter.UnitConverterUtil;
import net.polyv.web.model.resource.CustomerSumNumberVO;
import net.polyv.web.model.user.OtherBillingItemVO;

/**
 * 实现
 * <AUTHOR>
 * @date 2022/7/20 15:34
 */

@Service
@Slf4j
public class BillingConfigManageServiceImpl implements BillingConfigManageService {
    
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private BillingSettlementPeriodConfigRepository billingSettlementPeriodConfigRepository;
    @Resource
    private BillingWayConfigRepository billingWayConfigRepository;
    @Resource
    private BillingLongestOpenTimeRepository billingLongestOpenTimeRepository;
    @Resource
    private BillingItemExtRepository billingItemExtRepository;
    @Resource
    private BillingItemRepository billingItemRepository;
    @Resource
    private BillingItemExtDao billingItemExtDao;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;
    
    @Resource
    private CustomerResourceService customerResourceService;
    @Value("${pcs.config.numberBillWayId}")
    private Integer numberBillWayId;
    @Value("${pcs.config.ruleUseTimeWayId}")
    private Integer ruleUseTimeWayId;
    @Value("${pcs.config.dosageWayId}")
    private Integer dosageWayId;
    @Value("${pcs.config.periodDayId}")
    private Integer periodDayId;
    
    //按用量+限定使用时长
    @Value("${pcs.config.usageAndUseTimeWayId:7}")
    private Integer usageAndUseTimeWayId;
    
    @Override
    public CommonResult save(SaveBillingItemRequest request) {
        preCheckParams(request);
        BillingItemExt billItemExt = checkExist(request.getItemCode());
        log.info("新增计费项：{}", JsonUtil.beanToString(request).orElse(""));
        if (Objects.isNull(billItemExt)) {
            insertItem(request);
        } else {
            throw new BizException(PCSErrorCodeEnum.EXITS_BILLING_ITEM_ERROR);
        }
        //推送事件 通知下游
        return CommonResult.ok();
    }
    
    
    @Override
    public CommonResult update(UpdateBillingItemRequest request) {
        BillingItem billingItem = preUpdateCheckParams(request);
        BillingItemExt billingItemExt = checkExist(billingItem.getCode());
        if (Objects.isNull(billingItemExt)) {
            throw new BillConfigException("无法找到需更新的计费项数据");
        }
        updateItem(request, billingItemExt);
        return CommonResult.ok();
    }
    
    
    @Override
    public BillingItemInfoVO getByBillItemId(GetBillingItemRequest request) {
        if (Objects.isNull(request)) {
            throw new BizException(BizErrorCodeEnum.PARAM_REQUIRE);
        }
        Optional<BillingItem> optional = billingItemRepository.findById(request.getBillingItemId());
        if (!optional.isPresent()) {
            throw new BillConfigException("未找到计费项详细信息");
        }
        BillingItem one = optional.get();
        BillingItemExt extInfo = billingItemExtRepository.findByItemCode(one.getCode());
        if (Objects.isNull(extInfo)) {
            log.error("计费项扩展信息不存在：itemId={}", request.getBillingItemId());
            throw new BizException(BizErrorCodeEnum.DATA_NO_EXIST);
        }
        BillingItemInfoVO vo = BeanUtil.parse(extInfo, BillingItemInfoVO.class);
        /** BeanCopy 赋值字段
         *     private Integer longestOpenTimeId;
         *     private String longestOpenTimeName;
         *     private Integer billingWayId;
         *     private String billingWayName;
         *     private String functionCode;
         *     private String itemName;
         *     private String subCategory;
         *     private String productCode;
         *     private String categoryCode;
         *     private Integer isExpireReset;
         *    手动赋值字段
         *     private Integer billingItemId;
         *     private String detail;
         *     private BigDecimal defaultUnivalence;
         *     private String univalenceUnit;
         *     private String scaleCode;
         *     private String production;
         *     private String dosageUnit;
         *     private String itemCode;
         * */
        //补充参数
        vo.setBillingItemId(one.getId());
        vo.setDetail(one.getCategory());
        vo.setItemCode(one.getResourceCode());
        vo.setDefaultUnivalence(new BigDecimal(MoneyUtil.getHumanMoney(one.getDefaultUnivalence(), 3)));
        vo.setUnivalenceUnit(one.getUnivalenceUnit());
        vo.setScaleCode(one.getScaleCode());
        vo.setProduction(one.getProduction());
        vo.setDosageUnit(one.getItemConsumedUnit());
        return vo;
    }
    
    @Override
    public Pager<BillingItemPageVO> pageBillingItem(PageBillingItemRequest request) {
        BillingItemExt billingItemExt = new BillingItemExt();
        billingItemExt.setItemName(request.getBillingName());
        billingItemExt.setBillingWayId(request.getBillingWayId());
        Page<BillingItemExt> extPage = billingItemExtDao.pageBillItem(billingItemExt, request.getPage() - 1,
                request.getPageSize());
        if (extPage.isEmpty()) {
            return new Pager<>();
        }
        Map<String, BillingItemExt> extMap = extPage.getContent()
                .stream()
                .collect(Collectors.toMap(BillingItemExt::getItemCode, obj -> obj));
        List<String> codes = extPage.getContent()
                .stream()
                .map(BillingItemExt::getItemCode)
                .collect(Collectors.toList());
        List<BillingItemPageVO> pageList = new ArrayList<>();
        List<BillingItem> billingItemList = billingItemRepository.findAllByCodeIn(codes);
        if (CollectionUtils.isEmpty(billingItemList)) {
            return PageUtils.assemblyPage(extPage, pageList);
        }
        //遍历billingItemList，按id降序排序
        billingItemList.sort(Comparator.comparing(BillingItem::getId).reversed());
        for (BillingItem v : billingItemList) {
            BillingItemPageVO billingItemPageVO = buildPageVO(v, extMap.get(v.getCode()));
            pageList.add(billingItemPageVO);
        }
        return PageUtils.assemblyPage(extPage, pageList);
    }
    
    @Override
    public BillingConfigVO configList() {
        List<BillingWayConfig> billingWayConfigs = billingWayConfigRepository.listByEnable();
        List<BillingSettlementPeriodConfig> periodConfigs = billingSettlementPeriodConfigRepository.listByEnable();
        List<BillingLongestOpenTimeConfig> longestOpenTimeConfigs = billingLongestOpenTimeRepository.listByEnable();
        List<ConfigVO> wayList = billingWayConfigs.stream()
                .map(v -> new ConfigVO(v.getId(), v.getBillingWayName()))
                .collect(Collectors.toList());
        List<ConfigVO> periodList = periodConfigs.stream()
                .map(v -> new ConfigVO(v.getId(), v.getBillingPeriodName()))
                .collect(Collectors.toList());
        List<ConfigVO> openTimeList = longestOpenTimeConfigs.stream()
                .map(v -> new ConfigVO(v.getId(), v.getLongestOpenTimeName()))
                .collect(Collectors.toList());
        return new BillingConfigVO(wayList, periodList, openTimeList);
    }
    
    @Override
    public List<BillingItemBaseVO> listConfigItemByType(Integer typeId) {
        List<BillingItemExt> list = this.billingItemExtRepository.findByBillingWayIdAndIsDeleted(typeId,
                PcsEnumConst.NO.getValue());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream()
                .map(v -> new BillingItemBaseVO(null, v.getItemName(), v.getItemCode()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<BillingItemBaseVO> listByAll() {
        List<BillingItemExt> list = this.billingItemExtRepository.findByIsDeleted(PcsEnumConst.NO.getValue());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream()
                .map(v -> new BillingItemBaseVO(null, v.getItemName(), v.getItemCode()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtherBillingItemVO> getCustomerBillItemConfigs(String customerId) {

        List<OtherBillingItemVO> result = new ArrayList<>();
    
        //排除按用量+限定使用时长的计费项
        List<BillingCustomerConfig> list = this.billingCustomerConfigRepository.findCustomerEnableConfig(customerId,
                new Date(), PcsEnumConst.NO.getValue(), usageAndUseTimeWayId);
        List<String> codes = list.stream()
                .map(BillingCustomerConfig::getItemCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, BillingItem> billingItemMap = this.billingItemRepository.findAllByCodeIn(codes)
                .stream()
                .collect(Collectors.toMap(BillingItem::getCode, v -> v));
        for (BillingCustomerConfig config : list) {
            BillingItem item = billingItemMap.get(config.getItemCode());
            OtherBillingItemVO extracted = getAvailableInfo(customerId, config, item, null);
            if (Objects.nonNull(extracted)) {
                result.add(extracted);
            }
        }

        //获取用户按用量+限定使用时长 && 未过期的记录
        List<BillingCustomerConfig> usageAndUseTimeList =
                this.billingCustomerConfigRepository.findByUnionIdAndBillingWayIdAndIsDeletedAndExpireTimeGreaterThanEqual(
                customerId, usageAndUseTimeWayId, PcsEnumConst.NO.getValue(), new Date());
        for (BillingCustomerConfig usageAndUseTimeConfig : usageAndUseTimeList) {
            BillingItem item = billingItemRepository.findFirstByCode(usageAndUseTimeConfig.getItemCode());
            String openTime = Objects.nonNull(usageAndUseTimeConfig.getOpenTime()) ?
                    DateUtil.formatDate(usageAndUseTimeConfig.getOpenTime()) : null;
            String endTime = Objects.nonNull(usageAndUseTimeConfig.getExpireTime()) ?
                    DateUtil.formatDate(usageAndUseTimeConfig.getExpireTime()) : null;
            long available =
                    usageAndUseTimeConfig.getAvailableValue() == null ? 0L : usageAndUseTimeConfig.getAvailableValue();
            if (ItemCodeConst.material_space.getCode().equals(item.getCode())) {
                available = UnitConverterUtil.bytes2GB(available).longValue();
            }
            OtherBillingItemVO vo = new OtherBillingItemVO(3, item.getName(), item.getCode(), openTime, endTime,
                    available, item.getItemConsumedUnit());
            result.add(vo);
        }
        return result;
    }
    
    
    @Override
    public OtherBillingItemVO getCustomerAvailableByOther(String customerId, String resourceCode, Date baseDate) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(resourceCode)) {
            log.warn("缺少查询参数：customerId={},resourceCode={}", customerId, resourceCode);
            return null;
        }
        //获取该客户的计费项配置信息 ： 配置化的计费项（ 计费项编码= 资源编码）
        BillingCustomerConfig configs = this.billingCustomerConfigRepository.findByUnionIdAndItemCode(customerId,
                resourceCode);
        List<BillingItem> items = this.billingItemRepository.findByCode(resourceCode);
        if (CollectionUtils.isEmpty(items)) {
            log.warn("该计费项数据不存在,请检查：customerId={},resourceCode={}", customerId, resourceCode);
            return null;
        }
        return getAvailableInfo(customerId, configs, items.stream().findFirst().get(), baseDate);
    }
    
    private OtherBillingItemVO getAvailableInfo(String customerId, BillingCustomerConfig config, BillingItem item,
            Date baseDate) {
        BillingItemExt billingItemExt = billingItemExtRepository.findByItemCode(item.getCode());
        if (Objects.isNull(billingItemExt)) {
            return null;
        }
        if (Objects.nonNull(config) && this.ruleUseTimeWayId.equals(billingItemExt.getBillingWayId())) {
    
            long between = net.polyv.util.DateUtil.getDaysBetween(net.polyv.util.DateUtil.getCurrentDay(),
                    config.getExpireTime()) + 1L;
            return new OtherBillingItemVO(1, item.getName(), item.getCode(),
                    net.polyv.util.DateUtil.formatDate(config.getOpenTime()),
                    net.polyv.util.DateUtil.formatDate(config.getExpireTime()), between, item.getItemConsumedUnit());
        }
        if (this.numberBillWayId.equals(billingItemExt.getBillingWayId())) {
    
            CustomerSumNumberVO sumAvailableNumber = customerResourceService.getSumAvailableNumber(customerId,
                    item.getCode(), baseDate);
            if (Objects.isNull(sumAvailableNumber)) {
                return null;
            }
            String openTime = Objects.nonNull(config) && Objects.nonNull(config.getOpenTime()) ?
                    net.polyv.util.DateUtil.formatDate(config.getOpenTime()) : null;
            String endTime = Objects.nonNull(sumAvailableNumber) && Objects.nonNull(sumAvailableNumber.getEndTime()) ?
                    DateUtil.formatDate(sumAvailableNumber.getEndTime()) : null;
            if (ItemCodeConst.material_traffic.getCode().equals(item.getCode())) {
                sumAvailableNumber.setAvailable(
                        UnitConverterUtil.bytes2GB(sumAvailableNumber.getAvailable()).longValue());
            }
            return new OtherBillingItemVO(2, item.getName(), item.getCode(), openTime, endTime,
                    sumAvailableNumber.getAvailable(), item.getItemConsumedUnit());
        }
    
        return null;
    }
    @Override
    public OtherBillingItemVO getCustomerAvailableByOther(String customerId, String resourceCode, String type,
            Date baseDate) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(resourceCode)) {
            log.warn("缺少查询参数：customerId={},resourceCode={}", customerId, resourceCode);
            return null;
        }
        if (baseDate == null) {
            baseDate = net.polyv.util.DateUtil.getCurrentDay();
        }
        OtherBillingItemVO vo = new OtherBillingItemVO();
        List<BillingCustomerConfig> openConfigs = new ArrayList<>();
        if (StringUtils.isNotEmpty(type)) {
            //获取 该类型的计费编码，再获取该客户的开通记录
            BillingItem itemEntity = this.billingItemRepository.findFirstByResourceCodeAndScaleCode(resourceCode, type);
            if (Objects.isNull(itemEntity)) {
                log.error("该类型的资源计费项无法匹配: resourceCode={},type={}", resourceCode, type);
                return null;
            }
            //获取计费方式
            BillingItemExt billingItemExt = billingItemExtRepository.findByItemCode(itemEntity.getCode());
    
            if (Objects.isNull(billingItemExt)) {
                log.error("该计费项ext数据不存在,请检查：itemCode={},type={}", itemEntity.getCode(), type);
                return null;
            }
    
            //填充计费方式id和itemCode
            BillingCustomerConfig config = new BillingCustomerConfig();
            config.setItemCode(itemEntity.getCode());
            config.setBillingWayId(billingItemExt.getBillingWayId());
            openConfigs.add(config);
        } else {
            //根据resourceCode获取List<BillingItemExt>
            List<BillingItemExt> billingItemExtList = billingItemExtRepository.findAllByResourceCode(resourceCode);
            if (CollectionUtils.isEmpty(billingItemExtList)) {
                log.error("该计费项ext数据不存在,请检查：resourceCode={},type={}", resourceCode, type);
                return null;
            }
            //填充计费方式id和itemCode
            for (BillingItemExt billingItemExt : billingItemExtList) {
                BillingCustomerConfig config = new BillingCustomerConfig();
                config.setItemCode(billingItemExt.getItemCode());
                config.setBillingWayId(billingItemExt.getBillingWayId());
                openConfigs.add(config);
            }
        }
    
        if (CollectionUtils.isEmpty(openConfigs)) {
            return null;
        }
        HashMap<Integer, Long> resourceMap = new HashMap<>();
        HashMap<String, Long> resourceDetailMap = new HashMap<>();
        fillAndCalculationResource(customerId, openConfigs, resourceMap, resourceDetailMap, baseDate);
    
    
        //是否充值过使用时长的资源
        boolean isDepositRuleUserTime = resourceMap.containsKey(this.ruleUseTimeWayId);
        //是否充值过使用次数的资源
        boolean isDepositNumber = resourceMap.containsKey(this.numberBillWayId);
        //是否充值过按用量+限定使用时长的资源
        boolean isDepositUsageAndUseTime = resourceMap.containsKey(this.usageAndUseTimeWayId);
        vo.setAvailable(0L);
    
        long ruleUseTimeAvailable = resourceMap.getOrDefault(this.ruleUseTimeWayId, 0L);
        long numberAvailable = resourceMap.getOrDefault(this.numberBillWayId, 0L);
        long usageAndUseTimeAvailable = resourceMap.getOrDefault(this.usageAndUseTimeWayId, 0L);
    
        //如果充值过两种或以上种资源，只要其中一个 > 0就是可用，混合资源available=1
        int resourceTypeCount =
                isDepositRuleUserTime ? 1 : 0 + (isDepositNumber ? 1 : 0) + (isDepositUsageAndUseTime ? 1 : 0);
        if (resourceTypeCount >= 2) {
            if (ruleUseTimeAvailable > 0 || numberAvailable > 0 || usageAndUseTimeAvailable > 0) {
                vo.setAvailable(1L);
            }
        } else {
            //如果只充值过一种资源，available等于对应 资源的值
            if (isDepositRuleUserTime) {
                vo.setAvailable(resourceMap.getOrDefault(this.ruleUseTimeWayId, 0L));
            }
            if (isDepositNumber) {
                vo.setAvailable(resourceMap.getOrDefault(this.numberBillWayId, 0L));
            }
            if (isDepositUsageAndUseTime) {
                vo.setAvailable(resourceMap.getOrDefault(this.usageAndUseTimeWayId, 0L));
            }
        }
        vo.setResourceDetailMap(resourceDetailMap);
        return vo;
    }
    
    
    /**
     * 填充并计算资源
     * @param customerId 客户id
     * @param openConfigs 开通记录
     * @param resourceMap 资源map
     * @param resourceDetailMap 资源明细map
     */
    private void fillAndCalculationResource(String customerId, List<BillingCustomerConfig> openConfigs,
            HashMap<Integer, Long> resourceMap, HashMap<String, Long> resourceDetailMap, Date baseDate) {
        //需考虑 同一资源编码下多个计费类型
        for (BillingCustomerConfig config : openConfigs) {
            long available = 0L;
            if (this.ruleUseTimeWayId.equals(config.getBillingWayId())) {
                //获取生效中的配置过期时间
                BillingCustomerConfig billingCustomerConfig = billingCustomerConfigRepository.findByUnionIdAndItemCode(
                        customerId, config.getItemCode());
                if (Objects.nonNull(billingCustomerConfig) && Objects.nonNull(billingCustomerConfig.getExpireTime())) {
                    available = net.polyv.util.DateUtil.getDaysBetween(net.polyv.util.DateUtil.getCurrentDay(),
                            billingCustomerConfig.getExpireTime()) + 1L;
                    resourceMap.put(this.ruleUseTimeWayId, resourceMap.containsKey(this.ruleUseTimeWayId) ?
                            resourceMap.get(ruleUseTimeWayId) + available : available);
                }
            }
            if (this.numberBillWayId.equals(config.getBillingWayId())) {
                CustomerSumNumberVO sumAvailableNumber = customerResourceService.getSumAvailableNumber(customerId,
                        config.getItemCode(), baseDate);
                available = Objects.isNull(sumAvailableNumber) ? 0L : sumAvailableNumber.getAvailable();
                resourceMap.put(this.numberBillWayId,
                        resourceMap.containsKey(this.numberBillWayId) ? resourceMap.get(numberBillWayId) + available :
                                available);
            }
            if (this.usageAndUseTimeWayId.equals(config.getBillingWayId())) {
                //按用量+限定使用时从计费，资源独立到期，不覆盖
                List<BillingCustomerConfig> configList =
                        billingCustomerConfigRepository.findByUnionIdAndItemCodeAndIsDeletedAndExpireTimeGreaterThanEqual(
                        customerId, config.getItemCode(), 0, new Date());
                if (!CollectionUtils.isEmpty(configList)) {
                    available = configList.stream()
                            .filter(obj -> Objects.nonNull(obj.getAvailableValue()))
                            .map(BillingCustomerConfig::getAvailableValue)
                            .reduce(0L, Long::sum);
                    resourceMap.put(this.usageAndUseTimeWayId, resourceMap.containsKey(this.usageAndUseTimeWayId) ?
                            resourceMap.get(usageAndUseTimeWayId) + available : available);
                }
            }
    
            if (this.dosageWayId.equals(config.getBillingWayId())) {
                //按用量会在后续的 获取金额+授信进行判断不进行处理
            }
            resourceDetailMap.put(config.getItemCode(), available);
        }
    }
    
    /**
     * 前置效验参数完整性
     * @param request 请求效验
     */
    
    private void preCheckParams(SaveBillingItemRequest request) {
        if (Objects.isNull(request)) {
            throw new BillConfigException("填写参数！");
        }
        if (StringUtils.isBlank(request.getDetail())) {
            throw new BillConfigException("计费项明细不为空");
        }
        if (Objects.isNull(request.getBillingWayId())) {
            throw new BillConfigException("请选择计费方式！");
        }
        if (StringUtils.isBlank(request.getItemCode())) {
            throw new BillConfigException("请填写计费项编码！");
        }
        if (StringUtils.isBlank(request.getItemName())) {
            throw new BillConfigException("请填写计费项名称！");
        }
        if (StringUtils.isBlank(request.getScaleCode())) {
            throw new BillConfigException("请填写类别！");
        }
        
        Integer billingWayId = request.getBillingWayId();
        
        // 是否到期清零	 仅有选择计费方式为 按次数计费 才显示该选项
        if (this.numberBillWayId.equals(billingWayId)) {
            if (Objects.isNull(request.getIsExpireReset())) {
                throw new BillConfigException("请选择到期清零设置");
            }
            if (PcsEnumConst.YES.getValue() == request.getIsExpireReset()) {
                //最长开通时间 仅有勾选 是否到期清零 后才显示该下拉框
                if (Objects.isNull(request.getLongestOpenTimeId())) {
                    throw new BillConfigException("请选择最长开通时间");
                }
            }
            if (StringUtils.isEmpty(request.getDosageUnit())) {
                throw new BillConfigException("请填充用量单位");
            }
        }
        //按用量需要校验功能开关数据
        if (this.dosageWayId.equals(billingWayId)) {
            String functionCode = request.getFunctionCode();
            if (StringUtils.isBlank(functionCode)) {
                throw new BillConfigException("关联功能开关必填！");
            }
        }
    }
    
    
    /**
     * 检查计费项是否存在，存在即返回 计费项扩展信息主键
     * @param code 计费项编码
     * @return {@link java.lang.Integer}
     * <AUTHOR>
     * @date 2022/7/20
     */
    
    private BillingItemExt checkExist(String code) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(code);
        if (Objects.isNull(billingItem)) {
            return null;
        }
        BillingItemExt billingItemExt = billingItemExtRepository.findByItemCode(billingItem.getCode());
        return Objects.isNull(billingItemExt) ? null : billingItemExt;
    }
    
    private BillingItem preUpdateCheckParams(UpdateBillingItemRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getBillingItemId())) {
            throw new BillConfigException("请填充需更新的计费项");
        }
        
        BillingItem byId = this.billingItemRepository.getById(request.getBillingItemId());
        if (Objects.isNull(byId)) {
            throw new BillConfigException("无法找到该计费项");
        }
        return byId;
    }
    
    private void insertItem(SaveBillingItemRequest request) {
        
        BillingItemExt parse = buildBillingItemExt(request);
        BillingItemExt ext = billingItemExtRepository.save(parse);
        BillingItem billingItem = buildBillingItem(request);
        BillingItem item = billingItemRepository.save(billingItem);
        applicationEventPublisher.publishEvent(new BillingConfigEvent(this, BillingEventEnum.SAVE, item, ext));
    }
    
    
    private void updateItem(UpdateBillingItemRequest request, BillingItemExt billingItemExt) {
        BillingItem billingItem = buildUpdateBillingItem(request, billingItemExt);
        billingItemRepository.save(billingItem);
        applicationEventPublisher.publishEvent(
                new BillingConfigEvent(this, BillingEventEnum.UPDATE, billingItem, billingItemExt));
    }
    
    private BillingItemExt buildBillingItemExt(SaveBillingItemRequest request) {
        BillingItemExt parse = new BillingItemExt();
        parse.setItemName(request.getItemName());
        parse.setItemCode(request.getItemCode() + request.getScaleCode());
        parse.setBillingWayId(request.getBillingWayId());
        parse.setBillingWayName(request.getBillingWayName());
        parse.setIsExpireReset(
                (Objects.isNull(request.getIsExpireReset()) || Integer.valueOf(0).equals(request.getIsExpireReset())) ?
                        PcsEnumConst.NO.getValue() : PcsEnumConst.YES.getValue());
        parse.setIsDeleted(PcsEnumConst.NO.getValue());
        parse.setSubCategory(request.getSubCategory());
        parse.setFunctionCode(request.getFunctionCode());
        parse.setProductCode(request.getProductCode());
        parse.setCategoryCode(request.getClassifyCode());
        parse.setResourceCode(request.getItemCode());
        //统一按 结算周期=天
        Optional<BillingSettlementPeriodConfig> optional = this.billingSettlementPeriodConfigRepository.findById(
                this.periodDayId);
        if (optional.isPresent()) {
            BillingSettlementPeriodConfig periodConfig = optional.get();
            parse.setBillingPeriodId(periodConfig.getId());
            parse.setBillingPeriodName(periodConfig.getBillingPeriodName());
        }
        //次数类计费项的结算周期为 天
        if (this.numberBillWayId.equals(request.getBillingWayId())) {
            parse.setLongestOpenTimeId(request.getLongestOpenTimeId());
            parse.setLongestOpenTimeName(request.getLongestOpenTimeName());
        }
        if (this.ruleUseTimeWayId.equals(request.getBillingWayId())) {
            parse.setBillingPeriodId(null);
            parse.setBillingPeriodName(null);
            parse.setLongestOpenTimeName(null);
            parse.setLongestOpenTimeId(null);
            parse.setIsExpireReset(PcsEnumConst.NO.getValue());
        }
    
        //限制最长开通时间和不到期清理
        if (this.usageAndUseTimeWayId.equals(request.getBillingWayId())) {
            parse.setLongestOpenTimeId(request.getLongestOpenTimeId());
            parse.setLongestOpenTimeName(request.getLongestOpenTimeName());
            parse.setIsExpireReset(PcsEnumConst.NO.getValue());
        }
        if (this.dosageWayId.equals(request.getBillingWayId())) {
            parse.setLongestOpenTimeName(null);
            parse.setLongestOpenTimeId(null);
        }
        return parse;
    }
    
    private BillingItem buildUpdateBillingItem(UpdateBillingItemRequest request, BillingItemExt ext) {
        BillingItem billingItem = billingItemRepository.getById(request.getBillingItemId());
        //根据id获取计费项信息
        //切割单价单位：元/个-> 个
        spiltUnit(billingItem, request.getUnivalenceUnit(), ext.getBillingWayId(), request.getDosageUnit());
        billingItem.setDefaultUnivalence(
                MoneyUtil.enlargeNumericalHundredThousand(request.getDefaultUnivalence().toString()));
        return billingItem;
    }
    
    private void spiltUnit(BillingItem billingItem, String univalenceUnit, Integer billingWayId, String dosageUnit) {
        String[] split = StringUtils.split(univalenceUnit, Constant.DIVIDE_SIGN);
        if (split.length < Constant.TWO) {
            throw new BizException(BizErrorCodeEnum.PARAM_ERROR.getCode(), "无法获取单位");
        }
        if (this.numberBillWayId.equals(billingWayId)) {
            billingItem.setItemConsumedUnit(dosageUnit);
        } else {
            billingItem.setItemConsumedUnit(split[1]);
        }
        billingItem.setUnivalenceUnit(univalenceUnit);
    }
    
    private BillingItem buildBillingItem(SaveBillingItemRequest request) {
        BillingItem billingItem = new BillingItem();
        billingItem.setCode(request.getItemCode() + request.getScaleCode());
        billingItem.setName(request.getItemName());
        billingItem.setCategory(request.getDetail());
        billingItem.setScaleCode(request.getScaleCode());
        billingItem.setProduction(request.getProduction());
        billingItem.setDefaultUnivalence(
                MoneyUtil.enlargeNumericalHundredThousand(request.getDefaultUnivalence().toString()));
        billingItem.setResourceCode(request.getItemCode());
        billingItem.setDefaultRatio(PrecisionConst.DEFAULT_RATIO_UNIT_HUNDRED);
        //切割单价单位：元/个-> 个
        spiltUnit(billingItem, request.getUnivalenceUnit(), request.getBillingWayId(), request.getDosageUnit());
        billingItem.setIsManual(PcsEnumConst.NO.getValue());
        billingItem.setUnivalenceUnitConversion(1L);
        return billingItem;
    }
    
    
    private BillingItemPageVO buildPageVO(BillingItem billingItem, BillingItemExt ext) {
        BillingItemPageVO vo = new BillingItemPageVO();
        vo.setBillingItemId(billingItem.getId());
        vo.setBillingWayId(ext.getBillingWayId());
        vo.setBillingWayName(ext.getBillingWayName());
        vo.setItemCode(billingItem.getResourceCode());
        vo.setItemName(billingItem.getName());
        vo.setDefaultUnivalence(new BigDecimal(MoneyUtil.getHumanMoney(billingItem.getDefaultUnivalence(), 3)));
        vo.setUnivalenceUnit(billingItem.getUnivalenceUnit());
        vo.setScaleCode(billingItem.getScaleCode());
        return vo;
        
    }
}
