package net.polyv.service.impl.billingConfig;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.dao.primary.billingConfig.BillingSettlementPeriodConfigRepository;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.model.entity.primary.billingconfig.BillingSettlementPeriodConfig;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.service.clearing.ItemClearingService;
import net.polyv.service.impl.bill.BillingClearConfigLogicService;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;

/**
 * 计费项配置入口服务
 * <AUTHOR>
 * @date 2022/7/21 15:35
 */
@Service
@Slf4j
public class BillingConfigClearingEnterService {
    @Resource
    private BillingSettlementPeriodConfigRepository billingSettlementPeriodConfigRepository;
    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;
    @Resource
    private BillingClearConfigLogicService billingClearConfigLogicService;
    
    @Resource
    private BillingItemRepository billingItemRepository;
    
    @Resource
    private DingWarnRobot dingWarnRobot;
    
    @Resource
    private ItemClearingService itemClearingService;
    
    /**
     * 临时表同步数据走常规结算方式的itemCode
     */
    @Value("${pcs.temp-table-normal-clearing.item-code:live_flow,prtc_live_flow}")
    private String normalClearingItemCodes;
    @Value("${pcs.clearing-with-item-codes:inter_pd_over_rate,china_pd_over_rate,prtc_china_pd_over_rate," +
            "prtc_inter_pd_over_rate}")
    private String clearingWithItemCodes;
    
    @Value("${pcs.is-over-code-rate-clear:true}")
    private boolean overCodeRateClear;
    
    @Transactional(rollbackFor = Exception.class)
    public void doProcess(LocalDate statAt) {
        doProcess(statAt, "");
    }
    
    /**
     * 获取客户配置计费项的结算周期是否已经满足
     * 获取计费项配置的结算步骤
     * 组装结算步骤 资源扣除顺序
     * 1.测试-》先到期先扣
     * 2.正式-》先到期先扣
     * 3.周期-》 临时-》  资源包-》 永久
     */
    @Transactional(rollbackFor = Exception.class)
    public void doProcess(LocalDate statAt, String itemCode) {
        //常规结算方式
        if (isNormalClearingResourceCodes(itemCode)) {
            this.normalClearing(statAt, itemCode);
        } else if (isClearingWithItemCodes(itemCode)) {
            if (ItemCodeConst.isOverCodeRateItem(itemCode) && !overCodeRateClear) {
                log.info("不执行超码率结算，itemCode={}", itemCode);
                return;
        
            }
            this.clearingWithItemCodes(statAt, itemCode);
        }
        //自定义计费项结算方式
        else {
            //当前结算日期是在凌晨,所以T-1获取时间点
            statAt = Objects.isNull(statAt) ? LocalDate.now() : statAt;
            //结算时间
            Date date = net.polyv.util.DateUtil.localDate2Date(statAt);
            //按资源使用量（次数）和按用量计费需要出账
            billingClearConfigLogicService.clearing(date, itemCode);
        }
    }
    
    /**
     * 根据resourceCode，同步用量到临时表，走常规结算方式
     * @param statAt
     * @param itemCode
     */
    public void normalClearing(LocalDate statAt, String itemCode) {
        Date date = net.polyv.util.DateUtil.localDate2Date(statAt);
        //通过itemCode获取resourceCode
        BillingItem billingItem = billingItemRepository.findFirstByCode(itemCode);
        if (Objects.isNull(billingItem)) {
            dingWarnRobot.sendWarnMsg("【normalClearing方法出账告警】",
                    String.format("date == %s，itemCode ==%s，找不到配置项,请立即排查！！！！", date, itemCode));
            return;
        }
        List<ResourceCodeConst> resourceCodes = new ArrayList<>();
        ResourceCodeConst resourceCodeConst = ResourceCodeConst.getByResourceCode(billingItem.getResourceCode());
        resourceCodes.add(resourceCodeConst);
        //这里需要异步处理，不然接口调用会超时
        //普通账号和2.0主账号都需要结算
        new Thread(() -> {
            //普通账号
            itemClearingService.clearingWithResourceCodes(resourceCodes, SwitchEnum.N.getCode());
            //2.0主账号（目前暂不支持主账号直播流量结算，后续需要做成可以根据resourceCode配置的）
            if (!ItemCodeConst.live_flow.getCode().equals(itemCode) && !ItemCodeConst.prtc_live_flow.getCode().equals(itemCode)) {
                log.info("begin group calculate billing resource code {}" , resourceCodes);
                itemClearingService.clearingWithResourceCodes(resourceCodes, SwitchEnum.Y.getCode());
            }
        }).start();
    
    
    }
    
    public void clearingWithItemCodes(LocalDate statAt, String itemCode) {
        Date date = net.polyv.util.DateUtil.localDate2Date(statAt);
        //通过itemCode获取resourceCode
        BillingItem billingItem = billingItemRepository.findFirstByCode(itemCode);
        if (Objects.isNull(billingItem)) {
            dingWarnRobot.sendWarnMsg("【normalClearing方法出账告警】",
                    String.format("date == %s，itemCode ==%s，找不到配置项,请立即排查！！！！", date, itemCode));
            return;
        }
        List<ItemCodeConst> itemCodeConsts = new ArrayList<>();
        ItemCodeConst itemCodeConst = ItemCodeConst.getByCode(itemCode);
        itemCodeConsts.add(itemCodeConst);
        //这里需要异步处理，不然接口调用会超时
        //普通账号和2.0主账号都需要结算
        new Thread(() -> {
            //普通账号
            itemClearingService.clearingWithItemCodes(itemCodeConsts, SwitchEnum.N.getCode());
            //默认主账号结算，有特殊再判断（主账号不执行超码率消耗出账）
            if (!ItemCodeConst.isOverCodeRateItem(itemCode)) {
                itemClearingService.clearingWithItemCodes(itemCodeConsts, SwitchEnum.Y.getCode());
            }
    
        }).start();
        
        
    }
    
    public Date calculateBillTime(LocalDate billItem, Integer billingPeriodId) {
        Optional<BillingSettlementPeriodConfig> optional = billingSettlementPeriodConfigRepository.findById(
                billingPeriodId);
        if (!optional.isPresent()) {
            return null;
        }
        BillingSettlementPeriodConfig periodConfig = optional.get();
        return getBillTime(billItem, periodConfig);
    }
    
    private Date getBillTime(LocalDate billItem, BillingSettlementPeriodConfig periodConfig) {
        LocalDate localDate = DateUtil.calculateDateByConfig(billItem, periodConfig.getUnit(),
                periodConfig.getBillingPeriod(), periodConfig.getAttribute());
        return DateUtil.localDate2Date(localDate);
    }
    
    private boolean isNormalClearingResourceCodes(String itemCode) {
        if (StringUtils.isNotBlank(normalClearingItemCodes)) {
            List<String> itemCodeList = Arrays.asList(normalClearingItemCodes.split(","));
            return itemCodeList.contains(itemCode);
        }
        return false;
    }
    
    private boolean isClearingWithItemCodes(String itemCode) {
        if (StringUtils.isNotBlank(clearingWithItemCodes)) {
            List<String> itemCodeList = Arrays.asList(clearingWithItemCodes.split(","));
            return itemCodeList.contains(itemCode);
        }
        return false;
    }
}
