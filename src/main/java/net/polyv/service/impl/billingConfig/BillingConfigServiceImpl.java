package net.polyv.service.impl.billingConfig;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.SysTypeConst;
import net.polyv.constant.billing.BillItemTypeConst;
import net.polyv.constant.common.YesNoConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.dao.primary.billingConfig.BillingItemExtRepository;
import net.polyv.dao.primary.custom.BillingSettlementPeriodConfigDao;
import net.polyv.dao.primary.sync.SyncItemDailyRecordRepository;
import net.polyv.exception.bill.BillConfigException;
import net.polyv.model.data.bilingConfig.OtherBillConfigItemDO;
import net.polyv.model.data.salesopportunities.ExtObjectDO;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.model.entity.primary.billingconfig.BillingItemExt;
import net.polyv.model.entity.primary.billingconfig.BillingSettlementPeriodConfig;
import net.polyv.model.entity.primary.sync.SyncItemDailyRecord;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.service.billingconfig.BillingConfigService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 计费项配置化服务
 * <AUTHOR>
 * @date 2022/7/21 14:08
 */
@Slf4j
@Service
public class BillingConfigServiceImpl implements BillingConfigService {
    
    @Resource
    private BillingSettlementPeriodConfigDao billingSettlementPeriodConfigDao;
    @Resource
    private BillingItemExtRepository billingItemExtRepository;
    @Resource
    private BillingItemRepository billingItemRepository;
    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;
    @Value("${pcs.config.numberBillWayId}")
    private Integer numberBillWayId;
    @Value("${pcs.config.ruleUseTimeWayId}")
    private Integer ruleUseTimeWayId;
    
    
    //按用量+限定使用时长
    @Value("${pcs.config.usageAndUseTimeWayId:7}")
    private Integer usageAndUseTimeWayId;
    @Resource
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    @Resource
    private SyncItemDailyRecordRepository syncItemDailyRecordRepository;
    
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveConfig(BillingCustomerConfig customerConfig) {
        log.info("计费项-客户配置信息：{}", JsonUtil.beanToString(customerConfig).orElse(""));
        overrideConfig(customerConfig);
    }
    
    @Override
    public List<OtherBillConfigItemDO> listOpenBillItemExt() {
        List<BillingItemExt> extList = this.billingItemExtRepository.findByIsDeleted(PcsEnumConst.NO.getValue());
        if (CollectionUtils.isEmpty(extList)) {
            return null;
        }
        List<BillingItem> billingItems = this.billingItemRepository.findAllByCodeIn(
                extList.stream().map(BillingItemExt::getItemCode).collect(Collectors.toList()));
        Map<String, BillingItem> collect = billingItems.stream()
                .collect(Collectors.toMap(BillingItem::getCode, v -> v));
        List<OtherBillConfigItemDO> list = new ArrayList<>();
        for (BillingItemExt ext : extList) {
            list.add(buildOtherItemDO(collect, ext));
        }
        return list;
    }
    
    
    @Override
    public List<OtherBillConfigItemDO> getOtherItemDataBySoId(String soId) {
        if (StringUtils.isBlank(soId)) {
            log.warn("获取销售机会数据为空");
            return null;
        }
        SalesOpportunities salesOpportunities = salesOpportunitiesRepository.findBySoId(soId);
        if (Objects.isNull(salesOpportunities)) {
            return null;
        }
        ExtObjectDO extObjectDO = salesOpportunities.getExtObjectDO();
        List<OtherBillConfigItemDO> otherItemList = extObjectDO.getOtherBillItemList();
        String billingPlanCode = salesOpportunities.getBillingPlanCode();
        BillingItem billingItem = this.billingItemRepository.findFirstByCode(billingPlanCode);
        if (CollectionUtils.isEmpty(otherItemList)) {
            BillingItemExt billingItemExt = this.billingItemExtRepository.findByItemCode(billingPlanCode);
            if (Objects.isNull(billingItemExt)) {
                return null;
            }
            HashMap<String, BillingItem> map = new HashMap<>(1);
            map.put(billingItem.getCode(), billingItem);
            return Lists.newArrayList(buildOtherItemDO(map, billingItemExt));
        } else {
            List<String> itemCodes = otherItemList.stream()
                    .map(OtherBillConfigItemDO::getCode)
                    .collect(Collectors.toList());
            List<BillingItem> billingItems = this.billingItemRepository.findAllByCodeIn(itemCodes);
            Map<String, BillingItem> collect = billingItems.stream()
                    .collect(Collectors.toMap(BillingItem::getCode, v -> v));
            for (OtherBillConfigItemDO other : otherItemList) {
                if (collect.containsKey(other.getCode())) {
                    other.setUnit(collect.get(other.getCode()).getItemConsumedUnit());
                }
            }
        }
        return otherItemList;
    }
    
    
    private OtherBillConfigItemDO buildOtherItemDO(Map<String, BillingItem> collect, BillingItemExt ext) {
        OtherBillConfigItemDO other = new OtherBillConfigItemDO();
        other.setUnit(collect.get(ext.getItemCode()).getItemConsumedUnit());
        other.setCode(ext.getItemCode());
        other.setName(ext.getItemName());
        other.setSys(Objects.requireNonNull(SysTypeConst.findByLabel(collect.get(ext.getItemCode()).getProduction()))
                .name());
        if (this.numberBillWayId.equals(ext.getBillingWayId()) ||
                this.usageAndUseTimeWayId.equals(ext.getBillingWayId())) {
            other.setType(BillItemTypeConst.NUMBER);
            other.setValue("0");
        }
        if (this.ruleUseTimeWayId.equals(ext.getBillingWayId())) {
            other.setType(BillItemTypeConst.RULE_USE_TIME);
            other.setValue(SwitchEnum.N.name());
        }
        return other;
    }
    
    public BillingCustomerConfig findByCustomerIdAndItemCode(String customerId, String itemCode) {
        log.debug("查询客户计费项配置信息：customerId={},itemCode={}", customerId, itemCode);
        return this.billingCustomerConfigRepository.findByUnionIdAndItemCode(customerId, itemCode);
    }
    
    @Deprecated
    private void saveByOverride(List<BillingCustomerConfig> customerConfigs, String unionId) {
        List<String> itemCodes = customerConfigs.stream()
                .map(BillingCustomerConfig::getItemCode)
                .collect(Collectors.toList());
        // 逻辑删除旧的配置,直接插入新的配置
        int count = logicDeleteCustomerConfig(unionId, itemCodes);
        log.info("已经逻辑删除客户配置：unionId={},count={}", unionId, count);
        this.billingCustomerConfigRepository.saveAll(customerConfigs);
    }

    
    
    /**
     * 覆盖客户配置
     */
    private void overrideConfig(BillingCustomerConfig customerConfig) {
        // 查询是否存在当天结算的数据，并校验当天是否已经结算，避免覆盖后跳过今天出账
        String currentDay = DateFormatUtil.formatDateNormal(DateUtil.getCurrentDay());
        int todayBillingDataCount = this.billingCustomerConfigRepository.existsTodayBillingTimeData(customerConfig.getUnionId(), customerConfig.getItemCode(), currentDay);
        if (todayBillingDataCount > 0) {
            // 查询今天该计费项已经出账
            SyncItemDailyRecord syncRecord = this.syncItemDailyRecordRepository.findByItemCodeAndSyncTime(customerConfig.getItemCode(), DateFormatUtil.parseDateNormal(currentDay));
            if (Objects.isNull(syncRecord) || PcsEnumConst.NO.getValue() == syncRecord.getIsSyncSuccess()) {
                // 当天未出账，则覆盖日期为当天
                log.info(String.format(
                        "overrideConfig occurred override.UnionID[%s],ItemCode[%s],beforeDay[%s],afterDay[%s]",
                        customerConfig.getUnionId(), customerConfig.getItemCode(), customerConfig.getBillingTime(),
                        currentDay));
                customerConfig.setBillingTime(DateFormatUtil.parseDateNormal(currentDay));
            }
        }
        // 逻辑删除旧的配置,直接插入新的配置
        int count = logicDeleteCustomerConfig(customerConfig.getUnionId(),
                Lists.newArrayList(customerConfig.getItemCode()));
    
        List<BillingCustomerConfig> customerConfigs = billingCustomerConfigRepository.findByUnionIdAndItemCodeIn(
                customerConfig.getUnionId(), Lists.newArrayList(customerConfig.getItemCode()));
        if (!CollectionUtils.isEmpty(customerConfigs)) {
            customerConfigs.forEach(config -> {
                //原未删除，需要改成删除
                if (new Integer(0).equals(config.getIsDeleted())) {
                    config.setIsDeleted(1);
                }
                //原来未过期清理过的，需要重置过期时间
                if (new Integer(0).equals(config.getIsReset())) {
                    config.setExpireTime(DateUtil.getEndOfDay(customerConfig.getExpireTime()));
                }
            });
            this.billingCustomerConfigRepository.saveAll(customerConfigs);
        }
        // todo 先查出来要作废的数据，判断结算时间是否已经执行||已经执行出账
        Date date = new Date();
        log.info("已经逻辑删除客户配置：unionId={},count={}", customerConfig.getUnionId(), count);
        
        //获取计费项资源编码
        BillingItem firstByCode = billingItemRepository.findFirstByCode(customerConfig.getItemCode());
        customerConfig.setIsDeleted(PcsEnumConst.NO.getValue());
        customerConfig.setCreateTime(date);
        customerConfig.setUpdateTime(date);
        customerConfig.setClearStartTime(DateUtil.getCurrentDay());
        customerConfig.setExpireTime(DateUtil.getEndOfDay(customerConfig.getExpireTime()));
        customerConfig.setOpenTime(new Date());
        customerConfig.setResourceCode(firstByCode.getResourceCode());
        this.billingCustomerConfigRepository.save(customerConfig);
    }
    
    /**
     * 构建客户配置化数据列表
     */
    /*public List<BillingCustomerConfig> buildBillingCustomer(List<OtherBillConfigItemDO> doList, SalesOpportunities so) {
        List<String> itemCodes = doList.stream().map(OtherBillConfigItemDO::getCode).collect(Collectors.toList());
        List<BillingItemExt> itemExtList = billingItemExtRepository.findByItemCodeIn(itemCodes);
        if (CollectionUtils.isEmpty(itemExtList)) {
            return new ArrayList<>();
        }
        List<Integer> periodIds = itemExtList.stream()
                .map(BillingItemExt::getBillingPeriodId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, BillingItemExt> extMap = itemExtList.stream()
                .collect(Collectors.toMap(BillingItemExt::getItemCode, v -> v));
        Map<Integer, BillingSettlementPeriodConfig> periodMap = billingSettlementPeriodConfigDao.billingPeriodMapByIds(
                periodIds);
        List<BillingCustomerConfig> customerConfigs = new ArrayList<>();
        for (OtherBillConfigItemDO item : doList) {
            BillingItemExt billingItemExt = extMap.get(item.getCode());
            BillingSettlementPeriodConfig periodConfig = periodMap.get(billingItemExt.getBillingPeriodId());
            LocalDate localDate = DateUtil.calculateDateByConfig(LocalDate.now(), periodConfig.getUnit(),
                    periodConfig.getBillingPeriod(), periodConfig.getAttribute());
            Date date = new Date();
            BillingCustomerConfig build = BillingCustomerConfig.builder()
                    .billingPeriodId(billingItemExt.getBillingPeriodId())
                    .itemCode(item.getCode())
                    .billingWayId(billingItemExt.getBillingWayId())
                    .isClearByDay(
                            checkIsClearByDay(periodConfig) ? PcsEnumConst.YES.getValue() : PcsEnumConst.NO.getValue())
                    .billingTime(DateUtil.localDate2Date(localDate))
                    .clearStartTime(DateUtil.getCurrentDay())
                    .expireTime(item.getExpireTimeEnd())
                    .unionId(so.getCustomerId())
                    .salesUserId(so.getSaleUserId())
                    .soId(so.getSoId())
                    .openTime(new Date())
                    .isDeleted(PcsEnumConst.NO.getValue())
                    .createTime(date)
                    .updateTime(date)
                    .build();
            customerConfigs.add(build);
        }
        return customerConfigs;
    }*/
    
    
    public int logicDeleteCustomerConfig(String customerId, List<String> itemCodes) {
        return this.billingCustomerConfigRepository.logicDeleteByCustomerAndItemCodes(customerId, itemCodes);
    }
    
    /**
     * 获取是否为按天结算 根据配置周期表的 unit =days 判断
     */
    public boolean checkIsClearByDay(BillingSettlementPeriodConfig billingSettlementPeriodConfig) {
        if (Objects.isNull(billingSettlementPeriodConfig)) {
            throw new BillConfigException("无结算周期配置！");
        }
        return ChronoUnit.DAYS.name().toLowerCase().equals(billingSettlementPeriodConfig.getUnit());
    }
    
}
