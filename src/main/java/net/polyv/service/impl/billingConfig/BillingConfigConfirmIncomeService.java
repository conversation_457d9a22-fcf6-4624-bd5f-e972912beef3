package net.polyv.service.impl.billingConfig;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.dao.primary.custom.FinanceContractDao;
import net.polyv.dao.primary.finance.FinanceContractRepository;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.service.FinanceIncomeConfirmService;
import net.polyv.service.finance.FinanceContractService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;

/**
 * 计费项配置化确认收入
 * <AUTHOR>
 * @date 2022/11/7 14:05
 */
@Component
@Slf4j
public class BillingConfigConfirmIncomeService {
    @Resource
    private FinanceIncomeConfirmService financeIncomeConfirmService;
    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;
    @Resource
    private FinanceContractDao financeContractDao;
    
    @Value("${pcs.config.numberBillWayId}")
    private Integer numberBillWayId;
    @Value("${pcs.config.ruleUseTimeWayId}")
    private Integer ruleUseTimeWayId;
    
    //按用量+限定使用时长
    @Value("${pcs.config.usageAndUseTimeWayId:7}")
    private Integer usageAndUseTimeWayId;
    @Resource
    private BillingConfigIncomeService billingConfigIncomeService;
    
    @Resource
    private FinanceContractRepository financeContractRepository;
    
    @Resource
    private FinanceContractService financeContractService;
    
    @Resource
    private DingWarnRobot dingWarnRobot;
    
    
    @Transactional(rollbackFor = Exception.class)
    public void confirmIncome(String contractId, Date incomeDate) {
        String currentMonth = DateFormatUtil.formatDateMonth(incomeDate);
        List<BillingCustomerConfig> allConfigList = new ArrayList<>();
        //获取正式充值的计费项配置记录
        if (StringUtils.isNotBlank(contractId)) {
            BillingCustomerConfig contractConfig = this.billingCustomerConfigRepository.findByContractIdAndResourceType(
                    contractId, ResourceTypeEnum.DEPOSIT.getCode());
            if (Objects.nonNull(contractConfig)) {
                allConfigList.add(contractConfig);
            }
        } else {
            allConfigList = this.billingCustomerConfigRepository.findByResourceType(ResourceTypeEnum.DEPOSIT.getCode());
        }
        if (CollectionUtils.isEmpty(allConfigList)) {
            return;
        }
        
        //筛选出incomeStatus == 0 或者incomeStatus == 1 && incomeMoth >= currentMonth 需要确认收入的计费项配置
        allConfigList = allConfigList.stream()
                .filter(config -> this.needConfirmIncome(config))
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(allConfigList)) {
            return;
        }
        //获取计费项方式
        List<List<BillingCustomerConfig>> partition = Lists.partition(allConfigList, 200);
        for (List<BillingCustomerConfig> list : partition) {
            List<String> contractIds = list.stream()
                    .map(BillingCustomerConfig::getContractId)
                    .collect(Collectors.toList());
            Map<String, FinanceContractEntity> financeContractEntityMap =
                    this.financeContractDao.mapContractByContractIds(
                    contractIds);
            //获取合同数据
            for (BillingCustomerConfig config : list) {
                FinanceContractEntity contract = financeContractEntityMap.get(config.getContractId());
                if (numberBillWayId.equals(config.getBillingWayId())) {
                    this.billingConfigIncomeService.numberItemIncomeStream(incomeDate, config, contract);
                }
                //按时长 和按用量+限定使用时长都是按天确认收入
                if (ruleUseTimeWayId.equals(config.getBillingWayId()) ||
                        usageAndUseTimeWayId.equals(config.getBillingWayId())) {
                    //需要保证单价是对 天数*单价=  收入
                    //这里过期时间不能取配置表的，因为会受后续充值记录覆盖，所以需要取合同规格表的
                    CommonResult<BusinessPackageSpecification> contractSpec = financeContractService.getContractSpec(
                            contract, DateUtil.getCurrentDay());
                    if (CommonResult.isOk(contractSpec) && Objects.nonNull(contractSpec.getData())) {
                        this.financeIncomeConfirmService.confirmDailyIncome(contract, currentMonth, incomeDate,
                                config.getOpenTime(), contractSpec.getData().getValidPeriodEndDate());
                    } else {
                        log.error("自定义计费项按时长确认收入失败，合同id：{},contractSpec ：{}", contract.getContractId(),
                                contractSpec);
                        dingWarnRobot.sendWarnMsg("【自定义计费项按时长确认收入失败】",
                                String.format("合同id：%s,contractSpec：%s", contract.getContractId(), contractSpec));
                    }
                }
                //用量确认收入会走该客户的金额合同进行确认收入
            }
        }
    }
    
    /**
     * 判断是否需要确认收入
     * 收入状态 = 0 或者 收入状态 = 1 && 收入月份 >= 当前月份
     * @param config
     * @return
     */
    private boolean needConfirmIncome(BillingCustomerConfig config) {
        FinanceContractEntity contract = financeContractRepository.findByContractId(config.getContractId());
        if (Objects.isNull(contract) || Objects.isNull(contract.getIncomeStatus())) {
            return false;
        }
        return contract.getIncomeStatus() == 0 ||
                (contract.getIncomeStatus() == 1 && StringUtils.isNotBlank(contract.getIncomeMonth()) &&
                        contract.getIncomeMonth().compareTo(DateFormatUtil.formatDateMonth(new Date())) >= 0);
    }
}
