package net.polyv.service.impl.billingConfig;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.ResourceAlterTypeConst;
import net.polyv.constant.finance.FinanceTaxRateConst;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.dao.primary.resource.ResourceAlterationRecordRepository;
import net.polyv.model.entity.primary.CustomerBillingDaily;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.model.entity.primary.resource.ResourceAlterationRecord;
import net.polyv.service.FinanceIncomeConfirmService;
import net.polyv.service.finance.FinanceIncomeService;
import net.polyv.util.converter.UnitConverterUtil;
import net.polyv.web.model.finance.data.CurrentFinanceIncomeDTO;

/**
 * <AUTHOR>
 * @date 2022/11/18 17:28
 */
@Slf4j
@Component
public class BillingConfigIncomeService {
    @Resource
    private FinanceIncomeConfirmService financeIncomeConfirmService;
    @Resource
    private FinanceIncomeService financeIncomeService;
    @Resource
    private CustomerBillingDailyRepository customerBillingDailyRepository;
    
    @Resource
    private ResourceAlterationRecordRepository resourceAlterationRecordRepository;
    
    
    public void numberItemIncomeStream(Date date, BillingCustomerConfig billingCustomerConfig,
            FinanceContractEntity contract) {
        CurrentFinanceIncomeDTO currentFinanceIncome = this.financeIncomeService.getCurrentFinanceIncome(contract,
                date);
        //从账单表获取总消耗量：因为次数类会有到期清零的需要从账单表过滤出
        BigDecimal businessConsumed = this.financeIncomeConfirmService.getBusinessConsumed(
                billingCustomerConfig.getItemCode(), null, date, billingCustomerConfig.getUnionId(),
                contract.getContractId());
        BigDecimal ratio = new BigDecimal(contract.getIncomeUnivalent());
        BigDecimal usedConsumed = businessConsumed;
        BigDecimal otherConsumed = BigDecimal.ZERO;
        BigDecimal otherIncome = BigDecimal.ZERO;
    
        if (PcsEnumConst.YES.getValue() == billingCustomerConfig.getIsExpireReset()) {
            //一个合同只有一条清零记录
            List<ResourceAlterationRecord> list = resourceAlterationRecordRepository.findByContractIdAndType(
                    contract.getContractId(), ResourceAlterTypeConst.expire_clean.name());
            if (!CollectionUtils.isEmpty(list)) {
                ResourceAlterationRecord resourceAlterationRecord = list.get(0);
                otherConsumed = BigDecimal.valueOf(Math.negateExact(resourceAlterationRecord.getAlteration()));
                otherIncome = otherConsumed.multiply(ratio)
                        .divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0, BigDecimal.ROUND_HALF_EVEN);
                int max = Math.max(businessConsumed.intValue(), 0);
                usedConsumed = new BigDecimal(max);
            }
    
        }
        BigDecimal usedIncome = BigDecimal.ZERO;
        //素材库流量需要将字节转成gb，再算收入
        if (ItemCodeConst.material_traffic.getCode().equals(billingCustomerConfig.getItemCode())) {
            usedIncome = UnitConverterUtil.bytes2GB(usedConsumed)
                    .multiply(ratio)
                    .divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0, BigDecimal.ROUND_HALF_EVEN);
        } else {
            usedIncome = usedConsumed.multiply(ratio)
                    .divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0, BigDecimal.ROUND_HALF_EVEN);
        }
    
        log.info("当前合同收入数据： contractId ={},userIncome={},userConsumed={},otherIncome={},otherConsumed={}",
                contract.getContractId(), usedIncome, usedConsumed, otherIncome, otherConsumed);
        this.financeIncomeService.setCurrentFinanceIncome(contract, currentFinanceIncome, usedIncome, otherIncome,
                usedConsumed, otherConsumed);
        log.info("生成次数收入完成：date={},contractId={}", date, contract.getContractId());
    }
    
    
}
