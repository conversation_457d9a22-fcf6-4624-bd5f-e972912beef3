package net.polyv.service.impl;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.SysTypeConst;
import net.polyv.constant.common.PrecisionConst;
import net.polyv.constant.contract.ContractConstant;
import net.polyv.constant.customer.LiveBillingType;
import net.polyv.constant.deposit.CustomizedAccountBusinessTypeEnum;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.constant.group.ResourceAssignBusinessTypeEnum;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.dao.primary.CustomizedAccountDepositDetailRepository;
import net.polyv.dao.primary.GroupResourceAssignableRepository;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.deposit.DepositResourceDTO;
import net.polyv.model.data.group.GroupResourceAssignableCalculateDTO;
import net.polyv.model.data.salesopportunities.UserFunctionDO;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.CustomizedAccountDepositDetail;
import net.polyv.model.entity.primary.GroupResourceAssignable;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.*;
import net.polyv.modules.pcs.api.req.testdonate.DonateBillingItemResourceRequest;
import net.polyv.modules.pcs.api.req.testdonate.NumberRequest;
import net.polyv.modules.pcs.api.vo.BillingCustomerConfigVO;
import net.polyv.modules.user.api.req.UserConfigSaveReq;
import net.polyv.modules.user.api.service.UserConfigApi;
import net.polyv.modules.user.api.stereotype.UserConfigKey;
import net.polyv.rest.client.live.LivePackageClient;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.client.live.UserPackageClient;
import net.polyv.rest.client.live.business.LiveInnerFinanceApiClient;
import net.polyv.rest.client.vod.business.VodBusinessOperationClient;
import net.polyv.rest.model.live.UserFunctionVO;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.rest.util.ResponseUtil;
import net.polyv.service.AccountDepositService;
import net.polyv.service.CacheService;
import net.polyv.service.UserEstablishPackageService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.impl.examinationDonate.ResourceBillingItemService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.service.so.SalesOpportEstablishBusinessService;
import net.polyv.util.*;
import net.polyv.util.converter.UnitConverterUtil;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.account.DepositInputVO;
import net.polyv.web.model.account.GetBalanceStatusResultVO;
import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.examinationDonate.ResourceTypeExpireDateVO;
import net.polyv.web.model.user.CustomerStateVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class UserEstablishPackageServiceImpl implements UserEstablishPackageService {
    
    @Resource
    private CacheService cacheService;
    
    @Autowired
    private AccountDepositService accountDepositService;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private LiveInnerFinanceApiClient liveInnerApiClient;
    
    @Autowired
    private VodBusinessOperationClient vodBusinessOperationClient;

    @Autowired
    private SalesOpportEstablishBusinessService establishBusinessService;

    @Resource
    private LivePackageClient livePackageClient;

    @Resource
    private DingWarnRobot dingWarnRobot;

    @Resource
    private CustomerResourceService customerResourceService;

    @Resource
    private CustomizedAccountDepositDetailRepository customizedAccountDepositDetailRepository;

    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;

    @Autowired
    private UserConfigApi userConfigApi;

    @Autowired
    private CustomerService customerService;

    @Resource
    private GroupResourceAssignableRepository groupResourceAssignableRepository;

    @Override
    public CommonResult depositVodFlowPackage(DepositResourceDTO request) {
        DepositResourceRequest depositResourceRequest = request.getDepositResourceRequest();
        DepositFlowPackageRequest vodFlow = depositResourceRequest.getFlowPackageRequest();
        String contractId = "customized-"+StringUtil.getUUID().substring(0,10);
        Date endDate = vodFlow.getEndDate();
        String businessId = StringUtil.getUUID();
        String key = getEstablishPackageBusinessKey(businessId);
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(contractId,businessId
                ,ResourceTypeEnum.DEPOSIT.getCode(), DateFormatUtil.formatDateNormal(endDate));
        redisTemplate.opsForValue().set(key,JsonMapper.jsonToString(vo),24 * 3600, TimeUnit.SECONDS);


        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddVodPackageSign(timestamp);
        String flowStartDate = DateFormatUtil.formatDateNormal(vodFlow.getStartDate());
        String flowEndDate = DateFormatUtil.formatDateNormal(endDate);
        ResponseEntity<WrappedResponse<?>> wrappedResponse = vodBusinessOperationClient.addFlowOrSpace(
                request.getVodUserId(),sign, String.valueOf(vodFlow.getFlow()),"4", flowStartDate,
                String.valueOf(timestamp), flowEndDate, contractId, businessId);
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("vodUserId == {},addVodFlow error,response == {}", request.getVodUserId(), wrappedResponse);
            dingWarnRobot.sendWarnMsg("【集团分账号充值点播流量出错】",
                    getErrorMsg(wrappedResponse, request.getDepositResourceRequest().getCustomerId()));
            return CommonResult.fail("开通点播流量失败");
        }
        request.setContractId(contractId);
    
        saveDepositDetail(CustomizedAccountBusinessTypeEnum.FLOW_PACKAGE, depositResourceRequest.getGroupId(),
                depositResourceRequest.getCustomerId(), vodFlow.getStartDate(), vodFlow.getEndDate(), vodFlow.getFlow(),
                contractId);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(depositResourceRequest.getGroupId())
                .businessType(ResourceAssignBusinessTypeEnum.TRAFFIC.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        return CommonResult.ok();
    }

    @Override
    public CommonResult depositVodTempSpace(DepositResourceDTO request) {
        DepositResourceRequest depositResourceRequest = request.getDepositResourceRequest();
        DepositTempSpaceRequest vodSpace = depositResourceRequest.getTempSpaceRequest();

        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddVodPackageSign(timestamp);
        String contractId = "customized-"+StringUtil.getUUID().substring(0,10);
        request.setContractId(contractId);
        String spaceStartDate = DateFormatUtil.formatDateNormal(vodSpace.getStartDate());
        String spaceEndDate = DateFormatUtil.formatDateNormal(vodSpace.getEndDate());
        ResponseEntity<WrappedResponse<?>> wrappedResponse = vodBusinessOperationClient.addFlowOrSpace(
                request.getVodUserId(), sign, String.valueOf(vodSpace.getSpace()), "3", spaceStartDate,
                String.valueOf(timestamp), spaceEndDate, contractId, null);
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("vodUserId == {},addVodSpace error,response == {}", request.getVodUserId(), wrappedResponse);
            dingWarnRobot.sendWarnMsg("【集团分账号充值点播空间出错】",
                    getErrorMsg(wrappedResponse, request.getDepositResourceRequest().getCustomerId()));
            return CommonResult.fail("开通点播空间失败");
        }
        saveDepositDetail(CustomizedAccountBusinessTypeEnum.TEMP_SPACE, depositResourceRequest.getGroupId(),
                depositResourceRequest.getCustomerId(), vodSpace.getStartDate(), vodSpace.getEndDate(),
                vodSpace.getSpace(), contractId);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(depositResourceRequest.getGroupId())
                .businessType(ResourceAssignBusinessTypeEnum.SPACE.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        return CommonResult.ok();
    }

    /**
     * 充值金额（目标值）
     * @param request
     * @return
     */
    @Override
    public CommonResult depositAmount(DepositResourceDTO request) {
        DepositAmountRequest amountRequest = request.getDepositResourceRequest().getAmountRequest();
        String customerId = request.getDepositResourceRequest().getCustomerId();
        String groupId = request.getDepositResourceRequest().getGroupId();
        long donate = amountRequest.getDonate();
        //获取当前可用赠送金额 +本次充值金额（可能为负数）
        GetBalanceStatusResultVO accountBalanceState = accountDepositService.getBalanceStatus(
                CustomerInfoGetInputVO.builder().customerId(customerId).build());
        donate = Objects.nonNull(accountBalanceState) ? accountBalanceState.getValidDonateAmount() + donate : donate;
        DepositInputVO depositInputVO = DepositInputVO.builder()
                .donate(donate)
                .amountExpireDate(amountRequest.getAmountExpireDate())
                .amountExpireType(amountRequest.getAmountExpireType())
                .customerId(customerId)
                .contractId(ContractConstant.DEFAULT_CONTRACT_ID)
                .build();
        CommonOperateResultVO resultVO = establishBusinessService.groupUserEstablishAmount(depositInputVO);
        if (!CommonOperateResultVO.isSuccess(resultVO)) {
            dingWarnRobot.sendWarnMsg("【集团分账号充值金额出错】", "request == " + depositInputVO);
            return CommonResult.ok();
        }
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(groupId)
                .businessType(ResourceAssignBusinessTypeEnum.AMOUNT.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        return CommonResult.fail("金额充值失败");
    }

    @Override
    public CommonResult depositMicDuration(DepositResourceDTO request) {
        DepositResourceRequest depositResourceRequest = request.getDepositResourceRequest();
        DepositMicDurationRequest micDurationRequest = depositResourceRequest.getMicDurationRequest();
        String contractId = "customized-"+StringUtil.getUUID().substring(0,10);
        Date endDate = micDurationRequest.getEndDate();
        String businessId = StringUtil.getUUID();
        String key = getEstablishPackageBusinessKey(businessId);
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(contractId,businessId
                , ResourceTypeEnum.DEPOSIT.getCode(), DateFormatUtil.formatDateNormal(endDate));
        redisTemplate.opsForValue().set(key, JsonMapper.jsonToString(vo),24 * 3600, TimeUnit.SECONDS);

        ResponseEntity<WrappedResponse<?>> wrappedResponse = liveInnerApiClient.addMicPackages(request.getLiveUserId(),
                String.valueOf(micDurationRequest.getDuration()), "0", null, "0",
                LiveBillingType.Mic_Minutes.getValue(), String.valueOf(micDurationRequest.getDuration()), "0",
                businessId);
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("liveUserId == {},addMicDuration error,response == {}", request.getLiveUserId(), wrappedResponse);
            dingWarnRobot.sendWarnMsg("【集团分账号充值连麦分钟数出错】",
                    getErrorMsg(wrappedResponse, request.getDepositResourceRequest().getCustomerId()));
            return CommonResult.fail("开通连麦分钟数失败");
        }
        saveDepositDetail(CustomizedAccountBusinessTypeEnum.MIC_DURATION, depositResourceRequest.getGroupId(),
                depositResourceRequest.getCustomerId(), micDurationRequest.getStartDate(),
                micDurationRequest.getEndDate(), BigDecimal.valueOf(micDurationRequest.getDuration()), contractId);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(depositResourceRequest.getGroupId())
                .businessType(ResourceAssignBusinessTypeEnum.MIC_DURATION.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        return CommonResult.ok();
    }

    @Override
    public CommonResult depositLiveDuration(DepositResourceDTO request) {
        DepositResourceRequest depositResourceRequest = request.getDepositResourceRequest();
        DepositLiveDurationRequest liveDurationRequest = depositResourceRequest.getLiveDurationRequest();
        long maxTimeLong = liveDurationRequest.getEndDate().getTime();
        if(customerResourceService.isCustomizedGroupId(depositResourceRequest.getGroupId())) {
            //获取有效期内（包括此次充值）最大过期时间max(expireDate)，打套餐
            List<CustomizedAccountDepositDetail> list = customizedAccountDepositDetailRepository.getDepositDetailList(depositResourceRequest.getCustomerId(),
                    CustomizedAccountBusinessTypeEnum.DURATION.getCode(), DateUtil.getCurrentDay());
            if (CollectionUtils.isNotEmpty(list)) {
                //有效期内最大过期时间
                CustomizedAccountDepositDetail maxExpireDetail = list.stream().max(Comparator.comparing(CustomizedAccountDepositDetail::getExpireDate)).get();
                maxTimeLong = Math.max(liveDurationRequest.getEndDate().getTime(), maxExpireDetail.getExpireDate().getTime());
            }
        }
        String contractId = "customized-"+StringUtil.getUUID().substring(0,10);
        Date endDate = liveDurationRequest.getEndDate();
        String businessId = StringUtil.getUUID();
        String key = getEstablishPackageBusinessKey(businessId);
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(contractId,businessId
                ,ResourceTypeEnum.DEPOSIT.getCode(), DateFormatUtil.formatDateNormal(endDate));
        redisTemplate.opsForValue().set(key,JsonMapper.jsonToString(vo),24 * 3600, TimeUnit.SECONDS);

        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddPackageSign(timestamp);
        String packageId = String.valueOf(liveDurationRequest.getPackageId());
        //生效时间默认为当天
        String startDate = DateFormatUtil.formatDateNormal(liveDurationRequest.getStartDate());
        // 自定义套餐的设置使用基础版套餐设置
        String pId = (org.apache.commons.lang3.StringUtils.isNumeric(packageId)) ? packageId : "1";
        ResponseEntity<WrappedResponse<?>> wrappedResponse = livePackageClient.setPackagePro(request.getLiveUserId(),
                String.valueOf(timestamp),sign, LiveBillingType.Duration.getValue(),
                String.valueOf(liveDurationRequest.getDuration()),"-1","-1",
                startDate, DateFormatUtil.longToDateStr(maxTimeLong, DateFormatUtil.FORMAT_DATE_NORMAL), pId,
                liveDurationRequest.getIsUpdateStats(), String.valueOf(liveDurationRequest.getMaxChannels()), null, "0",
                businessId);
    
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("liveUserId == {},setPackagePro error,response == {}", request.getLiveUserId(), wrappedResponse);
            dingWarnRobot.sendWarnMsg("【集团分账号充值直播分钟数出错】",
                    getErrorMsg(wrappedResponse, request.getDepositResourceRequest().getCustomerId()));
            return CommonResult.fail("开通直播分钟数失败");
        }
        saveDepositDetail(CustomizedAccountBusinessTypeEnum.DURATION, depositResourceRequest.getGroupId(),
                depositResourceRequest.getCustomerId(), liveDurationRequest.getStartDate(),
                liveDurationRequest.getEndDate(), BigDecimal.valueOf(liveDurationRequest.getDuration()), contractId);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(depositResourceRequest.getGroupId())
                .businessType(ResourceAssignBusinessTypeEnum.DURATION.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        return CommonResult.ok();
    }

    @Override
    public CommonResult depositLiveConcurrenceWithoutRecalculate(DepositResourceDTO request) {
        DepositLiveConcurrenceRequest liveConcurrenceRequest = request.getDepositResourceRequest().getLiveConcurrenceRequest();
        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddPackageSign(timestamp);
        String pId = String.valueOf(liveConcurrenceRequest.getPackageId());
        ResponseEntity<WrappedResponse<?>> wrappedResponse = livePackageClient.setPackagePro(
                request.getLiveUserId(),String.valueOf(timestamp),sign,
                liveConcurrenceRequest.getLiveBillingPlan(),"0",
                String.valueOf(liveConcurrenceRequest.getConcurrences()),
                String.valueOf(liveConcurrenceRequest.getConcurrences()),
                DateFormatUtil.formatDateNormal(liveConcurrenceRequest.getStartDate()),
                DateFormatUtil.formatDateNormal(liveConcurrenceRequest.getEndDate()), pId,
                liveConcurrenceRequest.getIsUpdateStats(), String.valueOf(liveConcurrenceRequest.getMaxChannels()),
                null, "0", null);
    
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("liveUserId == {},setPackagePro error,response == {}", request.getLiveUserId(), wrappedResponse);
            dingWarnRobot.sendWarnMsg("【集团分账号充值直播套餐出错】",
                    getErrorMsg(wrappedResponse, request.getDepositResourceRequest().getCustomerId()));
            return CommonResult.fail("开通并发套餐失败");
        }
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(request.getDepositResourceRequest().getGroupId())
                .businessType(ResourceAssignBusinessTypeEnum.CONCURRENCE.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        return CommonResult.ok();
    }

    @Override
    public CommonResult depositLiveConcurrence(DepositResourceDTO request) {
        DepositResourceRequest depositResourceRequest = request.getDepositResourceRequest();
        DepositLiveConcurrenceRequest liveConcurrenceRequest = depositResourceRequest.getLiveConcurrenceRequest();
        long maxTimeLong = liveConcurrenceRequest.getEndDate().getTime();
        int totalConcurrence = liveConcurrenceRequest.getConcurrences();

        //定制客户流程
        if(customerResourceService.isCustomizedGroupIdByUnionId(depositResourceRequest.getCustomerId())){
            //获取有效期内（包括此次充值）的所有并发sum(concurrence)和最大过期时间max(expireDate)，打套餐
            List<CustomizedAccountDepositDetail> list = customizedAccountDepositDetailRepository.getDepositDetailList(depositResourceRequest.getCustomerId(),
                    CustomizedAccountBusinessTypeEnum.CONCURRENCE.getCode(), DateUtil.getCurrentDay());
            if(CollectionUtils.isNotEmpty(list)){
                //有效期内总并发
                BigDecimal sumConcurrence = list.stream().map(CustomizedAccountDepositDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                DepositLiveConcurrenceRequest concurrenceRequest = depositResourceRequest.getLiveConcurrenceRequest();
                totalConcurrence = concurrenceRequest.getConcurrences() + sumConcurrence.intValue();
                //有效期内最大过期时间
                CustomizedAccountDepositDetail maxExpireDetail = list.stream().max(Comparator.comparing(CustomizedAccountDepositDetail::getExpireDate)).get();
                maxTimeLong = Math.max(concurrenceRequest.getEndDate().getTime(),maxExpireDetail.getExpireDate().getTime());
            }
        }
        else{
           //非定制话客户，获取打套餐前并发可用 + 本次充值（可能为负数），作为并发数
            CustomerStateVO result = UserPackageClient.getUsersPackageByEmail(request.getEmail());
            if(Objects.nonNull(result)){
                totalConcurrence = result.getLivePackageState().getLiveConcurrences().intValue() +
                        liveConcurrenceRequest.getConcurrences() ;
            }
        }

        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddPackageSign(timestamp);
        String pId = String.valueOf(liveConcurrenceRequest.getPackageId());
        ResponseEntity<WrappedResponse<?>> wrappedResponse = livePackageClient.setPackagePro(
                request.getLiveUserId(),String.valueOf(timestamp),sign,
                liveConcurrenceRequest.getLiveBillingPlan(),"0",String.valueOf(totalConcurrence),
                String.valueOf(totalConcurrence),
                DateFormatUtil.formatDateNormal(liveConcurrenceRequest.getStartDate()),
                DateFormatUtil.longToDateStr(maxTimeLong,DateFormatUtil.FORMAT_DATE_NORMAL),
                pId,liveConcurrenceRequest.getIsUpdateStats(),
                String.valueOf(liveConcurrenceRequest.getMaxChannels()), null, "0", null);
    
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("liveUserId == {},setPackagePro error,response == {}", request.getLiveUserId(), wrappedResponse);
            dingWarnRobot.sendWarnMsg("【集团分账号充值直播套餐出错】",
                    getErrorMsg(wrappedResponse, request.getDepositResourceRequest().getCustomerId()));
            return CommonResult.fail("开通并发套餐失败");
        }
        String contractId = "customized-" + StringUtil.getUUID().substring(0, 10);
        saveDepositDetail(CustomizedAccountBusinessTypeEnum.CONCURRENCE, depositResourceRequest.getGroupId(),
                depositResourceRequest.getCustomerId(), liveConcurrenceRequest.getStartDate(),
                liveConcurrenceRequest.getEndDate(), BigDecimal.valueOf(liveConcurrenceRequest.getConcurrences()),
                contractId);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(request.getDepositResourceRequest().getGroupId())
                .businessType(ResourceAssignBusinessTypeEnum.CONCURRENCE.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        return CommonResult.ok();
    }

    @Override
    public CommonResult depositGuideDuration(DepositResourceDTO request) {
        DepositResourceRequest depositResourceRequest = request.getDepositResourceRequest();
        DepositGuideDurationRequest guideDurationRequest = depositResourceRequest.getGuideDurationRequest();
        String contractId = "customized-"+StringUtil.getUUID().substring(0,10);
        Date endDate = guideDurationRequest.getEndDate();

        String businessId = StringUtil.getUUID();
        String key = getEstablishPackageBusinessKey(businessId);
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(contractId,businessId
                ,ResourceTypeEnum.DEPOSIT.getCode(), DateFormatUtil.formatDateNormal(endDate));
        redisTemplate.opsForValue().set(key,JsonMapper.jsonToString(vo),24 * 3600, TimeUnit.SECONDS);
    
    
        ResponseEntity<WrappedResponse<?>> wrappedResponse = liveInnerApiClient.addGuideDurations(
                request.getLiveUserId(), String.valueOf(guideDurationRequest.getDuration()), "0", businessId);
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("liveUserId == {},addGuideDuration error,response == {}", request.getLiveUserId(),
                    wrappedResponse);
            dingWarnRobot.sendWarnMsg("【集团分账号充值导播台分钟数出错】",
                    getErrorMsg(wrappedResponse, request.getDepositResourceRequest().getCustomerId()));
            return CommonResult.fail("开通导播台分钟数失败");
        }
        saveDepositDetail(CustomizedAccountBusinessTypeEnum.GUIDE_DURATION, depositResourceRequest.getGroupId(),
                depositResourceRequest.getCustomerId(), guideDurationRequest.getStartDate(),
                guideDurationRequest.getEndDate(), BigDecimal.valueOf(guideDurationRequest.getDuration()), contractId);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(request.getDepositResourceRequest().getGroupId())
                .businessType(ResourceAssignBusinessTypeEnum.GUIDE_DURATION.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        return CommonResult.ok();
    }

    @Override
    public CommonResult depositChannels(DepositResourceDTO request) {
        DepositChannelsRequest aChannelsRequest = request.getDepositResourceRequest().getChannelsRequest();
        ResponseEntity<WrappedResponse<?>> wrappedResponse = liveInnerApiClient.addChannels(request.getLiveUserId(),
                String.valueOf(aChannelsRequest.getChannels()));
        if(ResponseUtil.isErrorResponse(wrappedResponse)){
            log.error("liveUserId == {},addChannels error,response == {}",request.getLiveUserId(),wrappedResponse);
            dingWarnRobot.sendWarnMsg("【集团分账号充值频道数出错】",getErrorMsg(wrappedResponse,request.getDepositResourceRequest().getCustomerId()));
            return CommonResult.fail("增加频道数失败");
        }
        return CommonResult.ok();
    }

    private String getEstablishPackageBusinessKey(String businessId){
        return String.format(GlobalConfigConst.ESTABLISH_PACKAGE_BUSINESS_KEY,businessId);
    }

    private ResourceTypeExpireDateVO buildResourceTypeExpireDateVO(String businessId,Integer resourceType,String expireDate){
        return ResourceTypeExpireDateVO.builder().businessId(businessId)
                .resourceType(resourceType).expireDate(expireDate).build();
    }

    private ResourceTypeExpireDateVO buildResourceTypeExpireDateVO(String contractId,String businessId,Integer resourceType,String expireDate){
        return ResourceTypeExpireDateVO.builder().businessId(businessId).contractId(contractId)
                .resourceType(resourceType).expireDate(expireDate).build();
    }
    private String getErrorMsg(ResponseEntity<WrappedResponse<?>> wrappedResponse,String unionId){
        String errorMsg = String.format("unionId == %s，response == %s",unionId,wrappedResponse);
        return  errorMsg;
    }

    /**
     * 保存定制化客户的充值详情
     * @param businessTypeEnum
     * @param groupId
     * @param unionId
     * @param startDate
     * @param endDate
     * @param amount
     */
    private void saveDepositDetail(CustomizedAccountBusinessTypeEnum businessTypeEnum,String groupId,
                                      String unionId, Date startDate, Date endDate, BigDecimal amount,String contractId){
        if(customerResourceService.isCustomizedGroupId(groupId)){
            CustomizedAccountDepositDetail depositDetail = new CustomizedAccountDepositDetail();
            depositDetail.setDepositId(contractId);
            depositDetail.setBusinessType(businessTypeEnum.getCode());
            depositDetail.setEffectiveDate(startDate);
            depositDetail.setExpireDate(endDate);
            depositDetail.setAmount(amount);
            if(CustomizedAccountBusinessTypeEnum.TEMP_SPACE.getCode().equals(businessTypeEnum.getCode()) ||
                    CustomizedAccountBusinessTypeEnum.FLOW_PACKAGE.getCode().equals(businessTypeEnum.getCode())){
                depositDetail.setAvailable(UnitConverterUtil.GB2bytes(amount));
            }
            else{
                depositDetail.setAvailable(amount.longValue());
            }
            depositDetail.setCustomerId(unionId);
            depositDetail.setGroupId(groupId);
            depositDetail.setCreateTime(new Date());
            customizedAccountDepositDetailRepository.save(depositDetail);
        }
    }

    @Override
    public CommonResult depositMaterialSpace(DepositResourceDTO request) {
        DonateBillingItemResourceRequest donateRequest = new DonateBillingItemResourceRequest();
        donateRequest.setLiveUserId(request.getLiveUserId());
        donateRequest.setVodUserId(request.getVodUserId());

        //判断所属集团主账号是否有该资源项资源
        if(StringUtils.isNotBlank(request.getDepositResourceRequest().getGroupId())){
            List<BillingCustomerConfig> mspaceConfigList =
                    billingCustomerConfigRepository.findByUnionIdAndItemCodeAndIsDeletedAndExpireTimeGreaterThanEqual(
                            request.getDepositResourceRequest().getGroupId(), ItemCodeConst.material_space.getCode(), 0, new Date());
            if(CollectionUtils.isEmpty(mspaceConfigList)){
                log.error("liveUserId == {},groupId {} materialspace not package error",request.getLiveUserId(),request.getGroupId());
                return CommonResult.fail("增加素材库空间失败，主账号没有对应的资源套餐");
            }

            //判断是否足够分配
            if(!groupAccountEnoughResourceToAssign(request.getDepositResourceRequest().getGroupId(),ResourceAssignBusinessTypeEnum.MATERIAL_SPACE.getCode(),
                    request.getDepositResourceRequest().getMaterialSpaceRequest().getSpace(),true)){
                log.error("liveUserId == {},groupId {} materialspace resource not enough error",request.getLiveUserId(),request.getGroupId());
                return CommonResult.fail("增加素材库空间失败，主账号没有足够的可分配资源");
            }
        }

        List<NumberRequest> resourceList = new ArrayList<>();
        NumberRequest numberRequest = new NumberRequest();
        numberRequest.setNumber(request.getDepositResourceRequest().getMaterialSpaceRequest().getSpace());
        numberRequest.setItemCode(ItemCodeConst.material_space.getCode());

        String expiredDate = getResourceExpiredDate(request,ItemCodeConst.material_space.getCode());
        if(null == expiredDate){
            log.error("liveUserId == {},groupId {} materialspace not package error",request.getLiveUserId(),request.getGroupId());
            return CommonResult.fail("增加素材库空间失败，主账号没有对应的素材资源套餐");
        }
        numberRequest.setExpireDate(expiredDate);

        resourceList.add(numberRequest);
        donateRequest.setResourceList(resourceList);
        customerResourceService.donateBillingItemResourceForGroup(donateRequest);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(request.getDepositResourceRequest().getGroupId())
                .businessType(ResourceAssignBusinessTypeEnum.MATERIAL_SPACE.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        //开启分账号的素材库开关
        UserConfigSaveReq userConfigSaveReq = new UserConfigSaveReq();
        userConfigSaveReq.setUserId(request.getLiveUserId());
        List<UserConfigSaveReq.KeyValue> items = Lists.newArrayList();
        userConfigSaveReq.setItems(items);
        items.add(new UserConfigSaveReq.KeyValue(UserConfigKey.MATERIAL_LIBRARY_ENABLED, SwitchEnum.Y.getCode()));
        userConfigApi.updateBatch(userConfigSaveReq);
        return CommonResult.ok();
    }

    @Override
    public CommonResult depositMaterialTraffic(DepositResourceDTO request) {
        DonateBillingItemResourceRequest donateRequest = new DonateBillingItemResourceRequest();
        donateRequest.setLiveUserId(request.getLiveUserId());
        donateRequest.setVodUserId(request.getVodUserId());
        String groupId = request.getDepositResourceRequest().getGroupId();
        //判断所属集团主账号是否有该资源项资源
        if(StringUtils.isNotBlank(groupId)){
            List<BillingCustomerConfig> mtrafficConfigList =
                    billingCustomerConfigRepository.findByUnionIdAndItemCodeAndIsDeletedAndExpireTimeGreaterThanEqual(
                            groupId, ItemCodeConst.material_traffic.getCode(), 0, new Date());
            if(CollectionUtils.isEmpty(mtrafficConfigList)){
                log.error("liveUserId == {},groupId {} materialtraffic not package error",request.getLiveUserId(),request.getGroupId());
                return CommonResult.fail("增加素材库流量失败，主账号没有对应的资源套餐");
            }
            //判断是否足够分配
            if(!groupAccountEnoughResourceToAssign(groupId,ResourceAssignBusinessTypeEnum.MATERIAL_TRAFFIC.getCode(),
                    request.getDepositResourceRequest().getMaterialTrafficRequest().getFlow(),true)){
                log.error("liveUserId == {},groupId {} materialtraffic resource not enough error",request.getLiveUserId(),request.getGroupId());
                return CommonResult.fail("增加素材库流量失败，主账号没有足够的可分配资源");
            }
        }

        List<NumberRequest> resourceList = new ArrayList<>();
        NumberRequest numberRequest = new NumberRequest();
        numberRequest.setNumber(request.getDepositResourceRequest().getMaterialTrafficRequest().getFlow());
        numberRequest.setItemCode(ItemCodeConst.material_traffic.getCode());

        String expiredDate = getResourceExpiredDate(request,ItemCodeConst.material_traffic.getCode());
        if(null == expiredDate){
            log.error("liveUserId == {},groupId {} materialtraffic not package error",request.getLiveUserId(),request.getGroupId());
            return CommonResult.fail("增加素材库流量失败，主账号没有对应的素材资源套餐");
        }
        numberRequest.setExpireDate(expiredDate);

        resourceList.add(numberRequest);
        donateRequest.setResourceList(resourceList);
        customerResourceService.donateBillingItemResourceForGroup(donateRequest);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(groupId)
                .businessType(ResourceAssignBusinessTypeEnum.MATERIAL_TRAFFIC.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        //开启分账号的素材库开关
        UserConfigSaveReq userConfigSaveReq = new UserConfigSaveReq();
        userConfigSaveReq.setUserId(request.getLiveUserId());
        List<UserConfigSaveReq.KeyValue> items = Lists.newArrayList();
        userConfigSaveReq.setItems(items);
        items.add(new UserConfigSaveReq.KeyValue(UserConfigKey.MATERIAL_LIBRARY_ENABLED, SwitchEnum.Y.getCode()));
        userConfigApi.updateBatch(userConfigSaveReq);
        return CommonResult.ok();
    }

    private String getResourceExpiredDate(DepositResourceDTO request,String itemCode){
        Date endDate = null;
        if(itemCode.equals(ItemCodeConst.material_traffic.getCode())){
            endDate = request.getDepositResourceRequest().getMaterialTrafficRequest().getEndDate();
        }
        if(itemCode.equals(ItemCodeConst.material_space.getCode())){
            endDate = request.getDepositResourceRequest().getMaterialSpaceRequest().getEndDate();
        }
        if(itemCode.equals(ItemCodeConst.ai_smart_class.getCode())){
            endDate = request.getDepositResourceRequest().getDepositAiVideoRequest().getEndDate();
        }
        if(itemCode.equals(ItemCodeConst.ai_digital_human.getCode())){
            endDate = request.getDepositResourceRequest().getDepositAiVideoWithDigitalRequest().getEndDate();
        }

        if(StringUtils.isBlank(request.getGroupId())){
            return DateFormatUtil.formatDateNormal(endDate);
        }

        //获取有效期内信息列表
        List<BillingCustomerConfig> configList =
                billingCustomerConfigRepository.findByUnionIdAndItemCodeAndIsDeletedAndExpireTimeGreaterThanEqual(
                        request.getGroupId(), itemCode, 0, new Date());
        if (!CollectionUtils.isEmpty(configList)) {
            //获取configList中BillingCustomerConfig的最大expireTime
            Date maxExpireTime = configList.stream()
                    .filter(obj -> Objects.nonNull(obj.getExpireTime()))
                    .map(BillingCustomerConfig::getExpireTime)
                    .max(Date::compareTo)
                    .get();
            if(Objects.nonNull(maxExpireTime)){
                endDate = maxExpireTime;
            }
        }
        return DateFormatUtil.formatDateNormal(endDate);
    }

    /**
     * 视频创作（不带数字人)
     * @param request
     */
    @Override
    public CommonResult depositAIPPTVideo(DepositResourceDTO request) {
        DonateBillingItemResourceRequest donateRequest = new DonateBillingItemResourceRequest();
        donateRequest.setLiveUserId(request.getLiveUserId());
        donateRequest.setVodUserId(request.getVodUserId());
        String groupId = request.getDepositResourceRequest().getGroupId();
        //判断所属集团主账号是否有该资源项资源
        if(StringUtils.isNotBlank(groupId)){
            List<BillingCustomerConfig> aiPptVideoConfigList =
                    billingCustomerConfigRepository.findByUnionIdAndItemCodeAndIsDeletedAndExpireTimeGreaterThanEqual(
                            groupId, ItemCodeConst.ai_smart_class.getCode(), 0, new Date());
            if(CollectionUtils.isEmpty(aiPptVideoConfigList)){
                log.error("liveUserId == {},groupId {} aiPPTVideoEnabled not package error",request.getLiveUserId(),groupId);
                return CommonResult.fail("增加视频创作（不带数字人)失败，主账号没有对应的资源");
            }
            //判断是否足够分配
            if(!groupAccountEnoughResourceToAssign(groupId,ResourceAssignBusinessTypeEnum.AI_PPTVIDEO_ENABLED.getCode(),
                    request.getDepositResourceRequest().getDepositAiVideoRequest().getDuration(),false)){
                log.error("liveUserId == {},groupId {} aiPPTVideoEnabled resource not enough error",request.getLiveUserId(),groupId);
                return CommonResult.fail("增加视频创作（不带数字人)失败，主账号没有足够的可分配资源");
            }
        }

        List<NumberRequest> resourceList = new ArrayList<>();
        NumberRequest numberRequest = new NumberRequest();
        numberRequest.setNumber(request.getDepositResourceRequest().getDepositAiVideoRequest().getDuration());
        numberRequest.setItemCode(ItemCodeConst.ai_smart_class.getCode());

        String expiredDate = getResourceExpiredDate(request,ItemCodeConst.ai_smart_class.getCode());
        if(null == expiredDate){
            log.error("liveUserId == {},groupId {} aiPPTVideoEnabled not package error",request.getLiveUserId(),request.getGroupId());
            return CommonResult.fail("增加视频创作（不带数字人)失败，主账号没有对应的资源");
        }
        numberRequest.setExpireDate(expiredDate);

        resourceList.add(numberRequest);
        donateRequest.setResourceList(resourceList);
        customerResourceService.donateBillingItemResourceForGroup(donateRequest);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(groupId)
                .businessType(ResourceAssignBusinessTypeEnum.AI_PPTVIDEO_ENABLED.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        //开启分账号的视频创作开关
        UserConfigSaveReq userConfigSaveReq = new UserConfigSaveReq();
        userConfigSaveReq.setUserId(request.getLiveUserId());
        List<UserConfigSaveReq.KeyValue> items = Lists.newArrayList();
        userConfigSaveReq.setItems(items);
        items.add(new UserConfigSaveReq.KeyValue(UserConfigKey.AI_PPT_VIDEO_ENABLED, SwitchEnum.Y.getCode()));
        userConfigApi.updateBatch(userConfigSaveReq);
        return CommonResult.ok();
    }

    /**
     * 视频创作（带数字人)
     * @param request
     */
    @Override
    public CommonResult depositAIPPTVideoWithDigital(DepositResourceDTO request) {
        DonateBillingItemResourceRequest donateRequest = new DonateBillingItemResourceRequest();
        donateRequest.setLiveUserId(request.getLiveUserId());
        donateRequest.setVodUserId(request.getVodUserId());
        String groupId = request.getDepositResourceRequest().getGroupId();
        //判断所属集团主账号是否有该资源项资源
        if(StringUtils.isNotBlank(groupId)){
            List<BillingCustomerConfig> aiPptVideoDigitalConfigList =
                    billingCustomerConfigRepository.findByUnionIdAndItemCodeAndIsDeletedAndExpireTimeGreaterThanEqual(
                            request.getDepositResourceRequest().getGroupId(), ItemCodeConst.ai_digital_human.getCode(), 0, new Date());
            if(CollectionUtils.isEmpty(aiPptVideoDigitalConfigList)){
                log.error("liveUserId == {},groupId {} aiPPTVideoDigitalHumanEnabled not package error",request.getLiveUserId(),request.getGroupId());
                return CommonResult.fail("增加视频创作（带数字人)失败，主账号没有对应的资源");
            }
            //判断是否足够分配
            if(!groupAccountEnoughResourceToAssign(groupId,ResourceAssignBusinessTypeEnum.AI_PPTVIDEO_DIGITALHUMAN_ENABLED.getCode(),
                    request.getDepositResourceRequest().getDepositAiVideoWithDigitalRequest().getDuration(),false)){
                log.error("liveUserId == {},groupId {} aiPPTVideoDigitalHumanEnabled resource not enough error",request.getLiveUserId(),request.getGroupId());
                return CommonResult.fail("增加视频创作（带数字人)失败，主账号没有足够的可分配资源");
            }
        }

        List<NumberRequest> resourceList = new ArrayList<>();
        NumberRequest numberRequest = new NumberRequest();
        numberRequest.setNumber(request.getDepositResourceRequest().getDepositAiVideoWithDigitalRequest().getDuration());
        numberRequest.setItemCode(ItemCodeConst.ai_digital_human.getCode());

        String expiredDate = getResourceExpiredDate(request,ItemCodeConst.ai_digital_human.getCode());
        if(null == expiredDate){
            log.error("liveUserId == {},groupId {} aiPPTVideoDigitalHumanEnabled not package error",request.getLiveUserId(),request.getGroupId());
            return CommonResult.fail("增加视频创作（带数字人)失败，主账号没有对应的资源套餐");
        }
        numberRequest.setExpireDate(expiredDate);

        resourceList.add(numberRequest);
        donateRequest.setResourceList(resourceList);
        customerResourceService.donateBillingItemResourceForGroup(donateRequest);
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(groupId)
                .businessType(ResourceAssignBusinessTypeEnum.AI_PPTVIDEO_DIGITALHUMAN_ENABLED.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
        //开启分账号的视频创作开关
        UserConfigSaveReq userConfigSaveReq = new UserConfigSaveReq();
        userConfigSaveReq.setUserId(request.getLiveUserId());
        List<UserConfigSaveReq.KeyValue> items = Lists.newArrayList();
        userConfigSaveReq.setItems(items);
        items.add(new UserConfigSaveReq.KeyValue(UserConfigKey.AI_PPT_VIDEO_DIGITAL_HUMAN_ENABLED, SwitchEnum.Y.getCode()));
        userConfigApi.updateBatch(userConfigSaveReq);
        return CommonResult.ok();
    }
    /**
     * 判断资源是否可分配
     * @param groupId
     * @param groupResourceCode
     * @param resource
     * @param onlyResource 是否仅根据资源判断 true 是，false 就先判断开关再判断资源
     * @return true 表示可以分配
     */
    private boolean groupAccountEnoughResourceToAssign(String groupId,Integer groupResourceCode,int resource,boolean onlyResource){
        if(!onlyResource){
            //判断是否开启用量限制
            SysTypeConst sys = SysTypeConst.GroupV2;
            List<String> codes = Lists.newArrayList("GroupV2_resourceLimit");
            List<UserFunctionVO> list = customerService.getGroupV2FunctionList(groupId, codes, sys);
            if(CollectionUtils.isEmpty(list)){
                return true;
            }
            String resourceLimit = (String) list.get(0).getValue();
            if(!SwitchEnum.isY(resourceLimit)){
                return true;
            }
        }
        GroupResourceAssignable assignable = groupResourceAssignableRepository.findByGroupIdAndBusinessType(groupId,groupResourceCode);
        if(Objects.isNull(assignable)){
            return true;
        }
        return assignable.getAssignableAmount() >= resource;
    }

}
