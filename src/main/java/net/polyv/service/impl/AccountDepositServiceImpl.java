package net.polyv.service.impl;

import static net.polyv.constant.GlobalConfigConst.ADVANCED_DEPOSIT_AMOUNT;
import static net.polyv.constant.GlobalConfigConst.MIN_CONSUMPTION;
import static net.polyv.constant.GlobalConfigConst.RECHARGE_PERIOD_DAYS;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.finance.ContractOperationCodeEnum;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.user.api.service.group.GroupAccountServiceApi;
import net.polyv.modules.user.api.vo.GroupAccountVO;
import net.polyv.util.*;
import net.polyv.web.model.account.*;
import net.polyv.web.model.finance.input.AddAmountContractDTO;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.annotation.SystemAuditLog;
import net.polyv.common.CommonResult;
import net.polyv.constant.auditlog.AuditLogEvent;
import net.polyv.constant.auditlog.AuditLogStatusConst;
import net.polyv.constant.balance.BalanceTypeConst;
import net.polyv.constant.bill.BillStatusConst;
import net.polyv.constant.billingdaily.CustomerBillingDailyAmountTypeConst;
import net.polyv.constant.billingdaily.CustomerBillingDailyStatusEnum;
import net.polyv.constant.billingdaily.CustomerBillingDailyTradeTypeConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.BusinessBillingPlanCodeEnum;
import net.polyv.constant.contract.ContractConstant;
import net.polyv.constant.credit.CreditAlterationTypeConst;
import net.polyv.constant.deposit.AmountDepositTypeEnum;
import net.polyv.constant.deposit.AmountExpireTypeEnum;
import net.polyv.constant.deposit.IsDonateConst;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.salesopportunities.SalesOpportunitiesBusinessTypeConst;
import net.polyv.constant.salesopportunities.SalesOpportunitiesIsDelConst;
import net.polyv.constant.schdule.ScheduleJobTypeConst;
import net.polyv.dao.primary.AccountRechargeRecordRepository;
import net.polyv.dao.primary.AccountRechargeRecordResourcePointRepository;
import net.polyv.dao.primary.AuditLogRepository;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.CreditAlterationRecordRepository;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.dao.primary.CustomerConfigRepository;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.custom.AccountRechargeRecordDao;
import net.polyv.dao.primary.custom.AuditLogDao;
import net.polyv.dao.primary.custom.CustomerConfigDao;
import net.polyv.exception.ClearingSystemException;
import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.exception.deposit.AccountDepositAmountErrorRunTimeException;
import net.polyv.exception.deposit.AccountDepositRunTimeException;
import net.polyv.helper.salesopportunities.SalesOpportunitiesHelper;
import net.polyv.model.data.deposit.DepositApproachingExpireMessageDTO;
import net.polyv.model.data.deposit.DepositPeriodDO;
import net.polyv.model.data.log.AuditLogSearchDO;
import net.polyv.model.data.salesopportunities.ExtObjectDO;
import net.polyv.model.data.schedule.ScheduleJobSubmitDO;
import net.polyv.model.entity.primary.AccountRechargeRecord;
import net.polyv.model.entity.primary.AccountRechargeRecordResourcePoint;
import net.polyv.model.entity.primary.AuditLog;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.CreditAlterationRecord;
import net.polyv.model.entity.primary.CustomerBillingDaily;
import net.polyv.model.entity.primary.CustomerConfig;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.modules.pcs.api.stereotype.CustomerFunctionEnum;
import net.polyv.modules.pcs.api.stereotype.GiftTypeEnum;
import net.polyv.modules.pcs.api.stereotype.SalesOpportunitiesStatusConst;
import net.polyv.modules.pcs.api.vo.packages.RechargeMaturityDateVO;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.model.finance.AddContractResultVO;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.rest.service.finance.ContractService;
import net.polyv.service.AccountDepositLogicService;
import net.polyv.service.AccountDepositService;
import net.polyv.service.CacheService;
import net.polyv.service.CustomerBillingItemSettingService;
import net.polyv.service.FinanceIncomeConfirmService;
import net.polyv.service.GlobalConfigService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.bill.UnPayBillService;
import net.polyv.service.common.AbstractService;
import net.polyv.service.finance.FinanceContractService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.service.schedule.ScheduleJobService;
import net.polyv.service.settlement.SettlementConfigService;
import net.polyv.web.model.account.deposit.AccountDepositWithValidRequest;
import net.polyv.web.model.account.recharge.AccountRegistrationGiftRequest;
import net.polyv.web.model.account.recharge.GetRechargeRecordResultVO;
import net.polyv.web.model.account.recharge.RechargeRecordSearchResultVO;
import net.polyv.web.model.account.recharge.RechargeRecordSearchVO;
import net.polyv.web.model.account.recharge.RechargeRecordVO;
import net.polyv.web.model.bill.PayForUnpayBillInputVO;
import net.polyv.web.model.common.OperaResultVO;
import net.polyv.web.model.common.PageDataVO;

/**
 * 账户余额相关服务
 *
 * <AUTHOR>
 * @since 08/05/2020
 */
@Slf4j
@Service
public class AccountDepositServiceImpl extends AbstractService implements AccountDepositService {

    /**
     * 默认的充值周期天数，当没找到配置时候，会取该值
     */
    private static final int DEFAULT_RECHARGE_PERIOD_DAYS = 365;

    private final GlobalConfigService globalConfigService;
    private final ScheduleJobService scheduleJobService;
    private final AuditLogDao auditLogDao;
    private final CustomerConfigDao customerConfigDao;
    private final AccountRechargeRecordRepository accountRechargeRecordRepository;
    private final CreditAlterationRecordRepository creditAlterationRecordRepository;
    private final CustomerBillingDailyRepository customerBillingDailyRepository;
    private final CustomerConfigRepository customerConfigRepository;
    private final SalesOpportunitiesHelper salesOpportunitiesHelper;
    private final SalesOpportunitiesRepository salesOpportunitiesRepository;
    private final AuditLogRepository auditLogRepository;
    private final ContractService contractService;
    private final CacheService cacheService;
    private final AccountRechargeRecordDao accountRechargeRecordDao;

    @Autowired
    private FinanceContractService financeContractService;

    @Autowired
    private FinanceIncomeConfirmService confirmService;

    @Autowired
    private CustomerService customerService;
    /**
     * 钉钉提醒服务
     */
    @Autowired
    private DingWarnRobot dingWarnRobot;

    /**
     * 计费项DAO
     */
    @Autowired
    private BillingItemRepository billingItemRepository;
    
    /**
     * 用户收费项设置服务
     */
    @Autowired
    private CustomerBillingItemSettingService customerBillingItemSettingService;
    
    private UnPayBillService unPayBillService;
    
    @Autowired
    private CustomerResourceService customerResourceService;

    @Autowired
    private AccountRechargeRecordResourcePointRepository resourcePointRepository ;
    
    //海外分钟数倍率
    @Value("${pcs.billing-item.inter-pd:250}")
    private int interPd;
    
    private final AccountRechargeRecordResourcePointRepository accountRechargeRecordResourcePointRepository;

    @Autowired
    private SettlementConfigService settlementConfigService;

    @Resource
    private GroupAccountServiceApi groupAccountServiceApi;
    
    @Autowired
    public void setUnPayBillService(UnPayBillService unPayBillService) {
        this.unPayBillService = unPayBillService;
    }
    
    @Autowired
    public AccountDepositServiceImpl(GlobalConfigService globalConfigService, ScheduleJobService scheduleJobService,
            AuditLogDao auditLogDao, CustomerConfigDao customerConfigDao,
            AccountRechargeRecordRepository accountRechargeRecordRepository,
                                     CreditAlterationRecordRepository creditAlterationRecordRepository,
                                     CustomerBillingDailyRepository customerBillingDailyRepository,
                                     CustomerConfigRepository customerConfigRepository,
                                     SalesOpportunitiesHelper salesOpportunitiesHelper,
                                     SalesOpportunitiesRepository salesOpportunitiesRepository,
                                     AuditLogRepository auditLogRepository, ContractService contractService,
                                     CacheService cacheService, AccountRechargeRecordDao accountRechargeRecordDao,
            AccountRechargeRecordResourcePointRepository accountRechargeRecordResourcePointRepository) {
        this.globalConfigService = globalConfigService;
        this.scheduleJobService = scheduleJobService;
        this.auditLogDao = auditLogDao;
        this.customerConfigDao = customerConfigDao;
        this.accountRechargeRecordRepository = accountRechargeRecordRepository;
        this.creditAlterationRecordRepository = creditAlterationRecordRepository;
        this.customerBillingDailyRepository = customerBillingDailyRepository;
        this.customerConfigRepository = customerConfigRepository;
        this.salesOpportunitiesHelper = salesOpportunitiesHelper;
        this.salesOpportunitiesRepository = salesOpportunitiesRepository;
        this.auditLogRepository = auditLogRepository;
        this.contractService = contractService;
        this.cacheService = cacheService;
        this.accountRechargeRecordDao = accountRechargeRecordDao;
        this.accountRechargeRecordResourcePointRepository = accountRechargeRecordResourcePointRepository;
    }
    
    @Resource
    private AccountDepositLogicService accountDepositLogicService;
    
    @Transactional(rollbackFor = Exception.class)
    @SystemAuditLog(event = AuditLogEvent.deposit, needSetOperaId = true)
    @Override
    public OperaResultVO deposit(@NotNull DepositInputVO inputVO) throws AccountDepositRunTimeException {
        
        // 业务逻辑校验
        this.depositParamCheck(inputVO);
        
        // 是否本次储值在未来时间生效(非今日)
        if (this.isDepositEffectiveInTheFuture(inputVO)) {

            /*
             * 此处为产品要求，当定时生效的充值时候，将销售机会更改成已关联，同时此时的合同id为null
             * 当储值生效的时候，会再次更新合同为已关联，并更新对应财务系统的合同id
             */
            this.updateSaleOpportunities(inputVO.getSalesOpportunitiesId(), new AddContractResultVO());

            return this.processDepositInTheFuture(inputVO);
        }

        // 储值立刻生效
        return processDepositRightNow(inputVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @SystemAuditLog(event = AuditLogEvent.resource_point_deposit, needSetOperaId = true)
    @Override
    public OperaResultVO depositResourcePoint(@NotNull DepositInputVO inputVO) throws AccountDepositRunTimeException {

        // 业务逻辑校验
        this.depositParamCheck(inputVO);

        // 是否本次储值在未来时间生效(非今日)
        if (this.isDepositEffectiveInTheFuture(inputVO)) {

            /*
             * 此处为产品要求，当定时生效的充值时候，将销售机会更改成已关联，同时此时的合同id为null
             * 当储值生效的时候，会再次更新合同为已关联，并更新对应财务系统的合同id
             */
            this.updateSaleOpportunities(inputVO.getSalesOpportunitiesId(), new AddContractResultVO());

            return this.processDepositInTheFutureResourcePoint(inputVO);
        }

        // 储值立刻生效
        return processDepositRightNowResourcePoint(inputVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @SystemAuditLog(event = AuditLogEvent.deposit, needSetOperaId = true)
    @Override
    public OperaResultVO groupUserDeposit(@NotNull DepositInputVO inputVO) throws AccountDepositRunTimeException {

        // 储值立刻生效
        return groupUserProcessDepositRightNow(inputVO);
    }

    /**
     * 充值测试赠送金额
     *
     * @param inputVO
     * @return
     * @throws AccountDepositRunTimeException
     */
    @Transactional(rollbackFor = Exception.class)
    @SystemAuditLog(event = AuditLogEvent.deposit, needSetOperaId = true)
    @Override
    public OperaResultVO depositTestAndDonateAmount(@NotNull DepositInputVO inputVO) throws AccountDepositRunTimeException {

        log.info("执行立即储值操作，参数={}", JSON.toJSONString(inputVO));

        inputVO.setContractId(ContractConstant.DEFAULT_CONTRACT_ID);
        if (AmountDepositTypeEnum.DONATE.getCode().equals(inputVO.getAmountDepositType())) {
            this.doGive(inputVO);
        }
        if (AmountDepositTypeEnum.TEST.getCode().equals(inputVO.getAmountDepositType())) {
            this.doTestAmount(inputVO);
        }
        // 顺延操作
        this.handlePostpone(inputVO);

        if (AccountTypeEnum.NORMAL.getCode().equals(inputVO.getAccountType())) {

            cacheService.cleanVodUserCache(inputVO.getCustomerId());
            cacheService.cleanLiveRestrictPcsResult(inputVO.getCustomerId());
            cacheService.cleanBillingItemCache(inputVO.getCustomerId());
            customerService.setStatusNormal(inputVO.getCustomerId());
        }
        // 此处返回new对象，由环绕切面返回操作记录id
        OperaResultVO depositResultVO = new OperaResultVO();
        depositResultVO.setOperaId(inputVO.getOperaId());
        return depositResultVO;
    }

    /**
     * 充值测试赠送资源点
     *
     * @param inputVO
     * @return
     * @throws AccountDepositRunTimeException
     */
    @Transactional(rollbackFor = Exception.class)
    @SystemAuditLog(event = AuditLogEvent.deposit, needSetOperaId = true)
    @Override
    public OperaResultVO depositTestAndDonateAmountResourcePoint(@NotNull DepositInputVO inputVO) throws AccountDepositRunTimeException {

        log.info("执行立即储值资源点操作，参数={}", JSON.toJSONString(inputVO));

        inputVO.setContractId(ContractConstant.DEFAULT_CONTRACT_ID);
        if (AmountDepositTypeEnum.DONATE.getCode().equals(inputVO.getAmountDepositType())) {
            this.doGiveResourcePoint(inputVO);
        }
        if (AmountDepositTypeEnum.TEST.getCode().equals(inputVO.getAmountDepositType())) {
            this.doTestAmountResourcePoint(inputVO);
        }
        // 顺延操作
//        this.handlePostponeResourcePoint(inputVO);

        if (AccountTypeEnum.NORMAL.getCode().equals(inputVO.getAccountType())) {

//            cacheService.cleanVodUserCache(inputVO.getCustomerId());
//            cacheService.cleanLiveRestrictPcsResult(inputVO.getCustomerId());
            cacheService.cleanBillingItemCache(inputVO.getCustomerId());
//            customerService.setStatusNormal(inputVO.getCustomerId());
        }
        // 此处返回new对象，由环绕切面返回操作记录id
        OperaResultVO depositResultVO = new OperaResultVO();
        depositResultVO.setOperaId(inputVO.getOperaId());
        return depositResultVO;
    }

    @Override
    public GetRechargeStatsResultVO getRechargeStats(GetRechargeStatsInputVO inputVO) {

        GetRechargeStatsResultVO resultVO = new GetRechargeStatsResultVO();
        resultVO.setSumRechargeAmount(accountRechargeRecordRepository.sumRechargeAmount(inputVO.getCustomerId()));
        resultVO.setPeriodRechargeAmount(accountRechargeRecordRepository.sumPeriodRechargeAmount(inputVO.getCustomerId()));
        return resultVO;
    }
    
    
    @Override
    public PageDataVO<GetRechargeRecordResultVO> listRechargeRecord(CustomerInfoGetInputVO inputVO) {

        // 转换为日志查询记录
        AuditLogSearchDO searchDO = this.transferAuditLogSearchDO(inputVO);

        // 查询
        Page<AuditLog> logPage = this.auditLogDao.list(searchDO);

        // 转换输出格式
        return this.transferPageDataVO(logPage);
    }

    @Override
    public GetBalanceStatusResultVO getBalanceStatus(CustomerInfoGetInputVO inputVO) {

        GetBalanceStatusResultVO resultVO = new GetBalanceStatusResultVO();
        
        // 可用余额
        resultVO.setValidAmount(accountRechargeRecordRepository.sumUserValidBalanceAmount(inputVO.getCustomerId()));
        
        resultVO.setValidDepositAmount(accountRechargeRecordRepository.sumUserValidBalanceAmountByIsDonate(inputVO
                .getCustomerId(), IsDonateConst.NO));
        resultVO.setValidDonateAmount(accountRechargeRecordRepository.sumUserValidBalanceAmountByIsDonate(inputVO
                .getCustomerId(), IsDonateConst.YES) + accountRechargeRecordRepository.sumUserValidBalanceAmountByIsDonate(inputVO
                .getCustomerId(), AmountDepositTypeEnum.TEST.getCode()));
        resultVO.setFreezeAmount(accountRechargeRecordRepository.sumUserFreezeBalanceAmount(inputVO.getCustomerId()) +
                creditAlterationRecordRepository.sumFreezeCreditByCustomerId(inputVO.getCustomerId()));
        resultVO.setWaitPayAmount(customerBillingDailyRepository.sumCustomerTotalUnPay(inputVO.getCustomerId()));
        resultVO.setAccumulatedDepositAmount(accountRechargeRecordRepository.sumRechargeAmount(inputVO.getCustomerId()));
        resultVO.setAccumulatedDonateAmount(accountRechargeRecordRepository.sumDonateAmount(inputVO.getCustomerId())
                + accountRechargeRecordRepository.sumTestAmount(inputVO.getCustomerId()));

        //资源点
        resultVO.setValidAmountResourcePoint(resourcePointRepository.sumUserValidBalanceAmount(inputVO.getCustomerId()));
        resultVO.setValidDepositAmountResourcePoint(resourcePointRepository.sumUserValidBalanceAmountByIsDonate(inputVO
                .getCustomerId(), IsDonateConst.NO));
        resultVO.setValidDonateAmountResourcePoint(resourcePointRepository.sumUserValidBalanceAmountByIsDonate(inputVO
                .getCustomerId(), IsDonateConst.YES) + resourcePointRepository.sumUserValidBalanceAmountByIsDonate(inputVO
                .getCustomerId(), AmountDepositTypeEnum.TEST.getCode()));
        resultVO.setAccumulatedDepositAmountResourcePoint(resourcePointRepository.sumRechargeAmount(inputVO.getCustomerId()));
        resultVO.setAccumulatedDonateAmountResourcePoint(resourcePointRepository.sumDonateAmount(inputVO.getCustomerId())
                + accountRechargeRecordRepository.sumTestAmount(inputVO.getCustomerId()));

        Optional<CustomerConfig> config = customerConfigDao.getCustomerConfig(inputVO.getCustomerId());
        if (config != null && config.isPresent()) {
            resultVO.setTotalCredit(config.get().getCredit());
            
            // 可用授信
            resultVO.setValidCredit(this.calcCustomerValidCredit(CustomerInfoGetInputVO.builder().customerId(inputVO.getCustomerId()).build()));
        } else {
            resultVO.setTotalCredit(0L);
            resultVO.setValidCredit(0L);
        }
        resultVO.setNewUser(this.isNewUser(inputVO));
        return resultVO;
    }

    /**
     * 是否是新用户
     * 新用户定义：从未设置过授信额度、顾客从未充值
     *
     * @param inputVO 用户参数
     * @return true表示是， false表示不是
     */
    private boolean isNewUser(CustomerInfoGetInputVO inputVO) {
        //TODO 这个判断要改下, 要大于0才算
        boolean customerConfigExists = customerConfigRepository.existsById(inputVO.getCustomerId());
        if (customerConfigExists) {
            return false;
        }

        long rechargeCount = accountRechargeRecordRepository.countByCustomerId(inputVO.getCustomerId());
        return rechargeCount == 0L;
    }

    @Override
    public long calcCustomerValidCredit(CustomerInfoGetInputVO inputVO) {
        long totalCredit = 0;
        CustomerConfig config = customerConfigDao.getOrCreateCustomerConfig(inputVO.getCustomerId());
        if (config != null) {
            totalCredit = config.getCredit();
        }
        long used = calcCustomerUsedCredit(inputVO);
        long validCredit = totalCredit - used;
        return validCredit > 0 ? validCredit : 0;
    }

    @Override
    public long calcCustomerValidCreditWithoutTotalZero(CustomerInfoGetInputVO inputVO) {
        long totalCredit = 0;
        CustomerConfig config = customerConfigDao.getOrCreateCustomerConfig(inputVO.getCustomerId());
        if (config != null) {
            totalCredit = config.getCredit();
        }
        if (totalCredit == 0) {
            return -1;
        }
        long used = calcCustomerUsedCredit(inputVO);
        long validCredit = totalCredit - used;
        return validCredit > 0 ? validCredit : 0;
    }

    @Override
    public long calcCustomerUsedCredit(CustomerInfoGetInputVO inputVO) {
        // 冻结和解冻
        long freeAndUnFree = creditAlterationRecordRepository.sumCurrentUseCreditAmount(inputVO.getCustomerId());
        // 待支付
        long unpaid = customerBillingDailyRepository.sumCustomerTotalUnPay(inputVO.getCustomerId());
        return freeAndUnFree + unpaid;
    }


    /**
     * 账户储值业务逻辑校验
     *
     * @param inputVO 储值参数
     * @throws AccountDepositRunTimeException 校验异常
     */
    private void depositParamCheck(DepositInputVO inputVO) throws AccountDepositRunTimeException {
        // 储值金额有误
        if (inputVO.getDepositAmount() == null || inputVO.getDepositAmount() == 0) {
            if (inputVO.getDonate() == null || inputVO.getDonate() == 0) {
                throw new AccountDepositAmountErrorRunTimeException("储值金额和赠送金额不能同时为0. inputVo=" + inputVO);
            }
        }

        // 储值金额需要大于用户的当前待支付金额
//        long unPayAmount = unPayBillService.calcUnPayAmount(inputVO.getCustomerId());
//        if (inputVO.getDepositAmount() < unPayAmount) {
//            throw new AccountDepositRunTimeException("储值金额不能小于待支付金额，当前顾客待支付金额为：" +
//                    MoneyUtil.getHumanMoney(unPayAmount) + " 元, inputVo="+inputVO);
//        }

        // 校验销售机会存在
        Optional<SalesOpportunities> opportunitiesOptional = salesOpportunitiesHelper
                .getSalesOpportunitiesByIdAndNullableSaleUserId(inputVO.getSalesOpportunitiesId(), inputVO.getSaleUserId());
        if (!opportunitiesOptional.isPresent()) {
            throw new AccountDepositRunTimeException(String.format("找不到对应的销售机会, salesOpportunitiesId=%s, saleUserId=%s",
                    inputVO.getSalesOpportunitiesId(), inputVO.getSaleUserId()));
        }
    }
    
    
    /**
     * 根据有效年数,算出过期日期
     * POP-1851新增了金额有效期的设置
     *
     * @param amountValidYears 有效年数
     * @return
     */
    private Date getExpireDate(int amountValidYears) {
        Date currentDay = DateUtil.beginOfDay(DateUtil.yesterday());    // 今天生效要扣除一天
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentDay);
        cal.add(Calendar.YEAR, amountValidYears);//增加一个自然年
        return cal.getTime();
    }

    /**
     * 执行赠送
     *
     * @param inputVO 赠送入参
     */
    private void doGive(DepositInputVO inputVO) {
        // 本单无赠送
        if (inputVO.getDonate() == null || inputVO.getDonate() <= 0L) {
            log.info("本单无赠送金额");
            return;
        }

        Optional<AccountRechargeRecord> rechargeRecord =
                this.accountRechargeRecordRepository.findByCustomerIdAndIsDonate(inputVO.getCustomerId(), IsDonateConst.YES);

        /*
         * {@see http://wiki.igeeker.org/pages/viewpage.action?pageId=*********}
         * 赠送金额 先判断是否已有数据, 有的话则往上面累加, 没有的话需要新增一条记录
         */
        if (rechargeRecord.isPresent()) {
            this.rechargeGiveAmount(inputVO);
        } else {
            this.initNewGiveRechargeRecord(inputVO);
        }
    }

    /**
     * 执行赠送
     *
     * @param inputVO 赠送入参
     */
    private void doGiveResourcePoint(DepositInputVO inputVO) {
        // 本单无赠送
        if (inputVO.getDonate() == null || inputVO.getDonate() <= 0L) {
            log.info("本单无赠送资源点");
            return;
        }

//        Optional<AccountRechargeRecordResourcePoint> rechargeRecord =
//                this.resourcePointRepository.findByCustomerIdAndIsDonate(inputVO.getCustomerId(), IsDonateConst.YES);
//
//        /*
//         * {@see http://wiki.igeeker.org/pages/viewpage.action?pageId=*********}
//         * 赠送金额 先判断是否已有数据, 有的话则往上面累加, 没有的话需要新增一条记录
//         */
//        if (rechargeRecord.isPresent()) {
//            this.rechargeGiveAmountResourcePoint(inputVO);
//        } else {
            this.initNewGiveRechargeRecordResourcePoint(inputVO);
//        }
    }



    /**
     * 集团分帐号执行赠送
     *
     * @param inputVO 赠送入参
     */
    private void groupUserDoGive(DepositInputVO inputVO) {
        // 本单无赠送
        if (inputVO.getDonate() == null) {
            log.info("本单无赠送金额");
            return;
        }

        Optional<AccountRechargeRecord> rechargeRecord =
                this.accountRechargeRecordRepository.findByCustomerIdAndIsDonate(inputVO.getCustomerId(), IsDonateConst.YES);

        /*
         * {@see http://wiki.igeeker.org/pages/viewpage.action?pageId=*********}
         * 赠送金额 先判断是否已有数据, 有的话则往上面累加, 没有的话需要新增一条记录
         */
        if (rechargeRecord.isPresent()) {
            this.resetGiveAmount(inputVO);
        } else {
            this.initNewGiveRechargeRecord(inputVO);
        }
    }

    /**
     * 执行测试金额
     *
     * @param inputVO 赠送入参
     */
    private void doTestAmount(DepositInputVO inputVO) {
        // 本单无赠送
        if (inputVO.getTestAmount() == null || inputVO.getTestAmount() <= 0L) {
            log.info("本单无测试金额");
            return;
        }

        Optional<AccountRechargeRecord> rechargeRecord =
                this.accountRechargeRecordRepository.findByCustomerIdAndIsDonate(inputVO.getCustomerId(), AmountDepositTypeEnum.TEST.getCode());

        /*
         * {@see http://wiki.igeeker.org/pages/viewpage.action?pageId=*********}
         * 赠送金额 先判断是否已有数据, 有的话则往上面累加, 没有的话需要新增一条记录
         */
        if (rechargeRecord.isPresent()) {
            this.rechargeTestAmount(inputVO);
        } else {
            this.initNewTestRechargeRecord(inputVO);
        }
    }

    /**
     * 执行测试金额
     *
     * @param inputVO 赠送入参
     */
    private void doTestAmountResourcePoint(DepositInputVO inputVO) {
        // 本单无赠送
        if (inputVO.getTestAmount() == null || inputVO.getTestAmount() <= 0L) {
            log.info("本单无测试资源点");
            return;
        }

//        Optional<AccountRechargeRecordResourcePoint> rechargeRecord =
//                this.resourcePointRepository.findByCustomerIdAndIsDonate(inputVO.getCustomerId(), AmountDepositTypeEnum.TEST.getCode());

//        /*
//         * {@see http://wiki.igeeker.org/pages/viewpage.action?pageId=*********}
//         * 赠送金额 先判断是否已有数据, 有的话则往上面累加, 没有的话需要新增一条记录
//         */
//        if (rechargeRecord.isPresent()) {
//            this.rechargeTestAmountResourcePoint(inputVO);
//        } else {
            this.initNewTestRechargeRecordResourcePoint(inputVO);
//        }
    }

    /**
     * 往已有的赠送储值记录中充值赠送金额
     * 此处用了 update xx set a += b 的方式，避免了get,set导致的并发问题
     *
     * @param inputVO 储值入参
     */
    private void rechargeGiveAmount(DepositInputVO inputVO) {
        // 增加赠送金额
        int effectRow = this.accountRechargeRecordRepository.rechargeDonate(inputVO.getCustomerId(), inputVO.getDonate(), inputVO.getOperaId());

        // 更新行数不等于1，必有逻辑错误
        if (effectRow != 1) {
            throw new ClearingSystemRuntimeException("更新用户赠送金额记录失败，影响行数=" + effectRow);
        }
    }

    /**
     * 往已有的资源点储值记录中充值赠送资源点
     * 此处用了 update xx set a += b 的方式，避免了get,set导致的并发问题
     *
     * @param inputVO 储值入参
     */
    private void rechargeGiveAmountResourcePoint(DepositInputVO inputVO) {
        // 增加赠送金额
        int effectRow = this.resourcePointRepository.rechargeDonate(inputVO.getCustomerId(), inputVO.getDonate(), inputVO.getOperaId());

        // 更新行数不等于1，必有逻辑错误
        if (effectRow != 1) {
            throw new ClearingSystemRuntimeException("更新用户赠送资源点记录失败，影响行数=" + effectRow);
        }
    }

    /**
     * 往已有的赠送储值记录中充值测试金额
     * 此处用了 update xx set a += b 的方式，避免了get,set导致的并发问题
     *
     * @param inputVO 储值入参
     */
    private void rechargeTestAmount(DepositInputVO inputVO) {
        // 增加赠送金额
        int effectRow = this.accountRechargeRecordRepository.rechargeTest(inputVO.getCustomerId(), inputVO.getTestAmount()
                , inputVO.getOperaId(), AmountDepositTypeEnum.TEST.getCode());

        // 更新行数不等于1，必有逻辑错误
        if (effectRow != 1) {
            throw new ClearingSystemRuntimeException("更新用户测试金额记录失败，影响行数=" + effectRow);
        }
    }

    /**
     * 往已有的赠送储值记录中充值测试资源点
     * 此处用了 update xx set a += b 的方式，避免了get,set导致的并发问题
     *
     * @param inputVO 储值入参
     */
    private void rechargeTestAmountResourcePoint(DepositInputVO inputVO) {
        // 增加赠送金额
        int effectRow = this.resourcePointRepository.rechargeTest(inputVO.getCustomerId(), inputVO.getTestAmount()
                , inputVO.getOperaId(), AmountDepositTypeEnum.TEST.getCode());

        // 更新行数不等于1，必有逻辑错误
        if (effectRow != 1) {
            throw new ClearingSystemRuntimeException("更新用户测试资源点记录失败，影响行数=" + effectRow);
        }
    }

    /**
     * 往已有的赠送储值记录中更新赠送金额
     * 此处用了 update xx set a = b 的方式，避免了get,set导致的并发问题
     *
     * @param inputVO 储值入参
     */
    private void resetGiveAmount(DepositInputVO inputVO) {
        // 增加赠送金额
        int effectRow = this.accountRechargeRecordRepository.resetDonate(inputVO.getCustomerId(), inputVO.getDonate(), inputVO.getOperaId());

        // 更新行数不等于1，必有逻辑错误
        if (effectRow != 1) {
            throw new ClearingSystemRuntimeException("更新用户赠送金额记录失败，影响行数=" + effectRow);
        }
    }

    /**
     * 往已有的赠送储值记录中更新测试金额
     * 此处用了 update xx set a = b 的方式，避免了get,set导致的并发问题
     *
     * @param inputVO 储值入参
     */
    private void resetTestAmount(DepositInputVO inputVO) {
        // 增加测试金额
        int effectRow = this.accountRechargeRecordRepository.resetTest(inputVO.getCustomerId(), inputVO.getTestAmount(),
                inputVO.getOperaId(), AmountDepositTypeEnum.TEST.getCode());

        // 更新行数不等于1，必有逻辑错误
        if (effectRow != 1) {
            throw new ClearingSystemRuntimeException("更新用户测试金额记录失败，影响行数=" + effectRow);
        }
    }

    /**
     * 初始化新的储值记录
     * 此处借用单例工厂的方法来防止并发问题
     * if {
     * synchronized {
     * if {
     * return;
     * }
     * code....
     * }
     * }
     *
     * @param inputVO 储值入参
     */
    private synchronized void initNewGiveRechargeRecord(DepositInputVO inputVO) {

        // 此处已经有赠送金额记录，则调用增加的方法，避免并发带来的问题
        Optional<AccountRechargeRecord> existsRechargeRecord =
                this.accountRechargeRecordRepository.findByCustomerIdAndIsDonate(inputVO.getCustomerId(), IsDonateConst.YES);
        if (existsRechargeRecord.isPresent()) {
            this.rechargeGiveAmount(inputVO);
            return;
        }

        // 基础信息
        AccountRechargeRecord rechargeRecord = new AccountRechargeRecord();
        rechargeRecord.setCustomerId(inputVO.getCustomerId());
        rechargeRecord.setContractId(inputVO.getContractId());
        rechargeRecord.setDeposit(inputVO.getDonate());
        rechargeRecord.setAvailable(inputVO.getDonate());
        rechargeRecord.setFreezeAmount(0L);
        rechargeRecord.setIsDonate(IsDonateConst.YES);
        rechargeRecord.setCreateUserId(inputVO.getCreateUserId());
        rechargeRecord.setCreateTime(new Date());
        rechargeRecord.setUpdateTime(new Date());
        rechargeRecord.setOperaId(inputVO.getOperaId());
        rechargeRecord.setExpireType(AmountExpireTypeEnum.POSTPONE.getCode());  // 赠送金额的过期类型默认为顺延
        if (Objects.nonNull(inputVO.getAmountExpireDate())) { // 如果设置了过期时间
            rechargeRecord.setExpireDate(inputVO.getAmountExpireDate());
        } else {
            rechargeRecord.setExpireDate(getExpireDate(inputVO.getAmountValidYears() == null ? 2 : inputVO.getAmountValidYears()));
        }

        log.info("保存充值记录, rechargeRecord={}", rechargeRecord);

        // 持久化
        this.accountRechargeRecordRepository.save(rechargeRecord);
    }

    /**
     * 初始化新资源点记录
     * 此处借用单例工厂的方法来防止并发问题
     * if {
     * synchronized {
     * if {
     * return;
     * }
     * code....
     * }
     * }
     *
     * @param inputVO 储值入参
     */
    private synchronized void initNewGiveRechargeRecordResourcePoint(DepositInputVO inputVO) {

//        // 此处已经有赠送金额记录，则调用增加的方法，避免并发带来的问题
//        Optional<AccountRechargeRecordResourcePoint> existsRechargeRecord =
//                this.resourcePointRepository.findByCustomerIdAndIsDonate(inputVO.getCustomerId(), IsDonateConst.YES);
//        if (existsRechargeRecord.isPresent()) {
//            this.rechargeGiveAmountResourcePoint(inputVO);
//            return;
//        }

        // 基础信息
        AccountRechargeRecordResourcePoint rechargeRecord = new AccountRechargeRecordResourcePoint();
        rechargeRecord.setCustomerId(inputVO.getCustomerId());
        rechargeRecord.setContractId(inputVO.getContractId());
        rechargeRecord.setDeposit(inputVO.getDonate());
        rechargeRecord.setAvailable(inputVO.getDonate());
        rechargeRecord.setFreezeAmount(0L);
        rechargeRecord.setIsDonate(IsDonateConst.YES);
        rechargeRecord.setCreateUserId(inputVO.getCreateUserId());
        rechargeRecord.setCreateTime(new Date());
        rechargeRecord.setUpdateTime(new Date());
        rechargeRecord.setOperaId(inputVO.getOperaId());
        rechargeRecord.setExpireType(AmountExpireTypeEnum.INDEPENDENCE.getCode());  // 赠送金额的过期类型默认为顺延
        if (Objects.nonNull(inputVO.getAmountExpireDate())) { // 如果设置了过期时间
            rechargeRecord.setExpireDate(inputVO.getAmountExpireDate());
        } else {
            rechargeRecord.setExpireDate(getExpireDate(inputVO.getAmountValidYears() == null ? 2 : inputVO.getAmountValidYears()));
        }

        log.info("保存资源点赠送记录, rechargeRecord={}", rechargeRecord);

        // 持久化
        this.resourcePointRepository.save(rechargeRecord);
    }

    /**
     * 初始化新的储值记录
     * 此处借用单例工厂的方法来防止并发问题
     * if {
     * synchronized {
     * if {
     * return;
     * }
     * code....
     * }
     * }
     *
     * @param inputVO 储值入参
     */
    private synchronized void initNewTestRechargeRecord(DepositInputVO inputVO) {

        // 此处已经有赠送金额记录，则调用增加的方法，避免并发带来的问题
        Optional<AccountRechargeRecord> existsRechargeRecord =
                this.accountRechargeRecordRepository.findByCustomerIdAndIsDonate(inputVO.getCustomerId(), AmountDepositTypeEnum.TEST.getCode());
        if (existsRechargeRecord.isPresent()) {
            this.rechargeTestAmount(inputVO);
            return;
        }

        // 基础信息
        AccountRechargeRecord rechargeRecord = new AccountRechargeRecord();
        rechargeRecord.setCustomerId(inputVO.getCustomerId());
        rechargeRecord.setContractId(inputVO.getContractId());
        rechargeRecord.setDeposit(inputVO.getTestAmount());
        rechargeRecord.setAvailable(inputVO.getTestAmount());
        rechargeRecord.setFreezeAmount(0L);
        rechargeRecord.setIsDonate(AmountDepositTypeEnum.TEST.getCode());
        rechargeRecord.setCreateUserId(inputVO.getCreateUserId());
        rechargeRecord.setCreateTime(new Date());
        rechargeRecord.setUpdateTime(new Date());
        rechargeRecord.setOperaId(inputVO.getOperaId());
        rechargeRecord.setExpireType(AmountExpireTypeEnum.POSTPONE.getCode());  // 测试金额的过期类型默认为顺延
        if (Objects.nonNull(inputVO.getAmountExpireDate())) { // 如果设置了过期时间
            rechargeRecord.setExpireDate(inputVO.getAmountExpireDate());
        } else {
            rechargeRecord.setExpireDate(getExpireDate(inputVO.getAmountValidYears() == null ? 2 : inputVO.getAmountValidYears()));
        }

        log.info("保存充值记录, rechargeRecord={}", rechargeRecord);

        // 持久化
        this.accountRechargeRecordRepository.save(rechargeRecord);
    }

    /**
     * 初始化新的储值记录
     * 此处借用单例工厂的方法来防止并发问题
     * if {
     * synchronized {
     * if {
     * return;
     * }
     * code....
     * }
     * }
     *
     * @param inputVO 储值入参
     */
    private synchronized void initNewTestRechargeRecordResourcePoint(DepositInputVO inputVO) {

//        // 此处已经有赠送金额记录，则调用增加的方法，避免并发带来的问题
//        Optional<AccountRechargeRecordResourcePoint> existsRechargeRecord =
//                this.resourcePointRepository.findByCustomerIdAndIsDonate(inputVO.getCustomerId(), AmountDepositTypeEnum.TEST.getCode());
//        if (existsRechargeRecord.isPresent()) {
//            this.rechargeTestAmountResourcePoint(inputVO);
//            return;
//        }

        // 基础信息
        AccountRechargeRecordResourcePoint rechargeRecord = new AccountRechargeRecordResourcePoint();
        rechargeRecord.setCustomerId(inputVO.getCustomerId());
        rechargeRecord.setContractId(inputVO.getContractId());
        rechargeRecord.setDeposit(inputVO.getTestAmount());
        rechargeRecord.setAvailable(inputVO.getTestAmount());
        rechargeRecord.setFreezeAmount(0L);
        rechargeRecord.setIsDonate(AmountDepositTypeEnum.TEST.getCode());
        rechargeRecord.setCreateUserId(inputVO.getCreateUserId());
        rechargeRecord.setCreateTime(new Date());
        rechargeRecord.setUpdateTime(new Date());
        rechargeRecord.setOperaId(inputVO.getOperaId());
        rechargeRecord.setExpireType(AmountExpireTypeEnum.INDEPENDENCE.getCode());  // 测试金额的过期类型默认为顺延
        if (Objects.nonNull(inputVO.getAmountExpireDate())) { // 如果设置了过期时间
            rechargeRecord.setExpireDate(inputVO.getAmountExpireDate());
        } else {
            rechargeRecord.setExpireDate(getExpireDate(inputVO.getAmountValidYears() == null ? 2 : inputVO.getAmountValidYears()));
        }

        log.info("保存充值资源点记录, rechargeRecord={}", rechargeRecord);

        // 持久化
        this.resourcePointRepository.save(rechargeRecord);
    }

    /**
     * 重算系统自动设定的最低消费额度
     *
     * @param inputVO 重算入参
     */
    private void recalculationMinConsumption(DepositInputVO inputVO) {

        // 获取用户的充值情况
        GetRechargeStatsInputVO statsInputVO = new GetRechargeStatsInputVO();
        statsInputVO.setCustomerId(inputVO.getCustomerId());
        GetRechargeStatsResultVO rechargeStatsResultVO = this.getRechargeStats(statsInputVO);

        /*
         * {@see http://wiki.igeeker.org/pages/viewpage.action?pageId=*********}
         * 判断周期累积充值金额+本次充值>=2w, 是的话需要设置customer_config的auto_min_consumption为1w(数据库没有记录则插入一条新的记录)
         * 判断周期累积充值金额+本次充值<2w, 是的话需要设置customer_config的auto_min_consumption为 周期累积充值金额+本次充值 (数据库没有记录则插入一条新的记录)
         */

        // 默认的高级套餐充值门槛
        long advancedDepositAmount = this.globalConfigService.getByKey(ADVANCED_DEPOSIT_AMOUNT, Long.class);
        // 默认的最低消费金额
        long minConsumption = this.globalConfigService.getByKey(MIN_CONSUMPTION, Long.class);

        long autoMinConsumption;
        if (rechargeStatsResultVO.getPeriodRechargeAmount() + inputVO.getDepositAmount() >= advancedDepositAmount) {
            autoMinConsumption = minConsumption;
        } else {
            autoMinConsumption = rechargeStatsResultVO.getPeriodRechargeAmount();
        }

        log.info("重新计算用户：{}最低消费为{}", inputVO.getCustomerId(), autoMinConsumption);

        // 更新系统自动设定的最低消费额度
        customerConfigDao.updateCustomerAutoMinConsumption(inputVO.getCustomerId(), autoMinConsumption);
    }

    /**
     * 获取新的充值周期
     *
     * @return 新的充值周期（从今日0点开始）
     */
    private DepositPeriodDO getNewPeriod() {
        DepositPeriodDO depositPeriodDO = new DepositPeriodDO();
        depositPeriodDO.setPeriodStartTime(DateUtil.beginOfDay(DateUtil.date()));

        // 获取当前周期天数配置
        int periodDays = this.getCurrentPeriodDays();
        depositPeriodDO.setPeriodEndTime(DateUtil.offsetDay(depositPeriodDO.getPeriodStartTime(), periodDays));

        return depositPeriodDO;
    }

    /**
     * 从充值记录中复制周期起止时间
     *
     * @param accountRechargeRecord 充值记录
     * @return 周期起止时间
     */
    private DepositPeriodDO copyPeriod(AccountRechargeRecord accountRechargeRecord) {
        DepositPeriodDO depositPeriodDO = new DepositPeriodDO();
        depositPeriodDO.setPeriodStartTime(accountRechargeRecord.getPeriodStartTime());
        depositPeriodDO.setPeriodEndTime(accountRechargeRecord.getPeriodEndTime());
        return depositPeriodDO;
    }

    /**
     * 获取当前充值周期天数
     * 如果获取配置失败，该方法会返回默认的充值周期天数 {@link #DEFAULT_RECHARGE_PERIOD_DAYS}
     *
     * @return 当前充值周期天数
     */
    private int getCurrentPeriodDays() {

        try {
            return this.globalConfigService.getByKey(RECHARGE_PERIOD_DAYS, Integer.class);
        } catch (Exception e) {
            log.error("获取当前充值周期天数失败，返回默认值:" + DEFAULT_RECHARGE_PERIOD_DAYS, e);
            return DEFAULT_RECHARGE_PERIOD_DAYS;
        }
    }

    /**
     * 处理未来生效的储值
     *
     * @param inputVO 储值参数
     * @return 储值预约结果
     */
    private DepositResultVO processDepositInTheFuture(DepositInputVO inputVO) {

        // 提交定时生效任务
        ScheduleJobSubmitDO submitDO = new ScheduleJobSubmitDO();
        submitDO.setCustomerId(inputVO.getCustomerId());
        submitDO.setEffectiveTime(DateUtil.beginOfDay(new Date(inputVO.getEffectiveTime())));
        submitDO.setType(ScheduleJobTypeConst.deposit);
        submitDO.setExt(inputVO);

        scheduleJobService.submitJob(submitDO);

        log.info("提交储值未来时间生效的任务，该笔储值将于{}生效", submitDO.getEffectiveTime());

        DepositResultVO resultVO = new DepositResultVO();
        resultVO.setOperaId(inputVO.getOperaId());
        return resultVO;
    }

    /**
     * 处理未来生效的资源点储值
     *
     * @param inputVO 储值参数
     * @return 储值预约结果
     */
    private DepositResultVO processDepositInTheFutureResourcePoint(DepositInputVO inputVO) {

        // 提交定时生效任务
        ScheduleJobSubmitDO submitDO = new ScheduleJobSubmitDO();
        submitDO.setCustomerId(inputVO.getCustomerId());
        submitDO.setEffectiveTime(DateUtil.beginOfDay(new Date(inputVO.getEffectiveTime())));
        submitDO.setType(ScheduleJobTypeConst.resource_point_deposit);
        submitDO.setExt(inputVO);

        scheduleJobService.submitJob(submitDO);

        log.info("提交资源点储值未来时间生效的任务，该笔储值将于{}生效", submitDO.getEffectiveTime());

        DepositResultVO resultVO = new DepositResultVO();
        resultVO.setOperaId(inputVO.getOperaId());
        return resultVO;
    }

    /**
     * 储值是否预约生效(今日内立刻生效,大于今日则预约生效)
     *
     * @param inputVO 储值参数
     * @return true表示未来生效, false表示立刻生效
     */
    private boolean isDepositEffectiveInTheFuture(DepositInputVO inputVO) {

        Long effectiveTime = inputVO.getEffectiveTime();

        // 设置了生效时间,并且生效时间大于等于明日0点,则为预约生效
        return effectiveTime != null && effectiveTime >= DateUtil.beginOfDay(DateUtil.tomorrow()).getTime();
    }

    /**
     * 储值立刻生效
     *
     * @param inputVO 储值参数
     * @return 储值结果
     */
    private OperaResultVO processDepositRightNow(@NotNull DepositInputVO inputVO) {

        log.info("执行立即储值操作，参数={}", JSON.toJSONString(inputVO));

        // 储值
        this.accountDepositLogicService.doDeposit(inputVO);

        // 赠送金额
        this.doGive(inputVO);

        // 顺延操作
        this.handlePostpone(inputVO);

        // 重算系统自动设定的最低消费额度
        this.recalculationMinConsumption(inputVO);

        // 执行的后续操作
        this.postOperate(inputVO);
    
    
        // 这里可以推送余额充值事件...

        // 此处返回new对象，由环绕切面返回操作记录id
        OperaResultVO depositResultVO = new OperaResultVO();
        depositResultVO.setOperaId(inputVO.getOperaId());
        return depositResultVO;
    }

    /**
     * 资源点储值立刻生效
     *
     * @param inputVO 储值参数
     * @return 储值结果
     */
    private OperaResultVO processDepositRightNowResourcePoint(@NotNull DepositInputVO inputVO) {

        log.info("执行资源点立即储值操作，参数={}", JSON.toJSONString(inputVO));

        // 储值
        this.accountDepositLogicService.doDepositResourcePoint(inputVO);

        // 赠送金额
        this.doGiveResourcePoint(inputVO);

//        // 执行的后续操作
        this.postOperateResourcePoint(inputVO);


        // 这里可以推送余额充值事件...

        // 此处返回new对象，由环绕切面返回操作记录id
        OperaResultVO depositResultVO = new OperaResultVO();
        depositResultVO.setOperaId(inputVO.getOperaId());
        return depositResultVO;
    }

    /**
     * 储值立刻生效
     *
     * @param inputVO 储值参数
     * @return 储值结果
     */
    private OperaResultVO groupUserProcessDepositRightNow(@NotNull DepositInputVO inputVO) {

        log.info("执行立即储值操作，参数={}", JSON.toJSONString(inputVO));

        // 赠送金额
        this.groupUserDoGive(inputVO);

        // 顺延操作
        this.handlePostpone(inputVO);

        // 此处返回new对象，由环绕切面返回操作记录id
        OperaResultVO depositResultVO = new OperaResultVO();
        depositResultVO.setOperaId(inputVO.getOperaId());
        return depositResultVO;
    }


    /**
     * 充值后的后续操作
     *
     * @param inputVO 储值入参
     */
    private void postOperate(DepositInputVO inputVO) {
        // 调用财务新建合同
        AddContractResultVO addContractResultVO = this.accountDepositLogicService.doAddContract(inputVO);

        if (addContractResultVO == null) {
            throw new AccountDepositRunTimeException("储值失败，添加财务合同失败");
        }

        boolean needCheckContractId = inputVO.getIsForce() || inputVO.getDepositAmount() > 0; // 是否需要检验合同id
        // 两个合同id同时为空，需要报错
        if (needCheckContractId && StringUtils.isEmpty(addContractResultVO.getContractId()) &&
                StringUtils.isEmpty(addContractResultVO.getPayOverContractId())) {
            throw new AccountDepositRunTimeException(
                    String.format("调财务系统接口创建充值合同，响应无合同id, inputVO=%s, addContractResultVO=%s", inputVO,
                            addContractResultVO));
        }

        // 更新状态
        this.postProcess(inputVO, addContractResultVO);
    }

    /**
     * 充值资源点后的后续操作
     *
     * @param inputVO 储值入参
     */
    private void postOperateResourcePoint(DepositInputVO inputVO) {
        // 调用财务新建合同
        AddContractResultVO addContractResultVO = this.accountDepositLogicService.doAddContractResourcePoint(inputVO);

        if (addContractResultVO == null) {
            throw new AccountDepositRunTimeException("储值失败，添加财务合同失败");
        }

        boolean needCheckContractId = inputVO.getIsForce() || inputVO.getDepositAmount() > 0; // 是否需要检验合同id
        // 两个合同id同时为空，需要报错
        if (needCheckContractId && StringUtils.isEmpty(addContractResultVO.getContractId()) &&
                StringUtils.isEmpty(addContractResultVO.getPayOverContractId())) {
            throw new AccountDepositRunTimeException(
                    String.format("调财务系统接口创建充值合同，响应无合同id, inputVO=%s, addContractResultVO=%s", inputVO,
                            addContractResultVO));
        }

        // 更新状态
        this.postProcessResourcePoint(inputVO, addContractResultVO);
    }


    /**
     * 将操作日志数据转换成输出结果数据
     *
     * @param logPage 分页数据
     * @return 充值记录分页结果
     */
    private PageDataVO<GetRechargeRecordResultVO> transferPageDataVO(Page<AuditLog> logPage) {
        // jpa页码从0开始
        return PageDataVO.<GetRechargeRecordResultVO>builder()
                .pageNumber(logPage.getNumber() + 1)
                .pageSize(logPage.getSize())
                .totalPages(logPage.getTotalPages())
                .totalItems(logPage.getTotalElements())
                .contents(logPage.get().map(this::transferVO).collect(Collectors.toList()))
                .build();
    }

    /**
     * 转换VO
     *
     * @param auditLog 操作记录
     * @return 储值记录
     */
    private GetRechargeRecordResultVO transferVO(AuditLog auditLog) {
        GetRechargeRecordResultVO record = new GetRechargeRecordResultVO();
        record.setCreateTime(auditLog.getCreateTime().getTime());

        DepositInputVO inputVO = JSON.parseObject(auditLog.getDetail(), DepositInputVO.class);
        record.setDepositAmount(inputVO.getDepositAmount());
        record.setDonateAmount(inputVO.getDonate());

        return record;
    }

    /**
     * 将充值记录检索参数转换成操作记录检索条件
     *
     * @param inputVO 检索参数
     * @return 操作记录检索条件
     */
    private AuditLogSearchDO transferAuditLogSearchDO(CustomerInfoGetInputVO inputVO) {
        AuditLogSearchDO searchDO = new AuditLogSearchDO();
        searchDO.setCustomerId(inputVO.getCustomerId());
        searchDO.setAuditLogEvent(AuditLogEvent.deposit);
        searchDO.setPage(inputVO.getPage());
        searchDO.setPageSize(inputVO.getPageSize());
        return searchDO;
    }

    private void postProcess(DepositInputVO inputVO, AddContractResultVO addContractResultVO) {

        // 统计待支付情况
        List<CustomerBillingDaily> waitPayBillList = unPayBillService.listUnPayBill(inputVO.getCustomerId());
        long payOverAmount = unPayBillService.calcUnPayAmount(inputVO.getCustomerId());

        if (inputVO.getDepositAmount() > 0) {
            // 更新充值记录的合同id
            this.updateRechargeRecord(inputVO, addContractResultVO);
        }


        // 充值金额 > 0，才需要偿还待支付
        if (inputVO.getDepositAmount() > 0 && inputVO.getDepositAmount() >= payOverAmount) {
            // 更新待支付账单
            this.updateUnPayBill(waitPayBillList, addContractResultVO);

            // 更新未关联账单的合同id
            customerBillingDailyRepository.relateUnrelatedBill(addContractResultVO.getPayOverContractId(), inputVO.getOperaId(),
                    CustomerBillingDailyAmountTypeConst.deposit.getAmountType(), BillStatusConst.PAID, inputVO.getCustomerId(),
                    BillStatusConst.UNRELATED);

            // 拆充值记录
            this.splitRechargeRecord(inputVO, payOverAmount, addContractResultVO);


        } else if (inputVO.getDepositAmount() < payOverAmount) {
            // 使用账号中的所有充值和赠送金额来偿还 待支付
            PayForUnpayBillInputVO payInputVO = PayForUnpayBillInputVO.builder()
                    .customerId(inputVO.getCustomerId())
                    .soId(inputVO.getSalesOpportunitiesId())
                    .accountType(inputVO.getAccountType())
                    .build();
            unPayBillService.payForUnPayBill(payInputVO);
        }


        // 更新销售机会为已关联
        this.updateSaleOpportunities(inputVO.getSalesOpportunitiesId(), addContractResultVO);

        // 更新销售机会的配置的授信额度
        this.updateCredit(inputVO.getSalesOpportunitiesId(), inputVO.getCustomerId());

    }

    private void postProcessResourcePoint(DepositInputVO inputVO, AddContractResultVO addContractResultVO) {

        if (inputVO.getDepositAmount() > 0) {
            // 更新充值记录的合同id
            this.updateRechargeRecord(inputVO, addContractResultVO);
        }

        // 更新销售机会为已关联
        this.updateSaleOpportunities(inputVO.getSalesOpportunitiesId(), addContractResultVO);

    }

    /**
     * 更新销售机会的配置的授信额度
     *
     * @param salesOpportunitiesId 销售机会id
     * @param customerId           顾客id
     */
    private void updateCredit(Long salesOpportunitiesId, String customerId) {

        log.info("更新销售机会的配置的授信额度");
        SalesOpportunities opportunities = salesOpportunitiesRepository.findById(salesOpportunitiesId)
                .orElse(null);
        assert opportunities != null;

        ExtObjectDO extObjectDO = JSON.parseObject(opportunities.getExt(), ExtObjectDO.class);
        if (extObjectDO == null || extObjectDO.getCredit() == null) {
            log.info("该销售机会无需更新顾客的授信额度");
            return;
        }

        Optional<CustomerConfig> configOptional = customerConfigRepository.findById(customerId);
        if (!configOptional.isPresent()) {
            throw new ClearingSystemRuntimeException("顾客的配置查找不到，顾客id：" + customerId);
        }

        log.info("充值成功执行更新顾客的授信额度，顾客id：{}, 销售机会id：{}, 更新前值：{}, 更新后值：{}",
                customerId, salesOpportunitiesId, configOptional.get().getCredit(), extObjectDO.getCredit());

        // 由于充值的时候已经将待支付的充值额度填平，所以这里无需判断大于已使用的授信额度，大方地修改授信额度为新值就OK
        configOptional.get().setCredit(extObjectDO.getCredit());
        customerConfigRepository.save(configOptional.get());
    }

    /**
     * 充值完成之后，回来将销售机会与财务系统的合同关联起来
     *
     * @param salesOpportunitiesId 销售机会id
     * @param addContractResultVO  调用添加财务的合同的返回结果
     */
    private void updateSaleOpportunities(Long salesOpportunitiesId, AddContractResultVO addContractResultVO) {
        SalesOpportunities opportunities = salesOpportunitiesRepository.findById(salesOpportunitiesId)
                .orElse(null);
        assert opportunities != null;

        String contractId = addContractResultVO.getContractId();
        if (StringUtils.isEmpty(contractId)) {
            // 充值的金额完全用于偿还待支付，销售机会关联偿还的充值合同id
            contractId = addContractResultVO.getPayOverContractId();
        }
        opportunities.setContractId(contractId);
        opportunities.setStatus(SalesOpportunitiesStatusConst.associate.getStatus());

        salesOpportunitiesRepository.save(opportunities);

        log.info("更新合同id：{} 为已关联, 合同id为 {}", opportunities.getId(), contractId);
    }

    /**
     * 偿还待支付授信额度
     *
     * @param customerId    顾客id
     * @param waitPayCredit 待支付授信额度
     */
    private void payCredit(String customerId, long waitPayCredit) {
        if (waitPayCredit <= 0) {
            log.info("无需偿还待支付授信额度");
            return;
        }

        log.info("执行偿还授信额度");
        CreditAlterationRecord record = new CreditAlterationRecord();
        record.setAmount(waitPayCredit);
        record.setCustomerId(customerId);
        record.setStatAt(new Date());
        record.setType(CreditAlterationTypeConst.RECHARGE);

        creditAlterationRecordRepository.save(record);
    }

    private void updateRechargeRecord(DepositInputVO inputVO, AddContractResultVO addContractResultVO) {
        // 获取充值记录
        AccountRechargeRecord rechargeRecord = accountRechargeRecordRepository
                .findByCustomerIdAndOperaIdAndIsDonate(inputVO.getCustomerId(), inputVO.getOperaId(), IsDonateConst.NO);

        String contractId = addContractResultVO.getContractId();
        if (StringUtils.isEmpty(addContractResultVO.getContractId())) {
            log.info("刚好完全抵销待支付，无合同id, 将余额置0，使用payOverContractId, customerId={}, saleOpportId={}, addContractResultVO={}",
                    inputVO.getCustomerId(), inputVO.getSalesOpportunitiesId(), addContractResultVO);
            contractId = addContractResultVO.getPayOverContractId();
            rechargeRecord.setAvailable(0L);
        }

        SalesOpportunities opportunities = salesOpportunitiesRepository.findById(inputVO.getSalesOpportunitiesId())
                .orElse(null);

        String soId = null;
        if (opportunities != null) {
            soId = opportunities.getSoId();
        }

        // 更新合同id
        log.info("更新充值记录的合同id, rechargeRecord={}, contractId={}, soId={}", rechargeRecord, contractId, soId);
        rechargeRecord.setContractId(contractId);
        rechargeRecord.setSoId(soId);
        rechargeRecord.setUpdateTime(new Date());

        log.info("保存充值记录, rechargeRecord={}", rechargeRecord);
        accountRechargeRecordRepository.save(rechargeRecord);
    }

    private void updateUnPayBill(List<CustomerBillingDaily> waitPayBillList, AddContractResultVO addContractResultVO) {
        if (CollectionUtils.isEmpty(waitPayBillList)) {
            log.info("无待支付账单，无需更新待支付账单");
            return;
        }

        for (int i = 0; i < waitPayBillList.size(); i++) {
            CustomerBillingDaily item = waitPayBillList.get(i);
            String amountType = item.getAmountType();
            long unpaid = item.getUnpaid();
            item.setAmountType(BalanceTypeConst.DEPOSIT.getType());
            item.setStatus(BillStatusConst.PAID);
            item.setUnpaid(0L);
            // item.setRepayOperationTime(new Date()); // 最后一笔正式金额偿还设置偿还时间
            item.setContractId(addContractResultVO.getPayOverContractId());
            if (i == waitPayBillList.size() - 1) {
                item.setIsSettledAccount(1);
            }
            customerBillingDailyRepository.save(item);

            // 判断是否是授信额度的账单，是的话需要恢复额度
            if (BalanceTypeConst.CREDIT.getType().equals(amountType)) {
                payCredit(item.getCustomerId(), unpaid);
            }
        }
    }

    private void splitRechargeRecord(DepositInputVO inputVO, long payOverAmount, AddContractResultVO addContractResultVO) {
        if (addContractResultVO == null || payOverAmount <= 0L) {
            log.info("没有待支付账单，不执行充值记录拆分操作");
            return;
        }

        SalesOpportunities opportunities = salesOpportunitiesRepository
                .findById(inputVO.getSalesOpportunitiesId()).orElse(null);
        assert opportunities != null;
        if (opportunities.getAmountGained() == payOverAmount) {
            log.info("充值金额刚好等于待支付金额，不需要做拆分");
            return;
        }

        log.info("执行拆分充值记录");
        // 拆分充值记录
        int effectiveRowCount = accountRechargeRecordRepository
                .reduceRechargeRecordAmount(inputVO.getCustomerId(), inputVO.getOperaId(), IsDonateConst.NO, payOverAmount);
        assert effectiveRowCount == 1;

        // 获取源充值记录
        AccountRechargeRecord rechargeRecord = accountRechargeRecordRepository
                .findByCustomerIdAndOperaIdAndIsDonate(inputVO.getCustomerId(), inputVO.getOperaId(), IsDonateConst.NO);

        // 操作记录
        AuditLog auditLog = new AuditLog();
        auditLog.setContractId(addContractResultVO.getPayOverContractId());
        auditLog.setCreateUserId(inputVO.getCreateUserId());
        auditLog.setCreateUserIp(inputVO.getCreateUserIp());
        auditLog.setCustomerId(inputVO.getCustomerId());
        auditLog.setDetail(JSON.toJSONString(addContractResultVO));
        auditLog.setEvent(AuditLogEvent.deposit.getEvent());
        auditLog.setStatus(AuditLogStatusConst.SUCCESS);
        auditLogRepository.save(auditLog);

        // 保存拆分的充值记录
        AccountRechargeRecord payOverRecord = new AccountRechargeRecord();
        BeanUtils.copyProperties(rechargeRecord, payOverRecord);
        payOverRecord.setDeposit(payOverAmount);
        payOverRecord.setAvailable(0L);
        payOverRecord.setContractId(addContractResultVO.getPayOverContractId());
        payOverRecord.setOperaId(auditLog.getId());

        log.info("保存充值记录, rechargeRecord={}", payOverRecord);
        accountRechargeRecordRepository.save(payOverRecord);
    }

    @Override
    public RechargeRecordSearchResultVO searchRechargeRecord(RechargeRecordSearchVO searchVO) {

        List<AccountRechargeRecord> recordPage = accountRechargeRecordDao.list(searchVO);

        RechargeRecordSearchResultVO resultVO = new RechargeRecordSearchResultVO();
        Map<String, List<RechargeRecordVO>> recordMap = recordPage.stream().map(item -> {
            RechargeRecordVO rechargeRecordVO = new RechargeRecordVO();
            BeanUtils.copyProperties(item, rechargeRecordVO);
            return rechargeRecordVO;
        }).collect(Collectors.groupingBy(RechargeRecordVO::getContractId));
        resultVO.setRecordMap(recordMap);

        return resultVO;
    }


    /**
     * 充值顺延操作
     *
     * @param inputVO 充值信息
     */
    private void handlePostpone(DepositInputVO inputVO) {
        if (inputVO.getIsForce()) {
            // 目前暂时只发现在0元充值的时候会为true
            return;
        }
        Long depositAmount = inputVO.getDepositAmount();
        Long donateAmount = inputVO.getDonate();
        Long testAmount = inputVO.getTestAmount();
        Integer amountExpireType = inputVO.getAmountExpireType();
        Date amountExpireDate = Objects.isNull(inputVO.getAmountExpireDate())
                ? getExpireDate(inputVO.getAmountValidYears() == null ? 2 : inputVO.getAmountValidYears())
                : inputVO.getAmountExpireDate();
        String customerId = inputVO.getCustomerId();
        if (Objects.isNull(amountExpireType)) {
            throw new AccountDepositRunTimeException(
                    String.format("充值对应的金额过期类型【%s】实体信息【%s】", amountExpireType, JSON.toJSON(inputVO)));
        }
        // 产品要求：不管 此次充值是否顺延类型，都去更新历史顺延的账单。
        // if (amountExpireType.equals(AmountExpireTypeEnum.POSTPONE.getCode())) {}
        // 金额充值
        if (Objects.nonNull(depositAmount) && depositAmount > 0L) {
            // 更新对应用户的，顺延类型的，未过期的，非赠送的，充值的过期时间
            int handleRechargePostpone = this.accountRechargeRecordRepository.handleAccountRechargePostpone(customerId, amountExpireDate);
            if (handleRechargePostpone < 1) {
                log.error(this.getClass().getName(),
                        String.format("handleAccountRechargePostpone return less than 1.param=[%s]", JSON.toJSON(inputVO)));
            }
        }
        // 赠送金额
        if (Objects.nonNull(donateAmount) && donateAmount > 0L) {
            int handleDonatePostpone = this.accountRechargeRecordRepository.handleAccountDonatePostpone(customerId, amountExpireDate);
            if (handleDonatePostpone < 1) {
                log.error(this.getClass().getName(),
                        String.format("handleAccountDonatePostpone return less than 1.param=[%s]", JSON.toJSON(inputVO)));
            }
        }

        log.info("===------------ payunpayenabled {}",inputVO.getPayUnpayEnabled());
        // 测试金额
        if (Objects.nonNull(testAmount) && testAmount > 0L) {
            int handleTestPostpone = this.accountRechargeRecordRepository.handleAccountTestPostpone(customerId, amountExpireDate);
            //新逻辑，判断是否可用于偿还待支付账单
            if(SwitchEnum.isY(inputVO.getPayUnpayEnabled())){
                doTestPayUnpay(inputVO);
            }else{
                if (handleTestPostpone < 1) {
                    log.error(this.getClass().getName(),
                            String.format("handleAccountDonatePostpone return less than 1.param=[%s]", JSON.toJSON(inputVO)));
                }
            }
        }
    }


    /**
     * 处理测试金额可以用于支付待支付逻辑
     */
    private void doTestPayUnpay(DepositInputVO inputVO){
        log.info("===------------ payunpayenabled getTestAmount {}",inputVO.getTestAmount());
        // 充值金额<=0，无需调财务系统创建合同
        if (inputVO.getTestAmount() <= 0) {
            return;
        }
        // 统计待支付情况
        long payOverAmount = unPayBillService.calcUnPayAmount(inputVO.getCustomerId());
        log.info("===------------ payunpayenabled getTestAmount {} getPayOverAmount {}",payOverAmount);
        if (inputVO.getTestAmount() < payOverAmount) {
            return ;
        }
        //固定的虚拟订单号
        String soId="888888888888888888888888";
        //点播cc_user表主键id
        Integer autoId;

        String email = "";
        String company = "";
        String salesUserName = "";
        //兼容集团账号主账号充值逻辑，autoId没有
        if (AccountTypeEnum.GROUP2.getCode().equals(inputVO.getAccountType())) {
            autoId = 0;
            ResponseVO<GroupAccountVO> responseVO = groupAccountServiceApi.getById(inputVO.getCustomerId());
            if (responseVO.isSuccess() && responseVO.getData() != null) {
                GroupAccountVO groupAccount2VO = responseVO.getData();
                email = groupAccount2VO.getEmail();
                company = groupAccount2VO.getCompany();
                salesUserName = groupAccount2VO.getSaleUserName();
            }
        } else {
            UserMsgVO userMsgVO = UserClient.getUserByUnionId(inputVO.getCustomerId());
            Assert.notNull(userMsgVO, "查询顾客信息失败");
            autoId = userMsgVO.getAutoId();
            email = userMsgVO.getEmail();
            company = userMsgVO.getCompanyName();
            salesUserName = userMsgVO.getSaleUserName();
        }
        GroupAccountAndNormalUserVO accountAndNormalUserVO = customerService.getByEmailAndAccountType(email, inputVO.getAccountType());

        AddAmountContractDTO addAc = AddAmountContractDTO.builder()
                .soId(soId)
                .unionId(inputVO.getCustomerId())
                .accountType(inputVO.getAccountType())
                .autoId(autoId)
                .operationCode(ContractOperationCodeEnum.DEPOSIT_AMOUNT.getCode())
                .contractAmount(inputVO.getTestAmount() - payOverAmount)
                .payOverAmount(payOverAmount)
                .donateAmount(inputVO.getTestAmount())
                .payBackStatus(inputVO.getPayBackStatus())
                .saleUserId(inputVO.getSaleUserId())
                .email(email)
                .company(company)
                .saleUserName(salesUserName)
                .build();
        CommonResult<AddContractResultVO> addAmountContractResult = this.financeContractService.addTestAmountContract(
                addAc);
        if (CommonResult.isNotOk(addAmountContractResult)) {
            log.error(String.format("addTestAmountContract fail,param{%s},result{%s}", JsonUtil.beanToString(addAc),
                    addAmountContractResult.getMsg()));
            return ;
        }

        // 更新状态
        this.postTestAmountProcess(inputVO, addAmountContractResult.getData());
    }

    /**
     * 处理测试金额可以用于支付待支付逻辑--偿还待支付
     */
    private void postTestAmountProcess(DepositInputVO inputVO, AddContractResultVO addContractResultVO) {
        // 统计待支付情况
        List<CustomerBillingDaily> waitPayBillList = unPayBillService.listUnPayBill(inputVO.getCustomerId());
        long payOverAmount = unPayBillService.calcUnPayAmount(inputVO.getCustomerId());
        // 充值金额 > 0，才需要偿还待支付
        if (inputVO.getTestAmount() > 0 && inputVO.getTestAmount() >= payOverAmount) {
            // 更新待支付账单
            this.updateUnPayBill(waitPayBillList, addContractResultVO);

            // 更新未关联账单的合同id
            customerBillingDailyRepository.relateUnrelatedBill(addContractResultVO.getPayOverContractId(), inputVO.getOperaId(),
                    CustomerBillingDailyAmountTypeConst.deposit.getAmountType(), BillStatusConst.PAID, inputVO.getCustomerId(),
                    BillStatusConst.UNRELATED);

            // 拆充值记录
            this.splitTestAmountRechargeRecord(inputVO, payOverAmount, addContractResultVO);
        }
    }

    /**
     * 处理测试金额可以用于支付待支付-拆分充值记录
     */
    private void splitTestAmountRechargeRecord(DepositInputVO inputVO, long payOverAmount, AddContractResultVO addContractResultVO) {
        if (addContractResultVO == null || payOverAmount <= 0L) {
            log.info("没有待支付账单，不执行充值记录拆分操作");
            return;
        }
        log.info("执行拆分充值记录");
        // 拆分充值记录
        int effectiveRowCount = accountRechargeRecordRepository
                .reduceRechargeRecordAmount(inputVO.getCustomerId(), inputVO.getOperaId(), AmountDepositTypeEnum.TEST.getCode(), payOverAmount);
        assert effectiveRowCount == 1;

        // 获取源充值记录
        AccountRechargeRecord rechargeRecord = accountRechargeRecordRepository
                .findByCustomerIdAndOperaIdAndIsDonate(inputVO.getCustomerId(), inputVO.getOperaId(), AmountDepositTypeEnum.TEST.getCode());

        // 操作记录
        AuditLog auditLog = new AuditLog();
        auditLog.setContractId(addContractResultVO.getPayOverContractId());
        auditLog.setCreateUserId(inputVO.getCreateUserId());
        auditLog.setCreateUserIp(inputVO.getCreateUserIp());
        auditLog.setCustomerId(inputVO.getCustomerId());
        auditLog.setDetail(JSON.toJSONString(addContractResultVO));
        auditLog.setEvent(AuditLogEvent.deposit.getEvent());
        auditLog.setStatus(AuditLogStatusConst.SUCCESS);
        auditLogRepository.save(auditLog);

        // 保存拆分的充值记录
        AccountRechargeRecord payOverRecord = new AccountRechargeRecord();
        BeanUtils.copyProperties(rechargeRecord, payOverRecord);
        payOverRecord.setDeposit(payOverAmount);
        payOverRecord.setAvailable(0L);
        payOverRecord.setContractId(addContractResultVO.getPayOverContractId());
        payOverRecord.setOperaId(auditLog.getId());
        payOverRecord.setIsDonate(IsDonateConst.NO);

        log.info("保存测试金额抵扣待支付充值记录, test-amount-rechargeRecord={}", payOverRecord);
        accountRechargeRecordRepository.save(payOverRecord);
    }

    /**
     * 充值顺延操作
     *
     * @param inputVO 充值信息
     */
    private void handlePostponeResourcePoint(DepositInputVO inputVO) {
        if (inputVO.getIsForce()) {
            // 目前暂时只发现在0元充值的时候会为true
            return;
        }
        Long depositAmount = inputVO.getDepositAmount();
        Long donateAmount = inputVO.getDonate();
        Long testAmount = inputVO.getTestAmount();
        Integer amountExpireType = inputVO.getAmountExpireType();
        Date amountExpireDate = Objects.isNull(inputVO.getAmountExpireDate())
                ? getExpireDate(inputVO.getAmountValidYears() == null ? 2 : inputVO.getAmountValidYears())
                : inputVO.getAmountExpireDate();
        String customerId = inputVO.getCustomerId();
        if (Objects.isNull(amountExpireType)) {
            throw new AccountDepositRunTimeException(
                    String.format("充值对应的资源点过期类型【%s】实体信息【%s】", amountExpireType, JSON.toJSON(inputVO)));
        }
        // 产品要求：不管 此次充值是否顺延类型，都去更新历史顺延的账单。
        // if (amountExpireType.equals(AmountExpireTypeEnum.POSTPONE.getCode())) {}
        // 金额充值
        if (Objects.nonNull(depositAmount) && depositAmount > 0L) {
            // 更新对应用户的，顺延类型的，未过期的，非赠送的，充值的过期时间
            int handleRechargePostpone = this.resourcePointRepository.handleAccountRechargePostpone(customerId, amountExpireDate);
            if (handleRechargePostpone < 1) {
                log.error(this.getClass().getName(),
                        String.format("handlePostponeResourcePoint return less than 1.param=[%s]", JSON.toJSON(inputVO)));
            }
        }
        // 赠送资源点
        if (Objects.nonNull(donateAmount) && donateAmount > 0L) {
            int handleDonatePostpone = this.resourcePointRepository.handleAccountDonatePostpone(customerId, amountExpireDate);
            if (handleDonatePostpone < 1) {
                log.error(this.getClass().getName(),
                        String.format("handlePostponeResourcePoint return less than 1.param=[%s]", JSON.toJSON(inputVO)));
            }
        }

        // 测试金额
        if (Objects.nonNull(testAmount) && testAmount > 0L) {
            int handleTestPostpone = this.resourcePointRepository.handleAccountTestPostpone(customerId, amountExpireDate);
            if (handleTestPostpone < 1) {
                log.error(this.getClass().getName(),
                        String.format("handlePostponeResourcePoint return less than 1.param=[%s]", JSON.toJSON(inputVO)));
            }
        }
    }

    @Override
    public RechargeMaturityDateVO accountRechargeExpireDateExhibit(String customerId) {
        RechargeMaturityDateVO rmdVO = new RechargeMaturityDateVO();
        List<RechargeMaturityDateVO.RechargeMaturityDateDetail> detailList = new ArrayList<>();
        // 查询用户昨日并发使用量数据 Start
        int arrPageNum = 0;
        int arrPageSize = 50;
        int arrTotalPage = 0;
        Page<AccountRechargeRecord> arrPagingResult;
        Date maxExpireTime = null;
        do {
            arrPagingResult = this.accountRechargeRecordRepository.rechargeExpireDatePaging(customerId,
                    PageRequest.of(arrPageNum++, arrPageSize, Sort.by("expireDate").descending()));
            // 如果没有分页数据，则退出循环
            if (Objects.isNull(arrPagingResult) || arrPagingResult.getNumberOfElements() == 0) {
                break;
            }
            if (arrTotalPage == 0) {
                arrTotalPage = arrPagingResult.getTotalPages();
            }
            List<AccountRechargeRecord> arrResultList = arrPagingResult.getContent();
            if (arrResultList.size() > 0) {
                for (AccountRechargeRecord arr : arrResultList) {
                    if (Objects.isNull(maxExpireTime)) {    // 最大过期时间
                        maxExpireTime = arr.getExpireDate();
                    }
                    RechargeMaturityDateVO.RechargeMaturityDateDetail curRmd = new RechargeMaturityDateVO.RechargeMaturityDateDetail();
                    Long available = arr.getAvailable();
                    Date expireDate = arr.getExpireDate();
                    curRmd.setDetailMaturityTime(DateUtil.format(expireDate, DateFormatUtil.FORMAT_DATE_NORMAL));
                    curRmd.setRemainingAmount(MoneyUtil.getHumanMoney(available));
                    if (IsDonateConst.YES == arr.getIsDonate()) {
                        if (Objects.isNull(rmdVO.getDonateEntity())) {
                            curRmd.setContractId("");
                            rmdVO.setDonateEntity(curRmd);
                        }
                    } else if (AmountDepositTypeEnum.TEST.getCode().equals(arr.getIsDonate())) {
                        if (Objects.isNull(rmdVO.getTestEntity())) {
                            curRmd.setContractId("");
                            rmdVO.setTestEntity(curRmd);
                        }
                    } else {
                        curRmd.setContractId(arr.getContractId());
                        detailList.add(curRmd);
                    }
                }
            }
        } while (arrPageNum < arrTotalPage);
        if (Objects.nonNull(maxExpireTime)) {
            rmdVO.setMaturityTime(DateUtil.format(maxExpireTime, DateFormatUtil.FORMAT_DATE_NORMAL));
        }
        if (detailList.size() < 1) {
            return rmdVO;
        }
        rmdVO.setDetailList(detailList);
        return rmdVO;
    }
    
    @Override
    public RechargeMaturityDateVO accountRechargeResourcePointExpireDateExhibit(String customerId) {
        RechargeMaturityDateVO rmdVO = new RechargeMaturityDateVO();
        List<RechargeMaturityDateVO.RechargeMaturityDateDetail> detailList = new ArrayList<>();
        // 查询用户昨日并发使用量数据 Start
        int arrPageNum = 0;
        int arrPageSize = 50;
        int arrTotalPage = 0;
        Page<AccountRechargeRecordResourcePoint> arrPagingResult;
        Date maxExpireTime = null;
        do {
            arrPagingResult = this.accountRechargeRecordResourcePointRepository.rechargeExpireDatePaging(customerId,
                    PageRequest.of(arrPageNum++, arrPageSize, Sort.by("expireDate").descending()));
            // 如果没有分页数据，则退出循环
            if (Objects.isNull(arrPagingResult) || arrPagingResult.getNumberOfElements() == 0) {
                break;
            }
            if (arrTotalPage == 0) {
                arrTotalPage = arrPagingResult.getTotalPages();
            }
            List<AccountRechargeRecordResourcePoint> arrResultList = arrPagingResult.getContent();
            if (arrResultList.size() > 0) {
                for (AccountRechargeRecordResourcePoint arr : arrResultList) {
                    if (Objects.isNull(maxExpireTime)) {    // 最大过期时间
                        maxExpireTime = arr.getExpireDate();
                    }
                    RechargeMaturityDateVO.RechargeMaturityDateDetail curRmd = new RechargeMaturityDateVO.RechargeMaturityDateDetail();
                    Long available = arr.getAvailable();
                    Date expireDate = arr.getExpireDate();
                    curRmd.setDetailMaturityTime(DateUtil.format(expireDate, DateFormatUtil.FORMAT_DATE_NORMAL));
                    curRmd.setRemainingAmount(MoneyUtil.getHumanMoney(available));
                    curRmd.setContractId(arr.getContractId());
                    curRmd.setType(1);
                    if (IsDonateConst.YES == arr.getIsDonate()) {
                        curRmd.setContractId("");
                        curRmd.setType(2);
                    }
                    if (AmountDepositTypeEnum.TEST.getCode().equals(arr.getIsDonate())) {
                        curRmd.setContractId("");
                        curRmd.setType(3);
                    }
                    detailList.add(curRmd);
                    
                }
            }
        } while (arrPageNum < arrTotalPage);
        if (Objects.nonNull(maxExpireTime)) {
            rmdVO.setMaturityTime(DateUtil.format(maxExpireTime, DateFormatUtil.FORMAT_DATE_NORMAL));
        }
        if (detailList.size() < 1) {
            return rmdVO;
        }
        rmdVO.setDetailList(detailList);
        return rmdVO;
    }
    
    
    @Override
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void executeExpiredCleanup(String inputSpecifyDate) throws ClearingSystemException {
        log.info(String.format("executeExpiredCleanup start.current time[%s].specifyDate[%s]", DateUtil.now(), inputSpecifyDate));
        // 查询出所有昨天过期的充值记录
        Date querySpecifyDate = null;
        List<AccountRechargeRecord> cleanupDepositRecordList = new ArrayList<>();
        try {
            if (StringUtils.isEmpty(inputSpecifyDate)) {
                querySpecifyDate = DateUtils.truncate(DateUtils.addDays(DateUtil.parseDate(DateUtil.now()), -1), Calendar.DATE);
            } else {
                querySpecifyDate = DateUtils.parseDate(inputSpecifyDate, DateFormatUtil.FORMAT_DATE_NORMAL);
            }
        } catch (ParseException e) {
            log.error(this.getClass().getName(), String.format("executeExpiredCleanup-parseDate error for input[%s]", inputSpecifyDate));
            throw new ClearingSystemException(String.format("executeExpiredCleanup-parseDate error for date[%s]", inputSpecifyDate));
        }
        int arrPageNum = 0;
        int arrPageSize = 50;
        int arrTotalPage = 0;
        Page<AccountRechargeRecord> arrPagingResult;
        Date maxExpireTime = null;
        do {
            arrPagingResult = this.accountRechargeRecordRepository.expiredRechargeQuery(querySpecifyDate,
                    PageRequest.of(arrPageNum++, arrPageSize, Sort.by("customerId").ascending()));
            // 如果没有分页数据，则退出循环
            if (Objects.isNull(arrPagingResult) || arrPagingResult.getNumberOfElements() == 0) {
                break;
            }
            if (arrTotalPage == 0) {
                arrTotalPage = arrPagingResult.getTotalPages();
            }
            List<AccountRechargeRecord> arrResultList = arrPagingResult.getContent();
            if (arrResultList.size() > 0) {
                cleanupDepositRecordList.addAll(arrResultList);
            }
        } while (arrPageNum < arrTotalPage);
        // 执行清理，由于没有主键id，只能用查询条件一致去保证查询出来的数据和执行update的数据一致
        int effectiveRows = this.accountRechargeRecordRepository.expiredRechargeCleanup(querySpecifyDate);
        if (effectiveRows != cleanupDepositRecordList.size()) {
            log.error("executeExpiredCleanup occur exception effectiveRows != cleanupDepositRecordList.size()");
        }
        createExpireCleanupBill(cleanupDepositRecordList, querySpecifyDate);
        handleGenerateGroupUserAlterationRecord(cleanupDepositRecordList);
    }

    @Override
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public void executeExpiredCleanupResourcePoint(String inputSpecifyDate) throws ClearingSystemException {
        log.info(String.format("executeExpiredCleanupResourcePoint start.current time[%s].specifyDate[%s]", DateUtil.now(), inputSpecifyDate));
        // 查询出所有昨天过期的充值记录
        Date querySpecifyDate = null;
        List<AccountRechargeRecordResourcePoint> cleanupDepositRecordList = new ArrayList<>();
        try {
            if (StringUtils.isEmpty(inputSpecifyDate)) {
                querySpecifyDate = DateUtils.truncate(DateUtils.addDays(DateUtil.parseDate(DateUtil.now()), -1), Calendar.DATE);
            } else {
                querySpecifyDate = DateUtils.parseDate(inputSpecifyDate, DateFormatUtil.FORMAT_DATE_NORMAL);
            }
        } catch (ParseException e) {
            log.error(this.getClass().getName(), String.format("executeExpiredCleanupResourcePoint-parseDate error for input[%s]", inputSpecifyDate));
            throw new ClearingSystemException(String.format("executeExpiredCleanupResourcePoint-parseDate error for date[%s]", inputSpecifyDate));
        }
        int arrPageNum = 0;
        int arrPageSize = 50;
        int arrTotalPage = 0;
        Page<AccountRechargeRecordResourcePoint> arrPagingResult;
        Date maxExpireTime = null;
        do {
            arrPagingResult = this.resourcePointRepository.expiredRechargeQuery(querySpecifyDate,
                    PageRequest.of(arrPageNum++, arrPageSize, Sort.by("customerId").ascending()));
            // 如果没有分页数据，则退出循环
            if (Objects.isNull(arrPagingResult) || arrPagingResult.getNumberOfElements() == 0) {
                break;
            }
            if (arrTotalPage == 0) {
                arrTotalPage = arrPagingResult.getTotalPages();
            }
            List<AccountRechargeRecordResourcePoint> arrResultList = arrPagingResult.getContent();
            if (arrResultList.size() > 0) {
                cleanupDepositRecordList.addAll(arrResultList);
            }
        } while (arrPageNum < arrTotalPage);
        // 执行清理，由于没有主键id，只能用查询条件一致去保证查询出来的数据和执行update的数据一致
        int effectiveRows = this.resourcePointRepository.expiredRechargeCleanup(querySpecifyDate);
        if (effectiveRows != cleanupDepositRecordList.size()) {
            log.error("executeExpiredCleanupResourcePoint occur exception effectiveRows != cleanupDepositRecordList.size()");
        }
        createExpireCleanupBillResourcePoint(cleanupDepositRecordList, querySpecifyDate);
    }

    /**
     * 生成过期清理账单数据
     *
     * @param cleanupDepositRecordList 过期清理充值记录
     */
    private void createExpireCleanupBill(List<AccountRechargeRecord> cleanupDepositRecordList, Date querySpecifyDate)
            throws ClearingSystemException {
        List<String> warningMessage = new ArrayList<>();
        BillingItem cleanupBi = this.billingItemRepository.findFirstByCode(ItemCodeConst.EXPIRED_CLEANUP.getCode());
        if (Objects.isNull(cleanupBi)) {
            throw new ClearingSystemException(String.format("createExpireCleanupBill fail." +
                    "billingItemRepository.findFirstByCode[%s]does not exists", ItemCodeConst.EXPIRED_CLEANUP.getCode()));
        }
        // 生成账单
        List<CustomerBillingDaily> expiredCleanupBillingDailyList = new ArrayList<>();
        for (AccountRechargeRecord arr : cleanupDepositRecordList) {
            CustomerBillingDaily curBill = new CustomerBillingDaily();
            // 清理的是昨天过期的充值，账单出今天的
            curBill.setCustomerId(arr.getCustomerId());
            curBill.setStatAt(org.apache.commons.lang3.time.DateUtils.addDays(querySpecifyDate, 1));
            // curBill.setConsumeStartDate(arr.getPeriodStartTime());
            // curBill.setConsumeEndDate(arr.getPeriodEndTime());
            curBill.setOperaId(arr.getOperaId());
            if (AmountDepositTypeEnum.DONATE.getCode().equals(arr.getIsDonate())
                    || AmountDepositTypeEnum.TEST.getCode().equals(arr.getIsDonate())) {
                curBill.setContractId("-1");
            } else {
                curBill.setContractId(arr.getContractId());
            }
            curBill.setItemId(cleanupBi.getId());
            curBill.setCusItemId(0);
            curBill.setTradeType(CustomerBillingDailyTradeTypeConst.amount_clearing.getTradeType());
            String amountType = "";
            if (AmountDepositTypeEnum.DONATE.getCode().equals(arr.getIsDonate())) {
                amountType = CustomerBillingDailyAmountTypeConst.donate.getAmountType();
            }
            if (AmountDepositTypeEnum.TEST.getCode().equals(arr.getIsDonate())) {
                amountType = CustomerBillingDailyAmountTypeConst.test.getAmountType();
            }
            if (AmountDepositTypeEnum.DEPOSIT.getCode().equals(arr.getIsDonate())) {
                amountType = CustomerBillingDailyAmountTypeConst.deposit.getAmountType();
            }
            curBill.setAmountType(amountType);
            curBill.setUnivalence(arr.getAvailable());
            curBill.setUnivalenceUnit("元");
            curBill.setUnivalenceUnitConversion(1L);
            curBill.setItemConsumed(BigDecimal.ONE);
            curBill.setOriginalItemConsumed(0L);
            curBill.setItemConsumedUnit("-");
            curBill.setCost(arr.getAvailable());
            curBill.setUnpaid(0L);
            curBill.setStatus(CustomerBillingDailyStatusEnum.BILLED.getStatus());
            curBill.setItemCategory("");
            curBill.setLastAdjustUnivalence(0L);
            expiredCleanupBillingDailyList.add(curBill);
        }
        if (expiredCleanupBillingDailyList.size() > 0) {
            // 保存账单
            List<CustomerBillingDaily> saveEffectiveRows = this.customerBillingDailyRepository.saveAll(expiredCleanupBillingDailyList);
            if (saveEffectiveRows.size() != cleanupDepositRecordList.size()) {
                log.error("executeExpiredCleanup occur exception saveEffectiveRows != cleanupDepositRecordList.size()");
            }
        }
        String contractIdCollection = cleanupDepositRecordList.stream()
                .map(AccountRechargeRecord::getContractId)
                .filter(StringUtils::hasLength)
                .distinct()
                .collect(Collectors.joining(","));
        warningMessage.add(String.format("【清理日期】%s\n 执行数据条数【%d】\n 合同ID【%s】",
                DateUtil.format(querySpecifyDate, DatePattern.NORM_DATE_PATTERN), cleanupDepositRecordList.size(),
                contractIdCollection));
        log.info("执行充值过期清零结果：{}", warningMessage);
        //提醒类消息不要告警
        //dingWarnRobot.sendWarnMsg("【执行充值过期清零】", warningMessage);
    }

    /**
     * 生成过期清理账单数据
     *
     * @param cleanupDepositRecordList 过期清理充值记录
     */
    private void createExpireCleanupBillResourcePoint(List<AccountRechargeRecordResourcePoint> cleanupDepositRecordList,
                                                      Date querySpecifyDate)
            throws ClearingSystemException {
        List<String> warningMessage = new ArrayList<>();
        BillingItem cleanupBi = this.billingItemRepository.findFirstByCode(ItemCodeConst.EXPIRED_CLEANUP_RESOURCE_POINT.getCode());
        if (Objects.isNull(cleanupBi)) {
            throw new ClearingSystemException(String.format("createExpireCleanupBillResourcePoint fail." +
                    "billingItemRepository.findFirstByCode[%s]does not exists", ItemCodeConst.EXPIRED_CLEANUP.getCode()));
        }
        // 生成账单
        List<CustomerBillingDaily> expiredCleanupBillingDailyList = new ArrayList<>();
        for (AccountRechargeRecordResourcePoint arr : cleanupDepositRecordList) {
            CustomerBillingDaily curBill = new CustomerBillingDaily();
            // 清理的是昨天过期的充值，账单出今天的
            curBill.setCustomerId(arr.getCustomerId());
            curBill.setStatAt(org.apache.commons.lang3.time.DateUtils.addDays(querySpecifyDate, 1));
            // curBill.setConsumeStartDate(arr.getPeriodStartTime());
            // curBill.setConsumeEndDate(arr.getPeriodEndTime());
            curBill.setOperaId(arr.getOperaId());
            if (AmountDepositTypeEnum.DONATE.getCode().equals(arr.getIsDonate())
                    || AmountDepositTypeEnum.TEST.getCode().equals(arr.getIsDonate())) {
                curBill.setContractId("-1");
            } else {
                curBill.setContractId(arr.getContractId());
            }
            curBill.setItemId(cleanupBi.getId());
            curBill.setCusItemId(0);
            curBill.setTradeType(CustomerBillingDailyTradeTypeConst.resource_point_bill.getTradeType());
            String amountType = "";
            if (AmountDepositTypeEnum.DONATE.getCode().equals(arr.getIsDonate())) {
                amountType = CustomerBillingDailyAmountTypeConst.donate.getAmountType();
            }
            if (AmountDepositTypeEnum.TEST.getCode().equals(arr.getIsDonate())) {
                amountType = CustomerBillingDailyAmountTypeConst.test.getAmountType();
            }
            if (AmountDepositTypeEnum.DEPOSIT.getCode().equals(arr.getIsDonate())) {
                amountType = CustomerBillingDailyAmountTypeConst.deposit.getAmountType();
            }
            curBill.setAmountType(amountType);
            curBill.setUnivalence(arr.getAvailable());
            curBill.setUnivalenceUnit("元");
            curBill.setUnivalenceUnitConversion(1L);
            curBill.setItemConsumed(BigDecimal.ONE);
            curBill.setOriginalItemConsumed(0L);
            curBill.setItemConsumedUnit("-");
            curBill.setCost(arr.getAvailable());
            curBill.setUnpaid(0L);
            curBill.setStatus(CustomerBillingDailyStatusEnum.BILLED.getStatus());
            curBill.setItemCategory("");
            curBill.setLastAdjustUnivalence(0L);
            expiredCleanupBillingDailyList.add(curBill);
        }
        if (expiredCleanupBillingDailyList.size() > 0) {
            // 保存账单
            List<CustomerBillingDaily> saveEffectiveRows = this.customerBillingDailyRepository.saveAll(expiredCleanupBillingDailyList);
            if (saveEffectiveRows.size() != cleanupDepositRecordList.size()) {
                log.error("createExpireCleanupBillResourcePoint occur exception saveEffectiveRows != cleanupDepositRecordList.size()");
            }
        }
        String contractIdCollection = cleanupDepositRecordList.stream()
                .map(AccountRechargeRecordResourcePoint::getContractId)
                .filter(StringUtils::hasLength)
                .distinct()
                .collect(Collectors.joining(","));
        warningMessage.add(String.format("资源点 【清理日期】%s\n 执行数据条数【%d】\n 合同ID【%s】",
                DateUtil.format(querySpecifyDate, DatePattern.NORM_DATE_PATTERN), cleanupDepositRecordList.size(),
                contractIdCollection));
        log.info("执行资源点过期清零结果：{}", warningMessage);
        //提醒类消息不要告警
        //dingWarnRobot.sendWarnMsg("【执行充值过期清零】", warningMessage);
    }

    @Override
    public List<DepositApproachingExpireMessageDTO> depositApproachingExpireNotify(AccountDepositExpireRequest inputVo) {
        List<DepositApproachingExpireMessageDTO> daeMessageList = new ArrayList<>();
        String specifyDate = inputVo.getSpecifyDate();
        Integer daysThreshold = inputVo.getDaysThreshold();
        Date querySpecifyDate = null;
        if (StringUtils.isEmpty(specifyDate)) {
            // 查询今天以及今天之前过期的充值
            querySpecifyDate = DateUtils.truncate(DateUtil.parseDate(DateUtil.now()), Calendar.DATE);
        } else {
            querySpecifyDate = DateUtils.truncate(DateUtil.parseDate(specifyDate), Calendar.DATE);
        }
        int arrPageNum = 0;
        int arrPageSize = 100;
        int arrTotalPage = 0;
        Page<AccountRechargeRecord> arrPagingResult;
        do {
            arrPagingResult = this.accountRechargeRecordRepository.queryApproachingExpireRecharge(querySpecifyDate,
                    PageRequest.of(arrPageNum++, arrPageSize, Sort.by("customerId").ascending()));
            // 如果没有分页数据，则退出循环
            if (Objects.isNull(arrPagingResult) || arrPagingResult.getNumberOfElements() == 0) {
                break;
            }
            if (arrTotalPage == 0) {
                arrTotalPage = arrPagingResult.getTotalPages();
            }
            List<AccountRechargeRecord> arrResultList = arrPagingResult.getContent();
            for (AccountRechargeRecord arr : arrResultList) {
                DepositApproachingExpireMessageDTO daeMessage = assembleDaeMessageDTO(arr, querySpecifyDate, daysThreshold);
                if (Objects.isNull(daeMessage)) {
                    continue;
                }
                daeMessageList.add(daeMessage);
            }
        } while (arrPageNum < arrTotalPage);
        return formatDepositApproachingExpireMessageResult(daeMessageList);
    }


    /**
     * 组装充值临期提醒数据对象
     *
     * @param arr              充值数据
     * @param querySpecifyDate 指定日期
     * @param daysThreshold    日期阈值
     * @return 组装对象
     */
    private DepositApproachingExpireMessageDTO assembleDaeMessageDTO(AccountRechargeRecord arr,
                                                                     Date querySpecifyDate, Integer daysThreshold) {
        Date expireDate = arr.getExpireDate();
        long diffDays = DateUtil.betweenDay(querySpecifyDate, expireDate, true);
        diffDays -= 1;
        Integer daysThresholdJudge = surplusDaysThresholdJudge(Long.valueOf(diffDays).intValue(), daysThreshold);
        if (Objects.isNull(daysThresholdJudge)) {  // 不在目标判断阈值内
            return null;
        }
        DepositApproachingExpireMessageDTO daeMessage = new DepositApproachingExpireMessageDTO();
        daeMessage.setCustomerId(arr.getCustomerId());
        if (arr.getIsDonate().equals(IsDonateConst.YES)) {
            daeMessage.setContractId("赠送");
        } else {
            daeMessage.setContractId(arr.getContractId());
        }
        daeMessage.setSurplusAmount(MoneyUtil.getHumanMoney(arr.getAvailable()));
        daeMessage.setDaysThreshold(daysThresholdJudge);
        daeMessage.setExpireDate(DateUtil.format(arr.getExpireDate(), DateFormatUtil.FORMAT_DATE_NORMAL));
        return daeMessage;
    }

    /**
     * 组装结果集合的额外信息
     *
     * @param daeMessageList daeMessageList
     * @return 携带额外信息的结果集
     */
    private List<DepositApproachingExpireMessageDTO> formatDepositApproachingExpireMessageResult(
            List<DepositApproachingExpireMessageDTO> daeMessageList) {
        // 查询公司信息
        Map<String, UserMsgVO> userMsgMap = new HashMap<>();
        List<String> depositCustomerIdList = daeMessageList.stream().map(DepositApproachingExpireMessageDTO::getCustomerId)
                .filter(org.apache.commons.lang3.StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (depositCustomerIdList.size() > 0) {
            List<UserMsgVO> overstepUserList = UserClient.getUsersByUnionIds(org.apache.commons.lang3.StringUtils.join(depositCustomerIdList, ","));
            userMsgMap = overstepUserList.stream().filter(f -> org.apache.commons.lang.StringUtils.isNotEmpty(f.getUnionId()))
                    .collect(Collectors.toMap(UserMsgVO::getUnionId, Function.identity(), (o, n) -> n, HashMap::new));
        }
        Iterator<DepositApproachingExpireMessageDTO> deaIt = daeMessageList.iterator();
        while (deaIt.hasNext()) {
            DepositApproachingExpireMessageDTO dea = deaIt.next();
            String currentCustomerId = dea.getCustomerId();
            UserMsgVO trUserMsg = userMsgMap.get(currentCustomerId);
            // 产品要求 要过滤 isGroup = Y 的
            if (Objects.nonNull(trUserMsg) && org.apache.commons.lang.StringUtils.isNotEmpty(trUserMsg.getIsGroup())
                    && trUserMsg.getIsGroup().trim().equals("Y")) {
                deaIt.remove();
            }
            if (Objects.nonNull(trUserMsg)) {
                dea.setCompany(trUserMsg.getCompanyName());
                dea.setAccount(trUserMsg.getEmail());
            }
            StringBuilder contentSb = new StringBuilder();
            contentSb.append("【金额到期提醒】\n");
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(dea.getCompany())) {
                contentSb.append(String.format("公司名：%s \n", dea.getCompany()));
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(dea.getAccount())) {
                contentSb.append(String.format("账号：%s \n", dea.getAccount()));
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(dea.getContractId())) {
                contentSb.append(String.format("合同ID：%s \n", dea.getContractId()));
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(dea.getSurplusAmount())) {
                contentSb.append(String.format("剩余金额：%s \n", dea.getSurplusAmount()));
            }
            Integer daysThreshold = dea.getDaysThreshold();
            if (daysThreshold == -1) {
                contentSb.append(String.format("说明：客户金额已过期，清零金额 %s 元", dea.getSurplusAmount()));
            } else {
                contentSb.append(String.format("说明：客户金额将于 %s 到期，剩余 %d 天,过期后将清零金额，请及时处理。剩余金额 %s 元",
                        dea.getExpireDate(), daysThreshold, dea.getSurplusAmount()));
            }
            dea.setContent(contentSb.toString());
        }
        return daeMessageList;
    }

    /**
     * 剩余过期天数判断
     *
     * @param input         输入
     * @param daysThreshold 阈值
     * @return 阈值
     */
    private Integer surplusDaysThresholdJudge(Integer input, Integer daysThreshold) {
        if (Objects.isNull(input)) {
            return null;
        }
        if (input.compareTo(daysThreshold) <= 0) {
            return input;
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public CommonResult depositRegistrationGift(List<AccountDepositWithValidRequest> adwvrList) throws ClearingSystemException {
        if (CollectionUtils.isEmpty(adwvrList)) {
            return CommonResult.fail("depositRegistrationGift request list is empty");
        }

        List<AccountRechargeRecord> saveRecordList = new ArrayList<>();
        for (AccountDepositWithValidRequest adwvr : adwvrList) {
            CommonResult<AccountRechargeRecord> checkResult = depositRegistrationGiftParamCheck(adwvr);
            if (CommonResult.isNotOk(checkResult)) {
                if (Objects.nonNull(adwvr.getRegularCustomer()) &&
                        adwvr.getRegularCustomer().equals(1) && Objects.nonNull(checkResult.getData())) {
                    Date updateExpireDate;
                    try {
                        updateExpireDate = DateUtils.parseDate(adwvr.getExpireDate(), DateFormatUtil.FORMAT_DATE_NORMAL);
                    } catch (ParseException pe) {
                        throw new ClearingSystemException(pe);
                    }
                    this.accountRechargeRecordRepository.rechargeDonateRegistrationGift(adwvr.getCustomerId(),
                            Long.parseLong(adwvr.getAmount()), updateExpireDate);
                }
                continue;
            }
            // 校验用户
            String customerId = adwvr.getCustomerId();
            AccountRechargeRecord arr = createRechargeData(adwvr);
            saveRecordList.add(arr);
        }
        if (!CollectionUtils.isEmpty(saveRecordList)) {
            this.accountRechargeRecordRepository.saveAll(saveRecordList);
        }

        return CommonResult.ok();
    }

    @Override
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public CommonResult depositRegistrationGiftResourcePoint(List<AccountDepositWithValidRequest> adwvrList) throws ClearingSystemException {
        if (CollectionUtils.isEmpty(adwvrList)) {
            return CommonResult.fail("depositRegistrationGiftResourcePoint request list is empty");
        }

        List<AccountRechargeRecordResourcePoint> saveRecordList = new ArrayList<>();
        for (AccountDepositWithValidRequest adwvr : adwvrList) {
            CommonResult<AccountRechargeRecordResourcePoint> checkResult = depositRegistrationGiftParamCheckResourcePoint(adwvr);
            if (CommonResult.isNotOk(checkResult)) {
                if (Objects.nonNull(adwvr.getRegularCustomer()) &&
                        adwvr.getRegularCustomer().equals(1) && Objects.nonNull(checkResult.getData())) {
                    Date updateExpireDate;
                    try {
                        updateExpireDate = DateUtils.parseDate(adwvr.getExpireDate(), DateFormatUtil.FORMAT_DATE_NORMAL);
                    } catch (ParseException pe) {
                        throw new ClearingSystemException(pe);
                    }
                    this.resourcePointRepository.rechargeDonateRegistrationGift(adwvr.getCustomerId(),
                            Long.parseLong(adwvr.getAmount()), updateExpireDate);
                }
                continue;
            }
            // 校验用户
            String customerId = adwvr.getCustomerId();
            AccountRechargeRecordResourcePoint arr = createRechargeDataResourcePoint(adwvr);
            saveRecordList.add(arr);
        }
        if (!CollectionUtils.isEmpty(saveRecordList)) {
            this.resourcePointRepository.saveAll(saveRecordList);
        }

        return CommonResult.ok();
    }

    /**
     * 校验新注册用户充值参数
     *
     * @param request 请求参数
     * @return 校验结果
     * @throws ClearingSystemException 异常
     */
    private CommonResult<AccountRechargeRecord> depositRegistrationGiftParamCheck(AccountDepositWithValidRequest request)
            throws ClearingSystemException {
        String customerId = request.getCustomerId();
        String amount = request.getAmount();
        String expireDate = request.getExpireDate();
        if (StringUtils.isEmpty(customerId)) {
            return CommonResult.fail("depositRegistrationGift-customerId could not be null");
        }
        if (StringUtils.isEmpty(amount)) {
            return CommonResult.fail("depositRegistrationGift-amount could not be null");
        }
        if (StringUtils.isEmpty(expireDate)) {
            return CommonResult.fail("depositRegistrationGift-expireDate could not be null");
        }
        // 校验输入日期
        try {
            long amountValue = Long.parseLong(amount);
            // 校验输入金额
            if (amountValue <= 0) {
                return CommonResult.fail(String.format("amount less than or equal to 0.[%d]", amountValue));
            }
        } catch (NumberFormatException nfe) {
            throw new ClearingSystemException(String.format("depositRegistrationGift input amount illegal %s", amount));
        }

        try {
            DateUtils.parseDate(expireDate, DateFormatUtil.FORMAT_DATE_NORMAL);
        } catch (ParseException e) {
            throw new ClearingSystemException(String.format("depositRegistrationGift input expireDate illegal.[%s]" +
                    "{expect yyyy-MM-dd}", expireDate));
        }
        Integer regularCustomer = request.getRegularCustomer();
        Optional<AccountRechargeRecord> existentDonateRecord =
                this.accountRechargeRecordRepository.findByCustomerIdAndIsDonate(customerId, IsDonateConst.YES);
        if (Objects.nonNull(regularCustomer) && regularCustomer == 1) {
            // 该接口用于新注册用户赠送金额，检查是否已存在充值记录
            if (existentDonateRecord.isPresent()) {
                return CommonResult.fail(existentDonateRecord.get(), null);
            }
        } else {
            if (existentDonateRecord.isPresent()) {
                return CommonResult.fail(String.format("depositRegistrationGift-customer[%s] exists donate data %s",
                        customerId, JSON.toJSON(existentDonateRecord.get())));
            }
        }
        return CommonResult.ok();
    }

    /**
     * 校验新注册用户充值参数
     *
     * @param request 请求参数
     * @return 校验结果
     * @throws ClearingSystemException 异常
     */
    private CommonResult<AccountRechargeRecordResourcePoint> depositRegistrationGiftParamCheckResourcePoint(AccountDepositWithValidRequest request)
            throws ClearingSystemException {
        String customerId = request.getCustomerId();
        String amount = request.getAmount();
        String expireDate = request.getExpireDate();
        if (StringUtils.isEmpty(customerId)) {
            return CommonResult.fail("depositRegistrationGiftParamCheckResourcePoint-customerId could not be null");
        }
        if (StringUtils.isEmpty(amount)) {
            return CommonResult.fail("depositRegistrationGiftParamCheckResourcePoint-amount could not be null");
        }
        if (StringUtils.isEmpty(expireDate)) {
            return CommonResult.fail("depositRegistrationGiftParamCheckResourcePoint-expireDate could not be null");
        }
        // 校验输入日期
        try {
            long amountValue = Long.parseLong(amount);
            // 校验输入金额
            if (amountValue <= 0) {
                return CommonResult.fail(String.format("amount less than or equal to 0.[%d]", amountValue));
            }
        } catch (NumberFormatException nfe) {
            throw new ClearingSystemException(String.format("depositRegistrationGift input amount illegal %s", amount));
        }

        try {
            DateUtils.parseDate(expireDate, DateFormatUtil.FORMAT_DATE_NORMAL);
        } catch (ParseException e) {
            throw new ClearingSystemException(String.format("depositRegistrationGift input expireDate illegal.[%s]" +
                    "{expect yyyy-MM-dd}", expireDate));
        }
        Integer regularCustomer = request.getRegularCustomer();
        Optional<AccountRechargeRecordResourcePoint> existentDonateRecord =
                this.resourcePointRepository.findByCustomerIdAndIsDonate(customerId, IsDonateConst.YES);
        if (Objects.nonNull(regularCustomer) && regularCustomer == 1) {
            // 该接口用于新注册用户赠送金额，检查是否已存在充值记录
            if (existentDonateRecord.isPresent()) {
                return CommonResult.fail(existentDonateRecord.get(), null);
            }
        } else {
            if (existentDonateRecord.isPresent()) {
                return CommonResult.fail(String.format("depositRegistrationGiftParamCheckResourcePoint-customer[%s] exists donate data %s",
                        customerId, JSON.toJSON(existentDonateRecord.get())));
            }
        }
        return CommonResult.ok();
    }

    /**
     * 根据请求对象创建充值数据
     *
     * @param request 请求对象
     * @return 充值数据实体
     * @throws ClearingSystemException 异常
     */
    private AccountRechargeRecord createRechargeData(AccountDepositWithValidRequest request) throws ClearingSystemException {
        String customerId = request.getCustomerId();
        String amount = request.getAmount();
        String expireDate = request.getExpireDate();
        Long amountValue = Long.parseLong(amount);
        Date expireDateValue = null;
        try {
            expireDateValue = DateUtils.parseDate(expireDate, DateFormatUtil.FORMAT_DATE_NORMAL);
        } catch (ParseException pe) {
            throw new ClearingSystemException(pe);
        }
        // 保存充值数据
        AccountRechargeRecord arr = new AccountRechargeRecord();
        arr.setCustomerId(customerId);
        arr.setContractId("");
        arr.setDeposit(amountValue);
        arr.setFreezeAmount(0L);
        arr.setOperaId(-1L);
        arr.setIsDonate(IsDonateConst.YES);
        arr.setAvailable(amountValue);
        arr.setExpireDate(expireDateValue);
        arr.setExpireType(AmountExpireTypeEnum.POSTPONE.getCode());
        arr.setCreateUserId("system");
        Date current = new Date();
        arr.setCreateTime(current);
        arr.setUpdateTime(current);
        return arr;
    }

    /**
     * 根据请求对象创建充值数据
     *
     * @param request 请求对象
     * @return 充值数据实体
     * @throws ClearingSystemException 异常
     */
    private AccountRechargeRecordResourcePoint createRechargeDataResourcePoint(AccountDepositWithValidRequest request) throws ClearingSystemException {
        String customerId = request.getCustomerId();
        String amount = request.getAmount();
        String expireDate = request.getExpireDate();
        Long amountValue = Long.parseLong(amount);
        Date expireDateValue = null;
        try {
            expireDateValue = DateUtils.parseDate(expireDate, DateFormatUtil.FORMAT_DATE_NORMAL);
        } catch (ParseException pe) {
            throw new ClearingSystemException(pe);
        }
        // 保存充值数据
        AccountRechargeRecordResourcePoint arr = new AccountRechargeRecordResourcePoint();
        arr.setCustomerId(customerId);
        arr.setContractId("");
        arr.setDeposit(amountValue);
        arr.setFreezeAmount(0L);
        arr.setOperaId(-1L);
        arr.setIsDonate(IsDonateConst.YES);
        arr.setAvailable(amountValue);
        arr.setExpireDate(expireDateValue);
        arr.setExpireType(AmountExpireTypeEnum.INDEPENDENCE.getCode());
        arr.setCreateUserId("system");
        Date current = new Date();
        arr.setCreateTime(current);
        arr.setUpdateTime(current);
        return arr;
    }

    /**
     * 校验充值赠送金额并偿还参数
     *
     * @param request 请求参数
     * @return 返回校验是否成功，成功额外返回是否存在数据
     * @throws ClearingSystemException 异常
     */
    private CommonResult<AccountRechargeRecord> donateWithRepayParamCheck(AccountDepositWithValidRequest request)
            throws ClearingSystemException {
        String customerId = request.getCustomerId();
        String amount = request.getAmount();
        if (StringUtils.isEmpty(customerId)) {
            return CommonResult.fail("donateWithRepayParamCheck-customerId could not be null");
        }
        if (StringUtils.isEmpty(amount)) {
            return CommonResult.fail("donateWithRepayParamCheck-amount could not be null");
        }
        try {
            long amountValue = Long.parseLong(amount);
            // 校验输入金额
            if (amountValue <= 0) {
                return CommonResult.fail(String.format("customerId[%s] amount less than 0 :%d", customerId, amountValue));
            }
        } catch (NumberFormatException nfe) {
            throw new ClearingSystemException(String.format("customerId[%s]donateWithRepayParamCheck input amount illegal :%s", customerId, amount));
        }
        Optional<AccountRechargeRecord> existentDonateRecord =
                this.accountRechargeRecordRepository.findByCustomerIdAndIsDonate(customerId, IsDonateConst.YES);
        // 参数校验成功返回是否存在赠送金额数据
        return existentDonateRecord.map(CommonResult::ok).orElseGet(CommonResult::ok);
    }


    @Override
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public CommonResult donateWithRepay(AccountDepositWithValidRequest adwvr) throws ClearingSystemException {
        if (Objects.isNull(adwvr)) {
            return CommonResult.fail("donateWithRepay request is empty");
        }
        // 查询用户
        // List<String> customerIdList = adwvrList.stream().map(AccountDepositWithValidRequest::getCustomerId).distinct().collect(Collectors.toList());
        CommonResult<AccountRechargeRecord> checkResult = donateWithRepayParamCheck(adwvr);
        // 设置过期时间为今天+1天
        Date expireDate = DateUtils.truncate(DateUtils.addDays(new Date(), 1), Calendar.DATE);
        adwvr.setExpireDate(DateUtil.format(expireDate, DateFormatUtil.FORMAT_DATE_NORMAL));
        if (CommonResult.isNotOk(checkResult)) {
            return checkResult;
        }
        String currentCustomerId = adwvr.getCustomerId();
        UserMsgVO currentUserMsgVO = UserClient.getUserByUnionId(currentCustomerId);
        if (Objects.isNull(currentUserMsgVO)) {
            return CommonResult.fail("current currentCustomerId is not found");
        }
        long donateAmount = Long.parseLong(adwvr.getAmount());
        // 创建销售机会
        SalesOpportunities currentSaveSo = createDonateSo(currentCustomerId, currentUserMsgVO, expireDate, adwvr.getAmount());
        AccountRechargeRecord arr;
        if (Objects.nonNull(checkResult.getData())) {
            arr = checkResult.getData();
            arr.setDeposit(arr.getDeposit() + donateAmount);
            arr.setAvailable(arr.getAvailable() + donateAmount);
            arr.setExpireDate(expireDate);
        } else {
            arr = createRechargeData(adwvr);
        }
        this.accountRechargeRecordRepository.save(arr);
        this.salesOpportunitiesRepository.save(currentSaveSo);
        PayForUnpayBillInputVO inputVO = PayForUnpayBillInputVO.builder()
                .customerId(currentCustomerId)
                .useDonate(false)
                .useDeposit(false)
                .accountRechargeRecordList(Lists.newArrayList(arr))
                .build();
        try {
            this.unPayBillService.payForUnPayBill(inputVO);
        } catch (Exception e) {
            log.error(String.format("donateWithRepay.创建财务合同发生错误.customerId[%s]", currentCustomerId),e);
            throw new ClearingSystemException(String.format("donateWithRepay." +
                    "创建财务合同发生错误.customerId[%s]saleUserId[%s]msg[%s]", currentCustomerId, currentSaveSo.getSaleUserId(), e.getMessage()));
        }
        return CommonResult.ok();
    }
    
    @Override
    public GetResourcePointStatusResultVO getResourcePointStatus(CustomerInfoGetInputVO inputVO) {
        GetResourcePointStatusResultVO resultVO = new GetResourcePointStatusResultVO();
    
        // 可用余额
        resultVO.setValidAmount(accountRechargeRecordResourcePointRepository.sumUserValidBalanceAmount(inputVO.getCustomerId()));
    
        resultVO.setValidDepositAmount(accountRechargeRecordResourcePointRepository.sumUserValidBalanceAmountByIsDonate(inputVO
                .getCustomerId(), IsDonateConst.NO));
        resultVO.setValidDonateAmount(accountRechargeRecordResourcePointRepository.sumUserValidBalanceAmountByIsDonate(inputVO
                .getCustomerId(), IsDonateConst.YES) + accountRechargeRecordResourcePointRepository.sumUserValidBalanceAmountByIsDonate(inputVO
                .getCustomerId(), AmountDepositTypeEnum.TEST.getCode()));
        resultVO.setFreezeAmount(accountRechargeRecordResourcePointRepository.sumUserFreezeBalanceAmount(inputVO.getCustomerId()) +
                creditAlterationRecordRepository.sumFreezeCreditByCustomerId(inputVO.getCustomerId()));
        resultVO.setWaitPayAmount(customerBillingDailyRepository.sumCustomerTotalUnPay(inputVO.getCustomerId()));
        resultVO.setAccumulatedDepositAmount(accountRechargeRecordResourcePointRepository.sumRechargeAmount(inputVO.getCustomerId()));
        resultVO.setAccumulatedDonateAmount(accountRechargeRecordResourcePointRepository.sumDonateAmount(inputVO.getCustomerId())
                + accountRechargeRecordResourcePointRepository.sumTestAmount(inputVO.getCustomerId()));
    
        Optional<CustomerConfig> config = customerConfigDao.getCustomerConfig(inputVO.getCustomerId());
        if (config != null && config.isPresent()) {
            resultVO.setTotalCredit(config.get().getCredit());
        
            // 可用授信
            resultVO.setValidCredit(this.calcCustomerValidCredit(CustomerInfoGetInputVO.builder().customerId(inputVO.getCustomerId()).build()));
        } else {
            resultVO.setTotalCredit(0L);
            resultVO.setValidCredit(0L);
        }
        resultVO.setNewUser(this.isNewUser(inputVO));
        return resultVO;
    }
    
    
    private SalesOpportunities createDonateSo(String currentCustomerId, UserMsgVO currentUserMsgVO, Date expireDate, String amount) {
        SalesOpportunities createSo = new SalesOpportunities();
        String generateSoId = StringUtil.getRandomString(20);
        createSo.setSoId(generateSoId);
        createSo.setContractId("");
        createSo.setContractType("无合同");
        createSo.setName("无");
        createSo.setCode("无");
        createSo.setCustomerId(currentCustomerId);
        createSo.setCompany(currentUserMsgVO.getCompanyName());
        createSo.setEmail(currentUserMsgVO.getEmail());
        createSo.setStatus(SalesOpportunitiesStatusConst.associate.getStatus());
        createSo.setAmountGainedDate(new Date());
        createSo.setAmountGained(0L);
        createSo.setContractAmount(0L);
        createSo.setFunctionPackage("金额模式");
        createSo.setAmountExpireDate(expireDate);
        createSo.setAmountExpireType(AmountExpireTypeEnum.POSTPONE.getCode());
        createSo.setBusinessType(SalesOpportunitiesBusinessTypeConst.amount.getBusinessType());
        createSo.setBillingPlan("金额");
        createSo.setBillingPlanCode(BusinessBillingPlanCodeEnum.AMOUNT.getCode());
        createSo.setSummary("执行充值赠送金额且自动偿还待支付");
        createSo.setIsDel(SalesOpportunitiesIsDelConst.is_not_del.getStatus());
        createSo.setIsGroupUser("Y".equals(currentUserMsgVO.getIsGroup()) ? 1 : 0);
        createSo.setCreateUserId("system");
        createSo.setInvoiceType("");
        // 为空默认使用强哥的
        createSo.setSaleUserId("TVhfQA==");
        createSo.setAcceptanceAccount("");
        createSo.setType("赠送");
        createSo.setSaleUserName("");
        createSo.setContractStartTime(new Date());
        createSo.setContractStartTime(new Date());
        ExtObjectDO extObjectDO = new ExtObjectDO();
        extObjectDO.setDonate(Long.parseLong(amount));
        extObjectDO.setOpenTime(new Date());
        extObjectDO.setEffectiveTime(new Date());
        extObjectDO.setFileDOList(Collections.EMPTY_LIST);
        extObjectDO.setBillingItemDOList(Collections.EMPTY_LIST);
        extObjectDO.setEePackageEnable(false);
        createSo.setExt(JSON.toJSONString(extObjectDO));
        return createSo;
    }


    private void handleGenerateGroupUserAlterationRecord(List<AccountRechargeRecord> cleanupDepositRecordList) {
        Map<String, Long> regularAlterationMap = new HashMap<>();
        Map<String, Long> donateAlterationMap = new HashMap<>();
        Map<String, Long> testAlterationMap = new HashMap<>();
        Set<String> customerIdSet = new HashSet<>();
        for (AccountRechargeRecord arr : cleanupDepositRecordList) {
            Integer isDonate = arr.getIsDonate();
            String customerId = arr.getCustomerId();
            Long available = arr.getAvailable();
            if (StringUtils.isEmpty(customerId) || Objects.isNull(isDonate)) {
                continue;
            }
            if (isDonate == 0) {
                regularAlterationMap.merge(customerId, available, Long::sum);
            }else if (isDonate == 1) {
                donateAlterationMap.merge(customerId, available, Long::sum);
            }else if (isDonate == 2) {
                testAlterationMap.merge(customerId, available, Long::sum);
            }
            customerIdSet.add(customerId);
        }
        for (String customerId : customerIdSet) {
            Long regularAlteration = regularAlterationMap.getOrDefault(customerId, 0L);
            Long donateAlteration = donateAlterationMap.getOrDefault(customerId, 0L);
            Long testAlteration = testAlterationMap.getOrDefault(customerId, 0L);
            if (regularAlteration + donateAlteration + testAlteration == 0) {
                continue;
            }
            try {
                this.customerResourceService.generateGroupUserAlterationRecord(customerId, "amount", testAlteration, donateAlteration, regularAlteration);
            } catch (Exception e) {
                log.error("generateGroupUserAlterationRecord occur exception: " + e.getMessage(), e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = {ClearingSystemException.class, Exception.class})
    public CommonResult<String> registrationGift(AccountRegistrationGiftRequest params) throws ClearingSystemException {
        
        List<AccountDepositWithValidRequest> requestList = params.getRequestList();
        
        List<AccountDepositWithValidRequest> amountRequestList = Lists.newArrayList();
        List<AccountDepositWithValidRequest> resourcePointRequestList = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(requestList)){
            for (AccountDepositWithValidRequest item: requestList){
                if (Objects.equals(GiftTypeEnum.RESOURCE_POINT.getCode(), item.getType())){
                    resourcePointRequestList.add(item);
                }else {
                    amountRequestList.add(item);
                }
            }
        }

        CommonResult<String> result = CommonResult.ok();
        if (!CollectionUtils.isEmpty(amountRequestList)){
            // 原来赠送金额的逻辑
            result = this.depositRegistrationGift(amountRequestList);
            if (CommonResult.isNotOk(result)){
                return result;
            }
        }

        if (!CollectionUtils.isEmpty(resourcePointRequestList)){
            // 赠送资源点逻辑
            result = this.depositRegistrationGiftResourcePoint(resourcePointRequestList);
            if (CommonResult.isNotOk(result)){
                return result;
            }
        }

        // 送金额/资源点，都要走设置超码率默认单价
        List<String> depositCustomerIdList = requestList.stream().map(AccountDepositWithValidRequest::getCustomerId)
                .filter(org.apache.commons.lang3.StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        new Thread(() -> {
            try {
                // Define item codes and their corresponding ratios
                Map<String, Integer> itemRatios = new HashMap<>();
                itemRatios.put(ItemCodeConst.inter_pd.getCode(), interPd);
                this.customerBillingItemSettingService.updateOverCodeRateItemRatio(depositCustomerIdList, itemRatios);
                //增加设置连麦的自定义配置
                this.customerBillingItemSettingService.updateMicPdItemSetting(depositCustomerIdList);
            } catch (Exception e) {
                log.error("updateCustomerBillingItemRatio error", e);
                dingWarnRobot.sendMsgToAtPeople("【普通账号注册设置单价异常】，请检查",
                        Lists.newArrayList(String.format("客户ids：%s", depositCustomerIdList)), null);
            }
        }).start();

        List<AccountFunctionWithValidRequest> functionList = params.getCustomerFunctions();
        if (CollectionUtils.isEmpty(functionList)){
            return result;
        }
        
        AccountFunctionWithValidRequest resourcePointFunction = functionList.stream()
                .filter(item -> Objects.equals(CustomerFunctionEnum.RESOURCE_POINT_SETTLEMENT.getCode(), item.getType()))
                .findFirst().orElse(null);
        // 开启资源点结算功能
        if (Objects.nonNull(resourcePointFunction)){
            result = settlementConfigService.saveOrUpdateCustomerFunction(resourcePointFunction.getCustomerId(), 
                    CustomerFunctionEnum.RESOURCE_POINT_SETTLEMENT, resourcePointFunction.getValue());
        }
        
        return result;

    }
}

