package net.polyv.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import net.polyv.constant.item.ItemCodeConst;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.modules.common.util.JacksonUtil;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.BillingCustomerConfigPageReq;
import net.polyv.modules.pcs.api.req.GroupResourceRecalculateReq;
import net.polyv.modules.pcs.api.vo.BillingCustomerConfigVO;
import net.polyv.web.model.resource.CustomerSumNumberVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.ConcurrenceModeConst;
import net.polyv.constant.group.ResourceAssignBusinessTypeEnum;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.GroupAccountConfigRepository;
import net.polyv.dao.primary.GroupResourceAssignableRepository;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.model.entity.primary.GroupAccountConfig;
import net.polyv.model.entity.primary.GroupResourceAssignable;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.user.api.service.group.GroupUserServiceApi;
import net.polyv.modules.user.api.vo.GroupUserRelationVO;
import net.polyv.service.AccountDepositService;
import net.polyv.service.GroupResourceAssignableService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.account.GetBalanceStatusResultVO;
import net.polyv.web.model.account.item.CustomerItemInfoGetInputVO;
import net.polyv.web.model.account.item.ResAvailableStateDetailVO;

@Service
@Slf4j
public class GroupResourceAssignableServiceImpl implements GroupResourceAssignableService {
    
    @Resource
    private CustomerResourceService customerResourceService;
    @Resource
    private LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository;
    @Resource
    private AccountDepositService accountDepositService;
    @Resource
    private GroupUserServiceApi groupUserServiceApi;
    @Resource
    private GroupAccountConfigRepository groupAccountConfigRepository;
    @Resource
    private GroupResourceAssignableRepository groupResourceAssignableRepository;
    
    @Autowired
    private DingWarnRobot dingWarnRobot;

    @Resource
    private ThreadPoolExecutor threadPoolExecutor;

    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;

    @Override
    public void recalculateGroupResource(String groupId, Integer businessType) {
        try {
            //指定groupId和businessType
            if (StringUtils.isNotBlank(groupId)) {
                if (Objects.nonNull(businessType)) {
                    long assignable = getGroupResourceByBusinessType(groupId, businessType);
                    createOrUpdate(groupId, businessType, assignable);
                }
            }
            //全部主账号，全部计费项
            else {
                List<GroupAccountConfig> groupAccountConfigs = groupAccountConfigRepository.findAll();
                groupAccountConfigs.forEach(groupAccountConfig -> {
                    String groupAccountId = groupAccountConfig.getGroupId();
                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.CONCURRENCE.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.CONCURRENCE.getCode()));
    
                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.DURATION.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.DURATION.getCode()));
    
                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.SPACE.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.SPACE.getCode()));
    
                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.TRAFFIC.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.TRAFFIC.getCode()));
    
                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.MIC_DURATION.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.MIC_DURATION.getCode()));
    
                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.GUIDE_DURATION.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.GUIDE_DURATION.getCode()));
    
                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.AMOUNT.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.AMOUNT.getCode()));

                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.MATERIAL_SPACE.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.MATERIAL_SPACE.getCode()));

                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.MATERIAL_TRAFFIC.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.MATERIAL_TRAFFIC.getCode()));

                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.AI_PPTVIDEO_ENABLED.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.AI_PPTVIDEO_ENABLED.getCode()));

                    createOrUpdate(groupAccountId, ResourceAssignBusinessTypeEnum.AI_PPTVIDEO_DIGITALHUMAN_ENABLED.getCode(),
                            getGroupResourceByBusinessType(groupAccountId,
                                    ResourceAssignBusinessTypeEnum.AI_PPTVIDEO_DIGITALHUMAN_ENABLED.getCode()));
                });
            }
        } catch (Exception e) {
            log.error("recalculateGroupResource error", e);
            dingWarnRobot.sendWarnMsg("【重算集团主账号剩余可分配资源错误】",
                    String.format("groupId = %s,businessType = %s", groupId, businessType));
        }
        
    }

    @Override
    public void recalculateGroupResource(GroupResourceRecalculateReq req) {

        log.info("recalculateGroupResource req {} " , JacksonUtil.writeAsString(req));

        if(CollectionUtils.isEmpty(req.getGroupIds())) {
            return ;
        }

        if(CollectionUtils.isEmpty(req.getBusinessTypes())){
            // 为空则拿所有的businessType
            req.setBusinessTypes(ResourceAssignBusinessTypeEnum.valuess() );
        }

        for(String groupId : req.getGroupIds()){
            threadPoolExecutor.execute(() ->{
                for(Integer businessType : req.getBusinessTypes()){
                    try {
                        createOrUpdate(groupId, businessType, getGroupResourceByBusinessType(groupId, businessType));
                    } catch (Exception e) {
                        log.error("recalculateGroupResource error", e);
                        dingWarnRobot.sendWarnMsg("【重算集团主账号剩余可分配资源错误】",
                                String.format("groupId = %s,businessType = %s", groupId, businessType));
                    }
                }
            });
        }
    }
    
    private List<String> getGroupUserList(String groupId) {
        ResponseVO<List<GroupUserRelationVO>> userListResponse = groupUserServiceApi.listAll(groupId, null, null);
        if (userListResponse.isSuccess() && CollectionUtils.isNotEmpty(userListResponse.getData())) {
            List<net.polyv.modules.user.api.vo.GroupUserRelationVO> list = userListResponse.getData();
            return list.stream()
                    .map(net.polyv.modules.user.api.vo.GroupUserRelationVO::getUnionId)
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
    
    //剩余可分配的初始值 = 主账号资源可用量 - sum(分账号资源可用量)
    @Override
    public long getGroupResourceByBusinessType(String groupId, Integer businessType) {
        if (StringUtils.isNotBlank(groupId)) {
            long groupAccountAvailable = getResourceAvailableByBusinessType(groupId, businessType,true);
            //获取主账号下所有分账号
            List<String> groupUserList = getGroupUserList(groupId);
            if (CollectionUtils.isNotEmpty(groupUserList)) {
                long groupUserAvailable = sumGroupUsersByBusinessType(groupUserList, businessType);
                log.info("groupUserList = {},businessType = {},available = {}", groupUserList, businessType,
                        groupUserAvailable);
                return groupAccountAvailable - groupUserAvailable;
            }
            return groupAccountAvailable;
        }
        return 0L;
    }
    
    @Override
    public long sumGroupUsersByBusinessType(List<String> customerIds, Integer businessType) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return 0;
        }
        log.info("sumGroupUsersByBusinessType，customerIds = {}，businessType = {}", customerIds, businessType);
        return customerIds.stream()
                .mapToLong(customerId -> getResourceAvailableByBusinessType(customerId, businessType))
                .sum();
    }
    
    /**
     * 获取主账号或分账号的可用资源
     * @param userId
     * @param businessType
     * @return
     */
    private long getResourceAvailableByBusinessType(String userId, Integer businessType) {
        return getResourceAvailableByBusinessType(userId,businessType,false);
    }

    /**
     * 获取主账号或分账号的可用资源
     * @param userId
     * @param businessType
     * @param isGroupAccount 是否为集团主账号
     * @return
     */
    private long getResourceAvailableByBusinessType(String userId, Integer businessType,boolean isGroupAccount) {
        CustomerItemInfoGetInputVO getInputVO = new CustomerItemInfoGetInputVO();
        ResourceAssignBusinessTypeEnum typeEnum = ResourceAssignBusinessTypeEnum.getEnumByCode(businessType);
        long available = 0L;
        switch (typeEnum) {
            //并发
            case CONCURRENCE:
                LiveCustomerConcurrenceSetting activeSetting =
                        liveCustomerConcurrenceSettingRepository.findCustomerCurrentEffectiveSetting(
                                userId, DateUtil.getCurrentDay());
                //如果是并发包天、包月，从live_customer_concurrence_setting表取
                //如果是峰值并发，从快照+变动表取
                if (Objects.nonNull(activeSetting)) {
                    if (ConcurrenceModeConst.DAILY == activeSetting.getMode() ||
                            ConcurrenceModeConst.PRTC_DAILY == activeSetting.getMode() ||
                            ConcurrenceModeConst.PRTC_MONTHLY == activeSetting.getMode() ||
                            ConcurrenceModeConst.MONTHLY == activeSetting.getMode()) {
                        available = activeSetting.getConcurrence();
                    } else if (ConcurrenceModeConst.PEAK == activeSetting.getMode()) {
                        getInputVO.setCustomerId(userId);
                        getInputVO.setResourceCode(ResourceCodeConst.concurrence.name());
                        available = customerResourceService.getCustomerCurrentAvailable(getInputVO)
                                .get(0)
                                .getAvailable();
                    }
                    //有生效中的并发套餐，如果并发为0，则显示1
                    available = available == 0 ? 1 : available;
                }
                break;
            //分钟数
            case DURATION:
                getInputVO.setCustomerId(userId);
                getInputVO.setResourceCode(ResourceCodeConst.duration.name());
                available = customerResourceService.getCustomerCurrentAvailable(getInputVO).get(0).getAvailable();
                break;
            //空间
            case SPACE:
                getInputVO.setCustomerId(userId);
                getInputVO.setResourceCode(ResourceCodeConst.space.name());
                ResAvailableStateDetailVO spaceResAvailVO = customerResourceService.getCustomerCurrentAvailableDetail(
                        getInputVO);
                available = Objects.isNull(spaceResAvailVO) ? 0L :
                        spaceResAvailVO.getTempAvailable() + spaceResAvailVO.getPermanentAvailable() +
                                spaceResAvailVO.getPeriodAvailable();
                break;
            //流量
            case TRAFFIC:
                getInputVO.setCustomerId(userId);
                getInputVO.setResourceCode(ResourceCodeConst.traffic.name());
                ResAvailableStateDetailVO trafficResAvailVO = customerResourceService.getCustomerCurrentAvailableDetail(
                        getInputVO);
                available = Objects.isNull(trafficResAvailVO) ? 0L :
                        trafficResAvailVO.getPermanentAvailable() + trafficResAvailVO.getPeriodAvailable() +
                                trafficResAvailVO.getTempAvailable();
                break;
            //连麦
            case MIC_DURATION:
                getInputVO.setCustomerId(userId);
                getInputVO.setResourceCode(ResourceCodeConst.mic_duration.name());
                available = customerResourceService.getCustomerCurrentAvailable(getInputVO).get(0).getAvailable();
                break;
            //导播台
            case GUIDE_DURATION:
                getInputVO.setCustomerId(userId);
                getInputVO.setResourceCode(ResourceCodeConst.guide_duration.name());
                available = customerResourceService.getCustomerCurrentAvailable(getInputVO).get(0).getAvailable();
                break;
            //测试金额
            case AMOUNT:
                GetBalanceStatusResultVO accountBalanceState = accountDepositService.getBalanceStatus(
                        CustomerInfoGetInputVO.builder().customerId(userId).build());
                available =
                        Objects.nonNull(accountBalanceState) && Objects.nonNull(accountBalanceState.getValidAmount()) ?
                                accountBalanceState.getValidAmount() : 0L;
                break;
            //素材空间
            case MATERIAL_SPACE:
                //获取有效期内空间信息
                getInputVO.setCustomerId(userId);
                List<BillingCustomerConfig> spaceConfigList =
                        billingCustomerConfigRepository.findByUnionIdAndItemCodeAndIsDeletedAndExpireTimeGreaterThanEqual(userId, ItemCodeConst.material_space.getCode(), 0, new Date());
                log.info("======= spaceConfigList {} customerId {}", spaceConfigList,userId);
                if(null != spaceConfigList && !org.springframework.util.CollectionUtils.isEmpty(spaceConfigList)){
                    Long spaceTotal = spaceConfigList.stream()
                            .filter(obj -> Objects.nonNull(obj.getAvailableValue()))
                            .map(BillingCustomerConfig::getAvailableValue)
                            .reduce(0L, Long::sum);
                    long usedSpace = 0l;
                    if(isGroupAccount){
                        usedSpace = customerResourceService.getGroupMaterialUsedSpace(getInputVO.getCustomerId());
                    }else{
                        usedSpace = customerResourceService.getMaterialUsedSpace(getInputVO.getCustomerId());
                    }
                    available = spaceTotal - usedSpace;
                }
                break;
            //素材流量
            case MATERIAL_TRAFFIC:
                //素材库流量
                getInputVO.setCustomerId(userId);
                CustomerSumNumberVO trafficInfo = customerResourceService.getSumAvailableNumber(userId, ItemCodeConst.material_traffic.getCode(),
                        DateUtil.getCurrentDay());
                log.info("======= materialFlow {} customerId {}", trafficInfo,getInputVO.getCustomerId());
                if(null != trafficInfo){
                    available = trafficInfo.getAvailable();
                }
                break;
            case AI_PPTVIDEO_DIGITALHUMAN_ENABLED:
                //视频创作含数字人
                getInputVO.setCustomerId(userId);
                CustomerSumNumberVO aiWithDHInfo = customerResourceService.getSumAvailableNumber(userId, "aiPPTVideoDigitalHumanEnabled",
                        DateUtil.getCurrentDay());
                log.info("======= aiWithDHInfo {} customerId {}", aiWithDHInfo,getInputVO.getCustomerId());
                if(null != aiWithDHInfo){
                    available = aiWithDHInfo.getAvailable();
                }
                break;
            case AI_PPTVIDEO_ENABLED:
                //视频创作含数字人
                getInputVO.setCustomerId(userId);
                CustomerSumNumberVO aiInfo = customerResourceService.getSumAvailableNumber(userId, "aiPPTVideoEnabled",
                        DateUtil.getCurrentDay());
                log.info("======= aiInfo {} customerId {}", aiInfo,getInputVO.getCustomerId());
                if(null != aiInfo){
                    available = aiInfo.getAvailable();
                }
                break;
        }
        log.info("userId = {},businessType = {},available = {}", userId, businessType, available);
        return available;
    }
    
    private void createOrUpdate(String groupId, Integer businessType, long assignableResource) {
        GroupResourceAssignable resourceAssignable = groupResourceAssignableRepository.findByGroupIdAndBusinessType(
                groupId, businessType);
        if (Objects.nonNull(resourceAssignable)) {
            resourceAssignable.setAssignableAmount(assignableResource);
            resourceAssignable.setUpdateTime(new Date());
            groupResourceAssignableRepository.save(resourceAssignable);
        } else {
            GroupResourceAssignable groupResourceAssignable = new GroupResourceAssignable();
            groupResourceAssignable.setGroupId(groupId);
            groupResourceAssignable.setBusinessType(businessType);
            groupResourceAssignable.setAssignableAmount(assignableResource);
            groupResourceAssignableRepository.save(groupResourceAssignable);
    
        }
    }
    
    @Override
    public void addResourceAssignable(String groupId, Integer businessType, long amount) {
        log.info("添加主账号剩余可分配,groupId = {},businessType = {},amount = {}", groupId, businessType, amount);
        GroupResourceAssignable resourceAssignable = groupResourceAssignableRepository.findByGroupIdAndBusinessType(
                groupId, businessType);
        if (Objects.nonNull(resourceAssignable)) {
            amount = resourceAssignable.getAssignableAmount() + amount;
        }
        createOrUpdate(groupId, businessType, amount);
    }
}
