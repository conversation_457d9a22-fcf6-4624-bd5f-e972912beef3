package net.polyv.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.ResourceAlterTypeConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.constant.resource.ResourceSourceConst;
import net.polyv.dao.primary.AccountRechargeRecordRepository;
import net.polyv.dao.primary.VodUnlimitedFlowPackageDao;
import net.polyv.dao.primary.business.BusinessPackageSpecificationRepository;
import net.polyv.dao.primary.custom.CustomerConfigDao;
import net.polyv.dao.primary.resource.ResourceAlterationRecordRepository;
import net.polyv.model.entity.primary.CustomerConfig;
import net.polyv.model.entity.primary.VodUnlimitedFlowPackage;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.model.entity.primary.resource.ResourceAlterationRecord;
import net.polyv.modules.common.util.BeanUtil;
import net.polyv.modules.common.util.StringUtil;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.VodFlowPackageLimitRequest;
import net.polyv.modules.pcs.api.req.VodUnlimitedFlowPackageListRequest;
import net.polyv.modules.pcs.api.stereotype.PCSErrorCodeEnum;
import net.polyv.modules.pcs.api.stereotype.VodUnlimitedPackageTypeEnum;
import net.polyv.modules.pcs.api.vo.VodPackageState;
import net.polyv.modules.pcs.api.vo.VodUnlimitedFlowPackageVO;
import net.polyv.service.AccountDepositLogicService;
import net.polyv.service.AccountDepositService;
import net.polyv.service.GrayTestService;
import net.polyv.service.GroupAccountOneService;
import net.polyv.service.VodUnlimitedFlowPackageService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.business.BusinessPackageSpecificationService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.MoneyConvertUtils;
import net.polyv.util.PageUtils;
import net.polyv.util.converter.UnitConverterUtil;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.account.GetCustomerStateInputVO;
import net.polyv.web.model.account.GroupAccountAndNormalUserVO;
import net.polyv.web.model.account.item.CustomerItemInfoGetInputVO;
import net.polyv.web.model.account.item.ResAvailableStateDetailVO;
import net.polyv.web.model.user.CustomerStateVO;

/**
 * 点播无限版流量套餐实现
 * <AUTHOR>
 * @since 2023/03/12
 */
@Service
@Slf4j
public class VodUnlimitedFlowPackageServiceImpl implements VodUnlimitedFlowPackageService {
    
    @Resource
    private BusinessPackageSpecificationService businessPackageSpecificationService;
    //无限版旗舰型
    @Value("${polyv.vod-unlimited-traffic-package.ultimate:1000}")
    private long ultimate;
    
    //无限版优享型
    @Value("${polyv.vod-unlimited-traffic-package.premium:400}")
    private long premium;
    
    @Resource
    private AccountDepositLogicService accountDepositLogicService;
    @Resource
    private GrayTestService grayTestService;
    @Resource
    private GroupAccountOneService groupAccountOneService;
    @Resource
    private VodUnlimitedFlowPackageDao vodUnlimitedFlowPackageDao;
    @Resource
    private BusinessPackageSpecificationRepository businessPackageSpecificationRepository;
    
    @Resource
    private CustomerService customerService;
    
    @Resource
    private CustomerResourceService customerResourceService;
    
    @Resource
    private ResourceAlterationRecordRepository resourceAlterationRecordRepository;
    
    @Resource
    private AccountDepositService accountDepositService;
    
    @Resource
    private AccountRechargeRecordRepository accountRechargeRecordRepository;
    @Resource
    private CustomerConfigDao customerConfigDao;
    
    @Override
    public Pager<VodUnlimitedFlowPackageVO> list(VodUnlimitedFlowPackageListRequest request) {
        //过滤掉过期的套餐
        LocalDate localDate = DateUtil.getCurrentDay().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        request.setEndDate(localDate);
        Page<VodUnlimitedFlowPackage> summaryPage = vodUnlimitedFlowPackageDao.listPage(request);
        if (CollectionUtils.isEmpty(summaryPage.getContent())) {
            return new Pager<>();
        }
        List<VodUnlimitedFlowPackageVO> copy = BeanUtil.copy(summaryPage.getContent(), VodUnlimitedFlowPackageVO.class);
        handleData(copy);
        return PageUtils.assemblyPage(summaryPage, copy);
    }
    
    @Override
    public ResponseVO<Object> markIsLimit(VodFlowPackageLimitRequest request) {
        VodUnlimitedFlowPackage vodUnlimitedFlowPackage = vodUnlimitedFlowPackageDao.findById(request.getId());
        if (Objects.isNull(vodUnlimitedFlowPackage)) {
            return ResponseVO.failure(PCSErrorCodeEnum.DATA_NOT_EXIST);
        }
        vodUnlimitedFlowPackage.setIsMarkLimit(request.getIsLimit());
        vodUnlimitedFlowPackageDao.updateOrSave(vodUnlimitedFlowPackage);
        return ResponseVO.success();
    }
    
    private void handleData(List<VodUnlimitedFlowPackageVO> list) {
        //过滤掉过期的套餐数
        list = list.stream().filter(vodUnlimitedFlowPackageVO -> {
            if (Objects.nonNull(vodUnlimitedFlowPackageVO.getEndDate()) &&
                    vodUnlimitedFlowPackageVO.getEndDate().compareTo(DateUtil.getCurrentDay()) >= 0) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        list.forEach(vodUnlimitedFlowPackageVO -> {
            Date startDate = vodUnlimitedFlowPackageVO.getStartDate();
            Date endDate = vodUnlimitedFlowPackageVO.getEndDate();
            StringBuffer periodEffectiveDateRangeSb = new StringBuffer();
            if (Objects.nonNull(startDate)) {
                periodEffectiveDateRangeSb.append(DateFormatUtil.formatDateNormal(startDate)).append(" 至 ");
            }
            if (Objects.nonNull(endDate)) {
                periodEffectiveDateRangeSb.append(DateFormatUtil.formatDateNormal(endDate));
            }
            vodUnlimitedFlowPackageVO.setPeriodEffectiveDateRange(periodEffectiveDateRangeSb.toString());
            if (StringUtil.isNotEmpty(vodUnlimitedFlowPackageVO.getThreshold())) {
                String threshold = MoneyConvertUtils.fmtMicrometer(vodUnlimitedFlowPackageVO.getThreshold());
                vodUnlimitedFlowPackageVO.setThresholdDesc(threshold + vodUnlimitedFlowPackageVO.getThresholdUnit());
            }
    
            Long periodFlowUsed = vodUnlimitedFlowPackageVO.getPeriodFlowUsed();
            if (Objects.nonNull(periodFlowUsed)) {
                vodUnlimitedFlowPackageVO.setPeriodFlowUsedDesc(UnitConverterUtil.formatByteSize(periodFlowUsed));
            }
    
            Long permanentFlowAvailable = vodUnlimitedFlowPackageVO.getPermanentFlowAvailable();
            if (Objects.nonNull(periodFlowUsed)) {
                vodUnlimitedFlowPackageVO.setPermanentFlowAvailableDesc(
                        UnitConverterUtil.formatByteSize(permanentFlowAvailable));
            }
            
            Integer isMarkLimit = vodUnlimitedFlowPackageVO.getIsMarkLimit();
            if (Objects.nonNull(isMarkLimit)) {
                if (isMarkLimit == 1) {
                    vodUnlimitedFlowPackageVO.setIsMarkLimitDesc("是");
                }
                if (isMarkLimit == 0) {
                    vodUnlimitedFlowPackageVO.setIsMarkLimitDesc("否");
                }
            }
            
            Integer isOverThreshold = vodUnlimitedFlowPackageVO.getIsOverThreshold();
            if (Objects.nonNull(isOverThreshold)) {
                if (isOverThreshold == 1) {
                    vodUnlimitedFlowPackageVO.setThresholdDesc(
                            vodUnlimitedFlowPackageVO.getThresholdDesc() + "(已超阈值)");
                }
                if (isOverThreshold == 0) {
                    vodUnlimitedFlowPackageVO.setThresholdDesc(
                            vodUnlimitedFlowPackageVO.getThresholdDesc() + "(未超阈值)");
                }
            }
        });
    
    
    }
    
    @Override
    public List<VodUnlimitedFlowPackageVO> periodFlowWarnNotifyList() {
        List<VodUnlimitedFlowPackage> all = vodUnlimitedFlowPackageDao.findAll();
        if (CollectionUtils.isEmpty(all)) {
            return Collections.emptyList(); // return an empty list directly
        }
        //过滤出过期的数据
        all = all.stream().filter(vodUnlimitedFlowPackage -> {
            if (Objects.nonNull(vodUnlimitedFlowPackage.getEndDate()) &&
                    vodUnlimitedFlowPackage.getEndDate().compareTo(DateUtil.getCurrentDay()) >= 0) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        //过滤出普通账号数据
        return all.stream().map(vodUnlimitedFlowPackage -> {
                    long permanentFlowAvailable = Objects.nonNull(vodUnlimitedFlowPackage.getPermanentFlowAvailable()) ?
                            vodUnlimitedFlowPackage.getPermanentFlowAvailable() : 0;
                    long flowFlowAvailable = Objects.nonNull(vodUnlimitedFlowPackage.getTempFlowAvailable()) ?
                            vodUnlimitedFlowPackage.getTempFlowAvailable() : 0;
                    //是否是普通账号
                    boolean isNormalAccount =
                            AccountTypeEnum.NORMAL.getCode().equals(vodUnlimitedFlowPackage.getAccountType());
                    //(临时流量剩余 + 流量包剩余 <= 0) && 普通账号 才需要阈值告警
                    if ((permanentFlowAvailable + flowFlowAvailable <= 0) && isNormalAccount) {
                        VodUnlimitedFlowPackageVO vodUnlimitedFlowPackageVO = new VodUnlimitedFlowPackageVO();
                        BeanUtils.copyProperties(vodUnlimitedFlowPackage, vodUnlimitedFlowPackageVO);
                        return vodUnlimitedFlowPackageVO;
                    }
                    return null;
                }).filter(Objects::nonNull) // filter out null values
                .collect(Collectors.toList()); // collect the result into a list
    
    }
    
    @Override
    public List<VodUnlimitedFlowPackageVO> permanentFlowWarnNotifyList() {
        List<VodUnlimitedFlowPackage> all = vodUnlimitedFlowPackageDao.findAll();
        if (CollectionUtils.isEmpty(all)) {
            return Collections.emptyList(); // return an empty list directly
        }
        //过滤出超出阈值、普通账号 未过期的数据
        all = all.stream().filter(vodUnlimitedFlowPackage -> {
            if (Objects.nonNull(vodUnlimitedFlowPackage.getIsOverThreshold()) &&
                    vodUnlimitedFlowPackage.getIsOverThreshold() == 1 &&
                    AccountTypeEnum.NORMAL.getCode().equals(vodUnlimitedFlowPackage.getAccountType()) &&
                    Objects.nonNull(vodUnlimitedFlowPackage.getEndDate()) &&
                    vodUnlimitedFlowPackage.getEndDate().compareTo(DateUtil.getCurrentDay()) >= 0) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        return all.stream().map(vodUnlimitedFlowPackage -> {
                    Long permanentFlowAvailable = vodUnlimitedFlowPackage.getPermanentFlowAvailable();
                    //流量包可用大于0判断
                    if (Objects.nonNull(permanentFlowAvailable) && permanentFlowAvailable > 0) {
                        //获取客户最近7天流量包消耗
                        //前7天的日期
                        Date last7Day = DateUtil.getDateAfterDays(-7);
                        //当前日期
                        Date now = DateUtil.getCurrentDay();
                        List<ResourceAlterationRecord> alterationRecordList =
                                resourceAlterationRecordRepository.findByStatAtBetweenAndCustomerIdAndResourceCodeAndTypeAndSource(
                                last7Day, now, vodUnlimitedFlowPackage.getUnionId(), ResourceCodeConst.traffic.name(),
                                ResourceAlterTypeConst.consume.name(), ResourceSourceConst.PERMANENT);
                        if (!CollectionUtils.isEmpty(alterationRecordList)) {
                            //消耗求和
                            long sum = Math.negateExact(
                                    alterationRecordList.stream().mapToLong(ResourceAlterationRecord::getAlteration).sum());
                            //根据statAt分组后求组数量
                            long days = alterationRecordList.stream()
                                    .collect(Collectors.groupingBy(ResourceAlterationRecord::getStatAt))
                                    .size();
                            //如果都不为空，求平均
                            if (sum != 0 && days != 0) {
                                long average = sum / days;
                                //如果平均值大于0，计算剩余天数
                                if (average > 0) {
                                    
                                    long remainingDays = permanentFlowAvailable / average;
                                    VodUnlimitedFlowPackageVO vodUnlimitedFlowPackageVO =
                                            new VodUnlimitedFlowPackageVO();
                                    BeanUtils.copyProperties(vodUnlimitedFlowPackage, vodUnlimitedFlowPackageVO);
                                    vodUnlimitedFlowPackageVO.setPermanentFlowAvailableDays((int) remainingDays);
                                    return vodUnlimitedFlowPackageVO;
                                }
                            }
                        }
                    }
                    return null;
                }).filter(Objects::nonNull) // filter out null values
                .collect(Collectors.toList()); // collect the result into a list
    }
    
    @Override
    public void preStatisticVodUnlimitedPackage() {
        List<BusinessPackageSpecification> list = businessPackageSpecificationService.findVodUnlimitedFlowPackage();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(businessPackageSpecification -> {
                log.info("preStatisticVodUnlimitedPackage,businessPackageSpecification == {}",
                        businessPackageSpecification);
                try {
                    VodUnlimitedFlowPackage vodUnlimitedFlowPackage = new VodUnlimitedFlowPackage();
                    String accountType = businessPackageSpecification.getAccountType();
                    String customerId = businessPackageSpecification.getCustomerId();
                    vodUnlimitedFlowPackage.setUnionId(customerId);
                    GroupAccountAndNormalUserVO normalUserVO = customerService.getUserInfoByCustomerIdAndAccountType(
                            customerId, accountType);
                    if (Objects.nonNull(normalUserVO)) {
                        vodUnlimitedFlowPackage.setEmail(normalUserVO.getEmail());
                        vodUnlimitedFlowPackage.setCompany(normalUserVO.getCompany());
                        vodUnlimitedFlowPackage.setSaleUserId(normalUserVO.getSaleUserId());
                        vodUnlimitedFlowPackage.setSaleUserName(normalUserVO.getSaleUserName());
                    }
                    vodUnlimitedFlowPackage.setStartDate(businessPackageSpecification.getValidPeriodStartDate());
                    vodUnlimitedFlowPackage.setEndDate(businessPackageSpecification.getValidPeriodEndDate());
                    vodUnlimitedFlowPackage.setContractId(businessPackageSpecification.getContractId());
                    vodUnlimitedFlowPackage.setAccountType(accountType);
                    //剩余流量包,已使用套餐流量（区分1.0主账号和其他账号类型）
        
                    if (AccountTypeEnum.GROUP1.getCode().equals(accountType)) {
                        //已使用套餐流量 = 获取主账号下所有分账号套餐有效期内的消耗变动
                        //1.获取主账号下所有分账号
                        List<String> subUserIdList = groupAccountOneService.getUnionIdsByGroupId(customerId);
                        //2.获取分账号的套餐流量消耗之和
                        if (!CollectionUtils.isEmpty(subUserIdList)) {
                            long sumPeriodTraffic = resourceAlterationRecordRepository.sumAlteration(
                                    businessPackageSpecification.getValidPeriodStartDate(),
                                    businessPackageSpecification.getValidPeriodEndDate(), ResourceSourceConst.PERIOD,
                                    subUserIdList, ResourceCodeConst.traffic.name(),
                                    ResourceAlterTypeConst.consume.name());
                            vodUnlimitedFlowPackage.setPeriodFlowUsed(Math.negateExact(sumPeriodTraffic));
                            long sumPermanentFlowAvailable = 0L; //分账号总的流量包可用
                            long sumTempFlowAvailable = 0L;
                            for (String subUserId : subUserIdList) {
                                GetCustomerStateInputVO inputVO = new GetCustomerStateInputVO();
                                inputVO.setUnionId(subUserId);
                                CustomerStateVO customerState = customerService.getCustomerState(inputVO);
                                if (Objects.nonNull(customerState) &&
                                        Objects.nonNull(customerState.getVodPackageState())) {
                                    VodPackageState vodPackageState = customerState.getVodPackageState();
                                    sumPermanentFlowAvailable += UnitConverterUtil.GB2bytes(
                                            BigDecimal.valueOf(vodPackageState.getValidTrafficPackages()));
                                    sumTempFlowAvailable += UnitConverterUtil.GB2bytes(
                                            BigDecimal.valueOf(vodPackageState.getValidTrafficTemp()));
                                }
                            }
                            vodUnlimitedFlowPackage.setPermanentFlowAvailable(sumPermanentFlowAvailable);
                            vodUnlimitedFlowPackage.setTempFlowAvailable(sumTempFlowAvailable);
                        }
            
                    } else if (AccountTypeEnum.GROUP2.getCode().equals(accountType)) {
                        long sumPeriodTraffic = resourceAlterationRecordRepository.sumAlteration(
                                businessPackageSpecification.getValidPeriodStartDate(),
                                businessPackageSpecification.getValidPeriodEndDate(), ResourceSourceConst.PERIOD,
                                Lists.newArrayList(customerId), ResourceCodeConst.traffic.name(),
                                ResourceAlterTypeConst.consume.name());
                        vodUnlimitedFlowPackage.setPeriodFlowUsed(Math.negateExact(sumPeriodTraffic));
            
                        CustomerItemInfoGetInputVO getInputVO = new CustomerItemInfoGetInputVO();
                        getInputVO.setResourceCode(ResourceCodeConst.traffic.name());
                        getInputVO.setCustomerId(customerId);
                        ResAvailableStateDetailVO resAvailVO =
                                customerResourceService.getCustomerCurrentAvailableDetail(
                                getInputVO);
                        vodUnlimitedFlowPackage.setPermanentFlowAvailable(resAvailVO.getPermanentAvailable());
                        vodUnlimitedFlowPackage.setTempFlowAvailable(resAvailVO.getTempAvailable());
                    } else if (AccountTypeEnum.NORMAL.getCode().equals(accountType)) {
                        long sumPeriodTraffic = resourceAlterationRecordRepository.sumAlteration(
                                businessPackageSpecification.getValidPeriodStartDate(),
                                businessPackageSpecification.getValidPeriodEndDate(), ResourceSourceConst.PERIOD,
                                Lists.newArrayList(customerId), ResourceCodeConst.traffic.name(),
                                ResourceAlterTypeConst.consume.name());
                        vodUnlimitedFlowPackage.setPeriodFlowUsed(Math.negateExact(sumPeriodTraffic));
            
                        GetCustomerStateInputVO inputVO = new GetCustomerStateInputVO();
                        inputVO.setUnionId(customerId);
                        CustomerStateVO customerState = customerService.getCustomerState(inputVO);
                        if (Objects.nonNull(customerState) && Objects.nonNull(customerState.getVodPackageState())) {
                            VodPackageState vodPackageState = customerState.getVodPackageState();
                            log.info("customerId == {},vodPackageState == {}", vodPackageState);
                            vodUnlimitedFlowPackage.setPermanentFlowAvailable(UnitConverterUtil.GB2bytes(
                                    BigDecimal.valueOf(vodPackageState.getValidTrafficPackages())));
                            vodUnlimitedFlowPackage.setTempFlowAvailable(UnitConverterUtil.GB2bytes(
                                    BigDecimal.valueOf(vodPackageState.getValidTrafficTemp())));
                        }
                    }
                    String packageName = businessPackageSpecification.getPackageName();
                    long periodFlowUsed = Objects.nonNull(vodUnlimitedFlowPackage.getPeriodFlowUsed()) ?
                            vodUnlimitedFlowPackage.getPeriodFlowUsed() : 0L;
                    //区分无限版-月流量（旗舰型）和无限版优享型
                    String vodPackageType = businessPackageSpecification.getVodPackageType();
                    //为了兼容历史数据有的没有id的情况
                    if (VodUnlimitedPackageTypeEnum.ULTIMATE.getCode().equals(vodPackageType) ||
                            "无限版".equals(packageName) || "无限版-月流量".equals(packageName)) {
                        vodUnlimitedFlowPackage.setVodPackageId("104");
                        vodUnlimitedFlowPackage.setVodPackageName("无限版旗舰型");
                        vodUnlimitedFlowPackage.setThreshold((int) ultimate);
                        vodUnlimitedFlowPackage.setThresholdUnit("T");
                        vodUnlimitedFlowPackage.setIsOverThreshold(
                                periodFlowUsed >= ultimate * 1024 * 1024 * 1024 * 1024 ? 1 : 0);
            
                    }
                    if (VodUnlimitedPackageTypeEnum.PREMIUM.getCode().equals(vodPackageType) ||
                            "无限版优享型".equals(packageName)) {
                        vodUnlimitedFlowPackage.setVodPackageId("115");
                        vodUnlimitedFlowPackage.setVodPackageName("无限版优享型");
                        vodUnlimitedFlowPackage.setThreshold((int) premium);
                        vodUnlimitedFlowPackage.setThresholdUnit("T");
                        vodUnlimitedFlowPackage.setIsOverThreshold(
                                periodFlowUsed >= premium * 1024 * 1024 * 1024 * 1024 ? 1 : 0);
                    }
        
                    List<VodUnlimitedFlowPackage> flowPackageList = vodUnlimitedFlowPackageDao.findByUnionId(
                            customerId);
                    //更新
                    if (!CollectionUtils.isEmpty(flowPackageList)) {
                        VodUnlimitedFlowPackage vfp = flowPackageList.get(0);
                        vodUnlimitedFlowPackage.setId(vfp.getId());
                        vodUnlimitedFlowPackage.setIsMarkLimit(vfp.getIsMarkLimit());
                        vodUnlimitedFlowPackage.setCreateTime(vfp.getCreateTime());
                        vodUnlimitedFlowPackage.setUpdateTime(new Date());
                    }
                    //创建
                    else {
                        vodUnlimitedFlowPackage.setIsMarkLimit(0);
                        vodUnlimitedFlowPackage.setCreateTime(new Date());
                    }
        
                    vodUnlimitedFlowPackageDao.updateOrSave(vodUnlimitedFlowPackage);
                } catch (Exception e) {
                    log.error("preStatisticVodUnlimitedPackage error", e);
                }
            });
    
    
        }
    }
}
