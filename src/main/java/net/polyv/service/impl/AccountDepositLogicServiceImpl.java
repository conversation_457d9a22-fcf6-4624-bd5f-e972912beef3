package net.polyv.service.impl;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.deposit.IsDonateConst;
import net.polyv.constant.finance.ContractOperationCodeEnum;
import net.polyv.dao.primary.*;
import net.polyv.dao.primary.custom.CustomerConfigDao;
import net.polyv.model.data.deposit.DepositPeriodDO;
import net.polyv.model.entity.primary.AccountRechargeRecord;
import net.polyv.model.entity.primary.AccountRechargeRecordResourcePoint;
import net.polyv.model.entity.primary.CustomerConfig;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.modules.pcs.api.vo.SettlementConfigVO;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.model.finance.AddContractResultVO;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.service.AccountDepositLogicService;
import net.polyv.service.GlobalConfigService;
import net.polyv.service.bill.UnPayBillService;
import net.polyv.service.finance.FinanceContractService;
import net.polyv.service.settlement.SettlementConfigService;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.account.DepositInputVO;
import net.polyv.web.model.finance.input.AddAmountContractDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

import static net.polyv.constant.GlobalConfigConst.RECHARGE_PERIOD_DAYS;

/**
 * 账户余额相关服务
 * <AUTHOR>
 * @since 08/05/2020
 */
@Slf4j
@Service
public class AccountDepositLogicServiceImpl implements AccountDepositLogicService {
    
    /**
     * 默认的充值周期天数，当没找到配置时候，会取该值
     */
    private static final int DEFAULT_RECHARGE_PERIOD_DAYS = 365;
    @Resource
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    
    @Autowired
    private UnPayBillService unPayBillService;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private FinanceContractService financeContractService;
    @Resource
    private AccountRechargeRecordRepository accountRechargeRecordRepository;
    @Resource
    private CustomerConfigRepository customerConfigRepository;
    @Resource
    private CustomerConfigDao customerConfigDao;
    @Resource
    private CreditAlterationRecordRepository creditAlterationRecordRepository;
    @Resource
    private CustomerBillingDailyRepository customerBillingDailyRepository;
    @Resource
    private SettlementConfigService settlementConfigService ;
    @Resource
    private AccountRechargeRecordResourcePointRepository resourcePointRepository ;
    
    @Override
    public DepositPeriodDO calcCurrentPeriod(DepositInputVO inputVO) {
        
        // 查询创建日期最新的一条充值记录
        AccountRechargeRecord rechargeRecord =
                accountRechargeRecordRepository.findFirstByCustomerIdOrderByCreateTimeDesc(
                inputVO.getCustomerId());
        
        // 初始化，之前没有充值记录，默认取今天为周期开始日期
        if (rechargeRecord == null) {
            return this.getNewPeriod();
        }
        
        // 当前日期在充值周期内，直接返回当前充值日志的周期区间
        Date current = DateUtil.date();
        if (DateUtil.compare(rechargeRecord.getPeriodStartTime(), current) <= 0 &&
                DateUtil.compare(rechargeRecord.getPeriodEndTime(), current) > 0) {
            return this.copyPeriod(rechargeRecord);
        }
        
        // 上一个充值周期已经过去，本次充值重新触发充值周期
        return this.getNewPeriod();
    }
    
    /**
     * 执行储值
     * @param inputVO 储值入参
     */
    public void doDeposit(DepositInputVO inputVO) {
        // 本单无储值
        if (!inputVO.getIsForce() && (inputVO.getDepositAmount() == null || inputVO.getDepositAmount() <= 0L)) {
            log.info("本单无储值金额");
            return;
        }
        
        // 基础信息
        AccountRechargeRecord rechargeRecord = new AccountRechargeRecord();
        rechargeRecord.setCustomerId(inputVO.getCustomerId());
        rechargeRecord.setContractId(inputVO.getContractId());
        rechargeRecord.setDeposit(inputVO.getDepositAmount());
        rechargeRecord.setAvailable(inputVO.getDepositAmount());
        rechargeRecord.setFreezeAmount(0L);
        rechargeRecord.setIsDonate(IsDonateConst.NO);
        rechargeRecord.setExpireType(inputVO.getAmountExpireType());
        if (Objects.nonNull(inputVO.getAmountExpireDate())) { // 如果设置了过期时间
            rechargeRecord.setExpireDate(inputVO.getAmountExpireDate());
        } else {
            rechargeRecord.setExpireDate(
                    getExpireDate(inputVO.getAmountValidYears() == null ? 2 : inputVO.getAmountValidYears()));
        }
        // 储值周期
        DepositPeriodDO depositPeriodDO = this.calcCurrentPeriod(inputVO);
        rechargeRecord.setPeriodStartTime(depositPeriodDO.getPeriodStartTime());
        rechargeRecord.setPeriodEndTime(depositPeriodDO.getPeriodEndTime());
        rechargeRecord.setCreateUserId(inputVO.getCreateUserId());
        rechargeRecord.setCreateTime(new Date());
        rechargeRecord.setUpdateTime(new Date());
        rechargeRecord.setOperaId(inputVO.getOperaId());
        
        log.info("保存充值记录, rechargeRecord={}", rechargeRecord);
        
        // 持久化
        this.accountRechargeRecordRepository.save(rechargeRecord);
    }

    /**
     * 执行资源点储值
     * @param inputVO 储值入参
     */
    public void doDepositResourcePoint(DepositInputVO inputVO) {
        // 本单无储值
        if (!inputVO.getIsForce() && (inputVO.getDepositAmount() == null || inputVO.getDepositAmount() <= 0L)) {
            log.info("本单无储值资源点");
            return;
        }

        // 基础信息
        AccountRechargeRecordResourcePoint rechargeRecord = new AccountRechargeRecordResourcePoint();
        rechargeRecord.setCustomerId(inputVO.getCustomerId());
        rechargeRecord.setContractId(inputVO.getContractId());
        rechargeRecord.setDeposit(inputVO.getDepositAmount());
        rechargeRecord.setAvailable(inputVO.getDepositAmount());
        rechargeRecord.setFreezeAmount(0L);
        rechargeRecord.setIsDonate(IsDonateConst.NO);
        rechargeRecord.setExpireType(inputVO.getAmountExpireType());
        if (Objects.nonNull(inputVO.getAmountExpireDate())) { // 如果设置了过期时间
            rechargeRecord.setExpireDate(inputVO.getAmountExpireDate());
        } else {
            rechargeRecord.setExpireDate(
                    getExpireDate(inputVO.getAmountValidYears() == null ? 2 : inputVO.getAmountValidYears()));
        }
        // 储值周期
        DepositPeriodDO depositPeriodDO = this.calcCurrentPeriod(inputVO);
        rechargeRecord.setPeriodStartTime(depositPeriodDO.getPeriodStartTime());
        rechargeRecord.setPeriodEndTime(depositPeriodDO.getPeriodEndTime());
        rechargeRecord.setCreateUserId(inputVO.getCreateUserId());
        rechargeRecord.setCreateTime(new Date());
        rechargeRecord.setUpdateTime(new Date());
        rechargeRecord.setOperaId(inputVO.getOperaId());

        log.info("保存资源点充值记录, rechargeRecord={}", rechargeRecord);

        // 持久化
        this.resourcePointRepository.save(rechargeRecord);
    }
    
    @Override
    public boolean hasDepositOrCredit(String customerId) {
        boolean hasRechargeGreaterZero = accountRechargeRecordRepository.existsByCustomerIdAndDepositGreaterThan(
                customerId, 0L);
        boolean hasCredit = customerConfigRepository.existsByCustomerIdAndCreditGreaterThan(customerId, 0L);
        return hasRechargeGreaterZero || hasCredit;
    }

    @Override
    public boolean hasResourcePoint(String customerId) {
        SettlementConfigVO vo = settlementConfigService.querySettlementConfig(customerId) ;
        if(vo == null) {
            return false ;
        }

        if(!SwitchEnum.isY(vo.getResourcePointSettlement())){
            return false ;
        }

        boolean hasRechargeGreaterZero = resourcePointRepository.existsByCustomerIdAndDepositGreaterThan(
                customerId, 0L);

        return hasRechargeGreaterZero  ;
    }
    
    @Override
    public AddContractResultVO doAddContract(DepositInputVO inputVO) {
        long payOverAmount = 0;
        if (!inputVO.getIsZeroContract()) {
            // 充值金额<=0，无需调财务系统创建合同
            if (inputVO.getDepositAmount() <= 0) {
                return new AddContractResultVO();
            }
            // 统计待支付情况
            payOverAmount = unPayBillService.calcUnPayAmount(inputVO.getCustomerId());
            if (inputVO.getDepositAmount() < payOverAmount) {
                payOverAmount = 0;
            }
        }
        
        SalesOpportunities opportunities = salesOpportunitiesRepository.findById(inputVO.getSalesOpportunitiesId())
                .orElse(null);
        assert opportunities != null;
        //点播cc_user表主键id
        Integer autoId;
        
        //兼容集团账号主账号充值逻辑，autoId没有
        if (AccountTypeEnum.GROUP2.getCode().equals(inputVO.getAccountType())) {
            autoId = 0;
        } else {
            UserMsgVO userMsgVO = UserClient.getUserByUnionId(opportunities.getCustomerId());
            Assert.notNull(userMsgVO, "查询顾客信息失败");
            autoId = userMsgVO.getAutoId();
        }
        // 前端没有传saleUserId的时候，标识是超管账号，这时候销售id则从销售机会里边取得
/*        String encodedOwnerUserId = StringUtils.hasLength(inputVO.getSaleUserId())
                ? inputVO.getSaleUserId() : opportunities.getSaleUserId();*/
        AddAmountContractDTO addAc = AddAmountContractDTO.builder()
                .soId(opportunities.getSoId())
                .unionId(opportunities.getCustomerId())
                .accountType(inputVO.getAccountType())
                .autoId(autoId)
                .operationCode(ContractOperationCodeEnum.DEPOSIT_AMOUNT.getCode())
                .contractAmount(inputVO.getIsZeroContract() ? 0L : opportunities.getAmountGained() - payOverAmount)
                .payOverAmount(payOverAmount)
                .donateAmount(inputVO.getDonate())
                .payBackStatus(inputVO.getPayBackStatus())
                .contractId(inputVO.getDepositAmountContractId())
                .build();
        CommonResult<AddContractResultVO> addAmountContractResult = this.financeContractService.addAmountContract(
                addAc);
        if (CommonResult.isNotOk(addAmountContractResult)) {
            log.error(String.format("addAmountContract fail,param{%s},result{%s}", JsonUtil.beanToString(addAc),
                    addAmountContractResult.getMsg()));
            return new AddContractResultVO();
        }
        return addAmountContractResult.getData();
    }

    @Override
    public AddContractResultVO doAddContractResourcePoint(DepositInputVO inputVO) {
        long payOverAmount = 0;
        if (!inputVO.getIsZeroContract()) {
            // 充值金额<=0，无需调财务系统创建合同
            if (inputVO.getDepositAmount() <= 0) {
                return new AddContractResultVO();
            }
        }

        SalesOpportunities opportunities = salesOpportunitiesRepository.findById(inputVO.getSalesOpportunitiesId())
                .orElse(null);
        assert opportunities != null;
        //点播cc_user表主键id
        Integer autoId;

        //兼容集团账号主账号充值逻辑，autoId没有
        UserMsgVO userMsgVO = UserClient.getUserByUnionId(opportunities.getCustomerId());
        Assert.notNull(userMsgVO, "查询顾客信息失败");
        autoId = userMsgVO.getAutoId();

        // 前端没有传saleUserId的时候，标识是超管账号，这时候销售id则从销售机会里边取得
/*        String encodedOwnerUserId = StringUtils.hasLength(inputVO.getSaleUserId())
                ? inputVO.getSaleUserId() : opportunities.getSaleUserId();*/
        AddAmountContractDTO addAc = AddAmountContractDTO.builder()
                .soId(opportunities.getSoId())
                .unionId(opportunities.getCustomerId())
                .accountType(inputVO.getAccountType())
                .autoId(autoId)
                .operationCode(ContractOperationCodeEnum.RESOURCE_POINT.getCode())
                .contractAmount(inputVO.getIsZeroContract() ? 0L : opportunities.getAmountGained() - payOverAmount)
                .payOverAmount(payOverAmount)
                .donateAmount(inputVO.getDonate())
                .payBackStatus(inputVO.getPayBackStatus())
                .contractId(inputVO.getDepositAmountContractId())
                .build();
        CommonResult<AddContractResultVO> addAmountContractResult = this.financeContractService.addAmountContractResourcePoint(
                addAc);
        if (CommonResult.isNotOk(addAmountContractResult)) {
            log.error(String.format("addAmountContract fail,param{%s},result{%s}", JsonUtil.beanToString(addAc),
                    addAmountContractResult.getMsg()));
            return new AddContractResultVO();
        }
        return addAmountContractResult.getData();
    }
    
    @Override
    public long calcCustomerValidCredit(CustomerInfoGetInputVO inputVO) {
        long totalCredit = 0;
        CustomerConfig config = customerConfigDao.getOrCreateCustomerConfig(inputVO.getCustomerId());
        if (config != null) {
            totalCredit = config.getCredit();
        }
        long used = calcCustomerUsedCredit(inputVO);
        long validCredit = totalCredit - used;
        return validCredit > 0 ? validCredit : 0;
    }
    
    public long calcCustomerUsedCredit(CustomerInfoGetInputVO inputVO) {
        // 冻结和解冻
        long freeAndUnFree = creditAlterationRecordRepository.sumCurrentUseCreditAmount(inputVO.getCustomerId());
        // 待支付
        long unpaid = customerBillingDailyRepository.sumCustomerTotalUnPay(inputVO.getCustomerId());
        return freeAndUnFree + unpaid;
    }
    
    /**
     * 从充值记录中复制周期起止时间
     * @param accountRechargeRecord 充值记录
     * @return 周期起止时间
     */
    private DepositPeriodDO copyPeriod(AccountRechargeRecord accountRechargeRecord) {
        DepositPeriodDO depositPeriodDO = new DepositPeriodDO();
        depositPeriodDO.setPeriodStartTime(accountRechargeRecord.getPeriodStartTime());
        depositPeriodDO.setPeriodEndTime(accountRechargeRecord.getPeriodEndTime());
        return depositPeriodDO;
    }
    
    
    /**
     * 根据有效年数,算出过期日期
     * POP-1851新增了金额有效期的设置
     * @param amountValidYears 有效年数
     * @return
     */
    private Date getExpireDate(int amountValidYears) {
        Date currentDay = DateUtil.beginOfDay(DateUtil.yesterday());    // 今天生效要扣除一天
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentDay);
        cal.add(Calendar.YEAR, amountValidYears);//增加一个自然年
        return cal.getTime();
    }
    
    /**
     * 获取新的充值周期
     * @return 新的充值周期（从今日0点开始）
     */
    private DepositPeriodDO getNewPeriod() {
        DepositPeriodDO depositPeriodDO = new DepositPeriodDO();
        depositPeriodDO.setPeriodStartTime(DateUtil.beginOfDay(DateUtil.date()));
        
        // 获取当前周期天数配置
        int periodDays = this.getCurrentPeriodDays();
        depositPeriodDO.setPeriodEndTime(DateUtil.offsetDay(depositPeriodDO.getPeriodStartTime(), periodDays));
        
        return depositPeriodDO;
    }
    
    /**
     * 获取当前充值周期天数
     * 如果获取配置失败，该方法会返回默认的充值周期天数 {@link #DEFAULT_RECHARGE_PERIOD_DAYS}
     * @return 当前充值周期天数
     */
    private int getCurrentPeriodDays() {
        
        try {
            return this.globalConfigService.getByKey(RECHARGE_PERIOD_DAYS, Integer.class);
        } catch (Exception e) {
            log.error("获取当前充值周期天数失败，返回默认值:" + DEFAULT_RECHARGE_PERIOD_DAYS, e);
            return DEFAULT_RECHARGE_PERIOD_DAYS;
        }
    }
    
    
}

