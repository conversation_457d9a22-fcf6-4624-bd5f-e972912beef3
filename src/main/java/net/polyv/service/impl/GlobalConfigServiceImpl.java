package net.polyv.service.impl;

import net.polyv.dao.primary.custom.GlobalConfigDao;
import net.polyv.exception.config.ConfigKeyNotFoundException;
import net.polyv.service.GlobalConfigService;
import net.polyv.util.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 全部配置相关服务
 * <AUTHOR>
 * @since 08/05/2020
 */
@Slf4j
@Service
public class GlobalConfigServiceImpl implements GlobalConfigService {

    /**
     * 缓存key template
     */
    private static final String GLOBAL_CONFIG_CACHE_KEY = "clearing_system:global_config:%s";

    /**
     * 默认缓存过期时间
     */
    private static final int DEFAULT_CACHE_KEY_TTL = 600;

    @Resource(name = "redisTemplate")
    ValueOperations<String, String> valueOperations;

    @Autowired
    GlobalConfigDao globalConfigDao;

    @Override
    public <T> T getByKey(@NotNull String key, Class<T> clazz) throws ConfigKeyNotFoundException, ClassCastException {

        System.out.println("进来");
        // 读取缓存
        Object value = this.getByCache(key);
        if (value != null) {
            return BeanUtil.parse(value, clazz);
        }

        // 穿透数据库
        System.out.println("穿透");
        value = this.getByPersistentStorage(key);
        if (value != null) {
            return BeanUtil.parse(value, clazz);
        }

        // key找不到
        throw new ConfigKeyNotFoundException();
    }

    /**
     * 从持久化介质中读取配置
     * 注意当从数据库查询到该值的时候，此处会触发缓存机制
     * 实际使用过程中需要尽量降低调用该方法的频率
     * @param key 键名
     * @return 配置
     * @throws ConfigKeyNotFoundException 根据键名找不到键值时候抛出该异常
     */
    private Object getByPersistentStorage(String key) throws ConfigKeyNotFoundException{

        log.info("load all global config from persistent storage, key={}", key);

        // 查询所有全局配置
        Map<String, Object> data = globalConfigDao.listAll();

        if (!data.containsKey(key)) {
            throw new ConfigKeyNotFoundException();
        }

        // 缓存
        this.cache(data);

        return data.get(key);
    }

    /**
     * 从缓存中读取配置
     * @param key 键名
     * @return 配置值
     */
    private Object getByCache(String key) {
        return valueOperations.get(this.getCacheKey(key));
    }

    /**
     * 缓存到缓存中，并设置默认过期时间为 {@link GlobalConfigServiceImpl#DEFAULT_CACHE_KEY_TTL}
     * 由于此处的设计是幂等的，不需要考虑同步的问题
     * @param data 需要缓存的数据
     */
    private void cache(Map<String, Object> data) {
        data.forEach((key, value) -> valueOperations.set(this.getCacheKey(key), String.valueOf(value),
                DEFAULT_CACHE_KEY_TTL, TimeUnit.SECONDS));
    }

    /**
     * 返回缓存的key
     * @param key 配置的key
     * @return 缓存key
     */
    private String getCacheKey(String key) {
        return String.format(GLOBAL_CONFIG_CACHE_KEY, key);
    }
}
