package net.polyv.service.impl.marketing;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.StringUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.cache.CacheConst;
import net.polyv.exception.ClearingSystemException;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.third.req.marketing.QingniaoAuthRequest;
import net.polyv.modules.third.req.marketing.QingniaoClueListRequest;
import net.polyv.modules.third.req.marketing.TencentClueListRequest;
import net.polyv.modules.third.service.crm.FXiaoKeService;
import net.polyv.modules.third.service.marketing.QingniaoClueService;
import net.polyv.modules.third.service.marketing.TencentClueService;
import net.polyv.modules.third.vo.fxiaoke.FXiaoKeBaseResponse;
import net.polyv.modules.third.vo.marketing.QingniaoAuthResponse;
import net.polyv.modules.third.vo.marketing.QingniaoClueResponse;
import net.polyv.modules.third.vo.marketing.TencentClueListResponse;
import net.polyv.service.marketing.MarketingClueService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @description: 市场营销线索对接服务接口
 * @author: Neo
 * @date: 2022-04-29
 */
@Service
@Slf4j
public class MarketingClueServiceImpl implements MarketingClueService {


    @Autowired
    private DingWarnRobot dingWarnRobot;


    @Autowired
    private QingniaoClueService qingniaoClueService;

    @Autowired
    private TencentClueService tencentClueService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Resource(name = "redisTemplate")
    private ValueOperations<String, String> valueOperations;

    @Autowired
    private FXiaoKeService fXiaoKeService;


    /**
     * 1.如果AccessToken为空或者过期->使用refreshToken刷新；
     * 2.如果refreshToken为空或者过期->使用authCode获取AccessToken和refreshToken
     * 3.如果authCode为空，烦请广告主登录账号访问连接
     * https://ad.oceanengine.com/openapi/audit/oauth.html?app_id=1683572419687452&state=&redirect_uri=https://fxiaoketest.polyv.net/auth-callback/qing-niao
     * 在跳转url获取到authCode填入marketing_clue_sync_info表中
     * <p>
     * 是否过期由过期字段确定，业务流程中不再考虑token过期问题
     */
    private CommonResult<String> getQingniaoAccessToken() {
        String currentAccessToken = valueOperations.get(CacheConst.QING_NIAO_CLUE_TOKEN_KEY);
        if (StringUtils.isNotEmpty(currentAccessToken)) {
            return CommonResult.ok(currentAccessToken);
        }
        return CommonResult.fail("AccessToken为空");
    }


    @Override
    public CommonResult syncQingniaoClue2Fxk() throws ClearingSystemException {
        CommonResult<String> accessTokenResult = this.getQingniaoAccessToken();
        if (CommonResult.isNotOk(accessTokenResult)) {
            // 初始化token
            this.refreshQingniaoTokenSchedule();
            // 重新获取token
            accessTokenResult = getQingniaoAccessToken();
        }
        // 依然获取不到则返回
        if (CommonResult.isNotOk(accessTokenResult)) {
            return accessTokenResult;
        }
        String accessToken = accessTokenResult.getData();
        // 查询最大日期
        String syncMaxDate = valueOperations.get(CacheConst.QING_NIAO_CLUE_SYNC_MAX_DATE);
        if (StringUtils.isEmpty(syncMaxDate)) {
            syncMaxDate = DateUtils.format(DateUtil.getCurrentDay(), DateFormatUtil.FORMAT_DATE_NORMAL);
            valueOperations.set(CacheConst.QING_NIAO_CLUE_SYNC_MAX_DATE, syncMaxDate);
        }
        QingniaoClueListRequest req = new QingniaoClueListRequest();
        req.setAdvertiser_ids(Lists.newArrayList("61073352893"));
        req.setStart_time(syncMaxDate);
        req.setEnd_time(DateUtils.format(DateUtil.getCurrentDay(), DateFormatUtil.FORMAT_DATE_NORMAL));
        req.setPage_size("50");
        int currentPage = 1;
        int totalPage = 0;
        int maxSafeLoopCount = 1000;
        do {
            req.setPage(String.valueOf(currentPage++));
            ResponseVO<QingniaoClueResponse> clubListResponse = qingniaoClueService.getClueList(req, accessToken, 0);
            if (clubListResponse.isSuccess() && Objects.nonNull(clubListResponse.getData().getData())) {
                QingniaoClueResponse.CLueData qcrCd = clubListResponse.getData().getData();
                if (Objects.isNull(qcrCd.getPage_info()) || CollectionUtils.isEmpty(qcrCd.getList())) break;
                totalPage = qcrCd.getPage_info().getTotal_page();
                List<QingniaoClueResponse.ClueDetail> qcrCdList = qcrCd.getList();
                for (QingniaoClueResponse.ClueDetail qcCd : qcrCdList) {
                    CommonResult currentAddClueResult = createQingniaoClue2Fxk(qcCd);
                }
            }
        } while (currentPage < totalPage && maxSafeLoopCount-- > 0);
        return CommonResult.ok();
    }

    @Override
    public CommonResult syncTencentClue2Fxk() throws ClearingSystemException {
        // 查询最大日期
        String syncMaxDate = valueOperations.get(CacheConst.TENCENT_CLUE_SYNC_MAX_DATE);
        if (StringUtils.isEmpty(syncMaxDate)) {
            syncMaxDate = DateUtils.format(DateUtil.getCurrentDay(), DateFormatUtil.FORMAT_DATE_NORMAL);
            valueOperations.set(CacheConst.TENCENT_CLUE_SYNC_MAX_DATE, syncMaxDate);
        }
        TencentClueListRequest req = new TencentClueListRequest();
        req.setStart_time(syncMaxDate);
        req.setEnd_time(DateUtils.format(DateUtil.getCurrentDay(), DateFormatUtil.FORMAT_DATE_NORMAL));
        req.setPage_size(100);
        int wechatClueCurrentPage = 1;
        int wechatClueTotalPage = 0;
        int wechatMaxSafeLoopCount = 1000;
        do {
            req.setPage(wechatClueCurrentPage++);
            ResponseVO<TencentClueListResponse> wechatClueResponse = tencentClueService.getClueList(req, TencentClueService.WECHAT_PLATFORM);
            if (wechatClueResponse.isSuccess() && Objects.nonNull(wechatClueResponse.getData().getData())) {
                TencentClueListResponse.ClueData tcrCd = wechatClueResponse.getData().getData();
                if (Objects.isNull(tcrCd.getPage_info()) || CollectionUtils.isEmpty(tcrCd.getList())) break;
                wechatClueTotalPage = tcrCd.getPage_info().getTotal_page();
                List<TencentClueListResponse.LeadsInfo> tcrCdList = tcrCd.getList();
                for (TencentClueListResponse.LeadsInfo tcli : tcrCdList) {
                    CommonResult currentAddClueResult = createTencentClue2Fxk(tcli, TencentClueService.WECHAT_PLATFORM);
                }
            }
        } while (wechatClueCurrentPage < wechatClueTotalPage && wechatMaxSafeLoopCount-- > 0);
        int qqClueCurrentPage = 1;
        int qqClueTotalPage = 0;
        int qqMaxSafeLoopCount = 1000;
        do {
            req.setPage(qqClueCurrentPage++);
            ResponseVO<TencentClueListResponse> wechatClueResponse = tencentClueService.getClueList(req, TencentClueService.QQ_PLATFORM);
            if (wechatClueResponse.isSuccess() && Objects.nonNull(wechatClueResponse.getData().getData())) {
                TencentClueListResponse.ClueData tcrCd = wechatClueResponse.getData().getData();
                if (Objects.isNull(tcrCd.getPage_info()) || CollectionUtils.isEmpty(tcrCd.getList())) break;
                qqClueTotalPage = tcrCd.getPage_info().getTotal_page();
                List<TencentClueListResponse.LeadsInfo> tcrCdList = tcrCd.getList();
                for (TencentClueListResponse.LeadsInfo tcli : tcrCdList) {
                    CommonResult currentAddClueResult = createTencentClue2Fxk(tcli, TencentClueService.QQ_PLATFORM);
                }
            }
        } while (qqClueCurrentPage < qqClueTotalPage && qqMaxSafeLoopCount-- > 0);
        return CommonResult.ok();
    }

    // 往纷享销客创建腾讯线索
    /*
        纷享销客对应字段：
        公司名称: company
        姓名: name
        手机号码: tel
        部门/职位:
        线索来源-一级来源: source
        线索来源-二级来源: field_w2b9S__c
        线索来源-三级来源: field_8H1ot__c
        线索来源-四级来源: field_V0ipt__c
     */
    private CommonResult createTencentClue2Fxk(TencentClueListResponse.LeadsInfo tcli, String platform) {
        if (Objects.isNull(tcli)) {
            return CommonResult.fail("param is null");
        }
        // 公司名称
        String company = "";
        String scenes = "";
        String bundle = tcli.getBundle();
        if (StringUtils.isNotEmpty(bundle)) {
            JSONObject bundleJo = JSON.parseObject(bundle);
            if (Objects.nonNull(bundleJo)) {
                company = Objects.nonNull(bundleJo.get("公司名称")) ? String.valueOf(bundleJo.get("公司名称")) : "";
                scenes = Objects.nonNull(bundleJo.get("直播场景")) ? String.valueOf(bundleJo.get("直播场景")) : "";
            }
        }
        // 姓名
        String leads_name = tcli.getLeads_name();
        // 手机号码
        String leads_tel = tcli.getLeads_tel();
        Map<String, Object> object_data_detail = new HashMap<>();
        object_data_detail.put("company", company);
        object_data_detail.put("name", leads_name);
        object_data_detail.put("tel", leads_tel);
        object_data_detail.put("source", 0); // 公司资源
        object_data_detail.put("field_w2b9S__c", 1); // 市场获取-付费推广
        object_data_detail.put("field_8H1ot__c", 2); // 表单
        if (platform.equals(TencentClueService.QQ_PLATFORM)) {
            object_data_detail.put("field_V0ipt__c", "Lmb7Od7Gb"); // 广点通
        } else if (platform.equals(TencentClueService.WECHAT_PLATFORM)) {
            object_data_detail.put("field_V0ipt__c", 15); // 朋友圈广告
        }
        object_data_detail.put("leads_pool_id", "62527732801bc10001685ec1"); // 线索池（无负责人）
        object_data_detail.put("field_1ts56__c", "system");
        object_data_detail.put("dataObjectApiName", "LeadsObj");
        if (StringUtils.isNotEmpty(scenes)) {
            object_data_detail.put("lead_note__c", scenes);
        }
        Map<String, Object> object_data_map = new HashMap<>();
        object_data_map.put("object_data", object_data_detail);
        Map<String, Object> data_map = new HashMap<>();
        data_map.put("data", object_data_map);
        ResponseVO<FXiaoKeBaseResponse> addClueResponse = this.fXiaoKeService.createClueCustomizeField(data_map);
        if (!addClueResponse.isSuccess()) {
            log.error("an error occur createTencentClue2Fxk: " + addClueResponse.getError().getDesc());
            return CommonResult.fail(addClueResponse.getError().getDesc());
        }

        // 直播场景不为空的情况下，需要添加跟进记录 2022-06-17 no need anymore
        /*if (StringUtils.isNotEmpty(scenes) && Objects.nonNull(addClueResponse.getData()) && StringUtils.isNotEmpty(addClueResponse.getData().getDataId())) {
            String dataId = addClueResponse.getData().getDataId();
            Map<String, Object> active_record_detail = new HashMap<>();
            active_record_detail.put("dataObjectApiName", "ActiveRecordObj");
            active_record_detail.put("active_record_content", scenes);
            active_record_detail.put("field_BrjyC__c", DateUtil.getCurrentDay().getTime());
            if (platform.equals(TencentClueService.QQ_PLATFORM)) {
                active_record_detail.put("active_record_type", "ef39bdee3e764d09a1ceadd376f6448e");
            } else if (platform.equals(TencentClueService.WECHAT_PLATFORM)) {
                active_record_detail.put("active_record_type", "52806e86ae3b4e1daeeda6583022b8b4");
            }
            active_record_detail.put("related_object", new HashMap<String, Object>() {{
                put("LeadsObj", Lists.newArrayList(dataId));
            }});
            object_data_map.put("object_data", active_record_detail);
            data_map.put("data", object_data_map);
            ResponseVO<FXiaoKeBaseResponse> addActiveResponse = this.fXiaoKeService.createClueCustomizeField(data_map);
            if (!addActiveResponse.isSuccess()) {
                log.error("an error occur addActiveResponse: " + addActiveResponse.getError().getDesc());
                return CommonResult.fail(addActiveResponse.getError().getDesc());
            }
        }*/
        return CommonResult.ok();
    }


    private CommonResult createQingniaoClue2Fxk(QingniaoClueResponse.ClueDetail qcCd) {
        if (Objects.isNull(qcCd)) {
            return CommonResult.fail("param is null");
        }
        // 公司名称
        String company = "测试公司"; // todo
        // 姓名
        String leads_name = qcCd.getName();
        // 手机号码
        String leads_tel = qcCd.getTelephone();
        String app_name = qcCd.getApp_name();
        Map<String, Object> object_data_detail = new HashMap<>();
        object_data_detail.put("company", company);
        object_data_detail.put("name", leads_name);
        object_data_detail.put("tel", leads_tel);
        object_data_detail.put("source", 0); // 公司资源
        object_data_detail.put("field_w2b9S__c", 1); // 市场获取-付费推广
        object_data_detail.put("field_8H1ot__c", 2); // 表单
        if (StringUtils.isNotEmpty(app_name) && app_name.contains("抖音")) {
            object_data_detail.put("field_V0ipt__c", 13); // 抖音平台
        } else {
            object_data_detail.put("field_V0ipt__c", 16); // 头条
        }
        object_data_detail.put("leads_pool_id", "62527732801bc10001685ec1"); // 线索池（无负责人）
        object_data_detail.put("field_1ts56__c", "system");
        object_data_detail.put("dataObjectApiName", "LeadsObj");
        Map<String, Object> object_data_map = new HashMap<>();
        object_data_map.put("object_data", object_data_detail);
        Map<String, Object> data_map = new HashMap<>();
        data_map.put("data", object_data_map);
        ResponseVO<FXiaoKeBaseResponse> addClueResponse = this.fXiaoKeService.createClueCustomizeField(data_map);
        if (!addClueResponse.isSuccess()) {
            log.error("an error occur createQingniaoClue2Fxk: " + addClueResponse.getError().getDesc());
            return CommonResult.fail(addClueResponse.getError().getDesc());
        }
        return CommonResult.ok();
    }


    @Override
    public CommonResult refreshQingniaoTokenSchedule() throws ClearingSystemException {
        String errorMessage2DingDing = ""; // 钉钉告警信息
        String refreshToken = valueOperations.get(CacheConst.QING_NIAO_CLUE_FRESH_TOKEN_KEY);
        Date currentDate = new Date();
        QingniaoAuthRequest refreshRequest = new QingniaoAuthRequest();
        QingniaoAuthResponse.ResponseDetail authDetail = null;
        try {
            if (StringUtils.isNotEmpty(refreshToken)) {
                // 使用refresh刷新AccessToken
                refreshRequest.setRefresh_token(refreshToken);
                ResponseVO<QingniaoAuthResponse> refreshResult = this.qingniaoClueService.refreshToken(refreshRequest);
                if (refreshResult.isSuccess()) {
                    authDetail = refreshResult.getData().getData();
                    if (Objects.nonNull(authDetail.getAccess_token()) && Objects.nonNull(authDetail.getRefresh_token())) {
                        // 设置token缓存
                        valueOperations.set(CacheConst.QING_NIAO_CLUE_TOKEN_KEY, authDetail.getAccess_token(), authDetail.getExpires_in(), TimeUnit.SECONDS);
                        // 设置refresh-token缓存
                        valueOperations.set(CacheConst.QING_NIAO_CLUE_FRESH_TOKEN_KEY, authDetail.getRefresh_token(), authDetail.getRefresh_token_expires_in(), TimeUnit.SECONDS);
                    } else {
                        errorMessage2DingDing = refreshResult.getData().getMessage();
                    }
                } else {
                    errorMessage2DingDing = refreshResult.getError().getDesc();
                }
            } else {
                // refreshToken过期或者为空的情况，使用AuthCode查询
                String authCode = valueOperations.get(CacheConst.QING_NIAO_CLUE_AUTH_CODE_KEY);
                if (StringUtils.isEmpty(authCode)) {
                    errorMessage2DingDing = "青鸟线索获取授权失败。authCode为空。请广告主登录账号访问连接" +
                            "https://ad.oceanengine.com/openapi/audit/oauth.html?app_id=1683572419687452&state=&redirect_uri=https://fxiaoke.polyv.net/crm-callback/qingniao-auth-callback";
                    dingWarnRobot.sendWarnMsg("【青鸟线索授权定时任务错误】", errorMessage2DingDing);
                    return CommonResult.fail("青鸟线索获取授权失败。authCode为空。");
                }
                refreshRequest.setAuth_code(authCode);
                ResponseVO<QingniaoAuthResponse> accessTokenResponse = this.qingniaoClueService.getAccessToken(refreshRequest);
                if (accessTokenResponse.isSuccess()) {
                    authDetail = accessTokenResponse.getData().getData();
                    if (Objects.nonNull(authDetail.getAccess_token()) && Objects.nonNull(authDetail.getRefresh_token())) {
                        valueOperations.set(CacheConst.QING_NIAO_CLUE_TOKEN_KEY, authDetail.getAccess_token(), authDetail.getExpires_in(), TimeUnit.SECONDS);
                        valueOperations.set(CacheConst.QING_NIAO_CLUE_FRESH_TOKEN_KEY, authDetail.getRefresh_token(), authDetail.getRefresh_token_expires_in(), TimeUnit.SECONDS);
                    } else {
                        errorMessage2DingDing = accessTokenResponse.getData().getMessage();
                    }
                } else {
                    errorMessage2DingDing = accessTokenResponse.getError().getDesc();
                }
            }
        } catch (Exception ex) {
            log.error(String.format("refreshQingniaoTokenSchedule error:%s", ex.getMessage()), ex);
        }
        // 接入钉钉告警
        if (StringUtils.isNotEmpty(errorMessage2DingDing)) {
            dingWarnRobot.sendWarnMsg("【青鸟线索授权定时任务错误】", errorMessage2DingDing);
        }
        return CommonResult.ok();
    }
}
