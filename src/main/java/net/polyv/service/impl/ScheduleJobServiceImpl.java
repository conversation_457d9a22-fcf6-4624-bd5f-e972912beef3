package net.polyv.service.impl;

import com.alibaba.fastjson.JSON;
import net.polyv.constant.schdule.ScheduleStatusConst;
import net.polyv.dao.primary.CustomerEffectiveScheduleJobRepository;
import net.polyv.model.data.schedule.ScheduleJobSubmitDO;
import net.polyv.model.entity.primary.CustomerEffectiveScheduleJob;
import net.polyv.service.schedule.ScheduleJobService;
import net.polyv.util.UserInfoThreadLocal;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.Objects;

/**
 * 定时任务服务层
 * <AUTHOR>
 * @since 11/05/2020
 */
@Service
public class ScheduleJobServiceImpl implements ScheduleJobService {

    @Autowired
    private CustomerEffectiveScheduleJobRepository customerEffectiveScheduleJobRepository;

    @Override
    public int submitJob(@Validated ScheduleJobSubmitDO submitDO) {

        CustomerEffectiveScheduleJob job = new CustomerEffectiveScheduleJob();
        job.setCustomerId(submitDO.getCustomerId());
        job.setStatus(ScheduleStatusConst.WAIT);
        job.setEffectiveTime(submitDO.getEffectiveTime());
        job.setExt(JSON.toJSONString(submitDO.getExt()));
        job.setType(submitDO.getType().getType());
        if(Objects.nonNull(submitDO.getPriority())){
            job.setPriority(submitDO.getPriority());
        }
        else{
            job.setPriority(0);
        }
        if(StringUtils.isNotBlank(submitDO.getCreateUserId())){
            job.setCreateUserId(submitDO.getCreateUserId());
        }
        else{
            job.setCreateUserId(UserInfoThreadLocal.getUserId());
        }

        Date current = new Date();
        job.setCreateTime(current);
        job.setUpdateTime(current);

        customerEffectiveScheduleJobRepository.save(job);

        return job.getId();
    }
}
