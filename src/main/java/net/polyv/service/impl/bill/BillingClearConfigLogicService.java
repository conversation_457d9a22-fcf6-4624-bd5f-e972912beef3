package net.polyv.service.impl.bill;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.billing.BillingWayConfigCodeEnum;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.dao.primary.billingConfig.BillingItemExtRepository;
import net.polyv.dao.primary.billingConfig.BillingSettlementPeriodConfigRepository;
import net.polyv.dao.primary.billingConfig.BillingWayConfigRepository;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.model.entity.primary.billingconfig.BillingItemExt;
import net.polyv.model.entity.primary.billingconfig.BillingSettlementPeriodConfig;
import net.polyv.model.entity.primary.billingconfig.BillingWayConfig;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.service.bill.BillingService;
import net.polyv.service.impl.billingConfig.BillingItemExpireRestService;
import net.polyv.web.model.bill.BillClearingInputVO;

import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 配置化结算 逻辑处理器
 * <AUTHOR>
 * @date 2022/11/23 16:36
 */
@Component
@Slf4j
public class BillingClearConfigLogicService {
    @Resource
    private BillingSettlementPeriodConfigRepository billingSettlementPeriodConfigRepository;
    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;
    @Resource
    private BillingItemExpireRestService billingItemExpireRestService;
    @Resource
    private BillingItemExtRepository billingItemExtRepository;
    @Value("${pcs.config.dosageWayId}")
    private Integer dosageWayId;
    @Resource
    private BillingService billingService;
    @Resource
    private BillingClearingConfigService billingClearingConfigService;
    
    @Resource
    private BillingWayConfigRepository billingWayConfigRepository;
    
    
    /**
     * 自定义计费项需要结算的计费方式code
     */
    @Value("${pcs.config.clearWayCodes:by_times,by_usage}")
    private String clearWayCodes;
    
    /**
     * 自定义计费项需要过期清理资源的计费方式code
     */
    @Value("${pcs.config.expireCleanWayCodes:by_times}")
    private String expireCleanWayCodes;
    
    /**
     * 先根据计费项编码 读取计费项配置表根据配置的结算方式开关
     * 进行生成结算器
     * @param customerConfigs 客户配置表
     * <AUTHOR>
     * @date 2022/8/12
     */
    public void clearing(List<BillingCustomerConfig> customerConfigs, LocalDate clearDate) {
        Date date = net.polyv.util.DateUtil.localDate2Date(clearDate);
        log.info("开始执行出账-time={}", DateUtil.format(date, Constant.DATE_TIME_FORMAT_yyyy_MM_dd_HH_mm_ss));
        if (!CollectionUtils.isEmpty(customerConfigs)) {
            List<BillClearingInputVO> clearingInputs = buildInputVO(customerConfigs, date);
            for (BillClearingInputVO input : clearingInputs) {
                billingClearingConfigService.doClearingTransactional(input);
            }
        }
        //将该到达出账客户进行判断是否需要清理资源
        this.expireRestAndOutCleanBills(customerConfigs, date);
        // 根据周期计算下一次结算时间
        processCustomerBillTime(customerConfigs, clearDate);
    }
    
    public void processCustomerBillTime(List<BillingCustomerConfig> configs, LocalDate date) {
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }
        //——————变更客户：下一次出账日期、下一次获取消耗数据开始时间
        List<Integer> periodIds = configs.stream().map(BillingCustomerConfig::getBillingPeriodId).distinct().collect(Collectors.toList());
        Map<Integer, BillingSettlementPeriodConfig> periodConfigsMap = billingSettlementPeriodConfigRepository.findAllById(
                periodIds).stream().collect(Collectors.toMap(BillingSettlementPeriodConfig::getId, v -> v));
        //检查该计费项是否有效
        
        for (BillingCustomerConfig config : configs) {
            if (DateUtils.isSameDay(config.getExpireTime(), net.polyv.util.DateUtil.localDate2Date(date)) || config.getExpireTime().before(net.polyv.util.DateUtil.localDate2Date(date))) {
                config.setIsDeleted(PcsEnumConst.YES.getValue());
                config.setClearStartTime(net.polyv.util.DateUtil.getCurrentDay());
                continue;
                
            }
            LocalDate currentDate = LocalDate.of(date.getYear(), date.getMonth(), date.getDayOfMonth());
            currentDate =
                    PcsEnumConst.YES.getValue() == config.getIsClearByDay() ? currentDate.plusDays(1) : currentDate;
            BillingSettlementPeriodConfig periodConfig = periodConfigsMap.get(config.getBillingPeriodId());
            config.setBillingTime(getBillTime(currentDate, periodConfig));
            config.setClearStartTime(net.polyv.util.DateUtil.getCurrentDay());
        }
        billingCustomerConfigRepository.saveAll(configs);
    }
    
    private static Date getBillTime(LocalDate billItem, BillingSettlementPeriodConfig periodConfig) {
        LocalDate localDate = net.polyv.util.DateUtil.calculateDateByConfig(billItem, periodConfig.getUnit(),
                periodConfig.getBillingPeriod(), periodConfig.getAttribute());
        return net.polyv.util.DateUtil.localDate2Date(localDate);
    }
    
    /**
     * 根据计费项编码 读取计费项配置表根据配置的结算方式开关
     * 进行生成结算器
     * @param date 结算时间
     * @param itemCode 计费项编码
     */
    public void clearing(Date date, String itemCode) {
        // //获取计费方式id
        BillingItemExt billingItemExt = billingItemExtRepository.findByItemCode(itemCode);
        if (billingItemExt == null) {
            return;
        }
    
        // 获取计费方式code
        BillingWayConfig billingWayConfig = billingWayConfigRepository.findById(billingItemExt.getBillingWayId())
                .orElse(null);
        if (billingWayConfig == null) {
            return;
        }
    
        String billingCode = billingWayConfig.getCode();
    
        //注意，这里一定要保证先清理过期资源，再出账的顺序！！
        //以前其他资源也是先清理，后出账，为了保证结算主流程一样，这里也保持一致
        //按资源使用量（次数）过期需要清理
        if (needExpireClean(billingCode)) {
            cleanExpiredBills(date, itemCode);
        }
        //按资源使用量（次数）和按用量计费需要结算
        if (needClear(billingCode)) {
            performClearing(date, itemCode);
        }
    
    
    }
    
    private void performClearing(Date date, String itemCode) {
        BillClearingInputVO inputVO = prepareBillClearingInput(date, itemCode, false);
        List<BillClearingInputVO> scaleInputList = this.billingService.prepareClearingInput(inputVO);
        scaleInputList.forEach(billingClearingConfigService::doClearingTransactional);
        
        inputVO.setIsGroupAccount(SwitchEnum.Y.getCode());
        List<BillClearingInputVO> groupList = this.billingService.prepareClearingInput(inputVO);
        groupList.forEach(billingClearingConfigService::doClearingTransactional);
    }
    
    private void cleanExpiredBills(Date date, String itemCode) {
        List<BillingCustomerConfig> customerConfigs = billingCustomerConfigRepository.findNeedResetByItemCode(itemCode,date);
        expireRestAndOutCleanBills(customerConfigs, date);
    }
    
    private BillClearingInputVO prepareBillClearingInput(Date date, String itemCode, boolean isGroupAccount) {
        BillClearingInputVO inputVO = new BillClearingInputVO();
        inputVO.setItemCode(itemCode);
        inputVO.setConsumeStartDate(date);
        inputVO.setConsumeEndDate(date);
        inputVO.setIsGroupAccount(isGroupAccount ? SwitchEnum.Y.getCode() : SwitchEnum.N.getCode());
        return inputVO;
    }
    
    private void expireRestAndOutCleanBills(List<BillingCustomerConfig> customerConfigs, Date date) {
        if (CollectionUtils.isEmpty(customerConfigs)) {
            return;
        }
        //因为出账是在第二天,所以获取的到期时间取当前就行
        //当过期时间到达且是到期清零计费项且可用量大于0 则代表存在到期清零数据
        List<BillingCustomerConfig> oneCustomerConfigs = customerConfigs.stream()
                .filter(obj -> PcsEnumConst.isYes(obj.getIsExpireReset()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oneCustomerConfigs)) {
            return;
        }
        this.billingItemExpireRestService.doExpireRest(oneCustomerConfigs, date);
    }
    
    
    public List<BillClearingInputVO> buildInputVO(List<BillingCustomerConfig> customerConfigs, Date date) {
        List<BillClearingInputVO> inputs = new ArrayList<>();
        for (BillingCustomerConfig config : customerConfigs) {
            BillClearingInputVO billClearingInputVO = new BillClearingInputVO();
            billClearingInputVO.setCustomerId(config.getUnionId());
            billClearingInputVO.setConsumeStartDate(config.getClearStartTime());
            //获取到达账单时间点，昨天的数据
            billClearingInputVO.setConsumeEndDate(date);
            billClearingInputVO.setItemCode(config.getItemCode());
            inputs.add(billClearingInputVO);
        }
        return inputs;
    }
    
    /**
     * 判断是否需要结算
     * 按资源使用量（次数）和按用量计费需要结算
     * @param billWayCode 计费方式code
     * @return
     */
    public boolean needClear(String billWayCode) {
        return StringUtils.isNotBlank(clearWayCodes) &&
                Arrays.stream(clearWayCodes.split(",")).anyMatch(code -> code.equals(billWayCode));
    }
    
    /**
     * 判断资源过期是否需要清理
     * 按次数计费需要清理
     * @param billWayCode 计费方式code
     * @return
     */
    public boolean needExpireClean(String billWayCode) {
        return StringUtils.isNotBlank(expireCleanWayCodes) &&
                Arrays.stream(expireCleanWayCodes.split(",")).anyMatch(code -> code.equals(billWayCode));
    }
    
}
