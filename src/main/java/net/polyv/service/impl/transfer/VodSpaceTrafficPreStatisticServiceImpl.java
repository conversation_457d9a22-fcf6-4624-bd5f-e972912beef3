package net.polyv.service.impl.transfer;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import net.polyv.service.GrayTestService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.config.PcsConfig;
import net.polyv.constant.transfer.TransferServiceNameConstant;
import net.polyv.dao.primary.CustomerBaseRepository;
import net.polyv.model.dto.TransferDataDTO;
import net.polyv.model.entity.primary.resource.ResourceChangeHistory;
import net.polyv.modules.common.constant.Constant;
import net.polyv.rest.client.vod.resource.VodResourceService;
import net.polyv.rest.model.vod.VodPackageVo;
import net.polyv.service.CacheService;
import net.polyv.service.impl.resource.sync.SyncVodResourceService;
import net.polyv.service.resource.ResourceChangeHistoryOperationService;
import net.polyv.service.resource.ResourceChangeHistoryService;
import net.polyv.service.transfer.AbstractTransferDateService;
import net.polyv.service.transfer.TransferVodDataProcessorService;
import net.polyv.util.DingWarnRobot;
import net.polyv.web.model.transfer.BaseTransferRequest;

/**
 * 点播流量空间预统计服务
 */
@Slf4j
@Service
public class VodSpaceTrafficPreStatisticServiceImpl extends AbstractTransferDateService {
    
    @Resource
    private SyncVodResourceService syncVodResourceService;
    
    @Resource
    private ThreadPoolExecutor threadPoolExecutor;
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private VodResourceService vodResourceService;
    @Resource
    private ResourceChangeHistoryService resourceChangeHistoryService;
    @Resource
    private ResourceChangeHistoryOperationService resourceChangeHistoryOperationService;
    @Resource
    private CustomerBaseRepository customerBaseRepository;
    @Resource
    private PcsConfig pcsConfig;
    
    @Resource
    private CacheService cacheService;

    @Autowired
    private GrayTestService grayTestService ;
    
    @Override
    public String taskName() {
        return TransferServiceNameConstant.VOD_TRAFFIC_SPACE_PRE_STATISTIC.getDesc();
    }
    
    @Override
    public boolean isAbleInvoke(String taskId) {
        return taskId.equals(TransferServiceNameConstant.VOD_TRAFFIC_SPACE_PRE_STATISTIC.getName());
    }
    
    @Override
    protected boolean pageResult(BaseTransferRequest request) {
        log.info("VodSpaceTrafficPreStatisticServiceImpl pageResult request businessId:{}", request.getBusinessId());
        List<String> allUsers = this.customerBaseRepository.findUnionIdByLimit(request.getBusinessId(), 50);
        if (CollectionUtils.isEmpty(allUsers)) {
            return false;
        }
        request.setBusinessId(allUsers.stream().max(String::compareTo).orElse(""));
        //过滤灰度
        List<String> notGrayUsers = allUsers.stream()
                .filter(v -> !grayTestService.isGrayTestUser(v))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notGrayUsers)) {
            //该批数据全是灰度用户
            return true;
        }
        List<List<String>> partition = Lists.partition(notGrayUsers, 50);
        for (List<String> unionIds : partition) {
            if (pcsConfig.isGrayUserContinue()) {
                return false;
            }
            String join = StringUtils.join(unionIds, Constant.COMMA);
            doTransferData(join);
        }
        return true;
    }
    
    public void doTransferData(String userIds) {
        log.info("预统计点播可用量数据,正在处理用户ids:{}", userIds);
        List<VodPackageVo> vodSyncData = this.vodResourceService.getVodSyncData(userIds);
        if (CollectionUtils.isEmpty(vodSyncData)) {
            return;
        }
        cacheService.setVodSpaceTrafficAvailable(vodSyncData);
        log.info("预统计点播可用量数据,处理完毕用户ids:{}", userIds);
        
    }
    
    
    @Override
    public CommonResult<TransferDataDTO> transferData(TransferDataDTO req) throws Exception {
        req.setBusinessId("");
        this.scheduling(req);
        return CommonResult.ok(req);
    }
    
    
}
