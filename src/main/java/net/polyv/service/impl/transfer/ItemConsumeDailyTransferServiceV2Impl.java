package net.polyv.service.impl.transfer;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import net.polyv.constant.item.ItemMicGradientForDmpConst;
import net.polyv.constant.item.ItemScaleCodeConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.CustomerBaseRepository;
import net.polyv.dao.primary.ItemConsumeDailyRepository;
import net.polyv.dao.primary.groupAccount.GroupConcurrenceBillingUserStatRepository;
import net.polyv.model.entity.primary.CustomerBase;
import net.polyv.model.entity.primary.groupAccount.GroupConcurrenceBillingUserStat;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.rest.model.dmp.*;
import net.polyv.rest.model.dmp.input.GroupPdConsumeInput;
import net.polyv.rest.model.dmp.vod.GroupVodSpaceVO;
import net.polyv.rest.model.dmp.vod.GroupVodTrafficVO;
import net.polyv.rest.service.dmp.DmpVodService;
import net.polyv.util.DateUtil;
import net.polyv.web.model.consume.input.SyncDmpToPcsVO;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.dmp.PullTypeConst;
import net.polyv.constant.dmp.TerritoryConst;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.custom.ItemConsumeDailyDao;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.ItemConsumeDaily;
import net.polyv.rest.model.dmp.input.CustomerPdConsumePageInput;
import net.polyv.rest.service.dmp.DmpLivePlayDurationService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.item.ItemConsumeDailyService;
import net.polyv.service.transfer.ItemConsumeDailyTransferServiceV2;
import net.polyv.util.DingWarnRobot;
import org.springframework.util.CollectionUtils;


/**
 * <AUTHOR>
 * @since 2020/10/14
 */
@Service
@Slf4j
public class ItemConsumeDailyTransferServiceV2Impl implements ItemConsumeDailyTransferServiceV2 {
    
    private static final int PAGE_SIZE = 200;
    
    @Autowired
    private ItemConsumeDailyDao itemConsumeDailyDao;
    
    @Autowired
    private ItemConsumeDailyService itemConsumeDailyService;
    
    @Autowired
    private BillingItemRepository billingItemRepository;
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private DmpLivePlayDurationService dmpLivePlayDurationService;
    
    @Autowired
    private DingWarnRobot dingWarnRobot;
    @Autowired
    private ItemConsumeDailyRepository itemConsumeDailyRepository;
    
    @Autowired
    private DmpVodService dmpVodService;
    
    @Autowired
    private GroupConcurrenceBillingUserStatRepository groupConcurrenceBillingUserStatRepository;

    @Autowired
    private CustomerBaseRepository customerBaseRepository;
    
    @Override
    public void dmpChinaDurationSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.china_pd.getCode());
        
        CustomerPdConsumePageInput input = CustomerPdConsumePageInput.builder()
                .statAt(vo.getStatDate())
                .territory(TerritoryConst.china)
                .pullType(PullTypeConst.cdn)
                .build();
        executeSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }
    
    @Override
    public void dmpOverseasDurationSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.inter_pd.getCode());
    
        CustomerPdConsumePageInput input = CustomerPdConsumePageInput.builder()
                .statAt(vo.getStatDate())
                .territory(TerritoryConst.overseas)
                .pullType(PullTypeConst.cdn)
                .build();
        executeSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }
    
    @Override
    public void dmpPRTCChinaDurationSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.prtc_china_pd.getCode());
    
        CustomerPdConsumePageInput input = CustomerPdConsumePageInput.builder()
                .statAt(vo.getStatDate())
                .territory(TerritoryConst.china)
                .pullType(PullTypeConst.prtc)
                .build();
        executeSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }
    
    @Override
    public void dmpPRTCOverseasDurationSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.prtc_inter_pd.getCode());
    
        CustomerPdConsumePageInput input = CustomerPdConsumePageInput.builder()
                .statAt(vo.getStatDate())
                .territory(TerritoryConst.overseas)
                .pullType(PullTypeConst.prtc)
                .build();
        executeSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }
    
    private void executeSync(BillingItem billingItem, Boolean needCheck, CustomerPdConsumePageInput input,Boolean isCovert){
        int itemId = billingItem.getId();
        Date date = input.getStatAt();
        if(needCheck && itemConsumeDailyService.isDataExist(date, itemId)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("concurrence data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                log.info("delete data,date={},itemId={}", date,itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAt(itemId,date);
            }
        }
        input.setPageIndex(1);
        input.setPageSize(PAGE_SIZE);
        Paginator<CustomerConsumeVO> page = dmpLivePlayDurationService.pageByCondition(input);
    
        save(page.getContents(), billingItem, date);
    
        // 分页获取数据并且插入结算系统的用量记录表
        int totalPage = page.getTotalPage();
        for (int index = 2; index <= totalPage; index++) {
            input.setPageIndex(index);
            page = dmpLivePlayDurationService.pageByCondition(input);
            save(page.getContents(), billingItem, date);
        }
    }
    
    /**
     * 保存进pcs的item_consume_daily中
     * @param list
     * @param billingItem
     */
    private void save(List<CustomerConsumeVO> list, BillingItem billingItem, Date statAt) {
        List<String> liveUserIds = list.stream()
                .map(CustomerConsumeVO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        
        Map<String, String> liveUnionIdMap = getCustomerIdByLiveUserIdList(liveUserIds);
        
        List<ItemConsumeDaily> targetList;
        targetList = list.stream()
                .map(source -> buildItemConsumeDaily(source, billingItem, statAt, liveUnionIdMap))
                .filter(Objects::nonNull)
                .filter(item -> StringUtils.length(item.getCustomerId()) == 10)
                .collect(Collectors.toList());
        try {
            itemConsumeDailyDao.batchAdd(targetList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            //跳过处理，钉钉告警
            dingWarnRobot.sendWarnMsg(String.format("批量将每日用量到数据库抛异常【itemCode=%s】", billingItem.getCode()),
                    e.getMessage());
            log.error("itemCode={},异常跳过的用户：{}", billingItem.getCode(), list.stream().map(CustomerConsumeVO::getUserId).collect(Collectors.joining()));
        }
    }

    /**
     * 根据直播用户 Id 列表获取 unionId 列表
     * @param liveUserIdList
     * @return
     */
    private Map<String,String> getCustomerIdByLiveUserIdList(List<String> liveUserIdList){
        if(CollectionUtils.isEmpty(liveUserIdList)){
            return Collections.emptyMap();
        }

        Map<String, String> userIdUnionIdMap = customerService.getLiveUnionIdMap(liveUserIdList);
        //如果接口返回为空，就再查一次本地的 customer_base表做备份策略
        if(null == userIdUnionIdMap || userIdUnionIdMap.isEmpty()){
            List<CustomerBase> customerList = customerBaseRepository.getCustomerIdByLiveUserId(liveUserIdList);
            if(!CollectionUtils.isEmpty(customerList)){
                userIdUnionIdMap = customerList.stream().collect(Collectors.toMap(CustomerBase::getLiveUserId, CustomerBase::getUnionId));
            }
        }
        return userIdUnionIdMap;
    }
    
    /**
     * 构建 itemConsumeDaily对象
     * @param obj
     * @param billingItem
     * @param statAt
     * @param liveUnionIdMap
     * @return
     */
    private ItemConsumeDaily buildItemConsumeDaily(CustomerConsumeVO obj, BillingItem billingItem, Date statAt,
            Map<String, String> liveUnionIdMap) {
        ItemConsumeDaily target = new ItemConsumeDaily();
        if (!liveUnionIdMap.containsKey(obj.getUserId())) {
            log.error("live userid cannot find unionId, live userid={}", obj.getUserId());
            target.setCustomerId(obj.getUserId());
        } else {
            target.setCustomerId(liveUnionIdMap.get(obj.getUserId()));
        }

        target.setResourceCode(billingItem.getResourceCode());
        target.setItemId(billingItem.getId());
        target.setStatAt(statAt);
        target.setConsumed(obj.getPlayDuration());
        target.setGroupId(obj.getGroupId());
        String isGroupAccount = StringUtils.defaultIfBlank(obj.getIsGroupAccout(), SwitchEnum.N.getCode());
        target.setIsGroupAccount(isGroupAccount);
        return target;
    }

    /**
     * 同步集团账号国内直播分钟数到pcs的用量表
     * @param vo
     */
    @Override
    public void dmpGroupChinaDurationSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.china_pd.getCode());
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .territory(TerritoryConst.china)
                .pullType(PullTypeConst.cdn)
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 同步集团账号海外直播分钟数到pcs的用量表
     * @param vo
     */
    @Override
    public void dmpGroupOverseasDurationSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.inter_pd.getCode());
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .territory(TerritoryConst.overseas)
                .pullType(PullTypeConst.cdn)
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 执行集团账号直播分钟数用量同步方法
     * @param billingItem
     * @param needCheck
     * @param input
     * @param isCovert
     */
    private void executeGroupSync(BillingItem billingItem, Boolean needCheck, GroupPdConsumeInput input,Boolean isCovert){
        int itemId = billingItem.getId();
        Date date = input.getStatAt();
        String isGroupAccount = input.getIsGroupAccount();
        if(needCheck && itemConsumeDailyService.isDataExistWithGroupAccount(date, itemId,isGroupAccount)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("group live cdn duration data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                log.info("delete data,date={},itemId={}", date,itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId,date,isGroupAccount);
            }
        }
        List<GroupConsumeVO> list = dmpLivePlayDurationService.listGroupLivePlayDuration(input);
        List<CustomerConsumeVO> result = new ArrayList<>();
        if(null != list){
            for (GroupConsumeVO obj : list) {
                CustomerConsumeVO vo = new CustomerConsumeVO();
                vo.setUserId(obj.getGroupId());
                vo.setPlayDuration(obj.getPlayDuration().longValue());
                vo.setIsGroupAccout(isGroupAccount);
                result.add(vo);
            }
        }
        save(result, billingItem, date);
    }

    /**
     * 同步集团账号导播台直播分钟数到pcs的用量表
     * @param vo
     */
    @Override
    public void dmpGuideDurationSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.guide_pd.getCode());
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupGuideSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 执行集团账号直播导播台分钟数用量同步方法
     * @param billingItem
     * @param needCheck
     * @param input
     * @param isCovert
     */
    private void executeGroupGuideSync(BillingItem billingItem, Boolean needCheck, GroupPdConsumeInput input,Boolean isCovert){
        int itemId = billingItem.getId();
        Date date = input.getStatAt();
        String isGroupAccount = input.getIsGroupAccount();
        if(needCheck && itemConsumeDailyService.isDataExistWithGroupAccount(date, itemId,isGroupAccount)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("group live guide data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                log.info("delete data,date={},itemId={}", date,itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId,date,isGroupAccount);
            }
        }
        List<GroupGuideConsumeVO> list = dmpLivePlayDurationService.listGroupGuidePlayDuration(input);
        List<CustomerConsumeVO> result = new ArrayList<>();
        if(null != list){
            for (GroupGuideConsumeVO obj : list) {
                CustomerConsumeVO vo = new CustomerConsumeVO();
                vo.setUserId(obj.getGroupId());
                vo.setPlayDuration(obj.getDuration().longValue());
                vo.setIsGroupAccout(isGroupAccount);
                result.add(vo);
            }
        }
        save(result, billingItem, date);
    }

    /**
     * 同步集团账号直播并发数到pcs的用量表
     * @param vo     */
    @Override
    public void dmpGroupConcurrenceSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.concur_daily.getCode());
        if(null != billingItem && StringUtils.isBlank(billingItem.getResourceCode())){
            billingItem.setResourceCode(ResourceCodeConst.concurrence.name());
        }
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupConcurrenceSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 执行集团账号直播并发用量同步方法
     * @param billingItem
     * @param needCheck
     * @param input
     * @param isCovert
     */
    private void executeGroupConcurrenceSync(BillingItem billingItem, Boolean needCheck, GroupPdConsumeInput input,Boolean isCovert){
        int itemId = billingItem.getId();
        Date date = input.getStatAt();
        String isGroupAccount = input.getIsGroupAccount();
        if(needCheck && itemConsumeDailyService.isDataExistWithGroupAccount(date, itemId,isGroupAccount)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("group live concurrence data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                log.info("delete data,date={},itemId={}", date,itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId,date,isGroupAccount);
            }
        }
        //增加无延迟并发的逻辑
        BillingItem prtcBillingItem = null;
        if(ItemCodeConst.concur_daily.getCode().equals(billingItem.getCode())){
            prtcBillingItem = billingItemRepository.findFirstByCode(ItemCodeConst.prtc_concur_daily.getCode());
            if(needCheck && itemConsumeDailyService.isDataExistWithGroupAccount(date, prtcBillingItem.getId(),isGroupAccount)){
                //数据存在时，是否要覆盖原数据
                //不覆盖，则跳过
                if(!isCovert){
                    log.info("group live prtc_concurrence data exists. date={}. skip...", date);
                    return;
                }
                //覆盖的话，要先删除原有数据
                else{
                    log.info("delete data,date={},prtcItemId={}", date,prtcBillingItem.getId());
                    itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(prtcBillingItem.getId(),date,isGroupAccount);
                }
            }
        }

        List<GroupConcurrenceBillingUserStat> list = groupConcurrenceBillingUserStatRepository.findByStatAt(date);
        List<CustomerConsumeVO> result = new ArrayList<>();
        if(null != list){
            for (GroupConcurrenceBillingUserStat obj : list) {
                CustomerConsumeVO vo = new CustomerConsumeVO();
                vo.setUserId(obj.getGroupId());
                vo.setPlayDuration(obj.getConcurrence());
                vo.setIsGroupAccout(isGroupAccount);
                result.add(vo);
            }
        }
        save(result, billingItem, date);
        //无延迟的并发
        if(null != prtcBillingItem){
            save(result, prtcBillingItem, date);
        }
    }


    /**
     * 同步集团账号普通连麦分钟数到pcs的用量表
     * @param vo
     */
    @Override
    public void dmpGroupMicDurationSyncToClearing(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.mic_pd.getCode());
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupMicSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 执行集团账号直播连麦分钟数用量同步方法
     * @param billingItem
     * @param needCheck
     * @param input
     * @param isCovert
     */
    private void executeGroupMicSync(BillingItem billingItem, Boolean needCheck, GroupPdConsumeInput input,Boolean isCovert){
        int itemId = billingItem.getId();
        Date date = input.getStatAt();
        String isGroupAccount = input.getIsGroupAccount();
        List<BillingItem> billingItemList = billingItemRepository.findByCode(ItemCodeConst.mic_pd.getCode());
        if(needCheck && itemConsumeDailyService.isDataExistWithGroupAccount(date, itemId,isGroupAccount)) {
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if (!isCovert) {
                log.info("group live mic data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else {
                //连麦分钟数同个code的有多条记录，需要根据code获取多个itemId来执行删除
                if (CollectionUtils.isEmpty(billingItemList)) {
                    dingWarnRobot.sendWarnMsg(String.format("根据code获取不到itemId【coce=%s】", ItemCodeConst.mic_pd.getCode()),
                            "");
                    return;
                }
                List<Integer> itemIdList = billingItemList.stream().map(item -> item.getId()).collect(Collectors.toList());
                log.info("delete data,date={},itemId={}", date, itemIdList.toArray());
                itemConsumeDailyRepository.deleteByStatDateAndItemIdListAndIsGroupAccount(date, itemIdList,isGroupAccount);
            }
        }
        for(BillingItem bItem : billingItemList){
            if(bItem.getScaleCode().equals(ItemScaleCodeConst.MicPd.mic_pd_7.name())){
                input.setConcurrenceGradientLevel(ItemMicGradientForDmpConst.mic_1v6.getCode());
            }else if(bItem.getScaleCode().equals(ItemScaleCodeConst.MicPd.mic_pd_8.name())){
                input.setConcurrenceGradientLevel(ItemMicGradientForDmpConst.mic_7v16.getCode());
            }else{
                continue;
            }
            List<GroupGuideConsumeVO> list = dmpLivePlayDurationService.listGroupMicPlayDuration(input);
            List<CustomerConsumeVO> result = new ArrayList<>();
            if(null != list){
                for (GroupGuideConsumeVO obj : list) {
                    CustomerConsumeVO vo = new CustomerConsumeVO();
                    vo.setUserId(obj.getGroupId());
                    vo.setPlayDuration(obj.getDuration().longValue());
                    vo.setIsGroupAccout(isGroupAccount);
                    result.add(vo);
                }
            }
            save(result, bItem, date);
        }
    }

    /**
     * 同步集团账号国内PRTC直播分钟数到pcs的用量表
     * @param vo
     */
    @Override
    public void dmpGroupPRTCChinaDurationSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.prtc_china_pd.getCode());
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .territory(TerritoryConst.china)
                .pullType(PullTypeConst.prtc)
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 同步集团账号海外PRTC直播分钟数到pcs的用量表
     * @param vo
     */
    @Override
    public void dmpGroupPRTCOverseasDurationSyncToPCS(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.prtc_inter_pd.getCode());
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .territory(TerritoryConst.overseas)
                .pullType(PullTypeConst.prtc)
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 同步集团账号点播流量数据到pcs的用量表
     * @param vo
     */
    @Override
    public void dmpGroupTrafficSyncToClearing(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.traffic.getCode());
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupTrafficSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 执行集团账号点播流量用量同步方法
     * @param billingItem
     * @param needCheck
     * @param input
     * @param isCovert
     */
    private void executeGroupTrafficSync(BillingItem billingItem, Boolean needCheck, GroupPdConsumeInput input,Boolean isCovert){
        int itemId = billingItem.getId();
        Date date = input.getStatAt();
        String isGroupAccount = input.getIsGroupAccount();
        if(needCheck && itemConsumeDailyService.isDataExistWithGroupAccount(date, itemId,isGroupAccount)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("group vod traffic data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                log.info("delete data,date={},itemId={}", date,itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId,date,isGroupAccount);
            }
        }
        List<GroupVodTrafficVO> list = dmpVodService.listGroupVodTraffic(input);
        List<CustomerConsumeVO> result = new ArrayList<>();
        if(null != list){
            for (GroupVodTrafficVO obj : list) {
                CustomerConsumeVO vo = new CustomerConsumeVO();
                vo.setUserId(obj.getGroupId());
                vo.setPlayDuration(obj.getTraffic());
                vo.setIsGroupAccout(isGroupAccount);
                result.add(vo);
            }
        }
        save(result, billingItem, date);
    }

    /**
     * 同步集团账号点播空间数据到pcs的用量表
     * @param vo
     */
    @Override
    public void dmpGroupSpaceSyncToClearing(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.video_space.getCode());
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupSpaceSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 执行集团账号点播空间用量同步方法
     * @param billingItem
     * @param needCheck
     * @param input
     * @param isCovert
     */
    private void executeGroupSpaceSync(BillingItem billingItem, Boolean needCheck, GroupPdConsumeInput input,Boolean isCovert){
        int itemId = billingItem.getId();
        Date date = input.getStatAt();
        String isGroupAccount = input.getIsGroupAccount();
        if(needCheck && itemConsumeDailyService.isDataExistWithGroupAccount(date, itemId,isGroupAccount)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("group vod space data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                log.info("delete data,date={},itemId={}", date,itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId,date,isGroupAccount);
            }
        }
        List<GroupVodSpaceVO> list = dmpVodService.listGroupVodSpace(input);
        List<CustomerConsumeVO> result = new ArrayList<>();
        if(null != list){
            for (GroupVodSpaceVO obj : list) {
                CustomerConsumeVO vo = new CustomerConsumeVO();
                vo.setUserId(obj.getGroupId());
                vo.setPlayDuration(obj.getSpace());
                vo.setIsGroupAccout(isGroupAccount);
                result.add(vo);
            }
            groupSpaceSyncPostHandle(date, itemId, isGroupAccount, result);
        }
    
    
        save(result, billingItem, date);
    }
    
    /**
     * 主账号空间同步后置处理
     */
    private void groupSpaceSyncPostHandle(Date date, int itemId, String isGroupAccount,
            List<CustomerConsumeVO> result) {
        //对于彻底删除点播视频的情况，大数据不会同步截止到当前占用空间为0的记录给到pcs，会导致pcs认为客户还是有视频占用的，
        // 影响空间的可用(空间可用 = 充值空间 - 最近一条消耗记录空间)，所以这里需要补充一条空间为0的记录
        //处理方法：判断前1天有空间消耗记录，如果当天没有消耗记录的情况，当天补充一条空间为0的记录
        
        Date preDate = DateUtil.getDateAfterDays(-1, date);
        
        // Fetch all previous day's user consume records
        List<ItemConsumeDaily> preDayItemConsumeList =
                itemConsumeDailyRepository.findByItemIdAndStatAtAndIsGroupAccount(
                itemId, preDate, isGroupAccount);
        
        if (!CollectionUtils.isEmpty(preDayItemConsumeList)) {
            List<String> preDayUserIdList = preDayItemConsumeList.stream()
                    .map(ItemConsumeDaily::getCustomerId)
                    .collect(Collectors.toList());
            
            // Convert the result list to a map for efficient lookup
            Map<String, CustomerConsumeVO> resultMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(result)) {
                resultMap = result.stream()
                        .collect(Collectors.toMap(CustomerConsumeVO::getUserId, Function.identity()));
            }
            // Add missing users to the result list with play duration 0
            Map<String, CustomerConsumeVO> finalResultMap = resultMap;
            preDayUserIdList.stream().filter(userId -> !finalResultMap.containsKey(userId)).forEach(userId -> {
                CustomerConsumeVO vo = new CustomerConsumeVO();
                vo.setUserId(userId);
                vo.setPlayDuration(0L);
                vo.setIsGroupAccout(isGroupAccount);
                result.add(vo);
            });
        }
    }
    
    /**
     * 同步集团账号直播研讨会分钟数数据到pcs的用量表
     * @param vo
     */
    @Override
    public void dmpGroupSeminarDurationSyncToClearing(SyncDmpToPcsVO vo) {
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.seminar_duration.getCode());
        GroupPdConsumeInput input = GroupPdConsumeInput.builder()
                .statAt(vo.getStatDate())
                .isGroupAccount(vo.getIsGroup())
                .build();
        executeGroupSeminarDurationSync(billingItem, vo.getIsCheck(), input,vo.getIsCover());
    }

    /**
     * 执行集团账号直播研讨会分钟数用量同步方法
     * @param billingItem
     * @param needCheck
     * @param input
     * @param isCovert
     */
    private void executeGroupSeminarDurationSync(BillingItem billingItem, Boolean needCheck, GroupPdConsumeInput input,Boolean isCovert){
        int itemId = billingItem.getId();
        Date date = input.getStatAt();
        String isGroupAccount = input.getIsGroupAccount();
        List<BillingItem> billingItemList = billingItemRepository.findByCode(ItemCodeConst.seminar_duration.getCode());
        if(needCheck && itemConsumeDailyService.isDataExistWithGroupAccount(date, itemId,isGroupAccount)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("group seminar duration data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                //研讨会同个code的有多条记录，需要根据code获取多个itemId来执行删除
                if(CollectionUtils.isEmpty(billingItemList)){
                    dingWarnRobot.sendWarnMsg(String.format("根据code获取不到itemId【coce=%s】", ItemCodeConst.seminar_duration.getCode()),
                            "");
                    return;
                }
                List<Integer> itemIdList = billingItemList.stream().map(item -> item.getId()).collect(Collectors.toList());
                log.info("delete data,date={},itemId={}",date,itemIdList.toArray());
                itemConsumeDailyRepository.deleteByStatDateAndItemIdListAndIsGroupAccount(date,itemIdList,isGroupAccount);
            }
        }
        for(BillingItem bItem : billingItemList){
            if(bItem.getScaleCode().equals(ItemScaleCodeConst.Seminar.seminar_1v6.name())){
                input.setConcurrenceGradientLevel(ItemMicGradientForDmpConst.mic_1v6.getCode());
            }else if(bItem.getScaleCode().equals(ItemScaleCodeConst.Seminar.seminar_1v16.name())){
                input.setConcurrenceGradientLevel(ItemMicGradientForDmpConst.mic_7v16.getCode());
            }else if(bItem.getScaleCode().equals(ItemScaleCodeConst.Seminar.seminar_6.name())){
                input.setConcurrenceGradientLevel(ItemMicGradientForDmpConst.mic_6.getCode());
            }else{
                continue;
            }
            List<GroupGuideConsumeVO> list = dmpLivePlayDurationService.listGroupSeminarDuration(input);
            List<CustomerConsumeVO> result = new ArrayList<>();
            if(null != list){
                for (GroupGuideConsumeVO obj : list) {
                    CustomerConsumeVO vo = new CustomerConsumeVO();
                    vo.setUserId(obj.getGroupId());
                    vo.setPlayDuration(obj.getDuration().longValue());
                    vo.setIsGroupAccout(isGroupAccount);
                    result.add(vo);
                }
            }
            save(result, bItem, date);
        }
    }
    
    
}
