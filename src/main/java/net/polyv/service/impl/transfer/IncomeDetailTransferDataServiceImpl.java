package net.polyv.service.impl.transfer;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.function.ToLongFunction;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.PcsConstant;
import net.polyv.constant.finance.FinanceIncomeDetailTagEnum;
import net.polyv.constant.finance.FinanceTaxRateConst;
import net.polyv.constant.finance.StatusValueEnum;
import net.polyv.constant.finance.SubCategoryEnum;
import net.polyv.constant.transfer.TransferServiceNameConstant;
import net.polyv.dao.oldFinance.FinanceInfoRepository;
import net.polyv.dao.oldFinance.custom.BillInfoDao;
import net.polyv.dao.oldFinance.custom.ContractDao;
import net.polyv.dao.primary.custom.BusinessEnumDefinitionDao;
import net.polyv.dao.primary.finance.FinanceIncomeDetailRepository;
import net.polyv.model.data.finance.BillObject;
import net.polyv.model.dto.TransferDataDTO;
import net.polyv.model.entity.oldFinance.Contract;
import net.polyv.model.entity.oldFinance.FinanceInfo;
import net.polyv.model.entity.primary.definition.BusinessEnumDefinition;
import net.polyv.model.entity.primary.finance.FinanceIncomeDetailEntity;
import net.polyv.modules.common.constant.Constant;
import net.polyv.service.CacheService;
import net.polyv.service.transfer.AbstractTransferDateService;
import net.polyv.service.transfer.TransferContractProcessorService;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.MoneyUtil;
import net.polyv.web.model.transfer.BaseTransferRequest;

/**
 * 收入明细数据传输服务
 * <AUTHOR>
 * @date 2022/9/2 17:26
 */
@Service
@Slf4j
public class IncomeDetailTransferDataServiceImpl extends AbstractTransferDateService
        implements TransferContractProcessorService {
    
    @Resource
    private FinanceInfoRepository financeInfoRepository;
    @Resource
    private FinanceIncomeDetailRepository financeIncomeDetailRepository;
    @Resource
    private CacheService cacheService;
    @Resource
    private BillInfoDao billInfoDao;
    @Resource
    private BusinessEnumDefinitionDao businessEnumDefinitionDao;
    @Resource
    private ContractDao contractDao;
    @Resource
    private DingWarnRobot dingWarnRobot;
    
    @Override
    public String taskName() {
        return TransferServiceNameConstant.INCOME_DETAIL_TRANSFER.getDesc();
    }
    
    @Override
    public boolean isAbleInvoke(String taskId) {
        return TransferServiceNameConstant.INCOME_DETAIL_TRANSFER.getName().equals(taskId);
    }
    
    @Override
    public CommonResult<TransferDataDTO> transferData(TransferDataDTO req) throws Exception {
        this.scheduling(req);
        return CommonResult.ok(req);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    protected boolean pageResult(BaseTransferRequest request) {
        request.setPageSize(50);
        Page<Contract> billInfos = this.contractDao.summaryPage(request);
        processData(billInfos.getContent());
        return billInfos.hasNext();
    }
    
    @Override
    public void processData(List<Contract> content) {
        if (CollectionUtils.isEmpty(content)) {
            return;
        }
        //先根据合同数据查询收入明细数据、有明细数据汇总单月总数据
        List<String> oldContractIds = content.stream()
                .map(Contract::getContractId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<FinanceInfo> list = financeInfoRepository.findAllAmountByContractIdIn(oldContractIds);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("无financeInfo信息：{}", oldContractIds);
            return;
        }
        //获取计费项枚举信息
        Set<String> itemNames = list.stream().map(FinanceInfo::getItemName).collect(Collectors.toSet());
        Map<String, BusinessEnumDefinition> enumDefinitionMap = this.businessEnumDefinitionDao.mapUnionByItemNames(
                itemNames);
     /*   Map<String, FinanceContractEntity> newContractMap = financeContractDao.mapFieldByContractIds(
                FinanceContractEntity::getContractId, oldContractIds);*/
        //合同与账单信息
        Map<String, List<FinanceInfo>> financeContractMap = list.stream()
                .collect(Collectors.groupingBy(FinanceInfo::getContractId));
        //加载所有的消耗数据 billInfo
        List<String> allFinanceIds = list.stream().map(FinanceInfo::getInfoId).collect(Collectors.toList());
        //分批查询明细
        List<List<String>> partition1 = Lists.partition(allFinanceIds, 200);
        Map<String, List<BillObject>> allBillMap = new HashMap<>(allFinanceIds.size());
        partition1.forEach(v -> allBillMap.putAll(this.billInfoDao.groupBillByFinanceId(v)));
        //生成收入明细表
        List<FinanceIncomeDetailEntity> saveList = buildIncomeDetailList(financeContractMap, allBillMap,
                enumDefinitionMap);
        List<List<FinanceIncomeDetailEntity>> partition = Lists.partition(saveList, 200);
        partition.forEach(v -> this.financeIncomeDetailRepository.saveByBatch(v));
    }
    
    
    /**
     * 方法意图说明：
     * 通过区分每一个合同 下面所有的当月账单信息 进行按月分组、在按计费项分组
     * @param allFinanceInfoMap  全部的账单数据 key:contractId value:所有月账单
     * @param allBillMap    全部的账单消耗数据 key:financeId value:所有天的账单消耗
     * @param businessEnumMap 业务枚举对应的细分 key: 产品+分类+计费项明细   value: 业务枚举信息
     * @return 创建所有合同的所有月份的收入明细数据
     */
    private List<FinanceIncomeDetailEntity> buildIncomeDetailList(Map<String, List<FinanceInfo>> allFinanceInfoMap,
            Map<String, List<BillObject>> allBillMap, Map<String, BusinessEnumDefinition> businessEnumMap) {
        List<FinanceIncomeDetailEntity> saveList = new ArrayList<>();
        allFinanceInfoMap.forEach((contractId, financeList) -> {
            try {
                //将finance按月区分
                Map<String, List<FinanceInfo>> collect = financeList.stream()
                        .sorted(Comparator.comparing(FinanceInfo::getMonth))
                        .collect(Collectors.groupingBy(FinanceInfo::getMonth));
                TreeMap<String, List<FinanceInfo>> sortMap = new TreeMap<>(collect);
                sortMap.forEach((month, monthFinanceList) -> saveList.addAll(
                        createIncomeDetail(allBillMap, businessEnumMap, monthFinanceList)));
            } catch (Exception e) {
                log.error("导入收入明细数据失败:contractId={},异常：", contractId, e);
                String format = String.format("合同Id：%s" + Constant.LINE_BREAK, contractId);
                dingWarnRobot.sendMsgToAtPeople("【导入收入明细数据失败】", Lists.newArrayList(format), "");
            }
        });
        return saveList;
    }
    
    private List<FinanceIncomeDetailEntity> createIncomeDetail(Map<String, List<BillObject>> allBillMap,
            Map<String, BusinessEnumDefinition> businessEnumMap, List<FinanceInfo> infoList) {
        List<FinanceIncomeDetailEntity> list = new ArrayList<>();
        //针对DEPOSIT_ZERO处理
        if (CollectionUtils.isEmpty(infoList)) {
            return list;
        }
        boolean match = infoList.stream().map(FinanceInfo::getItemName).anyMatch(PcsConstant.DEPOSIT_ZERO::equals);
        if (match) {
            //创建一条为0 收入明细
            list.add(createZeroDetail(infoList));
            return list;
        }
        List<String> infoIds = infoList.stream().map(FinanceInfo::getInfoId).collect(Collectors.toList());
        //map 有值表示该合同该月  多个计费项 有多天的消耗明细：key:financeId-计费项,value :当月多天消耗
        Map<String, List<BillObject>> map = this.billInfoDao.groupBillByFinanceId(infoIds);
        //infoList 当月的所有消耗计费项列表
        Map<String, FinanceInfo> monthFinanceMap = infoList.stream()
                .collect(Collectors.toMap(FinanceInfo::getInfoId, Function.identity()));
        //需要考虑两种情况： 1.当月正常消耗数据   2.当月无消耗数据-获取上一个月数据
        list.addAll(concatDetail(allBillMap, businessEnumMap, map, monthFinanceMap));
        return list;
    }
    
    private FinanceIncomeDetailEntity createZeroDetail(List<FinanceInfo> infoList) {
        Optional<FinanceInfo> first = infoList.stream()
                .filter(v -> v.getItemName().equals(PcsConstant.DEPOSIT_ZERO))
                .findFirst();
        FinanceInfo financeInfo = first.orElse(new FinanceInfo());
        return FinanceIncomeDetailEntity.builder()
                //无需求文档说明
                .contractId(financeInfo.getContractId())
                .incomeMonth(financeInfo.getMonth())
                //有需求文档说明一下字段来源
                .billingItemId(null)
                .subCategory(SubCategoryEnum.SC_AMOUNT.getCode())
                .taxRate(MoneyUtil.enlargeNumericalHundred(FinanceTaxRateConst.NORMAL.getTaxRate() + ""))
                .incomeBelong(null)
                .consumeUnit(null)
                //金额合同取对应计费项，对应月份的 currentAmount/其它取该月份的 untilAmount
                .cumulativeIncome(0L)
                // 金额合同取对应计费项，对应月份的 currentAmount/其它取该合同该月的currentAmount
                .currentIncome(0L)
                .currentConsume(0L)
                //累计消耗 获取截止当前月的消耗量
                .cumulativeConsume(0L)
                //单价获取 最后一个账单使用的单价
                .univalent(null)
                .univalentUnit(null)
                .createTime(financeInfo.getCreatedTime())
                .updateTime(new Date())
                .settledStatus(StatusValueEnum.NO.getValue())
                .status(StatusValueEnum.NO.getValue())
                .build();
    }
    
    private List<FinanceIncomeDetailEntity> concatDetail(Map<String, List<BillObject>> allBillMap,
            Map<String, BusinessEnumDefinition> enumDefinitionMap, Map<String, List<BillObject>> map,
            Map<String, FinanceInfo> monthFinanceMap) {
        List<FinanceIncomeDetailEntity> detailList = new ArrayList<>();
        // 获取每一个计费项多天消耗 进行归纳 汇总为一条
        monthFinanceMap.forEach((infoId, financeInfo) -> {
            String contractId = financeInfo.getContractId();
            String month = financeInfo.getMonth();
            String mapKey =  FinanceInfo.getUnionKey(financeInfo);
            //退费收入
            if (PcsConstant.AMOUNT_REFUND.equals(financeInfo.getItemName())){
                detailList.add(createRefundDetail(Lists.newArrayList(financeInfo)));
                return;
            }
            List<BillObject> dayConsumeList = map.get(infoId);
            BusinessEnumDefinition item = enumDefinitionMap.getOrDefault(mapKey, new BusinessEnumDefinition());
            if (Objects.isNull(item)) {
                log.info("无法获取计费项定义：{}", mapKey);
            }
           if(CollectionUtils.isEmpty(dayConsumeList)){
                log.info("读取上一个月的收入明细：contractId={},month={}",contractId,month);
                detailList.add(getBeforeMonthIncome(financeInfo,item.getBillItemId()));
                return;
            }
            List<BillObject> allBillObject = allBillMap.get(infoId);
            Optional<BillObject> first = dayConsumeList.stream().findFirst();
            if (!first.isPresent()) {
                return;
            }
            BillObject billObject = first.get();
            FinanceIncomeDetailEntity build = FinanceIncomeDetailEntity.builder()
                    //无需求文档说明
                    .contractId(financeInfo.getContractId()).incomeMonth(financeInfo.getMonth())
                    //有需求文档说明一下字段来源
                    .billingItemId(Objects.isNull(item.getBillItemId()) ? null : item.getBillItemId().intValue())
                    .subCategory(item.getDetailSubCategory())
                    .taxRate(item.getTaxRatio())
                    .incomeBelong(item.getIncomeBelong())
                    .consumeUnit(billObject.getItemConsumedUnit())
                    //金额合同取对应计费项，对应月份的 currentAmount/其它取该月份的 untilAmount
                    .cumulativeIncome(MoneyUtil.enlargeNumericalHundredThousand(financeInfo.getUntilAmount() + ""))
                    // 金额合同取对应计费项，对应月份的 currentAmount/其它取该合同该月的currentAmount
                    .currentIncome(MoneyUtil.enlargeNumericalHundredThousand(financeInfo.getCurrentAmount() + ""))
                    .currentConsume(getCurrentMonthNumByMonth(dayConsumeList, month, BillObject::getItemConsumed))
                    //累计消耗 获取截止当前月的消耗量
                    .cumulativeConsume(getCalculateNumByMonth(allBillObject, month, BillObject::getItemConsumed))
                    //单价获取 最后一个账单使用的单价
                    .univalent(billObject.getUnivalence())
                    .univalentUnit(billObject.getUnivalenceUnit())
                    .createTime(financeInfo.getCreatedTime())
                    .updateTime(new Date())
                    .settledStatus(getSettledStatusByMonth(dayConsumeList, month, item.getBillItemName()))
                    .status(StatusValueEnum.NO.getValue())
                    .build();
            detailList.add(build);
            //缓存设置
            cacheService.setCacheBeforeMonthIncomeByContractId(build);
        });
        return detailList;
    }
    
    private FinanceIncomeDetailEntity getBeforeMonthIncome(FinanceInfo financeInfo,Long itemId) {
        //  从缓存中获取上一个月的 收入明细数据，置空当月收入，当月消耗
        FinanceIncomeDetailEntity cacheEntity = cacheService.getCacheBeforeMonthIncomeByContractId(
                financeInfo.getContractId(), itemId);
        if (Objects.isNull(cacheEntity)){
            return  createZeroDetail(Lists.newArrayList(financeInfo));
        }
        cacheEntity.setCurrentIncome(0L);
        cacheEntity.setCurrentConsume(0L);
        cacheEntity.setId(0L);
        cacheEntity.setIncomeMonth(financeInfo.getMonth());
        cacheEntity.setCreateTime(financeInfo.getCreatedTime());
        return cacheEntity;
    }
    
    private FinanceIncomeDetailEntity createRefundDetail(List<FinanceInfo> infoList) {
        Optional<FinanceInfo> first = infoList.stream()
                .filter(v -> v.getItemName().equals(PcsConstant.AMOUNT_REFUND))
                .findFirst();
        FinanceInfo financeInfo = first.orElse(new FinanceInfo());
        return FinanceIncomeDetailEntity.builder()
                //无需求文档说明
                .contractId(financeInfo.getContractId())
                .incomeMonth(financeInfo.getMonth())
                //有需求文档说明一下字段来源
                .billingItemId(FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode())
                .subCategory(SubCategoryEnum.SC_REFUND_EXTRA.getCode())
                .taxRate(MoneyUtil.enlargeNumericalHundred(FinanceTaxRateConst.NORMAL.getTaxRate() + ""))
                .incomeBelong(null)
                .consumeUnit(null)
                //金额合同取对应计费项，对应月份的 currentAmount/其它取该月份的 untilAmount
                .cumulativeIncome(MoneyUtil.enlargeNumericalHundredThousand(financeInfo.getUntilAmount()+""))
                // 金额合同取对应计费项，对应月份的 currentAmount/其它取该合同该月的currentAmount
                .currentIncome(MoneyUtil.enlargeNumericalHundredThousand(financeInfo.getCurrentAmount()+""))
                .currentConsume(0L)
                //累计消耗 获取截止当前月的消耗量
                .cumulativeConsume(0L)
                //单价获取 最后一个账单使用的单价
                .univalent(null)
                .univalentUnit(null)
                .createTime(financeInfo.getCreatedTime())
                .updateTime(new Date())
                .settledStatus(getSettledStatusByMonth(infoList,financeInfo.getMonth()))
                .status(StatusValueEnum.NO.getValue())
                .build();
    }
    
    
    /**
     * 当月是否已经完结
     * <AUTHOR>
     * @date 2022/10/13
     */
    private Short getSettledStatusByMonth(List<BillObject> consumeList, String month, String billItemName) {
        boolean result = consumeList.stream()
                .filter(v -> v.getRealMonth().equals(month))
                .filter(v -> v.getItemName().equals(billItemName))
                .anyMatch(v -> Objects.nonNull(v.getIsSettledAccountInt())&&v.getIsSettledAccountInt().equals((int) StatusValueEnum.YES.getValue()));
        return result ? StatusValueEnum.YES.getValue() : StatusValueEnum.NO.getValue();
    }   /**
     * 当月是否已经完结
     * <AUTHOR>
     * @date 2022/10/13
     */
    private Short getSettledStatusByMonth(List<FinanceInfo> consumeList, String month) {
        boolean result = consumeList.stream()
                .filter(v -> v.getMonth().equals(month))
                .filter(v->v.getLeftAmount().equals(BigDecimal.ZERO))
                .anyMatch(v ->StatusValueEnum.getValueByStatus(v.getLastFinanceInCurMonth()).equals(StatusValueEnum.YES.getValue()));
        return result ? StatusValueEnum.YES.getValue() : StatusValueEnum.NO.getValue();
    }
    
    
    /**
     * 计算当月数值
     * @param billObjects 当月消耗
     * @param month 月份
     * @param keyMapper 查询列
     * @return {@link Long}
     * <AUTHOR>
     * @date 2022/10/19
     */
    private Long getCurrentMonthNumByMonth(List<BillObject> billObjects, String month,
            ToLongFunction<? super BillObject> keyMapper) {
        if (CollectionUtils.isEmpty(billObjects)) {
            return 0L;
        }
        return billObjects.stream().filter(obj -> month.equals(obj.getRealMonth())).mapToLong(keyMapper).sum();
    }
    
    /**
     * 计算累加数值
     * @param billObjects 所有月消耗
     * @param month 月份
     * @param keyMapper 查询列
     * @return {@link Long}
     * <AUTHOR>
     * @date 2022/10/19
     */
    private Long getCalculateNumByMonth(List<BillObject> billObjects, String month,
            ToLongFunction<? super BillObject> keyMapper) {
        if (CollectionUtils.isEmpty(billObjects)) {
            return 0L;
        }
        return billObjects.stream().filter(obj -> month.compareTo(obj.getRealMonth()) <= 0).mapToLong(keyMapper).sum();
    }
    
    
}
