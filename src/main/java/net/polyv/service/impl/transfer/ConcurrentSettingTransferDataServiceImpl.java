package net.polyv.service.impl.transfer;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.transfer.TransferServiceNameConstant;
import net.polyv.model.dto.TransferDataDTO;
import net.polyv.model.entity.oldFinance.BillInfo;
import net.polyv.service.transfer.AbstractTransferDateService;
import net.polyv.web.model.transfer.BaseTransferRequest;

/**
 * 并发设置迁移
 * <AUTHOR>
 * @date 2022/9/26 14:40
 */
@Service
@Slf4j
public class ConcurrentSettingTransferDataServiceImpl extends AbstractTransferDateService {
    
    private   static  final  String  TASK_ID ="ConcurrentSettingTransferDataServiceImpl";
    

  
    @Override
    public String taskName() {
        return TransferServiceNameConstant.CONCURRENT_SETTING_TRANSFER.getDesc();
    }
    
    @Override
    public boolean isAbleInvoke(String taskId) {
        return TransferServiceNameConstant.CONCURRENT_SETTING_TRANSFER.getName().equals(taskId);
    }
    @Override
    public CommonResult<TransferDataDTO> transferData(TransferDataDTO req)throws Exception {
        this.scheduling(req);
        return CommonResult.ok(req);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    protected boolean pageResult(BaseTransferRequest request) {
/*        Page<BillInfo> refunds = this.billInfoDao.summaryPage(request);
        processData(refunds.getContent());
        return  refunds.hasNext();*/
        return  false;
    }
    
    private void processData(List<BillInfo> content) {
        if (CollectionUtils.isEmpty(content)){
            return;
        }
        // TODO: 2022/9/26  处理 收入数据
        
    }
    
  
    
}
