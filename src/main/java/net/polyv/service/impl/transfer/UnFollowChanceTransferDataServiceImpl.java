package net.polyv.service.impl.transfer;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.config.PcsConfig;
import net.polyv.constant.transfer.TransferServiceNameConstant;
import net.polyv.dao.oldFinance.custom.UnFollowChanceDao;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.model.dto.TransferDataDTO;
import net.polyv.model.entity.oldFinance.UnFollowChance;
import net.polyv.modules.pcs.api.stereotype.SalesOpportunitiesStatusConst;
import net.polyv.service.transfer.AbstractTransferDateService;
import net.polyv.util.DingWarnRobot;
import net.polyv.web.model.transfer.BaseTransferRequest;

/**
 * 旧合同数据传输服务
 * <AUTHOR>
 * @date 2022/9/2 17:26
 */
@Service
@Slf4j
public class UnFollowChanceTransferDataServiceImpl extends AbstractTransferDateService {
    @Resource
    private UnFollowChanceDao unFollowChanceDao;
    @Resource
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private PcsConfig pcsConfig;
    
    @Override
    public CommonResult<TransferDataDTO> transferData(TransferDataDTO req) throws Exception {
        this.scheduling(req);
        return CommonResult.ok(req);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    protected boolean pageResult(BaseTransferRequest request) {
        Page<UnFollowChance> refunds = this.unFollowChanceDao.summaryPage(request);
        processData(refunds.getContent());
        return refunds.hasNext();
    }
    
    private void processData(List<UnFollowChance> content) {
        if (CollectionUtils.isEmpty(content)) {
            return;
        }
        List<String> soIds = content.stream()
                .filter(Objects::nonNull)
                .map(UnFollowChance::getChanceId)
                .filter(chanceId -> chanceId.length()<=10)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(soIds)){
            log.info("无符合条件的待关联销售机会！");
            return;
        }
        log.info("将以下数据的sales_opportunities中status改为=4{}",soIds);
        this.salesOpportunitiesRepository.updateStatusTo4BySoIds(soIds, SalesOpportunitiesStatusConst.pass.getStatus());
      //  asyncNoticeThePaymentTimeIsNull(soIds);
    
    }
    
    private void asyncNoticeThePaymentTimeIsNull(List<String> soIds ) {
        List<String> msg = new ArrayList<>();
        msg.add("将以下数据的sales_opportunities中status改为=4 :"+ soIds);
        dingWarnRobot.sendMsgToAtPeople("同步财务未关联的销售机会",msg,pcsConfig.getProductMobile());
    }
    

    
    @Override
    public String taskName() {
        return TransferServiceNameConstant.UN_FOLLOW_CHANCE_TRANSFER.getDesc();
    }
    
    @Override
    public boolean isAbleInvoke(String taskId) {
        return  taskId.equals(TransferServiceNameConstant.UN_FOLLOW_CHANCE_TRANSFER.getName());
    }
    
}
