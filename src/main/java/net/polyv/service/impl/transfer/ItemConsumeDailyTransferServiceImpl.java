package net.polyv.service.impl.transfer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import net.polyv.dao.primary.CustomerBaseRepository;
import net.polyv.model.entity.primary.CustomerBase;
import net.polyv.rest.model.live.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.item.ItemScaleCodeConst;
import net.polyv.constant.item.MicItemScaleGradientConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.ItemConsumeDailyRepository;
import net.polyv.dao.primary.custom.ItemConsumeDailyDao;
import net.polyv.model.entity.dmp.LiveDbUserMicDurationDaily;
import net.polyv.model.entity.dmp.LiveUserCCV;
import net.polyv.model.entity.dmp.LiveUserGuideDurationDaily;
import net.polyv.model.entity.dmp.common.ConsumeBaseEntity;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.ItemConsumeDaily;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.rest.client.vod.user.UnionUserClient;
import net.polyv.rest.model.dmp.Paginator;
import net.polyv.rest.model.dmp.input.CustomerPdConsumePageInput;
import net.polyv.rest.model.dmp.input.SmallClassAndSeminarDTO;
import net.polyv.rest.model.dmp.input.SmallClassAndSeminarRecordDTO;
import net.polyv.rest.model.dmp.input.VodSearchDTO;
import net.polyv.rest.model.dmp.vod.VodSpaceDTO;
import net.polyv.rest.model.dmp.vod.VodTrafficDTO;
import net.polyv.rest.model.vod.user.CustomerSearchDTO;
import net.polyv.rest.model.vod.user.UserDetail;
import net.polyv.service.account.CustomerService;
import net.polyv.service.consume.DmpLiveUserCCVService;
import net.polyv.service.consume.DmpLiveUserGuideDurationService;
import net.polyv.service.consume.DmpLiveUserMicDurationService;
import net.polyv.service.consume.PPTVideoCompositeService;
import net.polyv.service.item.ItemConsumeDailyService;
import net.polyv.service.live.LiveDataClientService;
import net.polyv.service.transfer.ItemConsumeDailyTransferService;
import net.polyv.service.vod.VodDataClientService;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.JsonMapper;
import net.polyv.web.model.consume.input.SyncDmpToPcsVO;

/**
 * 资源消耗量的同步
 * <AUTHOR>
 * @since 2020/6/18
 */
@Service
@Slf4j
public class ItemConsumeDailyTransferServiceImpl implements ItemConsumeDailyTransferService {
    
    private static final int PAGE_SIZE = 200;
    private static final String REGEX = "^[a-z0-9A-Z]+$";

    @Autowired
    private DmpLiveUserCCVService liveUserCCVService;
    @Autowired
    private DmpLiveUserGuideDurationService guideDurationService;
    @Autowired
    private DmpLiveUserMicDurationService micDurationService;
    @Autowired
    private ItemConsumeDailyDao itemConsumeDailyDao;
    @Autowired
    private ItemConsumeDailyService itemConsumeDailyService;
    @Autowired
    private BillingItemRepository billingItemRepository;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private PPTVideoCompositeService pptVideoCompositeService;
    @Autowired
    private DingWarnRobot dingWarnRobot;
    @Autowired
    private VodDataClientService vodDataClientService;
    @Autowired
    private LiveDataClientService liveDataClientService;
    @Autowired
    private UnionUserClient unionUserClient;
    @Autowired
    private ItemConsumeDailyRepository itemConsumeDailyRepository;

    @Autowired
    private CustomerBaseRepository customerBaseRepository;
    
    //是否终止空间消耗为0的数据同步
    @Value("${pcs.config.endSpace0Consumed:false}")
    private boolean endSpace0Consumed;
    //存放小班课和研讨会档位，分辨率对应的scaleCode
    private Map<String, String> smallClassAndSeminarScaleCodeMap = new HashMap<String, String>() {{
        put("small_class_duration::1::720", ItemScaleCodeConst.SmallClass.smallcalss_1v1_720p.name());
        put("small_class_duration::1::1080", ItemScaleCodeConst.SmallClass.smallcalss_1v1_1080p.name());
        put("small_class_duration::16::720", ItemScaleCodeConst.SmallClass.smallcalss_1v16_720p.name());
        put("seminar_duration::0", ItemScaleCodeConst.Seminar.seminar_6.name());
        put("seminar_duration::1", ItemScaleCodeConst.Seminar.seminar_1v6.name());
        put("seminar_duration::2", ItemScaleCodeConst.Seminar.seminar_1v16.name());
    }};
    //存放小班课和研讨会云录制不同档位对应的scaleCode
    private Map<String, String> smallClassAndSeminarRecordScaleCodeMap = new HashMap<String, String>() {{
        put("small_class_record_duration::1",ItemScaleCodeConst.SmallClassRecord.smallclass_record_1280_720.name());
        put("small_class_record_duration::2",ItemScaleCodeConst.SmallClassRecord.smallclass_record_1920_1080.name());
        put("small_class_record_duration::3",ItemScaleCodeConst.SmallClassRecord.smallclass_record_great_1920_1080.name());
        put("seminar_record_duration::1",ItemScaleCodeConst.SeminarRecord.seminar_record_1280_720.name());
        put("seminar_record_duration::2",ItemScaleCodeConst.SeminarRecord.seminar_record_1920_1080.name());
        put("seminar_record_duration::3",ItemScaleCodeConst.SeminarRecord.seminar_record_great_1920_1080.name());
    }};
    @Override
    public void dmpConcurrenceSyncToClearing(SyncDmpToPcsVO vo) {
        Boolean needCheck = vo.getIsCheck();
        Date date = vo.getStatDate();
        Boolean isCovert = vo.getIsCover();
        int itemId = billingItemRepository.findFirstByCode(ItemCodeConst.concur_daily.getCode()).getId();
        if(needCheck && itemConsumeDailyService.isDataExist(date, itemId)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if (!isCovert) {
                log.info("concurrence data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else {
                log.info("delete data,date={},itemId={}", date, itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId, date,
                        SwitchEnum.N.getCode());
            }
        }
        //直播无延迟按天并发
        int prtcDailyitemId = billingItemRepository.findFirstByCode(ItemCodeConst.prtc_concur_daily.getCode()).getId();
        if(needCheck && itemConsumeDailyService.isDataExist(date, prtcDailyitemId)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if (!isCovert) {
                log.info("prtc_concurrence data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else {
                log.info("delete data,date={},itemId={}", date, prtcDailyitemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(prtcDailyitemId, date,
                        SwitchEnum.N.getCode());
            }
        }

    
        CustomerPdConsumePageInput searchDTO = new CustomerPdConsumePageInput();
        searchDTO.setStatAt(date);
        searchDTO.setPageSize(PAGE_SIZE);
        searchDTO.setPageIndex(1);
        Paginator<LiveConcurrenceVO> paginator = liveDataClientService.listLiveConcurrence(searchDTO);
    
    
        List<ItemConsumeDaily> itemConsumeDailyList;
        while (paginator != null && !CollectionUtils.isEmpty(paginator.getContents())) {
        
            List<String> liveUserIdList = paginator.getContents()
                    .stream()
                    .map(LiveConcurrenceVO::getUserId)
                    .filter(userId -> Objects.nonNull(userId) && userId.matches(REGEX))
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, String> userIdUnionIdMap = getCustomerIdByLiveUserIdList(liveUserIdList);
        
            itemConsumeDailyList = new ArrayList<>(paginator.getContents().size());
            for (LiveConcurrenceVO liveConcurrenceVO : paginator.getContents()) {
            
                ItemConsumeDaily daily = new ItemConsumeDaily();
                daily.setItemId(itemId);
                daily.setResourceCode(ResourceCodeConst.concurrence.name());
                daily.setStatAt(date);
                daily.setConsumed(liveConcurrenceVO.getCcv());
                daily.setGroupId(liveConcurrenceVO.getGroupId());
                daily.setCustomerId(userIdUnionIdMap.getOrDefault(liveConcurrenceVO.getUserId(), liveConcurrenceVO.getUserId()));
                daily.setIsGroupAccount(SwitchEnum.N.getCode());
                if (StringUtils.isBlank(daily.getCustomerId())) {
                    log.warn("live userId = {}, 找不到对应的unionId，跳过用量同步", liveConcurrenceVO.getUserId());
                    continue;
                }
                // 用量为0则跳过
                if (daily.getConsumed() <= 0) {
                    continue;
                }
                itemConsumeDailyList.add(daily);
                //无延迟按天并发
                ItemConsumeDaily prtcDaily = new ItemConsumeDaily();
                prtcDaily.setItemId(prtcDailyitemId);
                prtcDaily.setResourceCode(ResourceCodeConst.prtc_concurrence.name());
                prtcDaily.setStatAt(date);
                prtcDaily.setConsumed(liveConcurrenceVO.getCcv());
                prtcDaily.setGroupId(liveConcurrenceVO.getGroupId());
                prtcDaily.setCustomerId(userIdUnionIdMap.getOrDefault(liveConcurrenceVO.getUserId(), liveConcurrenceVO.getUserId()));
                prtcDaily.setIsGroupAccount(SwitchEnum.N.getCode());
                itemConsumeDailyList.add(prtcDaily);
            }
            itemConsumeDailyDao.batchAdd(itemConsumeDailyList);
            searchDTO.setPageIndex(searchDTO.getPageIndex() + 1);
            paginator = liveDataClientService.listLiveConcurrence(searchDTO);
        }
    }
    
    @Override
    public void dmpMicDurationSyncToClearing(SyncDmpToPcsVO vo) {
        Boolean needCheck = vo.getIsCheck();
        Date date = vo.getStatDate();
        Boolean isCovert = vo.getIsCover();
        List<BillingItem> list = billingItemRepository.findByCode(ItemCodeConst.mic_pd.name());
        list.forEach(item -> {
            if(needCheck && itemConsumeDailyService.isDataExist(date, item.getId())){
                //不覆盖，则跳过
                if(!isCovert){
                    log.info("mic duration data exists. date={}, scaleCode={}. skip...", date, item.getScaleCode());
                    return;
                }
                //覆盖的话，要先删除原有数据
                else{
                    //连麦分钟数同个code的有多条记录，需要根据code获取多个itemId来执行删除
                    List<BillingItem> billingItemList = billingItemRepository.findByCode(ItemCodeConst.mic_pd.getCode());
                    if(CollectionUtils.isEmpty(billingItemList)){
                        dingWarnRobot.sendWarnMsg(
                                String.format("根据code获取不到itemId【coce=%s】", ItemCodeConst.mic_pd.getCode()), "");
                        return;
                    }
                    List<Integer> itemIdList = billingItemList.stream()
                            .map(billingItem -> billingItem.getId())
                            .collect(Collectors.toList());
                    log.info("delete data,date={},itemId={}", date, itemIdList.toArray());
                    itemConsumeDailyRepository.deleteByStatDateAndItemIdListAndIsGroupAccount(date, itemIdList,
                            SwitchEnum.N.getCode());
                }
            }
            MicItemScaleGradientConst gradientLevel = MicItemScaleGradientConst.getByMicScaleCode(item.getScaleCode());
            Integer micLevel = gradientLevel.getConcurrenceGradientLevel();
            CustomerPdConsumePageInput searchDTO = new CustomerPdConsumePageInput();
            searchDTO.setStatAt(date);
            searchDTO.setPageSize(PAGE_SIZE);
            searchDTO.setPageIndex(1);
            Paginator<LiveMicDurationVO> paginator = liveDataClientService.listLiveMicDuration(searchDTO);
    
    
            List<ItemConsumeDaily> itemConsumeDailyList;
            while (paginator != null && !CollectionUtils.isEmpty(paginator.getContents())) {
        
                List<String> liveUserIdList = paginator.getContents()
                        .stream()
                        .map(LiveMicDurationVO::getUserId)
                        .filter(userId -> Objects.nonNull(userId) && userId.matches(REGEX))
                        .distinct()
                        .collect(Collectors.toList());
                Map<String, String> userIdUnionIdMap = getCustomerIdByLiveUserIdList(liveUserIdList);
        
                itemConsumeDailyList = new ArrayList<>();
                for (LiveMicDurationVO liveMicDurationVO : paginator.getContents()) {
                    //筛选出档位匹配的数据
                    if (micLevel.equals(liveMicDurationVO.getConcurrenceGradientLevel())) {
                        ItemConsumeDaily daily = new ItemConsumeDaily();
                        daily.setItemId(item.getId());
                        daily.setResourceCode(ResourceCodeConst.mic_duration.name());
                        daily.setStatAt(date);
                        daily.setConsumed(liveMicDurationVO.getDuration());
                        daily.setGroupId(liveMicDurationVO.getGroupId());
                        daily.setCustomerId(userIdUnionIdMap.getOrDefault(liveMicDurationVO.getUserId(), liveMicDurationVO.getUserId()));
                        daily.setIsGroupAccount(SwitchEnum.N.getCode());
                        if (StringUtils.isBlank(daily.getCustomerId())) {
                            log.warn("live userId = {}, 找不到对应的unionId，跳过用量同步", liveMicDurationVO.getUserId());
                            continue;
                        }
                        // 用量为0则跳过
                        if (daily.getConsumed() <= 0) {
                            continue;
                        }
                        itemConsumeDailyList.add(daily);
                    }
            
                }
                itemConsumeDailyDao.batchAdd(itemConsumeDailyList);
                searchDTO.setPageIndex(searchDTO.getPageIndex() + 1);
                paginator = liveDataClientService.listLiveMicDuration(searchDTO);
            }
        });
    }
    
    @Override
    public void dmpGuideDurationSyncToClearing(SyncDmpToPcsVO vo) {
        Boolean needCheck = vo.getIsCheck();
        Date date = vo.getStatDate();
        Boolean isCovert = vo.getIsCover();
        int itemId = billingItemRepository.findFirstByCode(ItemCodeConst.guide_pd.getCode()).getId();
        if(needCheck && itemConsumeDailyService.isDataExist(date, itemId)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("concurrence data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else {
                log.info("delete data,date={},itemId={}", date, itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId, date,
                        SwitchEnum.N.getCode());
            }
        }
    
        CustomerPdConsumePageInput searchDTO = new CustomerPdConsumePageInput();
        searchDTO.setStatAt(date);
        searchDTO.setPageSize(PAGE_SIZE);
        searchDTO.setPageIndex(1);
        Paginator<LiveGuideDurationVO> paginator = liveDataClientService.listLiveGuideDuration(searchDTO);
    
    
        List<ItemConsumeDaily> itemConsumeDailyList;
        while (paginator != null && !CollectionUtils.isEmpty(paginator.getContents())) {
        
            List<String> liveUserIdList = paginator.getContents()
                    .stream()
                    .map(LiveGuideDurationVO::getUserId)
                    .filter(userId -> Objects.nonNull(userId) && userId.matches(REGEX))
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, String> userIdUnionIdMap = getCustomerIdByLiveUserIdList(liveUserIdList);
        
            itemConsumeDailyList = new ArrayList<>(paginator.getContents().size());
            for (LiveGuideDurationVO liveGuideDurationVO : paginator.getContents()) {
            
                ItemConsumeDaily daily = new ItemConsumeDaily();
                daily.setItemId(itemId);
                daily.setResourceCode(ResourceCodeConst.guide_duration.name());
                daily.setStatAt(date);
                daily.setConsumed(liveGuideDurationVO.getPlayDuration());
                daily.setGroupId(liveGuideDurationVO.getGroupId());
                daily.setCustomerId(userIdUnionIdMap.getOrDefault(liveGuideDurationVO.getUserId(), liveGuideDurationVO.getUserId()));
                daily.setIsGroupAccount(SwitchEnum.N.getCode());
                if (StringUtils.isBlank(daily.getCustomerId())) {
                    log.warn("live userId = {}, 找不到对应的unionId，跳过用量同步", liveGuideDurationVO.getUserId());
                    continue;
                }
                // 用量为0则跳过
                if (daily.getConsumed() <= 0) {
                    continue;
                }
                itemConsumeDailyList.add(daily);
            }
            itemConsumeDailyDao.batchAdd(itemConsumeDailyList);
            searchDTO.setPageIndex(searchDTO.getPageIndex() + 1);
            paginator = liveDataClientService.listLiveGuideDuration(searchDTO);
        }
    
    }
    
    @Override
    public void pptCompositeSyncToClearing(SyncDmpToPcsVO vo) {
       Boolean needCheck = vo.getIsCheck();
        Date date = vo.getStatDate();
        Boolean isCovert = vo.getIsCover();
        int itemId = billingItemRepository.findFirstByCode(ItemCodeConst.ppt_composite_duration.getCode()).getId();
        if(needCheck && itemConsumeDailyService.isDataExist(date, itemId)){
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("concurrence data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                log.info("delete data,date={},itemId={}", date,itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId,date,SwitchEnum.N.getCode());
            }
        }
    
        List<ItemConsumeVO> list = pptVideoCompositeService.listPPTCompositeDaily(date);
        // 分批处理
        int limit = (list.size() + PAGE_SIZE - 1) / PAGE_SIZE;
        Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
            save(list.stream().skip(i * PAGE_SIZE).limit(PAGE_SIZE).collect(Collectors.toList()), itemId);
        });
    }

    @Override
    public void dmpTrafficSyncToClearing(SyncDmpToPcsVO vo) {
        Boolean needCheck = vo.getIsCheck();
        Date date = vo.getStatDate();
        Boolean isCovert = vo.getIsCover();
        int itemId = billingItemRepository.findFirstByCode(ItemCodeConst.traffic.getCode()).getId();
        boolean dataExist = this.isDataExist(date, ItemCodeConst.traffic.getCode(), needCheck);
        if (dataExist) {
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("concurrence data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                log.info("delete data,date={},itemId={}", date,itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId,date,SwitchEnum.N.getCode());
            }
        }

        VodSearchDTO searchDTO = new VodSearchDTO();
        searchDTO.setStatAt(DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
        searchDTO.setPageSize(PAGE_SIZE);
        searchDTO.setPageIndex(1);
        Paginator<VodTrafficDTO> paginator = vodDataClientService.listUserVodTraffic(searchDTO);


        List<ItemConsumeDaily> itemConsumeDailyList;
        while (paginator != null && !CollectionUtils.isEmpty(paginator.getContents())) {

            List<String> vodUserIdList = paginator.getContents().stream()
                    .map(VodTrafficDTO::getUserId)
                    .filter(userId -> userId.matches(REGEX))
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, String> userIdUnionIdMap = getUserIdUnionIdMap(vodUserIdList);

            itemConsumeDailyList = new ArrayList<>(paginator.getContents().size());
            for (VodTrafficDTO vodTrafficDTO : paginator.getContents()) {

                ItemConsumeDaily daily = new ItemConsumeDaily();
                daily.setItemId(itemId);
                daily.setResourceCode(ResourceCodeConst.traffic.name());
                daily.setStatAt(date);
                daily.setConsumed(vodTrafficDTO.getTraffic());
                daily.setGroupId(vodTrafficDTO.getGroupId());
                daily.setCustomerId(userIdUnionIdMap.getOrDefault(vodTrafficDTO.getUserId(), vodTrafficDTO.getUserId()));
                daily.setIsGroupAccount(SwitchEnum.N.getCode());
                if (StringUtils.isBlank(daily.getCustomerId())) {
                    log.warn("vod userId = {}, 找不到对应的unionId，跳过用量同步", vodTrafficDTO.getUserId());
                    continue;
                }
                // 用量为0则跳过
                if (daily.getConsumed() <= 0) {
                    continue;
                }
                itemConsumeDailyList.add(daily);
            }

            itemConsumeDailyDao.batchAdd(itemConsumeDailyList);
            searchDTO.setPageIndex(searchDTO.getPageIndex() + 1);
            paginator = vodDataClientService.listUserVodTraffic(searchDTO);
        }
    }

    private Map<String, String> getUserIdUnionIdMap(List<String> vodUserIdList) {

        if (CollectionUtils.isEmpty(vodUserIdList)) {
            return Collections.emptyMap();
        }

        CustomerSearchDTO customerSearchDTO = new CustomerSearchDTO();
        customerSearchDTO.setVodUserIds(String.join(",", vodUserIdList));
        List<UserDetail> userDetailList = new ArrayList<>();
        try{
            userDetailList = unionUserClient.listCustomer(customerSearchDTO).getData();
        } catch (Exception e){
            dingWarnRobot.sendWarnMsg("【告警，批量获取点播用户信息异常】",
                    String.format("customerSearchDTO=%s, exception=%s", JsonMapper.jsonToString(customerSearchDTO), e.getMessage()));
        }
        if(CollectionUtils.isEmpty(userDetailList)){
            List<CustomerBase> customerList = customerBaseRepository.getCustomerIdByVodUserId(vodUserIdList);
            if(CollectionUtils.isEmpty(customerList)){
                return Collections.emptyMap();
            }else{
                return customerList.stream().collect(Collectors.toMap(CustomerBase::getVodUserId, CustomerBase::getUnionId));
            }
        }
        return userDetailList.stream().collect(Collectors.toMap(UserDetail::getUserId, UserDetail::getUnionId));
    }
    
    
    @Override
    public void dmpSpaceSyncToClearing(SyncDmpToPcsVO vo) {
        Boolean needCheck = vo.getIsCheck();
        Date date = vo.getStatDate();
        Boolean isCovert = vo.getIsCover();
        int itemId = billingItemRepository.findFirstByCode(ItemCodeConst.video_space.getCode()).getId();
        boolean dataExist = this.isDataExist(date, ItemCodeConst.video_space.getCode(), needCheck);
        if (dataExist) {
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if (!isCovert) {
                log.info("concurrence data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else {
                log.info("delete data,date={},itemId={}", date, itemId);
                itemConsumeDailyRepository.deleteByItemIdAndStatAtAndIsGroupAccount(itemId, date,
                        SwitchEnum.N.getCode());
            }
        }
        Date preDate = net.polyv.util.DateUtil.getDateAfterDays(-1, date);
        // Fetch all previous day's user consume records
        List<ItemConsumeDaily> preDayItemConsumeList =
                itemConsumeDailyRepository.findByItemIdAndStatAtAndIsGroupAccount(
                itemId, preDate, SwitchEnum.N.getCode());
        //当天有消耗的用户id
        Set<String> currentDayConsumedUserIds = new HashSet<>();
        VodSearchDTO searchDTO = new VodSearchDTO();
        searchDTO.setStatAt(DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
        searchDTO.setPageSize(PAGE_SIZE);
        searchDTO.setPageIndex(1);
        Paginator<VodSpaceDTO> paginator = vodDataClientService.listUserVodSpace(searchDTO);
        List<ItemConsumeDaily> itemConsumeDailyList;
        while (paginator != null && !CollectionUtils.isEmpty(paginator.getContents())) {
        
            List<String> vodUserIdList = paginator.getContents()
                    .stream()
                    .map(VodSpaceDTO::getUserId)
                    .filter(userId -> userId.matches(REGEX))
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, String> userIdUnionIdMap = getUserIdUnionIdMap(vodUserIdList);
        
            itemConsumeDailyList = new ArrayList<>();
            for (VodSpaceDTO vodSpaceDTO : paginator.getContents()) {

                ItemConsumeDaily daily = new ItemConsumeDaily();
                daily.setItemId(itemId);
                daily.setResourceCode(ResourceCodeConst.space.name());
                daily.setStatAt(date);
                daily.setConsumed(vodSpaceDTO.getSpace());
                daily.setGroupId(vodSpaceDTO.getGroupId());
                daily.setIsGroupAccount(SwitchEnum.N.getCode());
                daily.setCustomerId(userIdUnionIdMap.getOrDefault(vodSpaceDTO.getUserId(), vodSpaceDTO.getUserId()));
                if (StringUtils.isBlank(daily.getCustomerId())) {
                    log.warn("vod userId = {}, 找不到对应的unionId，跳过空间占用同步", vodSpaceDTO.getUserId());
                    continue;
                }
                // 用量为0则跳过
                if (daily.getConsumed() <= 0) {
                    continue;
                }
    
                itemConsumeDailyList.add(daily);
            }
        
            itemConsumeDailyDao.batchAdd(itemConsumeDailyList);
            searchDTO.setPageIndex(searchDTO.getPageIndex() + 1);
            //获取当天有消耗的用户id
            Set<String> customerIdSet = itemConsumeDailyList.stream()
                    .map(ItemConsumeDaily::getCustomerId)
                    .collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(customerIdSet)) {
                currentDayConsumedUserIds.addAll(customerIdSet);
            }
            paginator = vodDataClientService.listUserVodSpace(searchDTO);
        }
        normalUserSpaceSyncPostHandle(date, itemId, currentDayConsumedUserIds, preDayItemConsumeList);
    }
    
    
    /**
     * 普通账号空间同步后置处理
     */
    private void normalUserSpaceSyncPostHandle(Date date, int itemId, Set<String> currentConsumeCustomerIds,
            List<ItemConsumeDaily> preDayItemConsumeList) {
        if (endSpace0Consumed) {
            return;
        }
        //对于彻底删除点播视频的情况，大数据不会同步截止到当前占用空间为0的记录给到pcs，会导致pcs认为客户还是有视频占用的，
        // 影响空间的可用(空间可用 = 充值空间 - 最近一条消耗记录空间)，所以这里需要补充一条空间为0的记录
        //处理方法：判断前1天有空间消耗记录，如果当天没有消耗记录的情况，当天补充一条空间为0的记录
    
        if (CollectionUtils.isEmpty(currentConsumeCustomerIds) || CollectionUtils.isEmpty(preDayItemConsumeList)) {
            return;
        }
        //遍历preDayItemConsumeList,以customerId为key，ItemConsumeDaily为value，转成Map
        Map<String, ItemConsumeDaily> preDayItemConsumeMap = preDayItemConsumeList.stream()
                .collect(Collectors.toMap(ItemConsumeDaily::getCustomerId, Function.identity()));
        //遍历preDayItemConsumeMap，如果customerIdList中不包含customerId，则补充一条空间为0的记录
        Iterator<Map.Entry<String, ItemConsumeDaily>> iterator = preDayItemConsumeMap.entrySet().iterator();
        
        List<ItemConsumeDaily> space0ItemConsumeDailyList = new ArrayList<>(); //空间消耗为0的记录
    
        while (!endSpace0Consumed && iterator.hasNext()) {
            Map.Entry<String, ItemConsumeDaily> entry = iterator.next();
            String customerId = entry.getKey();
            ItemConsumeDaily itemConsumeDaily = entry.getValue();
            // 处理key和value
            if (!currentConsumeCustomerIds.contains(customerId)) {
                ItemConsumeDaily daily = new ItemConsumeDaily();
                daily.setItemId(itemId);
                daily.setResourceCode(ResourceCodeConst.space.name());
                daily.setStatAt(date);
                daily.setConsumed(0L);
                daily.setGroupId(itemConsumeDaily.getGroupId());
                daily.setCustomerId(customerId);
                daily.setIsGroupAccount(SwitchEnum.N.getCode());
                space0ItemConsumeDailyList.add(daily);
            }
        }
        //不终止执行才需要 分批入库
        if (!endSpace0Consumed && !CollectionUtils.isEmpty(space0ItemConsumeDailyList)) {
            List<List<ItemConsumeDaily>> partitionConsumeList = Lists.partition(space0ItemConsumeDailyList, 500);
            for (List<ItemConsumeDaily> partitionConsume : partitionConsumeList) {
                itemConsumeDailyDao.batchAdd(partitionConsume);
            }
        }
    }
    
    
    @Override
    public void dmpSeminarSyncToClearing(SyncDmpToPcsVO vo) {
        Boolean needCheck = vo.getIsCheck();
        Date date = vo.getStatDate();
        Boolean isCovert = vo.getIsCover();
        boolean dataExist = this.isDataExist(date, ItemCodeConst.seminar_duration.getCode(), needCheck);
        if (dataExist) {
            //数据存在时，是否要覆盖原数据
            //不覆盖，则跳过
            if(!isCovert){
                log.info("seminar data exists. date={}. skip...", date);
                return;
            }
            //覆盖的话，要先删除原有数据
            else{
                //研讨会同个code的有多条记录，需要根据code获取多个itemId来执行删除
                List<BillingItem> billingItemList = billingItemRepository.findByCode(ItemCodeConst.seminar_duration.getCode());
                if(CollectionUtils.isEmpty(billingItemList)){
                    dingWarnRobot.sendWarnMsg(String.format("根据code获取不到itemId【coce=%s】", ItemCodeConst.seminar_duration.getCode()),
                            "");
                    return;
                }
                List<Integer> itemIdList = billingItemList.stream().map(billingItem -> billingItem.getId()).collect(Collectors.toList());
                log.info("delete data,date={},itemId={}",date,itemIdList.toArray());
                itemConsumeDailyRepository.deleteByStatDateAndItemIdListAndIsGroupAccount(date,itemIdList, SwitchEnum.N.getCode());
            }
        }

        SmallClassAndSeminarDTO searchDTO = new SmallClassAndSeminarDTO();
        searchDTO.setStatAt(date.getTime());
        searchDTO.setPageSize(PAGE_SIZE);
        searchDTO.setPageIndex(1);
        Paginator<SeminarVO> paginator = liveDataClientService.listUserSeminarDuration(searchDTO);


        List<ItemConsumeDaily> itemConsumeDailyList;
        while (paginator != null && !CollectionUtils.isEmpty(paginator.getContents())) {

            List<String> liveUserIdList = paginator.getContents().stream()
                    .map(SeminarVO::getUserId)
                    .filter(userId -> userId.matches(REGEX))
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, String> userIdUnionIdMap = getCustomerIdByLiveUserIdList(liveUserIdList);

            itemConsumeDailyList = new ArrayList<>(paginator.getContents().size());
            for (SeminarVO seminarVO : paginator.getContents()) {
                String key = ItemCodeConst.seminar_duration.getCode()+"::"+seminarVO.getConcurrenceGradientLevel();
                String scaleCode = smallClassAndSeminarScaleCodeMap.get(key);
                if(StringUtils.isBlank(scaleCode)){
                    log.warn("[研讨会],key=={},获取不到scaleCode",key);
                    continue;
                }
                int itemId = billingItemRepository.findFirstByScaleCode(scaleCode).getId();
                ItemConsumeDaily daily = new ItemConsumeDaily();
                daily.setItemId(itemId);
                daily.setResourceCode(ResourceCodeConst.seminar_duration.name());
                daily.setStatAt(date);
                daily.setGroupId(seminarVO.getGroupId());
                daily.setConsumed(seminarVO.getDuration());
                daily.setIsGroupAccount(SwitchEnum.N.getCode());
                daily.setCustomerId(userIdUnionIdMap.getOrDefault(seminarVO.getUserId(), seminarVO.getUserId()));
                if (StringUtils.isBlank(daily.getCustomerId())) {
                    log.warn("live userId = {}, 找不到对应的unionId，跳过空间占用同步", seminarVO.getUserId());
                    continue;
                }
                // 用量为空或者小于等于0则跳过
                if (Objects.isNull(daily.getConsumed()) || daily.getConsumed() <= 0) {
                    continue;
                }
    
                itemConsumeDailyList.add(daily);
            }

            itemConsumeDailyDao.batchAdd(itemConsumeDailyList);
            searchDTO.setPageIndex(searchDTO.getPageIndex() + 1);
            paginator = liveDataClientService.listUserSeminarDuration(searchDTO);
        }
    }
    /**
     * 判断用量表数据是否存在
     */
    private boolean isDataExist(Date date, String itemCode, boolean needCheck){
        int itemId = billingItemRepository.findFirstByCode(itemCode).getId();
        return needCheck && itemConsumeDailyService.isDataExist(date, itemId);
    }

    /**
     * 保存进pcs的item_consume_daily中
     * @param list
     * @param itemId
     * @param <T>
     */
    private <T extends ConsumeBaseEntity> void save(List<T> list, int itemId) {
        List<String> liveUserIds = list.stream()
                .map(ConsumeBaseEntity::getUserId)
                .filter(userId -> userId.matches(REGEX))
                .distinct()
                .collect(Collectors.toList());
    
        Map<String, String> liveUnionIdMap = getCustomerIdByLiveUserIdList(liveUserIds);
    
        List<ItemConsumeDaily> targetList;
        targetList = list.stream()
                .map(source -> buildItemConsumeDaily(source, itemId, liveUnionIdMap))
                .filter(Objects::nonNull)
                .filter(item -> StringUtils.length(item.getCustomerId()) == 10)
                .collect(Collectors.toList());
        try {
            itemConsumeDailyDao.batchAdd(targetList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            //跳过处理，钉钉告警
            dingWarnRobot.sendWarnMsg(String.format("批量将每日用量到数据库抛异常【itemId=%s】", itemId),
                    e.getMessage());
            log.error("itemId={},异常跳过的用户：{}", itemId, list.stream().map(T::getUserId).collect(Collectors.joining()));
        }
    }
    
    /**
     * 构建 itemConsumeDaily对象
     * @param obj
     * @param itemId
     * @param liveUnionIdMap
     * @return
     */
    private ItemConsumeDaily buildItemConsumeDaily(ConsumeBaseEntity obj, int itemId,
            Map<String, String> liveUnionIdMap) {
        ItemConsumeDaily target = new ItemConsumeDaily();
        target.setItemId(itemId);
        if (!liveUnionIdMap.containsKey(obj.getUserId())) {
            log.error("live userid cannot find unionId, live userid={}", obj.getUserId());
            target.setCustomerId(obj.getUserId());
        } else {
            target.setCustomerId(liveUnionIdMap.get(obj.getUserId()));
        }
        target.setIsGroupAccount(SwitchEnum.N.getCode());
        if (obj instanceof LiveUserCCV) {
            LiveUserCCV source = (LiveUserCCV) obj;
            target.setStatAt(source.getStatAt());
            target.setConsumed(Long.valueOf(source.getCsCcvValue()));
            target.setResourceCode(ResourceCodeConst.concurrence.name());
            target.setGroupId(source.getGroupId());
            return target;
        }
        
        if (obj instanceof LiveDbUserMicDurationDaily) {
            LiveDbUserMicDurationDaily source = (LiveDbUserMicDurationDaily) obj;
            target.setStatAt(source.getStatAt());
            target.setConsumed(source.getDuration());
            target.setResourceCode(ResourceCodeConst.mic_duration.name());
            target.setGroupId(source.getGroupId());
            return target;
        }
        
        if (obj instanceof LiveUserGuideDurationDaily) {
            LiveUserGuideDurationDaily source = (LiveUserGuideDurationDaily) obj;
            target.setStatAt(source.getStatAt());
            target.setConsumed(Long.valueOf(source.getPlayDuration()));
            target.setResourceCode(ResourceCodeConst.guide_duration.name());
            target.setGroupId(source.getGroupId());
            return target;
        }
        
        if(obj instanceof ItemConsumeVO){
            ItemConsumeVO source = (ItemConsumeVO) obj;
            target.setStatAt(source.getStatAt());
            target.setConsumed(source.getConsume());
            target.setResourceCode(source.getResourceCodeConst().name());
            return target;
        }
        
        return null;
    }

    /**
     * 根据直播用户 Id 列表获取 unionId 列表
     * @param liveUserIdList
     * @return
     */
    private Map<String,String> getCustomerIdByLiveUserIdList(List<String> liveUserIdList){
        Map<String, String> userIdUnionIdMap = customerService.getLiveUnionIdMap(liveUserIdList);
        //如果接口返回为空，就再查一次本地的 customer_base表做备份策略
        if(null == userIdUnionIdMap || userIdUnionIdMap.isEmpty()){
            List<CustomerBase> customerList = customerBaseRepository.getCustomerIdByLiveUserId(liveUserIdList);
            if(!CollectionUtils.isEmpty(customerList)){
                userIdUnionIdMap = customerList.stream().collect(Collectors.toMap(CustomerBase::getLiveUserId, CustomerBase::getUnionId));
            }
        }
        return userIdUnionIdMap;
    }
    
}
