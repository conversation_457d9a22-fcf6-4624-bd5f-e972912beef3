package net.polyv.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import net.polyv.service.impl.bill.BillingClearingConfigService;
import net.polyv.service.item.UnivalenceItemService;
import net.polyv.service.settlement.SettlementConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.annotation.SystemAuditLog;
import net.polyv.common.CommonResult;
import net.polyv.constant.auditlog.AuditLogEvent;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.item.ItemDefaultScaleConst;
import net.polyv.constant.itemsetting.CustomerBillingItemSettingIsActiveConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.CustomerBillingItemSettingRepository;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.model.data.salesopportunities.BillingItemDO;
import net.polyv.model.data.salesopportunities.ExtObjectDO;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.CustomerBillingItemSetting;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.modules.pcs.api.stereotype.SalesOpportunitiesStatusConst;
import net.polyv.service.CacheService;
import net.polyv.service.CustomerBillingItemSettingService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.bill.RecalculateBillService;
import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.itemsetting.data.CustomerBillingItemManualVO;
import net.polyv.web.model.itemsetting.input.CustomerBillingItemRatioRequest;
import net.polyv.web.model.itemsetting.input.CustomerBillingItemSettingSaveInputVO;

/**
 * 用户-计费项-设置表操作的service接口的实现
 * <AUTHOR>
 * @since 2020/5/13
 */
@Slf4j
@Service
public class CustomerBillingItemSettingServiceImpl implements CustomerBillingItemSettingService {
    //国内分钟数超码率倍率
    @Value("${pcs.billing-item.china-pd-over-rate:50}")
    private int chinaPdOverRate;
    
    //海外分钟数超码率倍率
    @Value("${pcs.billing-item.inter-pd-over-rate:125}")
    private int interPdOverRate;
    
    // 无延迟国内分钟数超码率倍率
    @Value("${pcs.billing-item.prtc-china-pd-over-rate:50}")
    private int prtcChinaPdOverRate;
    
    // 无延迟海外分钟数超码率倍率
    @Value("${pcs.billing-item.prtc-inter-pd-over-rate:125}")
    private int prtcInterPdOverRate;
    @Autowired
    private CustomerBillingItemSettingRepository customerBillingItemSettingRepository;
    
    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    
    @Autowired
    private BillingItemRepository billingItemRepository;
    
    @Autowired
    private RecalculateBillService recalculateBillService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private SettlementConfigService settlementConfigService;

    @Autowired
    private UnivalenceItemService univalenceItemService;

    @Autowired
    private BillingClearingConfigService billingClearingConfigService;

    // 720分辨率的1v6连麦分钟数默认单价
    @Value("${pcs.billing-item.mic-duration-1v6-univalence:18000}")
    private int micDuration1V6Univalence;

    // 1v17连麦分钟数默认倍率
    @Value("${pcs.billing-item.mic-duration-1v17-ratio:300}")
    private int micDuration1V17Ratio;

    // 360分辨率的1v6连麦分钟数默认单价
    @Value("${pcs.billing-item.mic-duration-1v6-360-univalence:4500}")
    private int micDuration1V6And360Univalence;

    // 1080分辨率的1v6连麦分钟数默认单价
    @Value("${pcs.billing-item.mic-duration-1v6-1080-univalence:36000}")
    private int micDuration1V6And1080Univalence;

    @Transactional
    @SystemAuditLog(event = AuditLogEvent.update_univalence, needSetOperaId = true)
    @Override
    public void saveCustomerBillingItemSetting(CustomerBillingItemSettingSaveInputVO inputVO) {
        // 根据salesOpportunitiesId查询的销售记录
        Optional<SalesOpportunities> salesOpportunitiesOptional = salesOpportunitiesRepository.findByIdAndCustomerId(
                inputVO.getSalesOpportunitiesId(), inputVO.getCustomerId());
        SalesOpportunities salesOpportunities = salesOpportunitiesOptional.orElseThrow(() -> {
            log.error("根据salesOpportunitiesId与customerId查询的销售记录不存在，salesOpportunitiesId={}， customerId={}",
                    inputVO.getSalesOpportunitiesId(), inputVO.getCustomerId());
            return new ClearingSystemRuntimeException(
                    "根据salesOpportunitiesId与customerId查询的销售记录不存在，salesOpportunitiesId=" +
                            inputVO.getSalesOpportunitiesId() + "，customerId=" + inputVO.getCustomerId());
        });

        // 获取customerId
        String customerId = salesOpportunities.getCustomerId();

        // 解析ext字段，转换成ExtObjectDO对象
        String ext = salesOpportunities.getExt();
        ExtObjectDO extObjectDO = JSON.parseObject(ext, ExtObjectDO.class);
        log.info("根据salesOpportunitiesId=[{}],对应的ext字段解析后的ExtObjectDO=[{}]", inputVO.getSalesOpportunitiesId(),
                JSON.toJSON(extObjectDO));
        // 获取解析的新修改单价信息数据
        List<BillingItemDO> updatedItemList = extObjectDO.getBillingItemDOList();

        // 处理要保存的用户计费项设置数据
        List<CustomerBillingItemSetting> updatedItemSettingList = this.transferToCustomerBillingItemSettingList(inputVO,
                salesOpportunities, updatedItemList);
        // 获取要保存的用户计费项设置数据的所有itemId
        List<Integer> updatedItemIdList = updatedItemSettingList.stream()
                .map(CustomerBillingItemSetting::getItemId)
                .collect(Collectors.toList());

        // 保存用户修改的用户计费项设置数据
        try {
            // 将客户之前修改相应的itemId计费项的isActive设置为0无效
            customerBillingItemSettingRepository.updateIsActiveByCustomerIdAndItemIdList(customerId, updatedItemIdList,
                    CustomerBillingItemSettingIsActiveConst.is_not_active.getStatus());
            // 保存新修改的单价
            customerBillingItemSettingRepository.saveAll(updatedItemSettingList);

            // 设置ext字段的调整单价的生效时间
            extObjectDO.setAdjustBillEffectiveTime(inputVO.getEffectiveTime());
            extObjectDO.setEffectiveTime(inputVO.getEffectiveTime());
            extObjectDO.setOpenTime(inputVO.getEffectiveTime());

            SalesOpportunities updateSalesOpportunities = new SalesOpportunities();
            updateSalesOpportunities.setId(salesOpportunities.getId());
            // 更新销售机会的状态为已关联状态
            updateSalesOpportunities.setStatus(SalesOpportunitiesStatusConst.associate.getStatus());
            // 更新销售机会的ext字段的调整单价的生效时间
            updateSalesOpportunities.setExt(JSON.toJSONString(extObjectDO));
            salesOpportunitiesRepository.updateById(updateSalesOpportunities, false, false);
        } catch (Exception e) {
            log.error("更新用户计费项设置失败", e);
            throw new ClearingSystemRuntimeException("更新用户计费项设置失败，异常信息：" + e.getMessage());
        }

        // 单价生效日期小于当天，则需要调账，进行重新结算（单价生效日期等于当天不作处理）
        if (inputVO.getEffectiveTime().before(DateUtil.beginOfDay(new Date()))) {
            // 将并发相关的计费项remove走，这部分并发的计费项不需要调账
            List<BillingItem> concurBillingItemList = billingItemRepository.findAllByCodeIn(
                    Arrays.asList(ItemCodeConst.concur_monthly.getCode(), ItemCodeConst.concur_daily.getCode()
                    ,ItemCodeConst.prtc_concur_daily.getCode(),ItemCodeConst.prtc_concur_monthly.getCode()
                    ));
            List<Integer> concurItemIdList = concurBillingItemList.stream()
                    .map(BillingItem::getId)
                    .distinct()
                    .collect(Collectors.toList());
            updatedItemList.removeIf(billingItemDO -> concurItemIdList.contains(billingItemDO.getItemId()));

            // 重新结算用量
            recalculateBillService.recalculateConsumeBill(inputVO, customerId, updatedItemList);
            // 重新结算金额
            recalculateBillService.recalculateAmountBill(inputVO, customerId, updatedItemList);
        }
        // 清理使用限制缓存
        cacheService.cleanPcsCache(customerId);
        cacheService.cleanLiveRestrictPcsResult(customerId);
        cacheService.cleanVodUserCache(customerId);
    
        // 修改用户状态为正常
        customerService.setStatusNormal(customerId);
    }
    
    @Override
    public Map<String, Integer> getOverCodeRateItemRatio() {
        Map<String, Integer> itemRatios = new HashMap<>();
        itemRatios.put(ItemCodeConst.china_pd_over_rate.getCode(), chinaPdOverRate);
        itemRatios.put(ItemCodeConst.inter_pd_over_rate.getCode(), interPdOverRate);
        itemRatios.put(ItemCodeConst.prtc_china_pd_over_rate.getCode(), prtcChinaPdOverRate);
        itemRatios.put(ItemCodeConst.prtc_inter_pd_over_rate.getCode(), prtcInterPdOverRate);
        return itemRatios;
    }

    @Override
    public void updateOverCodeRateItemRatio(List<String> unionIds, Map<String, Integer> itemRatios)
            throws ClearingSystemException {
        if (CollectionUtils.isEmpty(itemRatios)) {
            itemRatios = Maps.newHashMap();
        }
        itemRatios.putAll(this.getOverCodeRateItemRatio());
        for (Map.Entry<String, Integer> entry : itemRatios.entrySet()) {
            String itemCode = entry.getKey();
            Integer ratio = entry.getValue();
            
            CustomerBillingItemRatioRequest request = new CustomerBillingItemRatioRequest();
            request.setCustomerIdList(unionIds);
            request.setItemCode(itemCode);
            request.setRatio(ratio);
            // Update customer billing item ratio
            this.updateCustomerBillingItemRatio(request);
            
        }
    }
    
    @Transactional
    @SystemAuditLog(event = AuditLogEvent.update_univalence, needSetOperaId = true)
    @Override
    public CommonOperateResultVO saveCustomerBillingItemManualSetting(CustomerBillingItemManualVO inputVO) {
        
        // 处理要保存的用户计费项设置数据
        List<CustomerBillingItemSetting> updatedItemSettingList = this.transferToCustomerBillingItemSettingList(
                inputVO);
        // 获取要保存的用户计费项设置数据的所有itemId
        List<Integer> updatedItemIdList = updatedItemSettingList.stream()
                .map(CustomerBillingItemSetting::getItemId)
                .collect(Collectors.toList());
        
        // 保存用户修改的用户计费项设置数据
        try {
            // 将客户之前修改相应的itemId计费项的isActive设置为0无效
            customerBillingItemSettingRepository.updateIsActiveByCustomerIdAndItemIdList(inputVO.getCustomerId(),
                    updatedItemIdList, CustomerBillingItemSettingIsActiveConst.is_not_active.getStatus());
            // 保存新修改的单价
            customerBillingItemSettingRepository.saveAll(updatedItemSettingList);

        } catch (Exception e) {
            log.error("更新用户计费项设置失败", e);
            throw new ClearingSystemRuntimeException("更新用户计费项设置失败，异常信息：" + e.getMessage());
        }

        // 清理使用限制缓存
        cacheService.cleanLiveRestrictPcsResult(inputVO.getCustomerId());
        cacheService.cleanVodUserCache(inputVO.getCustomerId());

        // 修改用户状态为正常
        customerService.setStatusNormal(inputVO.getCustomerId());
        
        return CommonOperateResultVO.success() ;
    }
    
    /**
     * 处理要保存的用户计费项设置数据：将数据组装到CustomerBillingItemSetting实例中
     *
     * @param inputVO            请求参数
     * @return 组装好的用户计费项设置数据
     */
    private List<CustomerBillingItemSetting> transferToCustomerBillingItemSettingList(CustomerBillingItemManualVO inputVO) {
        List<CustomerBillingItemSetting> list = new ArrayList<>();
        for (BillingItemDO item : inputVO.getItemList()) {
            BillingItem defaultItem = billingItemRepository.findFirstByCode(item.getCode());
            if (Objects.isNull(defaultItem)) {
                continue;
            }
            CustomerBillingItemSetting customerBillingItemSetting = new CustomerBillingItemSetting();
            if(inputVO.getEffectiveTime() != null) {
                customerBillingItemSetting.setEffectiveTime(new Date(inputVO.getEffectiveTime()));
            }
            customerBillingItemSetting.setOperaId(inputVO.getOperaId());
            customerBillingItemSetting.setCustomerId(inputVO.getCustomerId());
            customerBillingItemSetting.setBaseItemId(item.getBaseItemId());
            customerBillingItemSetting.setItemId(item.getItemId());
            // 计费项的单价需要除以单位换算值得到基础的单价
            customerBillingItemSetting.setUnivalence(item.getUnivalence() / defaultItem.getUnivalenceUnitConversion());
            customerBillingItemSetting.setScaleLte(
                    item.getScaleLte() == null ? ItemDefaultScaleConst.default_scale_max.getScale() : item.getScaleLte());
            customerBillingItemSetting.setScaleGte(
                    item.getScaleGte() == null ? ItemDefaultScaleConst.default_scale_min.getScale() :
                            item.getScaleGte());
            customerBillingItemSetting.setRatio(item.getRatio());
            customerBillingItemSetting.setIsActive(CustomerBillingItemSettingIsActiveConst.is_active.getStatus());
            customerBillingItemSetting.setUnivalenceUnitConversion(defaultItem.getUnivalenceUnitConversion());
            customerBillingItemSetting.setCreateUserId(inputVO.getCreateUserId());
            int isResourcePoint = billingClearingConfigService.isResourcePointType(inputVO.getCustomerId(),defaultItem.getResourceCode());
            customerBillingItemSetting.setIsResourcePoint(isResourcePoint);
            list.add(customerBillingItemSetting);
        }
        return list;
    }

    /**
     * 处理要保存的用户计费项设置数据：将数据组装到CustomerBillingItemSetting实例中
     *
     * @param inputVO            请求参数
     * @param salesOpportunities 销售机会记录
     * @param updatedItemList    要修改的单价信息数据
     * @return 组装好的用户计费项设置数据
     */
    private List<CustomerBillingItemSetting> transferToCustomerBillingItemSettingList(
            CustomerBillingItemSettingSaveInputVO inputVO, SalesOpportunities salesOpportunities,
            List<BillingItemDO> updatedItemList) {
        List<CustomerBillingItemSetting> list = new ArrayList<>();
        for (BillingItemDO item : updatedItemList) {
            BillingItem defaultItem = billingItemRepository.findFirstByCode(item.getCode());
            if (Objects.isNull(defaultItem)) {
                continue;
            }
            CustomerBillingItemSetting customerBillingItemSetting = new CustomerBillingItemSetting();
            // 使用日志操作记录切面返回的operaId
            customerBillingItemSetting.setOperaId(inputVO.getOperaId());
            customerBillingItemSetting.setEffectiveTime(inputVO.getEffectiveTime());
            customerBillingItemSetting.setCustomerId(salesOpportunities.getCustomerId());
            customerBillingItemSetting.setBaseItemId(item.getBaseItemId());
            customerBillingItemSetting.setItemId(item.getItemId());
            // 计费项的单价需要除以单位换算值得到基础的单价
            customerBillingItemSetting.setUnivalence(item.getUnivalence() / defaultItem.getUnivalenceUnitConversion());
            customerBillingItemSetting.setScaleLte(
                    item.getScaleLte() == null ? ItemDefaultScaleConst.default_scale_max.getScale() : item.getScaleLte());
            customerBillingItemSetting.setScaleGte(
                    item.getScaleGte() == null ? ItemDefaultScaleConst.default_scale_min.getScale() :
                            item.getScaleGte());
            customerBillingItemSetting.setRatio(item.getRatio());
            customerBillingItemSetting.setIsActive(CustomerBillingItemSettingIsActiveConst.is_active.getStatus());
            customerBillingItemSetting.setUnivalenceUnitConversion(defaultItem.getUnivalenceUnitConversion());
    
            if (StringUtils.isNotBlank(inputVO.getCreateUserId())) {
                customerBillingItemSetting.setCreateUserId(inputVO.getCreateUserId());
            }
            int isResourcePoint = billingClearingConfigService.isResourcePointType(salesOpportunities.getCustomerId(),defaultItem.getResourceCode());
            customerBillingItemSetting.setIsResourcePoint(isResourcePoint);

            list.add(customerBillingItemSetting);
        }
        return list;
    }
    
    /**
     * 注意！！！！这个方法只能修改有倍数关系的计费项，比如国内外海分钟数，连麦多档位
     * @param cbirRequest 请求对象
     * @return
     * @throws ClearingSystemException
     */
    @Override
    public CommonResult updateCustomerBillingItemRatio(CustomerBillingItemRatioRequest cbirRequest)
            throws ClearingSystemException {
        List<String> customerIdList = null;
        List<String> queryCustomerIdList = cbirRequest.getCustomerIdList();
        String itemCode = cbirRequest.getItemCode();
        Integer ratio = cbirRequest.getRatio();
        if (!CollectionUtils.isEmpty(queryCustomerIdList)) {
            customerIdList = queryCustomerIdList.stream()
                    .filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(customerIdList)) {
            return CommonResult.fail("updateCustomerBillingItemRatio fail. customerIdList is empty");
        }
        if (StringUtils.isEmpty(itemCode)) {
            return CommonResult.fail("updateCustomerBillingItemRatio fail. itemCode is empty");
        }
        if (Objects.isNull(ratio)) {
            return CommonResult.fail("updateCustomerBillingItemRatio fail. ratio is null");
        }
        // 分段收费类别不支持设置
        if (itemCode.equals(ItemCodeConst.concur_daily.getCode()) ||
                itemCode.equals(ItemCodeConst.prtc_concur_monthly.getCode()) ||
                itemCode.equals(ItemCodeConst.prtc_concur_daily.getCode()) ||
                itemCode.equals(ItemCodeConst.concur_monthly.getCode())) {
            return CommonResult.fail("updateCustomerBillingItemRatio-Segmented charging is not support");
        }
        // 查询收费项id
        BillingItem bi = this.billingItemRepository.findFirstByCode(itemCode);
        if (Objects.isNull(bi)) {
            return CommonResult.fail(String.format("updateCustomerBillingItemRatio-BillingItem not found.defined itemCode[%s]", itemCode));
        }
        Integer itemId = bi.getId();
        List<CustomerBillingItemSetting> cbisList = new ArrayList<>();
        // 将客户之前修改相应的itemId计费项的isActive设置为0无效
        BillingItem baseItem = this.billingItemRepository.getById(bi.getBaseItemId());
        // 查询base_item_id（国内单价）数据
        List<CustomerBillingItemSetting> queryBaseItemSettiongList = customerBillingItemSettingRepository.queryByItemIdAndCustomerIdList(customerIdList, baseItem.getId());
        Map<String, CustomerBillingItemSetting> baseItemSettingMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(queryBaseItemSettiongList)) {
            baseItemSettingMap = queryBaseItemSettiongList.stream()
                    .filter(f->StringUtils.isNotEmpty(f.getCustomerId()))
                    .collect(Collectors.toMap(CustomerBillingItemSetting::getCustomerId, Function.identity(),(o,n)->n,HashMap::new));
        }
        for (String customerId : customerIdList) {
            customerBillingItemSettingRepository.updateIsActiveByCustomerIdAndItemIdList(customerId, Lists.newArrayList(itemId),
                    CustomerBillingItemSettingIsActiveConst.is_not_active.getStatus());
            CustomerBillingItemSetting cbis = new CustomerBillingItemSetting();
            cbis.setOperaId(0L);
            cbis.setEffectiveTime(DateUtil.beginOfDay(new Date()));
            cbis.setCustomerId(customerId);
            cbis.setItemId(itemId);
            cbis.setBaseItemId(baseItem.getId());
            CustomerBillingItemSetting currentBaseItemSetting = baseItemSettingMap.get(customerId);
            // 如果有baseItem设置的单价，要计算出海外的单价设置
            if (Objects.nonNull(currentBaseItemSetting) && Objects.nonNull(currentBaseItemSetting.getUnivalence())) {
                cbis.setUnivalence(currentBaseItemSetting.getUnivalence() * ratio / 100);
            } else {
                cbis.setUnivalence(baseItem.getDefaultUnivalence() * ratio / 100);
            }
            cbis.setScaleLte(ItemDefaultScaleConst.default_scale_max.getScale());
            cbis.setScaleGte(ItemDefaultScaleConst.default_scale_min.getScale());
            cbis.setRatio(ratio);
            cbis.setIsActive(CustomerBillingItemSettingIsActiveConst.is_active.getStatus());
            cbis.setUnivalenceUnitConversion(bi.getUnivalenceUnitConversion());
            cbis.setCreateUserId("system");
            int isResourcePoint = billingClearingConfigService.isResourcePointType(customerId,bi.getResourceCode());
            cbis.setIsResourcePoint(isResourcePoint);
            cbisList.add(cbis);
        }
        if (!CollectionUtils.isEmpty(cbisList)) {
            this.customerBillingItemSettingRepository.saveAll(cbisList);
        }
        return CommonResult.ok();
    }

    @Override
    public void updateMicPdItemSetting(List<String> customerIdList) throws ClearingSystemException {
        if (CollectionUtils.isEmpty(customerIdList)) {
            log.info("updateMicPdItemSetting fail. customerIdList is empty");
            return;
        }
        List<CustomerBillingItemSetting> cbisList = new ArrayList<>();
        for (String customerId : customerIdList) {
            customerBillingItemSettingRepository.updateIsActiveByCustomerIdAndItemIdList(customerId, Lists.newArrayList(4),
                    CustomerBillingItemSettingIsActiveConst.is_not_active.getStatus());
            customerBillingItemSettingRepository.updateIsActiveByCustomerIdAndItemIdList(customerId, Lists.newArrayList(5),
                    CustomerBillingItemSettingIsActiveConst.is_not_active.getStatus());
            cbisList.add(getMicPdCustomerBillingSetting(customerId,4,null,micDuration1V6Univalence,100));
            cbisList.add(getMicPdCustomerBillingSetting(customerId,5,4,micDuration1V6Univalence * micDuration1V17Ratio / 100,
                    micDuration1V17Ratio));
        }
        if (!CollectionUtils.isEmpty(cbisList)) {
            this.customerBillingItemSettingRepository.saveAll(cbisList);
        }
    }

    private CustomerBillingItemSetting getMicPdCustomerBillingSetting(String customerId,int itemId,Integer baseItemId,long univalence,Integer ratio){
        CustomerBillingItemSetting cbis = new CustomerBillingItemSetting();
        cbis.setOperaId(0L);
        cbis.setEffectiveTime(DateUtil.beginOfDay(new Date()));
        cbis.setCustomerId(customerId);
        cbis.setItemId(itemId);
        cbis.setBaseItemId(baseItemId);
        cbis.setUnivalence(univalence);
        cbis.setScaleLte(ItemDefaultScaleConst.default_scale_max.getScale());
        cbis.setScaleGte(ItemDefaultScaleConst.default_scale_min.getScale());
        cbis.setRatio(ratio);
        cbis.setIsActive(CustomerBillingItemSettingIsActiveConst.is_active.getStatus());
        cbis.setUnivalenceUnitConversion(1l);
        cbis.setCreateUserId("system");
        int isResourcePoint = billingClearingConfigService.isResourcePointType(customerId,"mic_duration");
        cbis.setIsResourcePoint(isResourcePoint);
        return cbis;
    }

    /**
     * 根据连麦分辨率设置对应单价
     */
    @Override
    public void updateMicPdItemSettingByResolution(String customerId,String resolution) {
        if (StringUtils.isBlank(customerId) || StringUtils.isBlank(resolution)) {
            log.info("updateMicPdItemSettingByResolution fail. customer is or resolution is empty");
            return;
        }
        if(!",180,360,720,1080,".contains("," + resolution + ",")){
            log.info("updateMicPdItemSettingByResolution fail. customerId {} resolution {} not right",customerId,resolution);
            return;
        }
        List<CustomerBillingItemSetting> cbisList = new ArrayList<>();

        customerBillingItemSettingRepository.updateIsActiveByCustomerIdAndItemIdList(customerId, Lists.newArrayList(4),
                CustomerBillingItemSettingIsActiveConst.is_not_active.getStatus());
        customerBillingItemSettingRepository.updateIsActiveByCustomerIdAndItemIdList(customerId, Lists.newArrayList(5),
                CustomerBillingItemSettingIsActiveConst.is_not_active.getStatus());

        if(resolution.equals("720")){
            cbisList.add(getMicPdCustomerBillingSetting(customerId,4,null,micDuration1V6Univalence,100));
            cbisList.add(getMicPdCustomerBillingSetting(customerId,5,4,micDuration1V6Univalence * micDuration1V17Ratio / 100,
                    micDuration1V17Ratio));
        }else if(resolution.equals("180") || resolution.equals("360")){
            cbisList.add(getMicPdCustomerBillingSetting(customerId,4,null,micDuration1V6And360Univalence,100));
            cbisList.add(getMicPdCustomerBillingSetting(customerId,5,4,micDuration1V6And360Univalence * micDuration1V17Ratio / 100,
                    micDuration1V17Ratio));
        }else if(resolution.equals("1080")){
            cbisList.add(getMicPdCustomerBillingSetting(customerId,4,null,micDuration1V6And1080Univalence,100));
            cbisList.add(getMicPdCustomerBillingSetting(customerId,5,4,micDuration1V6And1080Univalence * micDuration1V17Ratio / 100,
                    micDuration1V17Ratio));
        }else{
            log.info("updateMicPdItemSettingByResolution fail. customerId {} resolution {} not right",customerId,resolution);
            return;
        }

        if (!CollectionUtils.isEmpty(cbisList)) {
            this.customerBillingItemSettingRepository.saveAll(cbisList);
        }
    }

    @Override
    public void updateCustomerItemSetting(String customerId,BillingItemDO billingItemDO,String resourceCode){
        //旧的更改为过期
        customerBillingItemSettingRepository.updateIsActiveByCustomerIdAndItemIdList(customerId, Lists.newArrayList(billingItemDO.getItemId()),
                CustomerBillingItemSettingIsActiveConst.is_not_active.getStatus());

        CustomerBillingItemSetting cbis = new CustomerBillingItemSetting();
        cbis.setOperaId(0L);
        cbis.setEffectiveTime(DateUtil.beginOfDay(new Date()));
        cbis.setCustomerId(customerId);
        cbis.setItemId(billingItemDO.getItemId());
        cbis.setBaseItemId(billingItemDO.getBaseItemId());
        cbis.setUnivalence(billingItemDO.getUnivalence());
        cbis.setScaleLte(ItemDefaultScaleConst.default_scale_max.getScale());
        cbis.setScaleGte(ItemDefaultScaleConst.default_scale_min.getScale());
        cbis.setRatio(billingItemDO.getRatio());
        cbis.setIsActive(CustomerBillingItemSettingIsActiveConst.is_active.getStatus());
        cbis.setUnivalenceUnitConversion(1l);
        cbis.setCreateUserId("system");
        int isResourcePoint = billingClearingConfigService.isResourcePointType(customerId,resourceCode);
        cbis.setIsResourcePoint(isResourcePoint);
        this.customerBillingItemSettingRepository.save(cbis);
    }
}
