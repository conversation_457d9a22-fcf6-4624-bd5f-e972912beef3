package net.polyv.service.impl.concurrence;

import static net.polyv.constant.GlobalConfigConst.Y_FLAG;

import cn.hutool.core.date.DateUtil;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.annotation.SystemAuditLog;
import net.polyv.constant.*;
import net.polyv.constant.auditlog.AuditLogEvent;
import net.polyv.constant.balance.BalanceChangeTypeConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.GroupLiveChargeType;
import net.polyv.constant.customer.LiveBillingPlanConst;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.item.OldPackItemCodeConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.constant.schdule.ScheduleJobTypeConst;
import net.polyv.dao.primary.*;
import net.polyv.dao.primary.custom.CustomerConfigDao;
import net.polyv.exception.concurrence.ConcurrenceSettingException;
import net.polyv.exception.concurrence.ConcurrenceSettingTimeIntervalException;
import net.polyv.exception.deposit.AccountDepositNoEnoughException;
import net.polyv.model.data.concurrence.BalanceChangeState;
import net.polyv.model.data.concurrence.ConcurrenceOverstepMessageDTO;
import net.polyv.model.data.deposit.BalanceChangeAmountDO;
import net.polyv.model.data.deposit.BalanceChangeResultDO;
import net.polyv.model.data.schedule.ScheduleJobSubmitDO;
import net.polyv.model.entity.primary.*;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.vo.stat.PeakConcurrenceWarnVO;
import net.polyv.rest.client.dmp.RtasClient;
import net.polyv.rest.client.live.LivePackageClient;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.model.live.LivePackageMsgVO;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.rest.service.live.LivePackageClientService;
import net.polyv.rest.util.ResponseUtil;
import net.polyv.service.BalanceOperateService;
import net.polyv.service.CacheService;
import net.polyv.service.SystemEnvService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.bill.UnPayBillService;
import net.polyv.service.concurrence.ConcurrenceSettingService;
import net.polyv.service.item.UnivalenceItemService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.service.schedule.ScheduleJobService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.SpecialSignUtils;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.account.item.CustomerItemInfoGetInputVO;
import net.polyv.web.model.bill.PayForUnpayBillInputVO;
import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.common.PageDataVO;
import net.polyv.web.model.concurrence.ConcurrenceOverstepRequest;
import net.polyv.web.model.concurrence.ConcurrenceSettingAddVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingCancelVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingCountVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingFreezeAmountCalcInputVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingFreezeAmountCalcResultVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingListVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingVO;
import net.polyv.web.model.itemsetting.input.ItemUnivalenceGetInput;
import net.polyv.web.model.resource.CleanResourceVO;

import org.apache.commons.lang.StringUtils;
import org.jsoup.helper.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 并发时段相关接口
 *
 * <AUTHOR>
 * @since 2020/5/14
 */
@Slf4j
@Service
public class ConcurrenceSettingServiceImpl implements ConcurrenceSettingService {

    @Resource
    private  CustomerConfigRepository customerConfigRepository;

    @Resource
    private  CustomerConfigDao customerConfigDao;

    @Autowired
    private LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository;

    @Autowired
    private BalanceOperateService balanceOperateService;

    @Autowired
    private BillingItemRepository billingItemRepository;

    @Autowired
    private UnivalenceItemService univalenceItemService;
    
    @Autowired
    private UnPayBillService unPayBillService;
    
    @Autowired
    private ScheduleJobService scheduleJobService;
    
    @Resource
    private LivePackageClientService livePackageClientService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private CustomerResourceService customerResourceService;
    
    @Resource
    private CacheService cacheService;
    
    @Autowired
    private DingWarnRobot dingWarnRobot;
    /**
     * 每日资源消耗DAO
     */
    @Autowired
    private ItemConsumeDailyRepository itemConsumeDailyRepository;
    @Resource
    private LiveFlowPackageInfoRepository liveFlowPackageInfoRepository;
    
    @Resource
    private LivePackageClient livePackageClient;
    @Autowired
    private GroupAccountConfigRepository groupAccountConfigRepository;
    /**
     * 可用资源快照DAO
     */
    @Autowired
    private AvailableCustomerResourceDailyRepository availableCustomerResourceDailyRepository;
    
    @Autowired
    private SystemEnvService systemEnvService;
    
    @Resource
    private RtasClient rtasClient;
    
    @Resource
    private CustomerBillingDailyRepository customerBillingDailyRepository;
    
    
    @Override
    public ResponseVO<List<PeakConcurrenceWarnVO>> getPeakInsufficientList(String date) {
        List<PeakConcurrenceWarnVO> result = new ArrayList<>();
        //获取计费方式为峰值并发的用户
        List<String> userIds = listCurrentEffectiveUserIds(net.polyv.util.DateUtil.getCurrentDay(),
                ConcurrenceModeConst.PEAK);
    
        if (CollectionUtils.isEmpty(userIds)) {
            return ResponseVO.success(Lists.newArrayList());
        }
        //这里需要获取并发包天的计费项，大数据同步过来的消耗都是包天的
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.concur_daily.getCode());
    
        //获取指定日期的客户峰值并发
        List<ItemConsumeDaily> dailyList = itemConsumeDailyRepository.findByCustomerIdInAndStatAtAndItemId(userIds,
                DateFormatUtil.parseDateNormal(date), billingItem.getId());
    
    
        if (!CollectionUtils.isEmpty(dailyList)) {
            Map<String, Long> userConusmeMap = dailyList.stream()
                    .collect(Collectors.toMap(ItemConsumeDaily::getCustomerId, ItemConsumeDaily::getConsumed));
            
            //指定日期有并发的用户列表
            List<String> consumeUserList = new ArrayList<>(userConusmeMap.keySet());
            CustomerItemInfoGetInputVO getInputVO = new CustomerItemInfoGetInputVO();
            getInputVO.setResourceCode(ResourceCodeConst.concurrence.name());
            Map<String, Long> finalUserConusmeMap = userConusmeMap;
            
            consumeUserList.forEach(userId -> {
                getInputVO.setCustomerId(userId);
                long available = customerResourceService.getCustomerCurrentAvailable(getInputVO).get(0).getAvailable();
                PeakConcurrenceWarnVO peakConcurrenceWarnVO = new PeakConcurrenceWarnVO();
                peakConcurrenceWarnVO.setUnionId(userId);
                peakConcurrenceWarnVO.setBeforeDayConcurrence(finalUserConusmeMap.getOrDefault(userId, 0L));
                peakConcurrenceWarnVO.setAvailable(available);
                result.add(peakConcurrenceWarnVO);
            });
            
        }
        return ResponseVO.success(result);
    }
    
    @Override
    public ResponseVO<List<PeakConcurrenceWarnVO>> getPeakAvailableList() {
        List<PeakConcurrenceWarnVO> result = new ArrayList<>();
        //获取计费方式为峰值并发的用户
        List<String> userIds = listCurrentEffectiveUserIds(net.polyv.util.DateUtil.getCurrentDay(),
                ConcurrenceModeConst.PEAK);
    
        if (CollectionUtils.isEmpty(userIds)) {
            return ResponseVO.success(result);
        }
        BillingItem billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.concur_peak.getCode());
        //当月1号
        Date lastMonthFirstDay = DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), 0));
        List<CustomerBillingDaily> billingDailyList =
                customerBillingDailyRepository.findByStatAtAndItemIdAndIsManualAndCustomerIdIn(
                lastMonthFirstDay, billingItem.getId(), 0, userIds);
    
        if (CollectionUtils.isEmpty(billingDailyList)) {
            return ResponseVO.success(result);
        }
    
        CustomerItemInfoGetInputVO getInputVO = new CustomerItemInfoGetInputVO();
        getInputVO.setResourceCode(ResourceCodeConst.concurrence.name());
        billingDailyList.forEach(customerBillingDaily -> {
            getInputVO.setCustomerId(customerBillingDaily.getCustomerId());
            long available = customerResourceService.getCustomerCurrentAvailable(getInputVO).get(0).getAvailable();
            PeakConcurrenceWarnVO peakConcurrenceWarnVO = new PeakConcurrenceWarnVO();
            peakConcurrenceWarnVO.setUnionId(customerBillingDaily.getCustomerId());
            peakConcurrenceWarnVO.setBillingConsume(customerBillingDaily.getOriginalItemConsumed());
            peakConcurrenceWarnVO.setAvailable(available);
            result.add(peakConcurrenceWarnVO);
        });
        return ResponseVO.success(result);
    }
    
    @Override
    public boolean isConcurrenceAvailable(String unionId, String liveUserId) throws Exception {
        
        Integer concurrenceLimit = cacheService.getConcurrenceLimit(liveUserId);
        if (Objects.isNull(concurrenceLimit)) {
            ResponseEntity<WrappedResponse<?>> response = livePackageClient.getPackageMsg(liveUserId);
            if (ResponseUtil.isErrorResponse(response)) {
                return false;
            }
            LivePackageMsgVO livePackageMsgVO = ResponseUtil.getDataFromResponse(response, LivePackageMsgVO.class);
            if (Objects.isNull(livePackageMsgVO) || Objects.isNull(livePackageMsgVO.getUserConcurrences())) {
                return false;
            }
            
            concurrenceLimit = livePackageMsgVO.getUserConcurrences();
            cacheService.setConcurrenceLimit(liveUserId, concurrenceLimit);
        }
        
        // Unlimited concurrent usage (-1 indicates no limit)
        if (concurrenceLimit == -1) {
            return true;
        }
        // Get the real-time online viewer count
        int realtimeViewerCount = rtasClient.getRealtimeViewerCountForUser(liveUserId);
        return concurrenceLimit >= realtimeViewerCount;
        
        
    }
    
    @Override
    public void endConcurrence(LiveCustomerConcurrenceSetting setting) {
        //集团账号2.0跳过此逻辑
        if (!AccountTypeEnum.GROUP2.getCode().equals(setting.getAccountType())) {
            UserMsgVO userMsgVO = UserClient.getUserByUnionId(setting.getCustomerId());
            if (Y_FLAG.equalsIgnoreCase(userMsgVO.getIsGroup())) {
                log.info("【并发套餐过期操作】客户为集团套餐，跳过处理切换套餐处理, customerId={}",
                        setting.getCustomerId());
                return;
            }
        }
        CustomerConfig customerConfig = customerConfigDao.getOrCreateCustomerConfig(setting.getCustomerId());
        
        // 恢复分钟数或者流量套餐
        String customerId = setting.getCustomerId();
        LivePackageMsgVO livePackageMsgVO = livePackageClientService.getConcurrenceEndToPackage(customerId);
        if (Objects.isNull(livePackageMsgVO) || StringUtil.isBlank(livePackageMsgVO.getBillingPlan())) {
            dingWarnRobot.sendWarnMsg("【并发结束获取上一个套餐信息失败】", String.format("setting == %s", setting));
            return;
        }
        String billingPlanCode = livePackageMsgVO.getBillingPlan();
        String startDate = livePackageMsgVO.getPackageStart();
        //这里强制处理成生效时间 = 并发结束 + 1天
        startDate = DateFormatUtil.formatDateNormal(net.polyv.util.DateUtil.getDateAfterDays(1, setting.getEndTime()));
        String endDate = livePackageMsgVO.getPackageEnd();
        //恢复分钟数套餐
        if (OldPackItemCodeConst.duration.getBillingPlan().equals(billingPlanCode)) {
            long timestamp = System.currentTimeMillis();
            String type = PackageTypeConst.duration.name();
            long duration = 0;
            long concurrences = 0L;
            long financeConcurrences = 0L;
            long packageId = 0L;
            long isUnionId = 1L;
            int needCallClearing = 0;
            int isKeepPackageId = 1;
            
            String sign = SpecialSignUtils.getAddPackageSign(timestamp);
            
            //集团账号2.0不需要同步业务系统,只需要修改group_account_config表直播套餐信息为分钟数
            //校验原来的计费类型如果为分钟数，则不打回分钟数（处理定时开通套餐时间比并发打回分钟数早的情况）
            if (needAffectProduct(customerId)) {
                if (AccountTypeEnum.NORMAL.getCode().equals(setting.getAccountType())) {
                    livePackageClient.addPackage(customerId, timestamp, sign, type, duration, concurrences,
                            financeConcurrences, startDate, endDate, packageId, isUnionId, needCallClearing,
                            isKeepPackageId);
                    log.info("【并发套餐过期操作】打回分钟数套餐, customerId={}, timestamp={}, sign={}, type={}, duration={}, " +
                                    "concurrences={}, " + "financeConcurrences={}," +
                                    "startDate={}, endDate={}, packageId={}, isUnionId={}, needCallClearing={}, " +
                                    "isKeepPackageId={}", customerId, timestamp, sign, type, duration, concurrences,
                            financeConcurrences, startDate, endDate, packageId, isUnionId, needCallClearing,
                            isKeepPackageId);
                } else if (AccountTypeEnum.GROUP2.getCode().equals(setting.getAccountType())) {
                    GroupAccountConfig groupAccountConfig = groupAccountConfigRepository.findByGroupId(setting.getCustomerId()).orElse(null);
                    if (Objects.nonNull(groupAccountConfig)) {
                        groupAccountConfig.setLiveConcurrenceType(type);
                        groupAccountConfig.setGroupLiveBillingPlan(GroupLiveChargeType.MINUTES.getValue());
                        groupAccountConfig.setLiveStart(new Date().getTime());
                        groupAccountConfig.setLiveEnd(DateFormatUtil.dateStrToLong(endDate));
                        groupAccountConfigRepository.save(groupAccountConfig);
                    }
                }
                
            }
            customerConfig.getConfig().setLivePrevDurationPackStart(setting.getStartTime().getTime());
            customerConfig.getConfig().setLivePrevDurationPackEnd(setting.getEndTime().getTime());
        }
        
        //恢复直播流量套餐
        else if (OldPackItemCodeConst.live_flow.getBillingPlan().equals(billingPlanCode)) {
            List<LiveFlowPackageInfo> list = liveFlowPackageInfoRepository.findByCustomerId(customerId);
            LiveFlowPackageInfo liveFlowPackageInfo;
            if (CollectionUtils.isEmpty(list)) {
                liveFlowPackageInfo = new LiveFlowPackageInfo();
                liveFlowPackageInfo.setCustomerId(customerId);
                liveFlowPackageInfo.setPackageId("");
                liveFlowPackageInfo.setPackageName("");
                liveFlowPackageInfo.setStartDate(DateFormatUtil.parseDateStr(startDate));
                liveFlowPackageInfo.setEndDate(DateFormatUtil.parseDateStr(endDate));
                liveFlowPackageInfo.setIsLiveFlowBilling(1);
                liveFlowPackageInfo.setCreateTime(new Date());
            } else {
                liveFlowPackageInfo = list.get(0);
                liveFlowPackageInfo.setStartDate(DateFormatUtil.parseDateStr(startDate));
                liveFlowPackageInfo.setEndDate(DateFormatUtil.parseDateStr(endDate));
                liveFlowPackageInfo.setIsLiveFlowBilling(1);
                liveFlowPackageInfo.setUpdateTime(new Date());
            }
            //添加直播流量套餐信息表
            liveFlowPackageInfoRepository.save(liveFlowPackageInfo);
            
            customerConfig.getConfig().setLivePrevFlowPackStart(setting.getStartTime().getTime());
            customerConfig.getConfig().setLivePrevFlowPackEnd(setting.getEndTime().getTime());
        }
        // 保存上一次的套餐信息
        String billingPlan;
        if (ConcurrenceModeConst.DAILY == setting.getMode()) {
            billingPlan = LiveBillingPlanConst.daily.name();
        } else if (ConcurrenceModeConst.MONTHLY == setting.getMode()) {
            billingPlan = LiveBillingPlanConst.monthly.name();
        }else if (ConcurrenceModeConst.PRTC_MONTHLY == setting.getMode()) {
            billingPlan = LiveBillingPlanConst.prtc_monthly.name();
        }else if (ConcurrenceModeConst.PRTC_DAILY == setting.getMode()) {
            billingPlan = LiveBillingPlanConst.prtc_daily.name();
        } else {
            billingPlan = LiveBillingPlanConst.peak.name();
        }
        customerConfig.getConfig().setLivePrevBillingPlan(billingPlan);
        customerConfig.getConfig().setLivePrevPackageStart(setting.getStartTime().getTime());
        customerConfig.getConfig().setLivePrevPackageEnd(setting.getEndTime().getTime());
        customerConfig.setUpdateTime(new Date());
        customerConfigDao.saveCustomerConfig(customerConfig);
        
        if (LiveBillingPlanConst.peak.name().equals(billingPlan)) {
            CleanResourceVO cleanResourceVO = CleanResourceVO.builder()
                    .resourceCode(ResourceCodeConst.concurrence.name())
                    .customerId(customerId)
                    .build();
            customerResourceService.cleanItemResource(cleanResourceVO);
        }
        // 清理使用限制的缓存
        cacheService.cleanLiveRestrictPcsResult(customerId);
        // 清理直播用户缓存
        cacheService.cleanLiveUserCache(setting.getCustomerId());
    }
    
    private boolean needAffectProduct(String customerId) {
        return systemEnvService.isProdEnv() || (systemEnvService.isTestAllowUser(customerId));
    }
    
    @SystemAuditLog(event = AuditLogEvent.add_concurrence_setting, needSetOperaId = false)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonOperateResultVO add(ConcurrenceSettingAddVO addVO)
            throws ConcurrenceSettingException, AccountDepositNoEnoughException {
        
        // 前置处理数据
        this.doCalcFreezeAmount(addVO);
        
        // 执行检查
        this.checkValid(addVO);

        // 冻结余额
        BalanceChangeResultDO reduceAmountResultDO = this.freezeBalanceAmount(addVO);

        if (log.isInfoEnabled()) {
            log.info("冻结余额结果：{}, 请求参数 {}", JSON.toJSONString(reduceAmountResultDO), addVO);
        }

        // 保存设置
        LiveCustomerConcurrenceSetting setting = this.saveConcurrenceSetting(addVO, reduceAmountResultDO);

        //提交定时开通任务
        if(Objects.nonNull(setting) && isBusinessOpenEffectiveInTheFuture(setting.getStartTime())){
            submitJob(setting);
        }
        // 成功
        return CommonOperateResultVO.success();
    }
    /**
     * 是否在未来开通业务
     *
     * @param businessOpenTime
     * @return
     */
    private boolean isBusinessOpenEffectiveInTheFuture(Date businessOpenTime) {

        Long effectiveTime = businessOpenTime.getTime();

        // 设置了生效时间,并且生效时间大于等于明日0点,则为预约生效
        return effectiveTime != null && effectiveTime >= DateUtil.beginOfDay(DateUtil.tomorrow()).getTime();
    }
    private void submitJob(LiveCustomerConcurrenceSetting setting){
        ScheduleJobSubmitDO submitDO = new ScheduleJobSubmitDO();
        submitDO.setCustomerId(setting.getCustomerId());
        submitDO.setEffectiveTime(cn.hutool.core.date.DateUtil.beginOfDay(setting.getStartTime()));
        submitDO.setType(ScheduleJobTypeConst.concurrence_deposit_with_amount);
        submitDO.setExt(setting);
        submitDO.setCreateUserId(setting.getCustomerId());
        submitDO.setPriority(0);
        int jobId = scheduleJobService.submitJob(submitDO);
        log.info("提交定时开通业务的任务，任务ID = {},该笔开通将于{}生效", jobId,submitDO.getEffectiveTime());
    }
    @SystemAuditLog(event = AuditLogEvent.cancel_concurrence_setting, needSetOperaId = false)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonOperateResultVO cancel(ConcurrenceSettingCancelVO cancelVO) {

        // 获取并发时段设置
        LiveCustomerConcurrenceSetting setting = this.getSetting(cancelVO);

        // 前置检查
        this.cancelPreCheck(cancelVO, setting);

        // 解冻余额
        this.unFreezeBalanceAmount(setting);

        // 偿还待支付
        PayForUnpayBillInputVO inputVO = PayForUnpayBillInputVO.builder()
                .customerId(cancelVO.getCustomerId())
                .build();
        unPayBillService.payForUnPayBill(inputVO);

        // 取消并发时段
        this.cancelConcurrenceSetting(cancelVO, setting);

        // 返回成功
        return CommonOperateResultVO.success();
    }

    /**
     * 取消预检查
     *
     * @param cancelVO 取消入参
     * @param setting  并发时段
     */
    private void cancelPreCheck(ConcurrenceSettingCancelVO cancelVO, LiveCustomerConcurrenceSetting setting) {

        // 只能取消未开始的并发时段
        if (DateUtil.compare(setting.getStartTime(), DateUtil.date()) <= 0) {
            throw new ConcurrenceSettingException("已经开始的并发时段不能取消, cancelVO=" + cancelVO);
        }

    }

    /**
     * 根据取消入参获取VO
     *
     * @param cancelVO 取消入参
     * @return 并发时段实体
     */
    private LiveCustomerConcurrenceSetting getSetting(ConcurrenceSettingCancelVO cancelVO) {

        // 获得并发时段实体
        LiveCustomerConcurrenceSetting.LiveCustomerConcurrenceSettingKey id
                = new LiveCustomerConcurrenceSetting.LiveCustomerConcurrenceSettingKey();
        id.setCustomerId(cancelVO.getCustomerId());
        id.setStartTime(new Date(cancelVO.getStartTime()));
        Optional<LiveCustomerConcurrenceSetting> settingOptional = liveCustomerConcurrenceSettingRepository.findById(id);
        if (!settingOptional.isPresent()) {
            throw new ConcurrenceSettingException("找不到对应并发时段, input=" + cancelVO);
        }
        return settingOptional.get();
    }

    /**
     * 解冻冻结余额
     *
     * @param setting 并发时段
     */
    private void unFreezeBalanceAmount(LiveCustomerConcurrenceSetting setting) {

        // 余额解冻
        BalanceChangeAmountDO changeAmountDO = new BalanceChangeAmountDO();
        changeAmountDO.setCustomerId(setting.getCustomerId());
        changeAmountDO.setReduceType(BalanceChangeTypeConst.UNFREEZE);
        changeAmountDO.setBalanceChangeState(setting.getFreezeState());
        BalanceChangeState state = changeAmountDO.getBalanceChangeState();
        changeAmountDO.setAmount(state.getDeposit() + state.getDonate() + state.getCredit());
        changeAmountDO.setLiveCustomerConcurrenceSetting(setting);

        BalanceChangeResultDO resultDO = this.balanceOperateService.unFreeze(changeAmountDO);

        if (log.isInfoEnabled()) {
            log.info("解冻冻结余额完成，结果：{}", JSON.toJSONString(resultDO));
        }
    }

    /**
     * 取消并发时段设置
     *
     * @param cancelVO 取消入参
     * @param setting  并发时段
     */
    private void cancelConcurrenceSetting(ConcurrenceSettingCancelVO cancelVO, LiveCustomerConcurrenceSetting setting) {
        this.liveCustomerConcurrenceSettingRepository.delete(setting);
    }

    @Override
    public PageDataVO<ConcurrenceSettingVO> list(ConcurrenceSettingListVO listVO) {
        // 列表
        Page<LiveCustomerConcurrenceSetting> page =
                this.liveCustomerConcurrenceSettingRepository.findByCustomerIdAndStartTimeAfter(
                        listVO.getCustomerId(), new Date(), PageRequest.of(listVO.getPage() - 1, listVO.getPageSize(), Sort.by("startTime").ascending()));

        // 组装
        return PageDataVO.<ConcurrenceSettingVO>builder()
                .pageNumber(page.getNumber() + 1)
                .pageSize(page.getSize())
                .totalPages(page.getTotalPages())
                .totalItems(page.getTotalElements())
                .contents(page.get().map(this::transferSettingVO).collect(Collectors.toList()))
                .build();
    }

    /**
     * 转换vo
     *
     * @param setting 并发时段实体
     * @return 并发时段VO
     */
    private ConcurrenceSettingVO transferSettingVO(LiveCustomerConcurrenceSetting setting) {

        // 并发时段VO
        ConcurrenceSettingVO settingVO = new ConcurrenceSettingVO();
        settingVO.setConcurrence(setting.getConcurrence());
        settingVO.setStartTime(setting.getStartTime().getTime());
        settingVO.setEndTime(setting.getEndTime().getTime());
        settingVO.setMode(setting.getMode());
        settingVO.setDays(setting.getDays());
        settingVO.setCreateTime(setting.getCreateTime().getTime());
        boolean isMonth = setting.getMode() == ConcurrenceModeConst.MONTHLY || setting.getMode() == ConcurrenceModeConst.PRTC_MONTHLY;
        settingVO.setQuantity(setting.getDays() / (isMonth ? 30 : 1));
        BalanceChangeState freezeState = setting.getFreezeState();
        settingVO.setFreezeAmount(freezeState.getDeposit() + freezeState.getDonate() + freezeState.getCredit());
        Date now = new Date();
        if (setting.getStartTime().after(now)) {
            settingVO.setState(ConcurrenceStateConst.UN_ACTIVE.getValue());
        } else if (setting.getEndTime().before(now)) {
            settingVO.setState(ConcurrenceStateConst.EXPIRED.getValue());
        } else {
            settingVO.setState(ConcurrenceStateConst.ACTIVE.getValue());
        }
        return settingVO;
    }

    @Override
    public ConcurrenceSettingFreezeAmountCalcResultVO calcFreezeAmount(ConcurrenceSettingFreezeAmountCalcInputVO inputVO) {

        // 执行检查
        this.checkValid(inputVO);

        // 计算冻结金额
        this.doCalcFreezeAmount(inputVO);

        ConcurrenceSettingFreezeAmountCalcResultVO resultVO = new ConcurrenceSettingFreezeAmountCalcResultVO();
        resultVO.setAmount(inputVO.getFreezeAmount());
        resultVO.setUnivalence(inputVO.getUnivalence());
        return resultVO;
    }

    @Override
    public ConcurrenceSettingCountVO count(CustomerInfoGetInputVO inputVO) {

        Date nowDate = new Date();

        long effectiveCount = liveCustomerConcurrenceSettingRepository
                .countByCustomerIdAndStartTimeLessThanEqualAndEndTimeGreaterThanAndIsOldPackage(inputVO.getCustomerId(), nowDate, nowDate, OldConcurrencePackageConst.NO);

        long waitEffectiveCount = liveCustomerConcurrenceSettingRepository
                .countByCustomerIdAndStartTimeAfterAndIsOldPackage(inputVO.getCustomerId(), nowDate, OldConcurrencePackageConst.NO);

        ConcurrenceSettingCountVO countVO = new ConcurrenceSettingCountVO();
        countVO.setEffectiveCount(effectiveCount);
        countVO.setWaitEffectiveCount(waitEffectiveCount);
        countVO.setValid(effectiveCount + waitEffectiveCount > 0);
        return countVO;
    }

    @Override
    public List<ConcurrenceSettingVO> listAll(String customerId, Date startDate, Date endDate) {
        List<LiveCustomerConcurrenceSetting> list =
                liveCustomerConcurrenceSettingRepository.findSettingByCustomerIdAndDate(
                        customerId, startDate, endDate);
        return list.stream().map(this::transferSettingVO).collect(Collectors.toList());
    }

    @Override
    public List<String> listCurrentEffectiveUserIds(Date currentDay, Integer mode) {
        return liveCustomerConcurrenceSettingRepository.findCurrentEffectiveSettings(currentDay, mode)
                .stream()
                .map(LiveCustomerConcurrenceSetting::getCustomerId)
                .collect(Collectors.toList());
    }

    /**
     * 计算冻结金额
     *
     * @param addVO 添加VO
     */
    private void doCalcFreezeAmount(ConcurrenceSettingAddVO addVO) {

        // 项目id
        Integer itemId = this.findItemId(addVO);
        addVO.setItemId(itemId);

        // 需冻结金额
        long unitPrice = this.calcUnitPrice(addVO);

        log.info("计算得到需冻结金额为：{}", unitPrice);
        boolean isMonth = addVO.getMode() == ConcurrenceModeConst.MONTHLY || addVO.getMode() == ConcurrenceModeConst.PRTC_MONTHLY;
        addVO.setFreezeAmount(unitPrice * addVO.getConcurrence() *
                (addVO.getDays() / (isMonth ? 30 : 1)));
        addVO.setUnivalence(unitPrice);
    }

    /**
     * 计算计费项单价
     *
     * @param addVO 计算参数
     * @return 计费项实际单价
     */
    private long calcUnitPrice(ConcurrenceSettingAddVO addVO) {
        ItemUnivalenceGetInput input = ItemUnivalenceGetInput.builder()
                .customerId(addVO.getCustomerId())
                .itemId(addVO.getItemId())
                .itemConsume(addVO.getConcurrence()).build();
        return univalenceItemService.getItemUnivalence(input);
    }

    /**
     * 通过项目code查找计费项id
     *
     * @param addVO 添加参数
     * @return 计费项id
     */
    private Integer findItemId(ConcurrenceSettingAddVO addVO) {
        BillingItem item = billingItemRepository.findFirstByCode(addVO.getItemCode().getCode());
        return item.getId();
    }

    /**
     * 检查添加时段参数是否合法
     *
     * @param addVO 添加时段参数
     * @throws ConcurrenceSettingException     并发时段时段异常
     * @throws AccountDepositNoEnoughException 账户余额不足
     */
    private void checkValid(ConcurrenceSettingAddVO addVO)
            throws ConcurrenceSettingException, AccountDepositNoEnoughException {

        // 开始生效时间需大于今日
        if (DateUtil.compare(addVO.getStartTime(), DateUtil.endOfDay(DateUtil.date())) <= 0) {
            throw new ConcurrenceSettingException("开始生效时间需大于今日");
        }

        // 余额充值
        if (addVO.getFreezeAmount() != null && addVO.getFreezeAmount() > 0) {
            BalanceChangeAmountDO amountDO = new BalanceChangeAmountDO();
            amountDO.setCustomerId(addVO.getCustomerId());
            amountDO.setReduceType(BalanceChangeTypeConst.FREEZE);
            amountDO.setAmount(addVO.getFreezeAmount());
            boolean balanceEnough = balanceOperateService.checkCurrentBalanceEnoughReduce(amountDO);
            if (!balanceEnough) {
                log.debug("余额不足");
                throw new AccountDepositNoEnoughException();
            }
        }

        // 并发时段冲突
        Date startTime = addVO.getStartTime();
        long conflictRow = liveCustomerConcurrenceSettingRepository.checkSettingConflict(
                addVO.getCustomerId(), startTime, DateUtil.offsetDay(startTime, addVO.getDays() - 1));
        if (conflictRow > 0L) {
            log.debug("并发时段冲突");
            throw new ConcurrenceSettingTimeIntervalException();
        }

    }

    /**
     * 冻结账户余额
     *
     * @param addVO 添加参数
     * @return 冻结结果
     * @throws AccountDepositNoEnoughException 账户余额不足
     */
    private BalanceChangeResultDO freezeBalanceAmount(ConcurrenceSettingAddVO addVO) throws AccountDepositNoEnoughException {

        BalanceChangeAmountDO amountDO = new BalanceChangeAmountDO();
        amountDO.setCustomerId(addVO.getCustomerId());
        amountDO.setReduceType(BalanceChangeTypeConst.FREEZE);
        amountDO.setAmount(addVO.getFreezeAmount());
        BalanceChangeResultDO resultDO = balanceOperateService.reduce(amountDO);

        if (OperateResultConst.SUCCESS != resultDO.getResult()) {
            log.debug("冻结金额失败, 原因={}", resultDO.getReason());
            throw new AccountDepositNoEnoughException();
        }

        return resultDO;
    }

    /**
     * 保存并发时段设置
     *
     * @param addVO                添加参数
     * @param reduceAmountResultDO 余额冻结情况
     */
    private LiveCustomerConcurrenceSetting saveConcurrenceSetting(ConcurrenceSettingAddVO addVO, BalanceChangeResultDO reduceAmountResultDO) {
        LiveCustomerConcurrenceSetting setting = new LiveCustomerConcurrenceSetting();

        setting.setCustomerId(addVO.getCustomerId());
        setting.setStartTime(addVO.getStartTime());
        setting.setEndTime(DateUtil.offsetDay(setting.getStartTime(), addVO.getDays() - 1));
        setting.setConcurrence(addVO.getConcurrence());
        setting.setItemId(addVO.getItemId());
        setting.setMode(addVO.getMode());
        setting.setDays(addVO.getDays());

        // 余额使用状态
        setting.setFreezeState(new BalanceChangeState(reduceAmountResultDO.getDepositAmount(),
                reduceAmountResultDO.getDonateAmount(), reduceAmountResultDO.getCreditAmount()));

        return liveCustomerConcurrenceSettingRepository.save(setting);
    }

    @Override
    public PageDataVO<ConcurrenceSettingVO> page(ConcurrenceSettingListVO listVO) {
        Page<LiveCustomerConcurrenceSetting> page = liveCustomerConcurrenceSettingRepository.page(listVO.getCustomerId(),
                PageRequest.of(listVO.getPage() - 1, listVO.getPageSize()));
        // 组装
        return PageDataVO.<ConcurrenceSettingVO>builder()
                .pageNumber(page.getNumber() + 1)
                .pageSize(page.getSize())
                .totalPages(page.getTotalPages())
                .totalItems(page.getTotalElements())
                .contents(page.get().map(this::transferSettingVO).collect(Collectors.toList()))
                .build();
    }


    /**
     * POP-599
     * <p>
     * 统计昨日并发
     * 机制一：超并发提醒收费
     * <p>
     * 客户实际并发大于并发套餐预设并发
     * 客户实际并发大于峰值并发中剩余并发
     *
     * @param coq 参数
     * @return 提醒实体类
     */
    @Override
    public List<ConcurrenceOverstepMessageDTO> concurrenceOverstepRemind(ConcurrenceOverstepRequest coq) {
        List<ConcurrenceOverstepMessageDTO> tempConcurrenceOverstepList = new ArrayList<>();
        // 查询用户昨日并发使用量数据 Start
        int icdPageNum = 0; // 从0开始
        int icdPageSize = 100;
        int icdTotalPage = 0;
        int loopCount = 0;
        int maxLoopCount = 1000;
        Page<ItemConsumeDaily> icdPagingResult;
        // 查询日期：昨天
        Date specifyDate = null;
        try {
            String inputSpecifyDate = coq.getSpecifyDate();
            if (StringUtils.isNotEmpty(inputSpecifyDate)) {
                specifyDate = DateUtils.parseDate(inputSpecifyDate, "yyyy-MM-dd");
            } else {
                specifyDate = org.apache.commons.lang3.time.DateUtils.truncate(
                        org.apache.commons.lang3.time.DateUtils.addDays(DateUtil.parseDate(DateUtil.now()), -1), Calendar.DATE);
            }
        } catch (ParseException e) {
            log.error(this.getClass().getName(), "concurrenceOverstepRemind-parseDate error");
        }
        List<String> codeList = Lists.newArrayList(ItemCodeConst.concur_daily.getCode(),
                ItemCodeConst.concur_monthly.getCode(), ItemCodeConst.concur_peak.getCode()
        ,ItemCodeConst.prtc_concur_monthly.getCode(), ItemCodeConst.prtc_concur_daily.getCode()
        );
        // 查询出并发对应的item_id(item_consume_daily没有resource_code没有索引)
        List<BillingItem> itemCodeList = this.billingItemRepository.findAllByCodeIn(codeList);
        if (Objects.isNull(itemCodeList) || itemCodeList.size() < 1) {
            log.error(this.getClass().getName(), String.format("concurrenceOverstepRemind " +
                    "billingItemRepository.findAllByCodeIn[%s] error", JSON.toJSON(codeList)));
            return tempConcurrenceOverstepList;
        }
        List<Integer> itemIdList = itemCodeList.stream().map(BillingItem::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        do {
            icdPagingResult = this.itemConsumeDailyRepository.querySpecifiedDateConsumeData(specifyDate, itemIdList,
                    PageRequest.of(icdPageNum++, icdPageSize, Sort.by("customerId").ascending()));
            // 如果没有分页数据，则退出循环
            if (icdPagingResult.getNumberOfElements() == 0) {
                return tempConcurrenceOverstepList;
            }
            if (icdTotalPage == 0) {
                icdTotalPage = icdPagingResult.getTotalPages();
            }
            List<ItemConsumeDaily> icdResultList = icdPagingResult.getContent();
            if (icdResultList.size() > 0) {
                List<ConcurrenceOverstepMessageDTO> currentMessageDTOList = assembleMessageData(icdResultList, specifyDate);
                tempConcurrenceOverstepList.addAll(currentMessageDTOList);
            }
        } while (icdPageNum < icdTotalPage && loopCount++ < maxLoopCount);
        // 查询用户昨日并发使用量数据 End
        return formatConcurrenceOverstepMessageResult(tempConcurrenceOverstepList, specifyDate);
    }

    @Override
    public void startConcurrence(LiveCustomerConcurrenceSetting setting, int syncClearing) {
        log.info("【并发时段生效】 setting={},syncClearing={}", setting, syncClearing);
        LivePackageMsgVO msgVO = livePackageClientService.getLivePackageMsg(setting.getCustomerId());
        if (msgVO != null) {
            // 保存上一次的直播套餐信息
            livePackageClientService.savePreLivePackageMsg(setting, msgVO);
        }
    
        this.addLivePackage(setting, syncClearing);
    
    
        // 清理使用限制的缓存
        cacheService.cleanLiveRestrictPcsResult(setting.getCustomerId());
        // 清理直播用户缓存
        cacheService.cleanLiveUserCache(setting.getCustomerId());
    }
    /**
     * 修改用户为直播套餐
     * @param setting 生效的并发时段
     */
    private void addLivePackage(LiveCustomerConcurrenceSetting setting,int needCallClearing) {

        String customerId = setting.getCustomerId();
        long timestamp = System.currentTimeMillis();
        String type = "";
        if(ConcurrenceModeConst.DAILY == setting.getMode()){
            type = PackageTypeConst.daily.name();
        }
        if(ConcurrenceModeConst.PRTC_MONTHLY == setting.getMode()){
            type = PackageTypeConst.prtc_monthly.name();
        }
        if(ConcurrenceModeConst.PRTC_DAILY == setting.getMode()){
            type = PackageTypeConst.prtc_daily.name();
        }
        if(ConcurrenceModeConst.MONTHLY == setting.getMode()){
            type = PackageTypeConst.monthly.name();
        }
        long duration = 0L;
        long concurrences = setting.getConcurrence();
        long financeConcurrences = setting.getConcurrence();
        String startDate = DateFormatUtil.formatDateNormal(setting.getStartTime());
        String endDate = DateFormatUtil.formatDateNormal(setting.getEndTime());
        long packageId = 0L;
        long isUnionId = 1L;
        //int needCallClearing = 0;
        int isKeepPackageId = 1;

        Map<String, String> map = new HashMap<>(11);
        map.put("customerId", customerId);
        map.put("timestamp", String.valueOf(timestamp));
        map.put("type", type);
        map.put("duration", String.valueOf(duration));
        map.put("concurrences", String.valueOf(concurrences));
        map.put("financeConcurrences", String.valueOf(financeConcurrences));
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("packageId", String.valueOf(packageId));
        map.put("isUnionId", String.valueOf(isUnionId));
        map.put("syncClearing", String.valueOf(needCallClearing));
        String sign = SpecialSignUtils.getAddPackageSign(timestamp);

        livePackageClient.addPackage(customerId, timestamp, sign, type, duration, concurrences, financeConcurrences,
                startDate, endDate, packageId, isUnionId, needCallClearing, isKeepPackageId);

    }
    
    
    /**
     * 组装超并发基础信息数据
     *
     * @param icdResultList 结果集
     * @param specifyDate   查询日期
     * @return 组装结果
     */
    private List<ConcurrenceOverstepMessageDTO> assembleMessageData(List<ItemConsumeDaily> icdResultList, Date specifyDate) {
        List<ConcurrenceOverstepMessageDTO> currentMessageDTOList = new ArrayList<>();
        // 用户昨日使用量Map
        Map<String, ItemConsumeDaily> customerConsumeMap = icdResultList.stream().filter(f -> StringUtils.isNotEmpty(f.getCustomerId()))
                .collect(Collectors.toMap(ItemConsumeDaily::getCustomerId, Function.identity(), (o, n) -> n, HashMap::new));
        List<String> customerIdList = icdResultList.stream().map(ItemConsumeDaily::getCustomerId)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        // 查询用户并发套餐预设量
        Map<String, LiveCustomerConcurrenceSetting> lccsMap = new HashMap<>();
        List<LiveCustomerConcurrenceSetting> lccsResultList = this.liveCustomerConcurrenceSettingRepository
                .pagingConcurrencePackageData(specifyDate, customerIdList);
        if (Objects.nonNull(lccsResultList) && lccsResultList.size() > 0) {
            lccsMap = lccsResultList.stream().filter(f -> StringUtils.isNotEmpty(f.getCustomerId()))
                    .collect(Collectors.toMap(LiveCustomerConcurrenceSetting::getCustomerId,
                            Function.identity(),
                            (o, n) -> n, HashMap::new));
        }

        // 查询用户并发剩余量
        Map<String, Long> acrdMap = new HashMap<>();
        List<AvailableCustomerResourceDaily> acrdResultList = this.availableCustomerResourceDailyRepository.queryAvailableResourceData(ResourceCodeConst.concurrence.name(),
                specifyDate, customerIdList);
        if (Objects.nonNull(acrdResultList) && acrdResultList.size() > 0) {
            acrdMap = acrdResultList.stream().filter(f -> StringUtils.isNotEmpty(f.getCustomerId()))
                    .collect(Collectors.toMap(AvailableCustomerResourceDaily::getCustomerId, AvailableCustomerResourceDaily::getAvailable,
                            (o, n) -> n, HashMap::new));
        }
        // 处理数据
        for (Map.Entry<String, ItemConsumeDaily> entry : customerConsumeMap.entrySet()) {
            String currentCustomerId = entry.getKey();
            ItemConsumeDaily currentIcd = entry.getValue();
            Long expendVal = currentIcd.getConsumed();
            // 并发套餐预设值超量判断
            LiveCustomerConcurrenceSetting currentLccs = lccsMap.get(currentCustomerId);
            Long lccsThreshold = null;
            if (Objects.nonNull(currentLccs)) {
                lccsThreshold = currentLccs.getConcurrence();
            }
            // -1为无限制套餐
            if (Objects.nonNull(lccsThreshold) && lccsThreshold.compareTo(-1L) != 0) {
                // 并发套餐预设值
                if (expendVal.compareTo(lccsThreshold) > 0) {
                    ConcurrenceOverstepMessageDTO lccsTrDTO = new ConcurrenceOverstepMessageDTO();
                    lccsTrDTO.setCustomerId(currentCustomerId);
                    lccsTrDTO.setExpendValue(expendVal);
                    lccsTrDTO.setPackageThreshold(lccsThreshold);
                    lccsTrDTO.setItemId(currentLccs.getItemId());
                    currentMessageDTOList.add(lccsTrDTO);
                }
            }
            // 峰值并发剩余量超量判断
            Long acrdPeakSpareVal = acrdMap.get(currentCustomerId);
            if (Objects.nonNull(acrdPeakSpareVal)) {
                // 并发套餐预设值
                if (expendVal.compareTo(acrdPeakSpareVal) > 0) {
                    ConcurrenceOverstepMessageDTO acrdTrDTO = new ConcurrenceOverstepMessageDTO();
                    if (Objects.nonNull(lccsThreshold)) {
                        acrdTrDTO.setPackageThreshold(lccsThreshold);
                        acrdTrDTO.setItemId(currentLccs.getItemId());
                    }
                    acrdTrDTO.setCustomerId(currentCustomerId);
                    acrdTrDTO.setExpendValue(expendVal);
                    acrdTrDTO.setPeakSpareValue(acrdPeakSpareVal);
                    currentMessageDTOList.add(acrdTrDTO);
                }
            }
        }
        return currentMessageDTOList;
    }

    /**
     * 处理结果数据
     *
     * @param comList     数据集合
     * @param specifyDate 指定日期
     * @return 处理结果
     */
    private List<ConcurrenceOverstepMessageDTO> formatConcurrenceOverstepMessageResult(List<ConcurrenceOverstepMessageDTO> comList,
                                                                                       Date specifyDate) {
        // 处理结果数据
        if (comList.size() > 0) {
            // 查询公司信息
            Map<String, UserMsgVO> userMsgMap = new HashMap<>();
            List<String> overstepCustomerIdList = comList.stream().map(ConcurrenceOverstepMessageDTO::getCustomerId).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            if (overstepCustomerIdList.size() > 0) {
                List<UserMsgVO> overstepUserList = UserClient.getUsersByUnionIds(StringUtils.join(overstepCustomerIdList, ","));
                userMsgMap = overstepUserList.stream().filter(f -> StringUtils.isNotEmpty(f.getUnionId()))
                        .collect(Collectors.toMap(UserMsgVO::getUnionId, Function.identity(), (o, n) -> n, HashMap::new));
            }
            // 查询计费项目信息
            Map<Integer, BillingItem> billingItemMap = new HashMap<>();
            List<Integer> overstepItemIdList = comList.stream().map(ConcurrenceOverstepMessageDTO::getItemId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (overstepItemIdList.size() > 0) {
                List<BillingItem> overstepItemList = this.billingItemRepository.findAllByIdIn(overstepItemIdList);
                billingItemMap = overstepItemList.stream().filter(f -> Objects.nonNull(f.getId()))
                        .collect(Collectors.toMap(BillingItem::getId, Function.identity(), (o, n) -> n, HashMap::new));
            }
            // 组装数据信息
            Iterator<ConcurrenceOverstepMessageDTO> comIt = comList.iterator();
            while (comIt.hasNext()) {
                ConcurrenceOverstepMessageDTO com = comIt.next();
                String trCustomerId = com.getCustomerId();
                UserMsgVO trUserMsg = userMsgMap.get(trCustomerId);
                // 产品要求 要过滤 isGroup = Y 的
                if (Objects.nonNull(trUserMsg) && StringUtils.isNotEmpty(trUserMsg.getIsGroup())
                        && trUserMsg.getIsGroup().trim().equals("Y")) {
                    comIt.remove();
                }
                if (Objects.nonNull(trUserMsg)) {
                    com.setCompany(trUserMsg.getCompanyName());
                    com.setAccount(trUserMsg.getEmail());
                }
                Long packageThreshold = com.getPackageThreshold();
                Integer trItemId = com.getItemId();
                BillingItem trBi = billingItemMap.get(trItemId);
                if (Objects.isNull(trBi)) {
                    continue;
                }
                if (trBi.getCode().equals(ItemCodeConst.concur_daily.getCode())) {
                    com.setBillingPlan("daily");
                }else if (trBi.getCode().equals(ItemCodeConst.prtc_concur_daily.getCode())) {
                    com.setBillingPlan("prtc_daily");
                } else if (trBi.getCode().equals(ItemCodeConst.concur_monthly.getCode())) {
                    com.setBillingPlan("monthly");
                }else if (trBi.getCode().equals(ItemCodeConst.prtc_concur_monthly.getCode())) {
                    com.setBillingPlan("prtc_monthly");
                } else if (trBi.getCode().equals(ItemCodeConst.concur_peak.getCode())) {
                    com.setBillingPlan("peak");
                }
                if (Objects.nonNull(packageThreshold)) {
                    if (packageThreshold.compareTo(-1L) == 0) {
                        com.setConcurrencePackageDesc(String.format("按 不限 %s计费", trBi.getName()));
                    } else {
                        com.setConcurrencePackageDesc(String.format("按 %d %s计费", packageThreshold, trBi.getName()));
                    }
                }
                // 实际并发值
                Long expendValue = com.getExpendValue();
                if (Objects.nonNull(packageThreshold) && packageThreshold.compareTo(-1L) != 0) {
                    com.setRemindContent(String.format("当前用户在 %s 的实际并发值 %d 大于预设并发值 %d ，请留意",
                            DateUtil.format(specifyDate, "yyyy-MM-dd"), expendValue, packageThreshold));
                } else if (Objects.nonNull(com.getPeakSpareValue())) {
                    com.setRemindContent(String.format("当前用户在 %s 的实际并发值 %d 大于剩余年峰值并发 %d ，请留意",
                            DateUtil.format(specifyDate, "yyyy-MM-dd"), expendValue, com.getPeakSpareValue()));
                }
            }
        }
        return comList;
    }
}
