package net.polyv.service.impl.examinationDonate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.logging.Logger;

import javax.annotation.Resource;

import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.ResourceAlterTypeConst;
import net.polyv.constant.group.ResourceAssignBusinessTypeEnum;
import net.polyv.dao.primary.custom.ResourceAlterationRecordDao;
import net.polyv.model.data.group.GroupResourceAssignableCalculateDTO;
import net.polyv.model.data.salesopportunities.BillingItemDO;
import net.polyv.service.CustomerBillingItemSettingService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.resource.ResourceSourceConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.billingConfig.BillingCustomerConfigRepository;
import net.polyv.dao.primary.billingConfig.BillingItemExtRepository;
import net.polyv.dao.primary.billingConfig.BillingLongestOpenTimeRepository;
import net.polyv.dao.primary.resource.ResourceAlterationRecordRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.helper.salesopportunities.billingItem.BillingItemHelper;
import net.polyv.model.data.salesopportunities.UserFunctionDO;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import net.polyv.model.entity.primary.billingconfig.BillingItemExt;
import net.polyv.model.entity.primary.billingconfig.BillingLongestOpenTimeConfig;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.pcs.api.req.AddResourceRequest;
import net.polyv.modules.pcs.api.req.AmountCalculateRequest;
import net.polyv.modules.pcs.api.req.testdonate.DonateAIResourceRequest;
import net.polyv.modules.pcs.api.req.testdonate.NumberRequest;
import net.polyv.modules.pcs.api.vo.AmountDetailVO;
import net.polyv.rest.client.live.FunctEnableClient;
import net.polyv.service.CacheService;
import net.polyv.service.billingconfig.BillingConfigService;
import net.polyv.service.business.BusinessFunctionService;
import net.polyv.service.impl.billingConfig.BillingConfigClearingEnterService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.RandomUtils;
import net.polyv.util.converter.UnitConverterUtil;
import net.polyv.web.model.cache.CleanLiveResourceCacheInputVO;
import org.springframework.util.StringUtils;

/**
 * 计费项赠送、测试 服务实现
 * <AUTHOR>
 * @date 2022/7/26 9:27
 */
@Service
@Slf4j
public class ResourceBillingItemService {
    @Resource
    private BillingCustomerConfigRepository billingCustomerConfigRepository;
    @Resource
    private ResourceLogicService resourceLogicService;
    @Resource
    private FunctEnableClient functEnableClient;
    @Resource
    private BillingConfigService billingConfigService;
    @Resource
    private BillingItemExtRepository billingItemExtRepository;
    @Resource
    private BillingConfigClearingEnterService billingConfigClearingService;
    @Resource
    private BillingLongestOpenTimeRepository billingLongestOpenTimeRepository;
    @Resource
    private BillingItemRepository billingItemRepository;
    @Resource
    private BusinessFunctionService businessFunctionService;
    
    @Value("${pcs.config.ruleUseTimeWayId}")
    private Integer ruleUseTimeWayId;
    @Value("${pcs.config.numberBillWayId}")
    private Integer numberBillWayId;
    
    //按用量+限定使用时长
    @Value("${pcs.config.usageAndUseTimeWayId:7}")
    private Integer usageAndUseTimeWayId;
    @Resource
    private BillingItemHelper billingItemHelper;
    @Resource
    private ResourceAlterationRecordRepository resourceAlterationRecordRepository;
    
    @Resource
    private CacheService cacheService;

    @Resource
    private CustomerBillingItemSettingService customerBillingItemSettingService;

    @Autowired
    private CustomerResourceService customerResourceService;

    @Resource
    private ResourceAlterationRecordDao resourceAlterationRecordDao;
    
    /**
     * 限制时间计费项测试开通
     * @param request 参数
     * @return
     * @throws ClearingSystemException
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonResult ruleUseTimeBillingItem(AddResourceRequest request) throws ClearingSystemException {
        return ruleUseTimeBillingItem(request, null);
    }
    
    /**
     * 限制时间计费项测试开通
     * @param request 参数
     * @return
     * @throws ClearingSystemException
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonResult ruleUseTimeBillingItem(AddResourceRequest request, SalesOpportunities salesOpportunities)
            throws ClearingSystemException {
        if (Objects.isNull(request) || Objects.isNull(request.getFunctionRequest())) {
            return CommonResult.fail("开通类型-功能开关-参数为空！");
        }
        //覆盖有效期->进入客户计费项配置表
        BillingCustomerConfig billingCustomerConfig = buildRuleUseTimeCustomerConfig(request, salesOpportunities);
        int daysBetween = DateUtil.getDaysBetween(DateUtil.getCurrentDay(), request.getEndDate());
        billingCustomerConfig.setRechargeValue(daysBetween + 1L);
        billingCustomerConfig.setAvailableValue(daysBetween + 1L);
        billingCustomerConfig.setResourceType(request.getResourceType());
        billingConfigService.saveConfig(billingCustomerConfig);
        functionOpen(request.getFunctionRequest().getCode(), request.getCustomerId());
    
        //清理结算缓存
        CleanLiveResourceCacheInputVO cleanInputVO = CleanLiveResourceCacheInputVO.builder()
                .customerId(request.getCustomerId())
                .resourceCodeList(Collections.singletonList(request.getFunctionRequest().getCode()))
                .build();
        cacheService.cleanPcsCache(cleanInputVO);
        return CommonResult.ok();
    }
    
    /**
     * 是否自定义计费项
     * @param itemCode
     * @return
     */
    public boolean isCustomBillingItem(String itemCode) {
        BillingItemExt billingItemExt = this.billingItemExtRepository.findByItemCode(itemCode);
        return billingItemExt != null;
    }
    
    /**
     * 数值类计费项开通测试、赠送资源（包括限定使用时长和使用量+限定使用时长）
     * @param request 参数
     * @return {@link CommonResult}
     * <AUTHOR>
     * @date 2022/7/26
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonResult numberBillItem(AddResourceRequest request, SalesOpportunities salesOpportunities)
            throws ClearingSystemException {
        if (Objects.isNull(request) || Objects.isNull(request.getNumberRequest())) {
            return CommonResult.fail("开通类型-次数-参数值为空！");
        }
        String randStr = RandomUtils.getRandStr(10);
        String contractId = Objects.isNull(salesOpportunities) ? randStr : salesOpportunities.getContractId();
        NumberRequest numberRequest = request.getNumberRequest();
        Date endDate = DateFormatUtil.parseDate(numberRequest.getExpireDate(), Constant.DATE_FORMAT_yyyy_MM_dd);
        Integer number = numberRequest.getNumber();
        BillingCustomerConfig billingCustomerConfig = buildNumberCustomerConfig(request, endDate, salesOpportunities);
        //素材库空间和流量，需要转成字节存入
        if (ItemCodeConst.material_space.getCode().equals(numberRequest.getItemCode()) ||
                ItemCodeConst.material_traffic.getCode().equals(numberRequest.getItemCode())) {
            billingCustomerConfig.setAvailableValue(UnitConverterUtil.GB2bytes(BigDecimal.valueOf(number)));
            billingCustomerConfig.setRechargeValue(UnitConverterUtil.GB2bytes(BigDecimal.valueOf(number)));
        } else {
            billingCustomerConfig.setAvailableValue(number.longValue());
            billingCustomerConfig.setRechargeValue(number.longValue());
        }
        billingCustomerConfig.setResourceType(request.getResourceType());
        billingCustomerConfig.setContractId(contractId);
        BillingItemExt billingItemExt = billingItemExtRepository.findByItemCode(numberRequest.getItemCode());
        if (Objects.nonNull(billingItemExt)) {
            billingCustomerConfig.setIsExpireReset(billingItemExt.getIsExpireReset());
        }
        billingCustomerConfig.setIsReset(PcsEnumConst.NO.getValue());
    
        //按使用量+限定使用时长计费项逻辑：只需要独立保存配置信息，不进行覆盖，不生成资源变动记录
    
        CommonResult result = CommonResult.ok();
        if (this.usageAndUseTimeWayId.equals(billingItemExt.getBillingWayId())) {
            fillConfigInfo(billingCustomerConfig);
            billingCustomerConfigRepository.save(billingCustomerConfig);
        } else {
            //覆盖更新
            billingConfigService.saveConfig(billingCustomerConfig);
            //获取合同id
            result = doOpenNumberSourceWithSalesOpportunities(request, numberRequest, endDate, contractId,
                    billingCustomerConfig.getRechargeValue(),salesOpportunities);
        }
        CleanLiveResourceCacheInputVO cleanInputVO = CleanLiveResourceCacheInputVO.builder()
                .customerId(request.getCustomerId())
                .resourceCodeList(Collections.singletonList(numberRequest.getItemCode()))
                .build();
        //清理pcs缓存
        cacheService.cleanPcsCache(cleanInputVO);
        return result;
    }
    /**
     * 填充其他计费项信息
     * @param customerConfig
     */
    private void fillConfigInfo(BillingCustomerConfig customerConfig) {
        customerConfig.setIsClearByDay(PcsEnumConst.NO.getValue());
        customerConfig.setIsDeleted(PcsEnumConst.NO.getValue());
        customerConfig.setCreateTime(new Date());
        customerConfig.setUpdateTime(new Date());
        customerConfig.setExpireTime(DateUtil.getEndOfDay(customerConfig.getExpireTime()));
        customerConfig.setOpenTime(new Date());
        BillingItem firstByCode = billingItemRepository.findFirstByCode(customerConfig.getItemCode());
        customerConfig.setResourceCode(firstByCode.getResourceCode());
    }
    
    public void donateAIBillItem(DonateAIResourceRequest request, String unionId) throws ClearingSystemException {
        String contractId = RandomUtils.getRandStr(10);
        NumberRequest numberRequest = request.getNumberRequest();
        Date endDate = DateFormatUtil.parseDate(numberRequest.getExpireDate(), Constant.DATE_FORMAT_yyyy_MM_dd);
        Integer number = numberRequest.getNumber();
        BillingItemExt billingItemExt = billingItemExtRepository.findByItemCode(
                request.getNumberRequest().getItemCode());
        
        BillingCustomerConfig billingCustomerConfig = BillingCustomerConfig.builder()
                .itemCode(request.getNumberRequest().getItemCode())
                .resourceCode(request.getNumberRequest().getItemCode())
                .openTime(DateUtil.getCurrentDay())
                .unionId(unionId)
                .billingTime(billingConfigClearingService.calculateBillTime(LocalDate.now(),
                        billingItemExt.getBillingPeriodId()))
                .billingPeriodId(billingItemExt.getBillingPeriodId())
                .billingWayId(billingItemExt.getBillingWayId())
                .expireTime(endDate)
                .isClearByDay(PcsEnumConst.YES.getValue())
                .availableValue(number.longValue())
                .rechargeValue(number.longValue())
                .resourceType(ResourceTypeEnum.DONATE.getCode())
                .contractId(contractId)
                .isReset(PcsEnumConst.NO.getValue())
                .isExpireReset(billingItemExt.getIsExpireReset())
                .build();
        //覆盖更新
        billingConfigService.saveConfig(billingCustomerConfig);
        //-->充值记录进入永久资源表
        CommonResult commonResult = resourceLogicService.saveResourceAlteration(unionId, numberRequest.getNumber(),
                numberRequest.getItemCode(), ResourceSourceConst.PERMANENT, contractId,
                net.polyv.util.DateUtil.getCurrentDay(), endDate, AccountTypeEnum.NORMAL.getCode(),
                ResourceTypeEnum.DONATE.getCode());
        if (CommonResult.isNotOk(commonResult)) {
            throw new ClearingSystemException(String.format("无法充值%s资源", numberRequest.getItemCode()));
        }
        //开通功能
        functionOpen(request.getNumberRequest().getItemCode(), unionId);
    }
    
    public void donateBillItemResource(NumberRequest numberRequest, String unionId) throws ClearingSystemException {
        donateBillItemResource(numberRequest,unionId,false);
    }

    public void donateBillItemResource(NumberRequest numberRequest, String unionId,boolean needUpdateCustomerBillingItemSet,Integer resourceType) throws ClearingSystemException {
        String contractId = RandomUtils.getRandStr(10);
        Date endDate = DateFormatUtil.parseDate(numberRequest.getExpireDate(), Constant.DATE_FORMAT_yyyy_MM_dd);
        Integer number = numberRequest.getNumber();
        BillingItemExt billingItemExt = billingItemExtRepository.findByItemCode(numberRequest.getItemCode());

        BillingCustomerConfig billingCustomerConfig = BillingCustomerConfig.builder()
                .itemCode(numberRequest.getItemCode())
                .resourceCode(numberRequest.getItemCode())
                .openTime(DateUtil.getCurrentDay())
                .unionId(unionId)
                .billingPeriodId(billingItemExt.getBillingPeriodId())
                .billingWayId(billingItemExt.getBillingWayId())
                .expireTime(endDate)
                .isClearByDay(PcsEnumConst.YES.getValue())
                .availableValue(number.longValue())
                .rechargeValue(number.longValue())
                .resourceType(resourceType)
                .contractId(contractId)
                .isReset(PcsEnumConst.NO.getValue())
                .isExpireReset(billingItemExt.getIsExpireReset())
                .build();

        if (Objects.nonNull(billingItemExt.getBillingPeriodId())) {
            billingCustomerConfig.setBillingTime(billingConfigClearingService.calculateBillTime(LocalDate.now(),
                    billingItemExt.getBillingPeriodId()));
        }
        //处理单位问题
        if (ItemCodeConst.material_space.getCode().equals(numberRequest.getItemCode()) ||
                ItemCodeConst.material_traffic.getCode().equals(numberRequest.getItemCode())) {
            billingCustomerConfig.setAvailableValue(UnitConverterUtil.GB2bytes(BigDecimal.valueOf(number)));
            billingCustomerConfig.setRechargeValue(UnitConverterUtil.GB2bytes(BigDecimal.valueOf(number)));
        }
        if (this.numberBillWayId.equals(billingItemExt.getBillingWayId())) {
            //覆盖更新
            billingConfigService.saveConfig(billingCustomerConfig);
            //-->充值记录进入永久资源表
            CommonResult commonResult = resourceLogicService.saveResourceAlteration(unionId,
                    billingCustomerConfig.getRechargeValue(), numberRequest.getItemCode(),
                    ResourceSourceConst.PERMANENT, contractId, net.polyv.util.DateUtil.getCurrentDay(), endDate,
                    AccountTypeEnum.NORMAL.getCode(), ResourceTypeEnum.DONATE.getCode());
            if (CommonResult.isNotOk(commonResult)) {
                throw new ClearingSystemException(String.format("无法充值%s资源", numberRequest.getItemCode()));
            }
            //素材库流量，增加自定义单价--后面可能要去掉
            if (ItemCodeConst.material_traffic.getCode().equals(numberRequest.getItemCode()) && needUpdateCustomerBillingItemSet) {
                BillingItem billingItem = billingItemRepository.findFirstByCode(billingCustomerConfig.getItemCode());
                BillingItemDO billingItemDO = new BillingItemDO();
                billingItemDO.setItemId(billingItem.getId());
                billingItemDO.setBaseItemId(billingItem.getBaseItemId());
                billingItemDO.setRatio(100);
                billingItemDO.setUnivalence(40000l);
                customerBillingItemSettingService.updateCustomerItemSetting(unionId,billingItemDO,numberRequest.getItemCode());
            }
        }

        if (this.usageAndUseTimeWayId.equals(billingItemExt.getBillingWayId())) {
            //获取计费项资源编码
            BillingItem firstByCode = billingItemRepository.findFirstByCode(billingCustomerConfig.getItemCode());
            billingCustomerConfig.setIsClearByDay(PcsEnumConst.NO.getValue());
            billingCustomerConfig.setExpireTime(DateUtil.getEndOfDay(billingCustomerConfig.getExpireTime()));
            billingCustomerConfig.setResourceCode(firstByCode.getResourceCode());
            billingCustomerConfig.setIsDeleted(0);
            billingCustomerConfig.setCreateTime(new Date());
            billingCustomerConfig.setUpdateTime(new Date());
            //独立添加
            billingCustomerConfigRepository.save(billingCustomerConfig);
        }

    }

    public void donateBillItemResource(NumberRequest numberRequest, String unionId,boolean needUpdateCustomerBillingItemSet) throws ClearingSystemException {
        donateBillItemResource(numberRequest,unionId,needUpdateCustomerBillingItemSet,ResourceTypeEnum.DONATE.getCode());
    }

    /**
     * 更新素材库的资源（流量和空间)的有效期为指定时间
     * @param customerId
     * @param itemCode
     * @param number
     * @param expiredDate
     * @return
     * @throws ClearingSystemException
     */
    public boolean addAndResetMaterialResource(String customerId,String itemCode,Integer number,String expiredDate,Date startDate,boolean isGroup) throws ClearingSystemException {
        if(null == startDate){
            startDate = new Date();
        }
        List<BillingCustomerConfig> list = billingCustomerConfigRepository.findCustomerUnExpirePackage(customerId,itemCode,startDate);
        if(CollectionUtils.isEmpty(list)){
            NumberRequest flowRequest = new NumberRequest();
            flowRequest.setItemCode(itemCode);
            flowRequest.setNumber(number);
            flowRequest.setExpireDate(expiredDate);
            donateBillItemResource(flowRequest, customerId,false,ResourceTypeEnum.DEPOSIT.getCode());

            if(ItemCodeConst.material_traffic.getCode().equals(itemCode)){
                List<BillingCustomerConfig> listAll = billingCustomerConfigRepository.findCustomerAndItemCode(customerId,itemCode);
                Date endDate = DateFormatUtil.parseDate(expiredDate, Constant.DATE_FORMAT_yyyy_MM_dd);
                if(!CollectionUtils.isEmpty(listAll)){
                    for(BillingCustomerConfig billingCustomerConfig : listAll){
                        //原来未过期清理过的，需要重置过期时间
                        if (new Integer(0).equals(billingCustomerConfig.getIsReset())) {
                            billingCustomerConfig.setExpireTime(DateUtil.getEndOfDay(endDate));
                        }
                        billingCustomerConfigRepository.save(billingCustomerConfig);
                    }

                    //如果过期时间不为空 && 非华为云分账号，需要根据customer+resourceCode把所有充值记录的过期时间修改
                    if(!customerResourceService.isCustomizedGroupIdByUnionId(customerId)){
                        resourceAlterationRecordDao.updateExpireDateByCustomerIdAndResourceCodeAndType(
                                endDate,customerId,itemCode, ResourceAlterTypeConst.deposit.name()
                        );
                    }
                }
            }
        }else{
            BillingItemExt billingItemExt = billingItemExtRepository.findByItemCode(itemCode);
            Date endDate = DateFormatUtil.parseDate(expiredDate, Constant.DATE_FORMAT_yyyy_MM_dd);
            Date expiredDateTime = DateUtil.getEndOfDay(endDate);
            if(ItemCodeConst.material_space.getCode().equals(itemCode)){
                for(BillingCustomerConfig billingCustomerConfig : list){
                    if(billingCustomerConfig.getExpireTime().before(expiredDateTime)){
                        billingCustomerConfig.setExpireTime(expiredDateTime);
                        billingCustomerConfigRepository.save(billingCustomerConfig);
                    }
                }
            }

            if(ItemCodeConst.material_traffic.getCode().equals(itemCode)){
                List<BillingCustomerConfig> listAll = billingCustomerConfigRepository.findCustomerAndItemCode(customerId,itemCode);
                if(!CollectionUtils.isEmpty(listAll)){
                    for(BillingCustomerConfig billingCustomerConfig : listAll){
                        //原来未过期清理过的，需要重置过期时间
                        if (new Integer(0).equals(billingCustomerConfig.getIsReset())) {
                            billingCustomerConfig.setExpireTime(expiredDateTime);
                        }
                        billingCustomerConfigRepository.save(billingCustomerConfig);
                    }
                    //如果过期时间不为空 && 非华为云分账号，需要根据customer+resourceCode把所有充值记录的过期时间修改
                    if(!customerResourceService.isCustomizedGroupIdByUnionId(customerId)){
                        resourceAlterationRecordDao.updateExpireDateByCustomerIdAndResourceCodeAndType(
                                endDate,customerId,itemCode, ResourceAlterTypeConst.deposit.name()
                        );
                    }
                }
            }
        }
        if(isGroup){
            //重算主账号剩余可分配资源
            if(ItemCodeConst.material_space.getCode().equals(itemCode)){
                log.info("=============================groupId {} itemcode {}",customerId,itemCode);
                GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                        .groupId(customerId)
                        .businessType(ResourceAssignBusinessTypeEnum.MATERIAL_SPACE.getCode())
                        .build();
                cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                        JsonMapper.jsonToString(dto));
            }
            if(ItemCodeConst.material_traffic.getCode().equals(itemCode)){
                log.info("=============================groupId {} itemcode {}",customerId,itemCode);
                GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                        .groupId(customerId)
                        .businessType(ResourceAssignBusinessTypeEnum.MATERIAL_TRAFFIC.getCode())
                        .build();
                cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                        JsonMapper.jsonToString(dto));
            }
        }
        return true;
    }
    
    /**
     * 数值类计费项开通测试、赠送资源
     * @param request 参数
     * @return {@link CommonResult}
     * <AUTHOR>
     * @date 2022/7/26
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonResult numberBillItem(AddResourceRequest request) throws ClearingSystemException {
        return numberBillItem(request, null);
    }
    
    
    public CommonResult doOpenNumberSource(AddResourceRequest request, NumberRequest numberRequest, Date endDate,
            String contractId, long deposit) throws ClearingSystemException {
        
        //-->充值记录进入永久资源表
        CommonResult commonResult = resourceLogicService.saveResourceAlteration(request.getCustomerId(), deposit,
                numberRequest.getItemCode(), ResourceSourceConst.PERMANENT, contractId,
                net.polyv.util.DateUtil.getCurrentDay(), endDate, request.getAccountType(), request.getResourceType());
        if (CommonResult.isNotOk(commonResult)) {
            throw new ClearingSystemException(String.format("无法充值%s资源", numberRequest.getItemCode()));
        }
        //开通功能
        functionOpen(request.getNumberRequest().getItemCode(), request.getCustomerId());
        return commonResult;
    }

    public CommonResult doOpenNumberSourceWithSalesOpportunities(AddResourceRequest request, NumberRequest numberRequest, Date endDate,
                                           String contractId, long deposit,SalesOpportunities salesOpportunities) throws ClearingSystemException {

        //-->充值记录进入永久资源表
        CommonResult commonResult = resourceLogicService.saveResourceAlteration(request.getCustomerId(), deposit,
                numberRequest.getItemCode(), ResourceSourceConst.PERMANENT, contractId,
                net.polyv.util.DateUtil.getCurrentDay(), endDate, request.getAccountType(), request.getResourceType());
        if (CommonResult.isNotOk(commonResult)) {
            throw new ClearingSystemException(String.format("无法充值%s资源", numberRequest.getItemCode()));
        }
        //开通功能
        if(null != salesOpportunities){
            functionOpenWithSID(request.getNumberRequest().getItemCode(), request.getCustomerId(),salesOpportunities.getId());
        }else{
            functionOpen(request.getNumberRequest().getItemCode(), request.getCustomerId());
        }

        return commonResult;
    }
    
    /**
     * 计算计费项配置化的 单价消耗
     * @param request 金额计算请求
     * @return {@link List< AmountDetailVO>}
     * <AUTHOR>
     * @date 2022/8/3
     */
    public List<AmountDetailVO> calculateAmountByBillingConfig(AmountCalculateRequest request)
            throws ClearingSystemException {

        BillingItem billingItem = billingItemHelper.getBillItem(request.getCode(), request.getBusinessType());
        if (Objects.isNull(billingItem)) {
            return new ArrayList<>();
        }
        BillingItemExt itemCode = billingItemExtRepository.findByItemCode(billingItem.getCode());
        if (Objects.isNull(itemCode)) {
            return new ArrayList<>();
        }
        //区分按次数还是按时间
        long number = checkOrGetNumberByParam(itemCode, request);
        if (number<=0){
            return  new ArrayList<>();
        }
        //设置规格
        return Lists.newArrayList(
                ResourceSpecificationServiceImpl.getAmountCostDetail(0, new BigDecimal(number + ""), billingItem, 1));
    }
    
    /**
     * 检查并获取次数
     * @param billingItemExt 计费项扩展信息
     * @param request 请求参数
     * @return {@link long}
     * <AUTHOR>
     * @date 2022/8/3
     */
    public long checkOrGetNumberByParam(BillingItemExt billingItemExt, AmountCalculateRequest request)
            throws ClearingSystemException {
        log.info("计算参数：{}",JsonUtil.beanToString(request).orElse(""));
        if (Objects.isNull(request.getResourceType())||Objects.isNull(billingItemExt.getBillingWayId())||Objects.isNull(request.getExpireDate())){
            throw new ClearingSystemException("缺少必要计算参数请检查！");
        }
        Integer resourceType = request.getResourceType();
        Integer billingWayId = billingItemExt.getBillingWayId();
        LocalDate expire = LocalDate.parse(request.getExpireDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate now = LocalDate.now();
        int overMonths = 2;
        //如果是素材库的空间和流量，测试的有效期放开到 2 年
        String itemCode = billingItemExt.getItemCode();
        if (ItemCodeConst.material_space.getCode().equals(itemCode) ||
                ItemCodeConst.material_traffic.getCode().equals(itemCode)) {
            overMonths = 24;
        }
        LocalDate end = now.plusMonths(overMonths);
        if (expire.isBefore(now)) {
            throw new ClearingSystemException("结束时间请勿小于当前时间！");
        }
        //转换时间测试资源 只能在 当天日期 到 当天日期+2个月 内选择
        if (expire.isAfter(end) && ResourceTypeEnum.TEST.getCode().equals(resourceType)) {
            throw new ClearingSystemException("结束时间请勿超出"+overMonths+"个月！");
        }
    
        // 次数计费 赠送的有效期的开始时间-结束时间不能超过该计费项的最长开通时长
        if (this.numberBillWayId.equals(billingWayId)) {
            if (ResourceTypeEnum.DONATE.getCode().equals(resourceType) &&
                    PcsEnumConst.YES.getValue() == billingItemExt.getIsExpireReset()) {
                Optional<BillingLongestOpenTimeConfig> longOpen = this.billingLongestOpenTimeRepository.findById(
                        billingItemExt.getLongestOpenTimeId());
                if (longOpen.isPresent()) {
                    BillingLongestOpenTimeConfig config = longOpen.get();
                    LocalDate endDate = net.polyv.util.DateUtil.calculateDateByConfig(now, config.getUnit(),
                            config.getLongestOpenTime(), null);
                    if (Objects.nonNull(endDate) && expire.isAfter(endDate)) {
                        throw new ClearingSystemException("结束时间请勿大于" + LocalDateTimeUtil.formatNormal(endDate));
                    }
                }
            }
            //获取次数
            return request.getNumber();
        }
        //按使用量 + 限定时长计费项，计算有效天数 * 开通量
        if (this.usageAndUseTimeWayId.equals(billingWayId)) {
            return (LocalDateTimeUtil.between(now.atStartOfDay(), expire.atStartOfDay(), ChronoUnit.DAYS) + 1) *
                    request.getNumber();
        }
        //获取的是限定时长的天数
        return LocalDateTimeUtil.between(now.atStartOfDay(), expire.atStartOfDay(), ChronoUnit.DAYS) + 1;
    }
    
    
    private void functionOpen(String code, String customerId) throws ClearingSystemException {
        BillingItem billingItem = billingItemRepository.findFirstByCode(code);
        if (Objects.isNull(billingItem)) {
            log.warn("获取计费项信息失败 code={}", code);
            return;
        }
        List<UserFunctionDO> list = businessFunctionService.functionProcess(Lists.newArrayList(billingItem), true);
        try {
            functEnableClient.enableFunctAccess(null, list, customerId, null);
            log.info("开通计费项功能成功:customerId={},code={}", customerId, code);
        } catch (Exception e) {
            log.error("功能开关计费项，开通功能失败：", e);
            String format = String.format("【计费项】开通功能失败：code=%s,customerId=%s ", code, customerId);
            throw new ClearingSystemException(format);
        }
    }

    private void functionOpenWithSID(String code, String customerId,Long sid) throws ClearingSystemException {
        BillingItem billingItem = billingItemRepository.findFirstByCode(code);
        if (Objects.isNull(billingItem)) {
            log.warn("获取计费项信息失败 code={}", code);
            return;
        }
        List<UserFunctionDO> list = businessFunctionService.functionProcess(Lists.newArrayList(billingItem), true);
        try {
            functEnableClient.enableFunctAccess(sid, list, customerId, null);
            log.info("开通计费项功能成功:customerId={},code={}", customerId, code);
        } catch (Exception e) {
            log.error("功能开关计费项，开通功能失败：", e);
            String format = String.format("【计费项】开通功能失败：code=%s,customerId=%s ", code, customerId);
            throw new ClearingSystemException(format);
        }
    }
    
    private BillingCustomerConfig buildNumberCustomerConfig(AddResourceRequest request, Date expireDate,
            SalesOpportunities salesOpportunities) {
        BillingItemExt billingItemExt = billingItemExtRepository.findByItemCode(
                request.getNumberRequest().getItemCode());
        
        BillingCustomerConfig build = BillingCustomerConfig.builder()
                .itemCode(request.getNumberRequest().getItemCode())
                .openTime(DateUtil.getCurrentDay())
                .salesUserId(request.getSaleUserId())
                .unionId(request.getCustomerId())
                .billingPeriodId(billingItemExt.getBillingPeriodId())
                .billingWayId(billingItemExt.getBillingWayId())
                .expireTime(expireDate)
                .isClearByDay(PcsEnumConst.YES.getValue())
                //   .clearStartTime(null)
                .build();
        if (Objects.nonNull(billingItemExt.getBillingPeriodId())) {
            build.setBillingTime(billingConfigClearingService.calculateBillTime(LocalDate.now(),
                    billingItemExt.getBillingPeriodId()));
        }
        return fillOrderInfo(salesOpportunities, build);
    
    }
    
    
    private BillingCustomerConfig buildRuleUseTimeCustomerConfig(AddResourceRequest request,
            SalesOpportunities salesOpportunities) {
        Date date = DateFormatUtil.parseDate(request.getFunctionRequest().getExpireDate(),
                Constant.DATE_FORMAT_yyyy_MM_dd);
        BillingCustomerConfig build = BillingCustomerConfig.builder()
                .itemCode(request.getFunctionRequest().getCode())
                .openTime(DateUtil.getCurrentDay())
                .salesUserId(request.getSaleUserId())
                .unionId(request.getCustomerId())
                .billingTime(null)
                .billingPeriodId(null)
                .billingWayId(this.ruleUseTimeWayId)
                .expireTime(date)
                .isClearByDay(PcsEnumConst.NO.getValue())
                //  .clearStartTime(null)
                .build();
        return fillOrderInfo(salesOpportunities, build);
    }
    
    private BillingCustomerConfig fillOrderInfo(SalesOpportunities salesOpportunities, BillingCustomerConfig build) {
        if (Objects.isNull(salesOpportunities)) {
            return build;
        }
        build.setSoId(salesOpportunities.getSoId());
        build.setSalesUserId(salesOpportunities.getSaleUserId());
        build.setContractId(salesOpportunities.getContractId());
        return build;
    }
    
    
}
