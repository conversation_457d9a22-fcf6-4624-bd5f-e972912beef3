package net.polyv.service.impl.examinationDonate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DecimalStyle;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import net.polyv.modules.pcs.api.req.LiveFlowRequest;
import net.polyv.modules.pcs.api.vo.*;
import net.polyv.service.settlement.SettlementConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.BusinessBillingPlanCodeEnum;
import net.polyv.constant.common.PrecisionConst;
import net.polyv.constant.customer.LiveBillingPlanConst;
import net.polyv.constant.customer.LiveBillingType;
import net.polyv.constant.deposit.AmountDepositTypeEnum;
import net.polyv.constant.deposit.AmountExpireTypeEnum;
import net.polyv.constant.examinationDonate.BusinessTypeEnum;
import net.polyv.constant.examinationDonate.LiveDailyBillingPlanEnum;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.item.ItemScaleCodeConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.GroupAccountConfigRepository;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.business.SystemDictionaryRepository;
import net.polyv.dao.primary.examinationDonate.ExaminationDonateResourceSpecificationRepository;
import net.polyv.dao.primary.examinationDonate.ExaminationDonateUserDetailRepository;
import net.polyv.dao.primary.examinationDonate.SaleExtraAmountRecordRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.helper.salesopportunities.billingItem.BillingItemHelper;
import net.polyv.model.data.examinationDonate.LiveDepositPackage;
import net.polyv.model.data.examinationDonate.VodDepositPackage;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.model.entity.primary.business.SystemDictionary;
import net.polyv.model.entity.primary.examinationDonate.ExaminationDonateResourceSpecification;
import net.polyv.model.entity.primary.examinationDonate.ExaminationDonateUserDetail;
import net.polyv.model.entity.primary.examinationDonate.SaleDepartmentBaseAmount;
import net.polyv.model.entity.primary.examinationDonate.SaleExtraAmountRecord;
import net.polyv.modules.common.stereotype.BizErrorCodeEnum;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.AddChannelsRequest;
import net.polyv.modules.pcs.api.req.AddResourceRequest;
import net.polyv.modules.pcs.api.req.AmountCalculateRequest;
import net.polyv.modules.pcs.api.req.AmountRequest;
import net.polyv.modules.pcs.api.req.GuideDurationRequest;
import net.polyv.modules.pcs.api.req.LiveDailyConcurrenceRequest;
import net.polyv.modules.pcs.api.req.LiveDurationRequest;
import net.polyv.modules.pcs.api.req.LivePeakConcurrenceRequest;
import net.polyv.modules.pcs.api.req.MicDurationRequest;
import net.polyv.modules.pcs.api.req.PackageSpecificationGetRequest;
import net.polyv.modules.pcs.api.req.PrtcRequest;
import net.polyv.modules.pcs.api.req.VodFlowRequest;
import net.polyv.modules.pcs.api.req.VodPackageRequest;
import net.polyv.modules.pcs.api.req.VodSpaceRequest;
import net.polyv.modules.pcs.api.stereotype.PCSErrorCodeEnum;
import net.polyv.modules.pcs.api.stereotype.SalesOpportunitiesStatusConst;
import net.polyv.modules.user.api.service.group.GroupAccountServiceApi;
import net.polyv.modules.user.api.service.group.GroupUserServiceApi;
import net.polyv.modules.user.api.vo.GroupAccountVO;
import net.polyv.rest.client.live.LivePackageClient;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.client.live.business.LiveInnerFinanceApiClient;
import net.polyv.rest.client.vod.business.VodBusinessOperationClient;
import net.polyv.rest.client.vod.user.PolyvApiRestTemplate;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.rest.util.ResponseUtil;
import net.polyv.service.AccountDepositService;
import net.polyv.service.CacheService;
import net.polyv.service.GroupAccountEstablishPackageService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.examinationDonate.ResourceSpecificationService;
import net.polyv.service.resource.ResourceDepositService;
import net.polyv.service.so.BusinessOperationService;
import net.polyv.util.BeanUtil;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.JsonMapper;
import net.polyv.util.MoneyUtil;
import net.polyv.util.RandomUtils;
import net.polyv.util.SpecialSignUtils;
import net.polyv.util.converter.UnitConverterUtil;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.account.DepositInputVO;
import net.polyv.web.model.cache.CleanLiveResourceCacheInputVO;
import net.polyv.web.model.examinationDonate.AvailableAmountDetailVO;
import net.polyv.web.model.examinationDonate.ResourceTypeExpireDateVO;
import net.polyv.web.model.oldpackage.ResourceDepositInputVO;

/**
 * 测试赠送套餐规格service实现类
 * <AUTHOR>
 * @since 2022/06/14
 */
@Service
@Slf4j
public class ResourceSpecificationServiceImpl implements ResourceSpecificationService {
    
    
    @Autowired
    private GroupAccountEstablishPackageService groupAccountEstablishPackageService;
    @Autowired
    private GroupAccountServiceApi groupAccountServiceApi;
    
    @Autowired
    private BusinessOperationService businessOperationService;
    
    @Value("${polyv.test.package.vod:34,36,37,38,49,122}")
    private String vodTestPackageIds;
    
    @Value("${polyv.test.package.live:4,7,8,9,14}")
    private String liveTestPackageIds;
    
    @Autowired
    private AccountDepositService accountDepositService;
    
    @Autowired
    private SystemDictionaryRepository systemDictionaryRepository;
    
    @Autowired
    private LiveInnerFinanceApiClient liveInnerFinanceApiClient;
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private ResourceDepositService resourceDepositService;
    
    @Autowired
    private GroupAccountConfigRepository groupAccountConfigRepository;
    
    @Autowired
    private VodBusinessOperationClient vodBusinessOperationClient;
    
    @Autowired
    private BillingItemRepository billingItemRepository;
    
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private LivePackageClient livePackageClient;
    
    @Autowired
    private SaleExtraAmountRecordRepository saleExtraAmountRecordRepository;
    
    @Autowired
    private ExaminationDonateResourceSpecificationRepository examinationDonateResourceSpecificationRepository;
    
    @Autowired
    private ExaminationDonateUserDetailRepository examinationDonateUserDetailRepository;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private LiveInnerFinanceApiClient liveInnerApiClient;
    
    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    
    @Autowired
    private LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository;
    
    @Resource
    private ResourceBillingItemService resourceBillingItemService;
    @Resource
    private BillingItemHelper billingItemHelper;
    @Resource
    private SettlementConfigService settlementConfigService ;
    
    //添加资源
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseVO<Object> addResource(AddResourceRequest inputVO) {
        try {
            CommonResult packageResult = CommonResult.ok();
            //打套餐
            ResponseVO<Object> checkResult = addResourceCheck(inputVO);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
            //普通账号
            if (AccountTypeEnum.NORMAL.getCode().equals(inputVO.getAccountType())) {
                packageResult = normalAccountEstablishPackage(inputVO);
            }
            //集团主账号
            if (AccountTypeEnum.GROUP2.getCode().equals(inputVO.getAccountType())) {
                packageResult = groupAccountEstablishPackage(inputVO);
            }
            
            if (CommonResult.isNotOk(packageResult)) {
                throw new ClearingSystemRuntimeException(packageResult.getMsg());
            }
            //生成打套餐明细
            CommonResult result = createPackageRecord(inputVO);
            if (CommonResult.isNotOk(result)) {
                throw new ClearingSystemRuntimeException(result.getMsg());
            }
            return ResponseVO.success();
        } catch (Exception e) {
            log.error("addResource error", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResponseVO.failure(PCSErrorCodeEnum.ADD_RESOURCE_ERROR);
        }
    }
    
    
    /**
     * 生成打套餐记录
     * @param inputVO
     * @return
     */
    private CommonResult createPackageRecord(AddResourceRequest inputVO) {
        //添加套餐规格记录
        ExaminationDonateResourceSpecification specification = new ExaminationDonateResourceSpecification();
        specification.setSpecificationDesc(inputVO.getSpecification());
        specification.setBusinessId(inputVO.getBusinessId());
        specification.setAccountType(inputVO.getAccountType());
        specification.setCreateTime(new Date());
        specification.setCustomerId(inputVO.getCustomerId());
        specification.setBusinessType(inputVO.getBusinessType());
        AmountCalculateResultVO amountCalculateResultVO = inputVO.getAmountCalculateResultVO();
        specification.setCostAmount(amountCalculateResultVO.getEstablishCostAmount()
                .multiply(new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION))
                .longValue());
        specification.setPackageExt(buildPackageExt(inputVO));
        specification.setResourceType(inputVO.getResourceType());
        specification.setRemark(inputVO.getRemark());
        specification.setIsDel(0);
        specification.setCurrentMonth(DateFormatUtil.formatDateMonth(new Date()));
        specification.setMonthLeftAmount(amountCalculateResultVO.getEstablishLeftAmount()
                .multiply(new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION))
                .longValue());
        specification.setStartDate(inputVO.getStartDate());
        specification.setEndDate(inputVO.getEndDate());
        examinationDonateResourceSpecificationRepository.save(specification);
        
        //添加套餐用户详情表
        ExaminationDonateUserDetail userDetail = new ExaminationDonateUserDetail();
        userDetail.setBusinessId(inputVO.getBusinessId());
        userDetail.setCustomerId(inputVO.getCustomerId());
        userDetail.setSaleUserId(inputVO.getSaleUserId());
        userDetail.setSaleUserName(amountCalculateResultVO.getSaleUserName());
        userDetail.setEmail(inputVO.getEmail());
        userDetail.setCompanyName(inputVO.getCompany());
        userDetail.setSaleUserDepartmentType(amountCalculateResultVO.getSaleDepartmentType());
        examinationDonateUserDetailRepository.save(userDetail);
        
        return CommonResult.ok();
        
    }
    
    private String buildPackageExt(AddResourceRequest inputVO) {
        Integer businessType = inputVO.getBusinessType();
        List<AmountDetailVO> amountCostDetails = inputVO.getAmountCalculateResultVO().getAmountCostDetails();
        if (BusinessTypeEnum.VOD_PACKAGE.getCode().equals(businessType)) {
            VodPackageRequest VodPackageRequest = inputVO.getVodPackageRequest();
            VodPackageRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(VodPackageRequest);
        }
        if (BusinessTypeEnum.VOD_SPACE.getCode().equals(businessType)) {
            VodSpaceRequest VodSpaceRequest = inputVO.getVodSpaceRequest();
            VodSpaceRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(VodSpaceRequest);
        }
        if (BusinessTypeEnum.VOD_FLOW.getCode().equals(businessType)) {
            VodFlowRequest VodFlowRequest = inputVO.getVodFlowRequest();
            VodFlowRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(VodFlowRequest);
        }
        if (BusinessTypeEnum.LIVE_FLOW.getCode().equals(businessType)) {
            LiveFlowRequest liveFlowRequest = inputVO.getLiveFlowRequest();
            liveFlowRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(liveFlowRequest);
        }
        if (BusinessTypeEnum.LIVE_DURATION.getCode().equals(businessType)) {
            LiveDurationRequest LiveDurationRequest = inputVO.getLiveDurationRequest();
            LiveDurationRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(LiveDurationRequest);
        }
        if (BusinessTypeEnum.LIVE_DAILY_TEST.getCode().equals(businessType)) {
            LiveDailyConcurrenceRequest LiveDailyConcurrenceRequest = inputVO.getLiveDailyConcurrenceRequest();
            LiveDailyConcurrenceRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(LiveDailyConcurrenceRequest);
        }

        if (BusinessTypeEnum.LIVE_PRTC_DAILY_TEST.getCode().equals(businessType)) {
            LiveDailyConcurrenceRequest LiveDailyConcurrenceRequest = inputVO.getLiveDailyConcurrenceRequest();
            LiveDailyConcurrenceRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(LiveDailyConcurrenceRequest);
        }

        if (BusinessTypeEnum.GUIDE_DURATION.getCode().equals(businessType)) {
            GuideDurationRequest GuideDurationRequest = inputVO.getGuideDurationRequest();
            GuideDurationRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(GuideDurationRequest);
        }
        //连麦分钟数获取mic_pd_7档的
        if (BusinessTypeEnum.MIC_DURATION.getCode().equals(businessType)) {
            MicDurationRequest MicDurationRequest = inputVO.getMicDurationRequest();
            MicDurationRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(MicDurationRequest);
        }
        if (BusinessTypeEnum.PRTC_AMOUNT.getCode().equals(businessType)) {
            PrtcRequest PrtcRequest = inputVO.getPrtcRequest();
            PrtcRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(PrtcRequest);
        }
        if (BusinessTypeEnum.AMOUNT.getCode().equals(businessType)) {
            AmountRequest AmountRequest = inputVO.getAmountRequest();
            AmountRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(AmountRequest);
        }
        if (BusinessTypeEnum.LIVE_PEAK_CONCURRENCE.getCode().equals(businessType)) {
            LivePeakConcurrenceRequest peakConcurrenceInputVO = inputVO.getLivePeakConcurrenceRequest();
            peakConcurrenceInputVO.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(peakConcurrenceInputVO);
        }
        if (BusinessTypeEnum.ADD_CHANNELS.getCode().equals(businessType)) {
            AddChannelsRequest AddChannelsRequest = inputVO.getAddChannelsRequest();
            AddChannelsRequest.setAmountCostDetails(amountCostDetails);
            return JsonMapper.jsonToString(AddChannelsRequest);
        }
        return "{}";
    }
    
    private AmountCalculateRequest buildAmountCalculateInputVO(AddResourceRequest inputVO) {
        AmountCalculateRequest result = new AmountCalculateRequest();
        Integer businessType = inputVO.getBusinessType();
        result.setBusinessType(businessType);
        result.setSaleUserId(inputVO.getSaleUserId());
        if (BusinessTypeEnum.VOD_PACKAGE.getCode().equals(businessType)) {
            result.setVodSpaceSize(inputVO.getVodPackageRequest().getPackageSpace());
            result.setVodFlowSize(inputVO.getVodPackageRequest().getPackageMonthFlow());
            String specificationDesc = result.getVodSpaceSize() + "G空间," + result.getVodFlowSize() + "G流量";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getVodPackageRequest().getExpireDate());
            result.setSpaceEndDate(DateFormatUtil.formatDateNormal(inputVO.getEndDate()));
            
        }
        if (BusinessTypeEnum.VOD_SPACE.getCode().equals(businessType)) {
            result.setVodSpaceSize(inputVO.getVodSpaceRequest().getVodSpace());
            String specificationDesc = result.getVodSpaceSize() + "G";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getVodSpaceRequest().getSpaceExpireDate());
            result.setSpaceEndDate(DateFormatUtil.formatDateNormal(inputVO.getEndDate()));
        }
        if (BusinessTypeEnum.VOD_FLOW.getCode().equals(businessType)) {
            result.setVodFlowSize(inputVO.getVodFlowRequest().getVodFlow());
            String specificationDesc = result.getVodFlowSize() + "G";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getVodFlowRequest().getFlowExpireDate());
        }
        if (BusinessTypeEnum.LIVE_FLOW.getCode().equals(businessType)) {
            result.setLiveFlowSize(inputVO.getLiveFlowRequest().getLiveFlow());
            String specificationDesc = result.getLiveFlowSize() + "G";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getLiveFlowRequest().getFlowExpireDate());
        }
        if (BusinessTypeEnum.LIVE_DURATION.getCode().equals(businessType)) {
            result.setLiveDuration(inputVO.getLiveDurationRequest().getDuration());
            String specificationDesc = result.getLiveDuration() + "min";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getLiveDurationRequest().getExpireDate());
        }

        if (BusinessTypeEnum.LIVE_DAILY_TEST.getCode().equals(businessType)
        || BusinessTypeEnum.LIVE_PRTC_DAILY_TEST.getCode().equals(businessType)
        ) {
            result.setDailyConcurrence(inputVO.getLiveDailyConcurrenceRequest().getConcurrence());
            String specificationDesc = result.getDailyConcurrence() + "";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(inputVO.getLiveDailyConcurrenceRequest().getLiveStartDate());
            inputVO.setEndDate(inputVO.getLiveDailyConcurrenceRequest().getLiveEndDate());
        }
        
        if (BusinessTypeEnum.GUIDE_DURATION.getCode().equals(businessType)) {
            result.setGuideDuration(inputVO.getGuideDurationRequest().getDuration());
            String specificationDesc = result.getGuideDuration() + "min";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getGuideDurationRequest().getExpireDate());
        }
        //连麦分钟数获取mic_pd_7档的
        if (BusinessTypeEnum.MIC_DURATION.getCode().equals(businessType)) {
            result.setMicDuration(inputVO.getMicDurationRequest().getDuration());
            String specificationDesc = result.getMicDuration() + "min";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getMicDurationRequest().getExpireDate());
        }
        if (BusinessTypeEnum.PRTC_AMOUNT.getCode().equals(businessType)) {
            result.setPrtcAmount(inputVO.getPrtcRequest().getAmount());
            String specificationDesc = result.getPrtcAmount() + "元";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getPrtcRequest().getExpireDate());
        }
        if (BusinessTypeEnum.AMOUNT.getCode().equals(businessType)) {
            result.setAmount(inputVO.getAmountRequest().getAmount());
            String specificationDesc = result.getAmount() + "元";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getAmountRequest().getExpireDate());
        }
        if (BusinessTypeEnum.LIVE_PEAK_CONCURRENCE.getCode().equals(businessType)) {
            result.setPeakConcurrence(inputVO.getLivePeakConcurrenceRequest().getConcurrence());
            String specificationDesc = result.getPeakConcurrence() + "";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(inputVO.getLivePeakConcurrenceRequest().getLiveStartDate());
            inputVO.setEndDate(inputVO.getLivePeakConcurrenceRequest().getLiveEndDate());
        }
        if (BusinessTypeEnum.ADD_CHANNELS.getCode().equals(businessType)) {
            result.setChannels(inputVO.getAddChannelsRequest().getChannels());
            String specificationDesc = result.getChannels() + "";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
        }
        if (BusinessTypeEnum.RESOURCE_POINT.getCode().equals(businessType)) {
            result.setAmount(inputVO.getAmountRequest().getAmount());
            String specificationDesc = result.getAmount() + "点";
            inputVO.setSpecification(specificationDesc);
            inputVO.setStartDate(DateUtil.getCurrentDay());
            inputVO.setEndDate(inputVO.getAmountRequest().getExpireDate());
        }
        return result;
    }
    
    private CommonResult normalAccountEstablishPackage(AddResourceRequest inputVO) {
        if (BusinessTypeEnum.VOD_PACKAGE.getCode().equals(inputVO.getBusinessType())) {
            return addVodPackage(inputVO);
        }
        if (BusinessTypeEnum.VOD_SPACE.getCode().equals(inputVO.getBusinessType())) {
            return addVodSpace(inputVO);
        }
        if (BusinessTypeEnum.VOD_FLOW.getCode().equals(inputVO.getBusinessType())) {
            return addVodFlow(inputVO);
        }
        if (BusinessTypeEnum.LIVE_FLOW.getCode().equals(inputVO.getBusinessType())) {
            return addLiveFlow(inputVO);
        }
        if (BusinessTypeEnum.LIVE_DURATION.getCode().equals(inputVO.getBusinessType())) {
            return addLiveDuration(inputVO);
        }
        if (BusinessTypeEnum.LIVE_DAILY_TEST.getCode().equals(inputVO.getBusinessType())
        || BusinessTypeEnum.LIVE_PRTC_DAILY_TEST.getCode().equals(inputVO.getBusinessType())) {
            return addLiveDailyConcurrence(inputVO);
        }

        if (BusinessTypeEnum.GUIDE_DURATION.getCode().equals(inputVO.getBusinessType())) {
            return addGuideDuration(inputVO);
        }
        if (BusinessTypeEnum.MIC_DURATION.getCode().equals(inputVO.getBusinessType())) {
            return addMicDuration(inputVO);
        }
        if (BusinessTypeEnum.ADD_CHANNELS.getCode().equals(inputVO.getBusinessType())) {
            return addChannels(inputVO);
        }
        if (BusinessTypeEnum.LIVE_PEAK_CONCURRENCE.getCode().equals(inputVO.getBusinessType())) {
            return addPeakConcurrence(inputVO);
        }
        if (BusinessTypeEnum.PRTC_AMOUNT.getCode().equals(inputVO.getBusinessType())) {
            return addPrtcDuration(inputVO);
        }
        if (BusinessTypeEnum.AMOUNT.getCode().equals(inputVO.getBusinessType())) {
            return addAmount(inputVO);
        }
        if (BusinessTypeEnum.RESOURCE_POINT.getCode().equals(inputVO.getBusinessType())){
            return addAmountResourcePoint(inputVO) ;
        }
        return CommonResult.ok();
    }
    
    
    private CommonResult groupAccountEstablishPackage(AddResourceRequest inputVO) {
        if (BusinessTypeEnum.VOD_PACKAGE.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addVodPackage(inputVO);
        }
        if (BusinessTypeEnum.VOD_SPACE.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addVodSpace(inputVO);
        }
        if (BusinessTypeEnum.VOD_FLOW.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addVodFlow(inputVO);
        }
        if (BusinessTypeEnum.LIVE_DURATION.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addLiveDuration(inputVO);
        }
        if (BusinessTypeEnum.LIVE_DAILY_TEST.getCode().equals(inputVO.getBusinessType())
        || BusinessTypeEnum.LIVE_PRTC_DAILY_TEST.getCode().equals(inputVO.getBusinessType())
        ) {
            return groupAccountEstablishPackageService.addLiveDailyConcurrence(inputVO);
        }
        if (BusinessTypeEnum.GUIDE_DURATION.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addGuideDuration(inputVO);
        }
        if (BusinessTypeEnum.MIC_DURATION.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addMicDuration(inputVO);
        }
        if (BusinessTypeEnum.ADD_CHANNELS.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addChannels(inputVO);
        }
        if (BusinessTypeEnum.LIVE_PEAK_CONCURRENCE.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addPeakConcurrence(inputVO);
        }
        if (BusinessTypeEnum.PRTC_AMOUNT.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addPrtcDuration(inputVO);
        }
        if (BusinessTypeEnum.AMOUNT.getCode().equals(inputVO.getBusinessType())) {
            return groupAccountEstablishPackageService.addAmount(inputVO);
        }
        return CommonResult.ok();
    }
    
    /**
     * TODO 有很多重复代码，有待优化
     */
    @Override
    public CommonResult<AmountCalculateResultVO> calculateAmount(AmountCalculateRequest inputVO) {
        
        Integer businessType = inputVO.getBusinessType();
        List<AmountDetailVO> amountDetailVOList = Lists.newArrayList();
        BillingItem billingItem;
        
        if (BusinessTypeEnum.VOD_PACKAGE.getCode().equals(businessType)) {
            int days = DateUtil.getleftDays(new Date(), DateFormatUtil.parseDateStr(inputVO.getSpaceEndDate())) + 1;
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.video_space.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getVodSpaceSize()), billingItem,
                                days));
            }
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.traffic.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getVodFlowSize()), billingItem, 1));
            }
        }
        if (BusinessTypeEnum.VOD_SPACE.getCode().equals(businessType)) {
            int days = DateUtil.getleftDays(new Date(), DateFormatUtil.parseDateStr(inputVO.getSpaceEndDate())) + 1;
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.video_space.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getVodSpaceSize()), billingItem,
                                days));
            }
        }
        if (BusinessTypeEnum.VOD_FLOW.getCode().equals(businessType)) {
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.traffic.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getVodFlowSize()), billingItem, 1));
            }
        }
        if (BusinessTypeEnum.LIVE_FLOW.getCode().equals(businessType)) {
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.live_flow.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getLiveFlowSize()), billingItem, 1));
            }
        }
        if (BusinessTypeEnum.LIVE_DURATION.getCode().equals(businessType)) {
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.china_pd.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getLiveDuration()), billingItem, 1));
            }
        }
        if (BusinessTypeEnum.LIVE_DAILY_TEST.getCode().equals(businessType)) {
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.concur_daily.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getDailyConcurrence()), billingItem,
                                1));
            }
        }

        if (BusinessTypeEnum.LIVE_PRTC_DAILY_TEST.getCode().equals(businessType)) {
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.prtc_concur_daily.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getDailyConcurrence()), billingItem,
                                1));
            }
        }
        
        if (BusinessTypeEnum.GUIDE_DURATION.getCode().equals(businessType)) {
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.guide_pd.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getGuideDuration()), billingItem, 1));
            }
        }
        //连麦分钟数获取mic_pd_7档的
        if (BusinessTypeEnum.MIC_DURATION.getCode().equals(businessType)) {
            billingItem = billingItemRepository.findFirstByCodeAndScaleCode(ItemCodeConst.mic_pd.getCode(),
                    ItemScaleCodeConst.MicPd.mic_pd_7.name());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getMicDuration()), billingItem, 1));
            }
        }
        if (BusinessTypeEnum.PRTC_AMOUNT.getCode().equals(businessType)) {
            amountDetailVOList.add(getAmountCostDetail(businessType, new BigDecimal(inputVO.getPrtcAmount()), null, 1));
        }
        if (BusinessTypeEnum.AMOUNT.getCode().equals(businessType)) {
            amountDetailVOList.add(getAmountCostDetail(businessType, new BigDecimal(inputVO.getAmount()), null, 1));
        }
        if (BusinessTypeEnum.RESOURCE_POINT.getCode().equals(businessType)) {
            amountDetailVOList.add(getAmountCostDetail(businessType, new BigDecimal(inputVO.getAmount()), null, 1));
        }
        if (BusinessTypeEnum.LIVE_PEAK_CONCURRENCE.getCode().equals(businessType)) {
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.concur_peak.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getPeakConcurrence()), billingItem,
                                1));
            }
        }
        if (BusinessTypeEnum.ADD_CHANNELS.getCode().equals(businessType)) {
            billingItem = billingItemRepository.findFirstByCode(ItemCodeConst.channel_number.getCode());
            if (Objects.nonNull(billingItem)) {
                amountDetailVOList.add(
                        getAmountCostDetail(businessType, new BigDecimal(inputVO.getChannels()), billingItem, 1));
            }
        }
        return CommonResult.ok(packageAccountAmount(inputVO, amountDetailVOList));
    }
    
    /**
     * 封装账号余额
     */
    private AmountCalculateResultVO packageAccountAmount(AmountCalculateRequest inputVO,
            List<AmountDetailVO> amountDetailVOList) {
        AmountCalculateResultVO resultVO = new AmountCalculateResultVO();
        if (CollectionUtils.isNotEmpty(amountDetailVOList)) {
            resultVO.setAmountCostDetails(amountDetailVOList);
            //总共消耗额度
            BigDecimal totalCostAmount = amountDetailVOList.stream()
                    .map(AmountDetailVO::getAmountCost)
                    .reduce(BigDecimal::add)
                    .get()
                    .setScale(2, RoundingMode.HALF_UP);
            resultVO.setEstablishCostAmount(totalCostAmount);
        }
        
        //个人剩余额度
        AvailableAmountDetailVO detailVO = getSaleAvailableAmount(inputVO.getSaleUserId(),
                DateFormatUtil.formatDateMonth(new Date()));
        resultVO.setPersonalLeftAmount(detailVO.getMonthLeftAmount());
        resultVO.setEstablishLeftAmount(detailVO.getMonthLeftAmount().subtract(resultVO.getEstablishCostAmount()));
        resultVO.setStatus(resultVO.getEstablishLeftAmount().compareTo(new BigDecimal(0.00)) < 0 ? 0 : 1);
        
        resultVO.setSaleUserName(detailVO.getSaleUserName());
        resultVO.setSaleDepartmentType(detailVO.getSaleDepartmentType());
        return resultVO;
    }
    
    
    /**
     * @param businessType：业务类别
     * @param number:数量
     * @param billingItem
     * @param days :天数，空间才会用到，需要根据天数算消耗额度
     * @return
     */
    public static AmountDetailVO getAmountCostDetail(Integer businessType, BigDecimal number, BillingItem billingItem,
            int days) {
        
        AmountDetailVO amountDetailVO = new AmountDetailVO();
        
        //金额单独处理
        if (BusinessTypeEnum.AMOUNT.getCode().equals(businessType) ||
            BusinessTypeEnum.PRTC_AMOUNT.getCode().equals(businessType)) {
            amountDetailVO.setBillingItemDesc("金额");
            amountDetailVO.setPriceDesc("-");
            amountDetailVO.setAmountCost(number);
        } else if (BusinessTypeEnum.RESOURCE_POINT.getCode().equals(businessType)) {
            amountDetailVO.setBillingItemDesc("资源点");
            amountDetailVO.setPriceDesc("-");
            amountDetailVO.setAmountCost(number);
        }else {
            amountDetailVO.setBillingItemDesc(billingItem.getCategory());
            BigDecimal price = new BigDecimal(billingItem.getDefaultUnivalence()).divide(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION));
            amountDetailVO.setPriceDesc(price.doubleValue() + billingItem.getUnivalenceUnit());
            amountDetailVO.setAmountCost(number.multiply(price).multiply(new BigDecimal(days)));
        }

        return amountDetailVO;
    }
    
    @Override
    public CommonResult<PackageSpecificationGetResultVO> getPackageSpecification(
            PackageSpecificationGetRequest inputVO) {
        PackageSpecificationGetResultVO resultVO = new PackageSpecificationGetResultVO();
        String resourceDesc = ResourceTypeEnum.getValue(inputVO.getResourceType());
        if (StringUtils.isBlank(resourceDesc)) {
            return CommonResult.fail("resourceType传值有误");
        }
        if (BusinessTypeEnum.LIVE_DAILY_TEST.getCode().equals(inputVO.getBusinessType())) {
            List<SystemDictionary> billingPlanList = systemDictionaryRepository.getByDictTypeCode(
                    "live.daily.billing.plan");
            List<SystemDictionaryVO> liveDailyBillingPlanList = Lists.newArrayList();
            for (SystemDictionary systemDictionary : billingPlanList) {
                SystemDictionaryVO systemDictionaryVO = new SystemDictionaryVO();
                BeanUtils.copyProperties(systemDictionary, systemDictionaryVO);
                liveDailyBillingPlanList.add(systemDictionaryVO);
            }
            resultVO.setLiveDailyBillingPlanList(liveDailyBillingPlanList);
        }
        if (BusinessTypeEnum.LIVE_PRTC_DAILY_TEST.getCode().equals(inputVO.getBusinessType())) {
            List<SystemDictionary> billingPlanList = systemDictionaryRepository.getByDictTypeCode(
                    "live.prtc.daily.billing.plan");
            List<SystemDictionaryVO> liveDailyBillingPlanList = Lists.newArrayList();
            for (SystemDictionary systemDictionary : billingPlanList) {
                SystemDictionaryVO systemDictionaryVO = new SystemDictionaryVO();
                BeanUtils.copyProperties(systemDictionary, systemDictionaryVO);
                liveDailyBillingPlanList.add(systemDictionaryVO);
            }
            resultVO.setLiveDailyBillingPlanList(liveDailyBillingPlanList);
        }
        if (BusinessTypeEnum.LIVE_DURATION.getCode().equals(inputVO.getBusinessType())) {
            resultVO.setLiveTestPackageList(getLivePackageOptions(BusinessBillingPlanCodeEnum.LIVE_DURATION.getCode()));
            resultVO.setLiveBillingPlan(LiveBillingPlanConst.duration.getLabel());
        }
        if (BusinessTypeEnum.VOD_PACKAGE.getCode().equals(inputVO.getBusinessType())) {
            resultVO.setVodTestPackageList(getVodPackageOptions());
            //获取点播套餐信息
            VodPackageState vodPackage = customerService.getVodPackage(inputVO.getAccountType(),
                    inputVO.getCustomerId());
            resultVO.setVodPackageState(vodPackage);
        }
        return CommonResult.ok(resultVO);
    }
    
    /**
     * 添加点播套餐(周期资源)
     * @param inputVO
     * @return
     */
    private CommonResult addVodPackage(AddResourceRequest inputVO) {
        VodPackageRequest vodPackage = inputVO.getVodPackageRequest();
        
        String key = getEstablishPackageBusinessKey(inputVO.getBusinessId());
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(inputVO.getBusinessId(), inputVO.getResourceType(),
                null);
        redisTemplate.opsForValue().set(key, JsonMapper.jsonToString(vo), 24 * 3600, TimeUnit.SECONDS);
        
        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddVodPackageSign(timestamp);
        long space = UnitConverterUtil.GB2bytes(new BigDecimal(vodPackage.getPackageSpace()));
        long flow = UnitConverterUtil.GB2bytes(new BigDecimal(vodPackage.getPackageMonthFlow()));
        String contractId = RandomUtils.getRandStr(10);
        inputVO.setContractId(contractId);
        ResponseEntity<WrappedResponse<?>> wrappedResponse = vodBusinessOperationClient.addPackagePro(
                inputVO.getVodUserId(), sign, String.valueOf(vodPackage.getTestPackageId()),
                DateFormatUtil.formatDateNormal(vodPackage.getExpireDate()), String.valueOf(1),
                String.valueOf(timestamp), String.valueOf(space), String.valueOf(flow), contractId, null,
                inputVO.getBusinessId());
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("customerId == {},addPackagePro error,response == {}", inputVO.getCustomerId(), wrappedResponse);
            return CommonResult.fail("开通点播套餐失败");
        }
        
        return CommonResult.ok();
    }
    
    
    private String getEstablishPackageBusinessKey(String businessId) {
        return String.format(GlobalConfigConst.ESTABLISH_PACKAGE_BUSINESS_KEY, businessId);
    }
    
    private ResourceTypeExpireDateVO buildResourceTypeExpireDateVO(String businessId, Integer resourceType,
            String expireDate) {
        return ResourceTypeExpireDateVO.builder()
                .businessId(businessId)
                .resourceType(resourceType)
                .expireDate(expireDate)
                .build();
    }
    
    /**
     * 添加点播空间(临时空间)
     * @param inputVO
     * @return
     */
    private CommonResult addVodSpace(AddResourceRequest inputVO) {
        VodSpaceRequest vodSpace = inputVO.getVodSpaceRequest();
        
        String key = getEstablishPackageBusinessKey(inputVO.getBusinessId());
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(inputVO.getBusinessId(), inputVO.getResourceType(),
                null);
        redisTemplate.opsForValue().set(key, JsonMapper.jsonToString(vo), 24 * 3600, TimeUnit.SECONDS);
        
        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddVodPackageSign(timestamp);
        String contractId = RandomUtils.getRandStr(10);
        inputVO.setContractId(contractId);
        //生效时间默认为当天
        String spaceStartDate = DateFormatUtil.formatDateNormal(new Date());
        String spaceEndDate = DateFormatUtil.formatDateNormal(vodSpace.getSpaceExpireDate());
        ResponseEntity<WrappedResponse<?>> wrappedResponse = vodBusinessOperationClient.addFlowOrSpace(
                inputVO.getVodUserId(), sign, String.valueOf(vodSpace.getVodSpace()), "3", spaceStartDate,
                String.valueOf(timestamp), spaceEndDate, contractId, inputVO.getBusinessId());
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("customerId == {},addVodSpace error,response == {}", inputVO.getCustomerId(), wrappedResponse);
            return CommonResult.fail("开通点播空间失败");
        }
        
        return CommonResult.ok();
    }
    
    private CommonResult addPrtcDuration(AddResourceRequest inputVO) {
        PrtcRequest prtcRequest = inputVO.getPrtcRequest();
        ResourceDepositInputVO resourceDepositInputVO = new ResourceDepositInputVO();
        resourceDepositInputVO.setUnionId(inputVO.getCustomerId());
        resourceDepositInputVO.setResourceCode(ResourceCodeConst.prtc_duration.name());
        resourceDepositInputVO.setDeposit(
                new BigDecimal(prtcRequest.getAmount()).divide(PrecisionConst.DEFAULT_PRTC_PRICE_PER_MINUTE)
                        .longValue());
        resourceDepositInputVO.setResourceType(inputVO.getResourceType());
        resourceDepositInputVO.setExpireDate(prtcRequest.getExpireDate());
        resourceDepositService.depositResource(resourceDepositInputVO);
        return CommonResult.ok();
        
    }
    
    /**
     * 添加直播流量
     */
    private CommonResult addLiveFlow(AddResourceRequest inputVO) {
        LiveFlowRequest liveFlowRequest = inputVO.getLiveFlowRequest();
        Date today = DateUtil.getCurrentDay();
        long liveFlow = Double.valueOf(Double.parseDouble(liveFlowRequest.getLiveFlow()) * 1024 * 1024 * 1024)
                .longValue(); // GB -> Byte
        String packageId= "21"; // 套餐ID和名称，目前使用固定值
        String packageName= "直播流量套餐（100GB）";
        businessOperationService.addLiveFlowPackage(inputVO.getCustomerId(), today, liveFlowRequest.getFlowExpireDate(),
                liveFlow, packageId, packageName);
        //素材库资源对齐套餐时间
        String expiredDate = DateFormatUtil.formatDateNormal(liveFlowRequest.getFlowExpireDate());
        try {
            resourceBillingItemService.addAndResetMaterialResource(inputVO.getCustomerId(),"materialtraffic",10,expiredDate,today,false);
            resourceBillingItemService.addAndResetMaterialResource(inputVO.getCustomerId(),"materialspace",10,expiredDate,today,false);
        }catch (Exception ex){

        }
        return CommonResult.ok();
    }
    
    /**
     * 添加直播分钟数
     * @param inputVO
     * @return
     */
    private CommonResult addLiveDuration(AddResourceRequest inputVO) {
        
        LiveDurationRequest LiveDurationRequest = inputVO.getLiveDurationRequest();
        
        String key = getEstablishPackageBusinessKey(inputVO.getBusinessId());
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(inputVO.getBusinessId(), inputVO.getResourceType(),
                DateFormatUtil.formatDateNormal(LiveDurationRequest.getExpireDate()));
        redisTemplate.opsForValue().set(key, JsonMapper.jsonToString(vo), 24 * 3600, TimeUnit.SECONDS);
        
        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddPackageSign(timestamp);
        String packageId = String.valueOf(LiveDurationRequest.getLiveTestPackageId());
        //生效时间默认为当天
        String startDate = DateFormatUtil.formatDateNormal(new Date());
        // 自定义套餐的设置使用基础版套餐设置
        String pId = (org.apache.commons.lang3.StringUtils.isNumeric(packageId)) ? packageId : "1";
        ResponseEntity<WrappedResponse<?>> wrappedResponse = livePackageClient.setPackagePro(inputVO.getLiveUserId(),
                String.valueOf(timestamp), sign, LiveBillingType.Duration.getValue(),
                String.valueOf(LiveDurationRequest.getDuration()), "-1", "-1", startDate,
                DateFormatUtil.formatDateNormal(LiveDurationRequest.getExpireDate()), pId, "Y", "-1", null, "0",
                inputVO.getBusinessId());
        
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("customerId == {},setPackagePro error,response == {}", inputVO.getCustomerId(), wrappedResponse);
            return CommonResult.fail("开通直播分钟数失败");
        }

        //素材库资源对齐套餐时间
        String expiredDate = DateFormatUtil.formatDateNormal(LiveDurationRequest.getExpireDate());
        try {
            resourceBillingItemService.addAndResetMaterialResource(inputVO.getCustomerId(),"materialtraffic",10,expiredDate,new Date(),false);
            resourceBillingItemService.addAndResetMaterialResource(inputVO.getCustomerId(),"materialspace",10,expiredDate,new Date(),false);
        }catch (Exception ex){

        }
        return CommonResult.ok();
    }
    
    /**
     * 添加导播台分钟数
     * @param inputVO
     * @return
     */
    private CommonResult addGuideDuration(AddResourceRequest inputVO) {
        GuideDurationRequest GuideDurationRequest = inputVO.getGuideDurationRequest();
        
        String key = getEstablishPackageBusinessKey(inputVO.getBusinessId());
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(inputVO.getBusinessId(), inputVO.getResourceType(),
                DateFormatUtil.formatDateNormal(GuideDurationRequest.getExpireDate()));
        redisTemplate.opsForValue().set(key, JsonMapper.jsonToString(vo), 24 * 3600, TimeUnit.SECONDS);
        
        ResponseEntity<WrappedResponse<?>> wrappedResponse = liveInnerApiClient.addGuideDurations(
                inputVO.getLiveUserId(), String.valueOf(GuideDurationRequest.getDuration()), "0",
                inputVO.getBusinessId());
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("customerId == {},addGuideDuration error,response == {}", inputVO.getCustomerId(),
                    wrappedResponse);
            return CommonResult.fail("开通导播台分钟数失败");
        }
        
        return CommonResult.ok();
    }
    
    /**
     * 添加连麦分钟数
     * @param inputVO
     * @return
     */
    private CommonResult addMicDuration(AddResourceRequest inputVO) {
        MicDurationRequest MicDurationRequest = inputVO.getMicDurationRequest();
        
        String key = getEstablishPackageBusinessKey(inputVO.getBusinessId());
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(inputVO.getBusinessId(), inputVO.getResourceType(),
                DateFormatUtil.formatDateNormal(MicDurationRequest.getExpireDate()));
        redisTemplate.opsForValue().set(key, JsonMapper.jsonToString(vo), 24 * 3600, TimeUnit.SECONDS);
        
        ResponseEntity<WrappedResponse<?>> wrappedResponse = liveInnerApiClient.addMicPackages(inputVO.getLiveUserId(),
                String.valueOf(MicDurationRequest.getDuration()), "0", null, "0",
                LiveBillingType.Mic_Minutes.getValue(), String.valueOf(MicDurationRequest.getDuration()), "0",
                inputVO.getBusinessId());
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("customerId == {},addMicDuration error,response == {}", inputVO.getCustomerId(), wrappedResponse);
            return CommonResult.fail("开通连麦分钟数失败");
        }
        
        return CommonResult.ok();
    }
    
    private CommonResult addAmount(AddResourceRequest inputVO) {
        AmountRequest AmountRequest = inputVO.getAmountRequest();
        DepositInputVO depositInputVO = new DepositInputVO();
        depositInputVO.setCustomerId(inputVO.getCustomerId());
        if (ResourceTypeEnum.TEST.getCode().equals(inputVO.getResourceType())) {
            depositInputVO.setAmountDepositType(AmountDepositTypeEnum.TEST.getCode());
            depositInputVO.setTestAmount(new BigDecimal(AmountRequest.getAmount()).multiply(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION)).longValue());
            depositInputVO.setPayUnpayEnabled(AmountRequest.getPayUnpayEnabled());
        }
        if (ResourceTypeEnum.DONATE.getCode().equals(inputVO.getResourceType())) {
            depositInputVO.setAmountDepositType(AmountDepositTypeEnum.DONATE.getCode());
            depositInputVO.setDonate(new BigDecimal(AmountRequest.getAmount()).multiply(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION)).longValue());
        }
        depositInputVO.setAmountExpireDate(AmountRequest.getExpireDate());
        depositInputVO.setAmountExpireType(AmountExpireTypeEnum.POSTPONE.getCode());
        depositInputVO.setAccountType(inputVO.getAccountType());
        depositInputVO.setSaleUserId(inputVO.getSaleUserId());
        accountDepositService.depositTestAndDonateAmount(depositInputVO);
        return CommonResult.ok();
    }

    private CommonResult addAmountResourcePoint(AddResourceRequest inputVO) {

        AmountRequest AmountRequest = inputVO.getAmountRequest();
        DepositInputVO depositInputVO = new DepositInputVO();
        depositInputVO.setCustomerId(inputVO.getCustomerId());
        if (ResourceTypeEnum.TEST.getCode().equals(inputVO.getResourceType())) {
            depositInputVO.setAmountDepositType(AmountDepositTypeEnum.TEST.getCode());
            depositInputVO.setTestAmount(new BigDecimal(AmountRequest.getAmount()).multiply(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION)).longValue());
        }
        if (ResourceTypeEnum.DONATE.getCode().equals(inputVO.getResourceType())) {
            depositInputVO.setAmountDepositType(AmountDepositTypeEnum.DONATE.getCode());
            depositInputVO.setDonate(new BigDecimal(AmountRequest.getAmount()).multiply(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION)).longValue());
        }
        depositInputVO.setAmountExpireDate(AmountRequest.getExpireDate());
        depositInputVO.setAmountExpireType(AmountExpireTypeEnum.INDEPENDENCE.getCode());
        depositInputVO.setAccountType(inputVO.getAccountType());
        accountDepositService.depositTestAndDonateAmountResourcePoint(depositInputVO);
        return CommonResult.ok();
    }
    
    /**
     * 添加频道数
     * @param inputVO
     * @return
     */
    private CommonResult addChannels(AddResourceRequest inputVO) {
        AddChannelsRequest AddChannelsRequest = inputVO.getAddChannelsRequest();
        ResponseEntity<WrappedResponse<?>> wrappedResponse = liveInnerApiClient.addChannels(inputVO.getLiveUserId(),
                String.valueOf(AddChannelsRequest.getChannels()));
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("customerId == {},addChannels error,response == {}", inputVO.getCustomerId(), wrappedResponse);
            return CommonResult.fail("增加频道数失败");
        }
        return CommonResult.ok();
    }
    
    /**
     * 添加直播并发包天测试
     * @param inputVO
     * @return
     */
    private CommonResult addLiveDailyConcurrence(AddResourceRequest inputVO) {
        LiveDailyConcurrenceRequest liveDailyConcurrenceRequest = inputVO.getLiveDailyConcurrenceRequest();
        Integer billingPlanType = liveDailyConcurrenceRequest.getBillingPlanType();
        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddPackageSign(timestamp);
        //并发测试包天套餐id为14
        String pId = "14";
        long concurrences = -1;
        if (SwitchEnum.isY(liveDailyConcurrenceRequest.getRealLimit())) {
            
            concurrences = liveDailyConcurrenceRequest.getConcurrence();
        }
        String useDailyTest = null;
        if (Objects.nonNull(billingPlanType) && LiveDailyBillingPlanEnum.TEST.getCode().equals(billingPlanType)) {
            useDailyTest = "1";
        }
        String liveBillingType = LiveBillingType.Daily.getValue();
        if(BusinessTypeEnum.LIVE_PRTC_DAILY_TEST.getCode() == inputVO.getBusinessType()){
            liveBillingType = LiveBillingType.Prtc_Daily.getValue();
        }
        ResponseEntity<WrappedResponse<?>> wrappedResponse = livePackageClient.setPackagePro(inputVO.getLiveUserId(),
                String.valueOf(timestamp), sign,liveBillingType , "0", String.valueOf(concurrences),
                String.valueOf(liveDailyConcurrenceRequest.getConcurrence()),
                DateFormatUtil.formatDateNormal(liveDailyConcurrenceRequest.getLiveStartDate()),
                DateFormatUtil.formatDateNormal(liveDailyConcurrenceRequest.getLiveEndDate()), pId, "Y", "0",
                useDailyTest, "0", inputVO.getBusinessId());
        
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("customerId == {},setPackagePro error,response == {}", inputVO.getCustomerId(), wrappedResponse);
            return CommonResult.fail("开通并发包天失败");
        }
        return CommonResult.ok();
    }
    
    /**
     * 添加直播峰值并发测试
     * @param inputVO
     * @return
     */
    private CommonResult addPeakConcurrence(AddResourceRequest inputVO) {
        LivePeakConcurrenceRequest LivePeakConcurrenceRequest = inputVO.getLivePeakConcurrenceRequest();
        
        String key = getEstablishPackageBusinessKey(inputVO.getBusinessId());
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(inputVO.getBusinessId(), inputVO.getResourceType(),
                DateFormatUtil.formatDateNormal(LivePeakConcurrenceRequest.getLiveEndDate()));
        redisTemplate.opsForValue().set(key, JsonMapper.jsonToString(vo), 24 * 3600, TimeUnit.SECONDS);
        
        long concurrences = LivePeakConcurrenceRequest.getConcurrence();
        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddPackageSign(timestamp);
        ResponseEntity<WrappedResponse<?>> wrappedResponse = livePackageClient.setPackagePro(inputVO.getLiveUserId(),
                String.valueOf(timestamp), sign, LiveBillingType.Peak.getValue(), "0", String.valueOf(concurrences),
                String.valueOf(concurrences),
                DateFormatUtil.formatDateNormal(LivePeakConcurrenceRequest.getLiveStartDate()),
                DateFormatUtil.formatDateNormal(LivePeakConcurrenceRequest.getLiveEndDate()), "0", "Y", "0", null, "0",
                inputVO.getBusinessId());
        
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("customerId == {},setPackagePro error,response == {}", inputVO.getCustomerId(), wrappedResponse);
            return CommonResult.fail("开通并发包天失败");
        }

        //素材库资源对齐套餐时间
        String expiredDate = DateFormatUtil.formatDateNormal(LivePeakConcurrenceRequest.getLiveEndDate());
        try {
            resourceBillingItemService.addAndResetMaterialResource(inputVO.getCustomerId(),"materialtraffic",10,expiredDate,LivePeakConcurrenceRequest.getLiveStartDate(),false);
            resourceBillingItemService.addAndResetMaterialResource(inputVO.getCustomerId(),"materialspace",10,expiredDate,LivePeakConcurrenceRequest.getLiveStartDate(),false);
        }catch (Exception ex){

        }
        
        return CommonResult.ok();
    }
    
    /**
     * 添加点播流量(永久流量)
     * @param inputVO
     * @return
     */
    private CommonResult addVodFlow(AddResourceRequest inputVO) {
        VodFlowRequest vodFlow = inputVO.getVodFlowRequest();
        
        String key = getEstablishPackageBusinessKey(inputVO.getBusinessId());
        ResourceTypeExpireDateVO vo = buildResourceTypeExpireDateVO(inputVO.getBusinessId(), inputVO.getResourceType(),
                DateFormatUtil.formatDateNormal(vodFlow.getFlowExpireDate()));
        redisTemplate.opsForValue().set(key, JsonMapper.jsonToString(vo), 24 * 3600, TimeUnit.SECONDS);
        
        long timestamp = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddVodPackageSign(timestamp);
        String contractId = RandomUtils.getRandStr(10);
        //生效时间默认为当天
        String flowStartDate = DateFormatUtil.formatDateNormal(new Date());
        String flowEndDate = DateFormatUtil.formatDateNormal(vodFlow.getFlowExpireDate());
        ResponseEntity<WrappedResponse<?>> wrappedResponse = vodBusinessOperationClient.addFlowOrSpace(
                inputVO.getVodUserId(), sign, String.valueOf(vodFlow.getVodFlow()),
                String.valueOf(vodFlow.getTrafficType()), flowStartDate, String.valueOf(timestamp), flowEndDate,
                contractId, inputVO.getBusinessId());
        if (ResponseUtil.isErrorResponse(wrappedResponse)) {
            log.error("customerId == {},addVodFlow error,response == {}", inputVO.getCustomerId(), wrappedResponse);
            return CommonResult.fail("开通点播流量失败");
        }
        inputVO.setContractId(contractId);
        
        return CommonResult.ok();
    }
    
    @Override
    public CommonResult<ResourceAndBusinessTypeGetResultVO> getResourceAndBusinessTypeList(String resourceTypeCode,
            String accountType) {
        
        // 先查出操作类型（测试，赠送）
        List<SystemDictionary> resourceTypeList = systemDictionaryRepository.getByDictTypeCode(resourceTypeCode);
        List<SystemDictionaryVO> resourceTypeVOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(resourceTypeList)) {
            return CommonResult.ok();
        }
        
        resourceTypeList.forEach(resourceSystemDictionary -> {
            SystemDictionaryVO systemDictionaryVO = new SystemDictionaryVO();
            BeanUtils.copyProperties(resourceSystemDictionary, systemDictionaryVO);
            resourceTypeVOList.add(systemDictionaryVO);
        });
        ResourceAndBusinessTypeGetResultVO resultVO = new ResourceAndBusinessTypeGetResultVO();
        Map<String, List<SystemDictionaryVO>> businessTypeMap = Maps.newHashMap();
        
        // 每种操作类型，再查询其下的资源类型
        for (SystemDictionary systemDictionary : resourceTypeList) {
            List<SystemDictionary> businessTypeList = systemDictionaryRepository.getByPid(systemDictionary.getId());
            List<SystemDictionaryVO> businessTypeVOList = Lists.newArrayList();
            businessTypeList.forEach(businessSystemDictionary -> {
                String desc = BusinessTypeEnum.getValue(Integer.parseInt(businessSystemDictionary.getValue()));
                //集团主账号只返回枚举里边的计费项
                if (AccountTypeEnum.GROUP2.getCode().equals(accountType)) {
                    if (StringUtils.isNotBlank(desc)) {
                        SystemDictionaryVO systemDictionaryVO = new SystemDictionaryVO();
                        BeanUtils.copyProperties(businessSystemDictionary, systemDictionaryVO);
                        businessTypeVOList.add(systemDictionaryVO);
                    }
                } else {
                    SystemDictionaryVO systemDictionaryVO = new SystemDictionaryVO();
                    BeanUtils.copyProperties(businessSystemDictionary, systemDictionaryVO);
                    businessTypeVOList.add(systemDictionaryVO);
                }
            });
            businessTypeMap.put(systemDictionary.getId() + "", businessTypeVOList);
        }
        resultVO.setResourceTypeList(resourceTypeVOList);
        resultVO.setBusinessTypeList(businessTypeMap);
        return CommonResult.ok(resultVO);
    }
    
    /**
     * 获取点播测试套餐选项
     * @return 选项map
     */
    private List<Map<String, String>> getVodPackageOptions() {
        List<Map<String, String>> optionList = new ArrayList<>();
        List<VodDepositPackage> vodDepositPackages = PolyvApiRestTemplate.getPackageTypes();
        List<String> vodTestPackageIdList = Arrays.asList(vodTestPackageIds.split(","));
        if (CollectionUtils.isNotEmpty(vodDepositPackages)) {
            for (VodDepositPackage vpo : vodDepositPackages) {
                //只筛选出测试套餐
                if (vodTestPackageIdList.contains(String.valueOf(vpo.getId()))) {
                    Map<String, String> optionsMap = new HashMap<>();
                    optionsMap.put("id", String.valueOf(vpo.getId()));
                    optionsMap.put("name", vpo.getName());
                    Long currentFlow = Objects.nonNull(vpo.getFlow()) ? vpo.getFlow() : 0L;
                    Long currentSpace = Objects.nonNull(vpo.getSpace()) ? vpo.getSpace() : 0L;
                    optionsMap.put("flow", MoneyUtil.convertBytes2GB(currentFlow));
                    optionsMap.put("space", MoneyUtil.convertBytes2GB(currentSpace));
                    optionsMap.put("price", Objects.nonNull(vpo.getPrice()) ? String.valueOf(vpo.getPrice()) : "");
                    optionsMap.put("txt", vpo.getTxt());
                    optionsMap.put("expireDate", getLiveTestPackageExpireDate(String.valueOf(vpo.getId())));
                    optionList.add(optionsMap);
                }
            }
        }
        return optionList;
    }
    
    private String getLiveTestPackageExpireDate(String packageId) {
        String expireDate = "";
        if (StringUtils.isNotBlank(packageId)) {
            
            switch (packageId) {
                //内测版,当前系统时间+1年
                case "34":
                    expireDate = DateFormatUtil.formatDateNormal(
                            DateUtil.getDateAfterDays(-1, DateUtil.getDateAfterYears(1)));
                    break;
                //测试版-月,当前系统时间+30天（包括当天）
                case "37":
                    expireDate = DateFormatUtil.formatDateNormal(DateUtil.getDateAfterDays(29));
                    break;
                //测试版-15天,当前系统时间+15天（包括当天）
                case "38":
                    expireDate = DateFormatUtil.formatDateNormal(DateUtil.getDateAfterDays(14));
                    break;
                //酷播云免费版,当前系统时间+5年（包括当天）
                case "49":
                    expireDate = DateFormatUtil.formatDateNormal(
                            DateUtil.getDateAfterDays(-1, DateUtil.getDateAfterYears(5)));
                    break;
                //测试版-7天,当前系统时间+7天（包括当天）
                case "122":
                    expireDate = DateFormatUtil.formatDateNormal(DateUtil.getDateAfterDays(6));
                    break;
            }
        }
        return expireDate;
    }
    
    /**
     * 获取直播套餐选项
     * @return 选项map
     */
    private List<Map<String, String>> getLivePackageOptions(String billingPlanCode) {
        List<Map<String, String>> optionList = new ArrayList<>();
        List<String> liveTestPackageIdList = Arrays.asList(liveTestPackageIds.split(","));
        ResponseEntity<WrappedResponse<?>> lpoResult = this.liveInnerFinanceApiClient.getDepositPackages();
        if (!ResponseUtil.isErrorResponse(lpoResult) && Objects.nonNull(lpoResult.getBody().getData())) {
            List<LiveDepositPackage> lpoList = JsonMapper.objectToComplicatedBean(lpoResult.getBody().getData(),
                    List.class, LiveDepositPackage.class);
            //过滤出且packageType不为空,且packageType = billPlanCode的数据
            lpoList = lpoList.stream()
                    .filter(lpo -> StringUtils.isNotBlank(lpo.getPackageType()) &&
                            lpo.getPackageType().equals(billingPlanCode))
                    .collect(Collectors.toList());
            for (LiveDepositPackage lpo : lpoList) {
                //只筛选出测试套餐
                if (liveTestPackageIdList.contains(String.valueOf(lpo.getPackageId()))) {
                    Map<String, String> optionsMap = new HashMap<>();
                    optionsMap.put("id", String.valueOf(lpo.getPackageId()));
                    optionsMap.put("name", lpo.getName());
                    optionsMap.put("note", lpo.getNote());
                    if (BusinessBillingPlanCodeEnum.LIVE_DURATION.getCode().equals(billingPlanCode)) {
                        optionsMap.put("duration",
                                Objects.nonNull(lpo.getDuration()) ? String.valueOf(lpo.getDuration()) : "");
                        //分钟数过期时间，默认当前系统时间+30天(包括当天)
                        optionsMap.put("expireDate", DateFormatUtil.formatDateNormal(DateUtil.getDateAfterDays(29)));
                    } else if (BusinessBillingPlanCodeEnum.LIVE_FLOW.getCode().equals(billingPlanCode)) {
                        optionsMap.put("flow", Objects.nonNull(lpo.getFlow()) ? String.valueOf(lpo.getFlow()) : "");
                    }
                    optionsMap.put("amount", Objects.nonNull(lpo.getAmount()) ? String.valueOf(lpo.getAmount()) : "");
                    optionList.add(optionsMap);
                }
            }
        }
        return optionList;
    }
    
    private ResponseVO<Object> addResourceCheck(AddResourceRequest inputVO) {
        String resourceType = ResourceTypeEnum.getValue(inputVO.getResourceType());
        if (StringUtils.isBlank(resourceType)) {
            return ResponseVO.failure(PCSErrorCodeEnum.RESOURCE_TYPE_ERROR);
        }
        String businessType = BusinessTypeEnum.getValue(inputVO.getBusinessType());
        if (StringUtils.isBlank(businessType)) {
            return ResponseVO.failure(PCSErrorCodeEnum.BUSINESS_TYPE_ERROR);
        }
        String accountTypeDesc = AccountTypeEnum.getValue(inputVO.getAccountType());
        if (StringUtils.isBlank(accountTypeDesc)) {
            return ResponseVO.failure(PCSErrorCodeEnum.ACCOUNT_TYPE_ERROR);
        }
        //根据账号类型填充参数
        fillAccountParamsByAddResource(inputVO);
        //只有当前是并发峰值方式的才能打峰值套餐
        if (BusinessTypeEnum.LIVE_PEAK_CONCURRENCE.getCode().equals(inputVO.getBusinessType())) {
            List<BillingItem> billingItems = billingItemRepository.findByCode(ItemCodeConst.concur_peak.getCode());
            Integer itemId = 8; //峰值并发id
            if (CollectionUtils.isNotEmpty(billingItems)) {
                itemId = billingItems.get(0).getId();
            }
            LiveCustomerConcurrenceSetting customerCurrentEffectiveSettings =
                    liveCustomerConcurrenceSettingRepository.findCustomerCurrentEffectiveSetting(
                    inputVO.getCustomerId(), new Date(), itemId);
            //当前生效套餐不是峰值
            if (Objects.isNull(customerCurrentEffectiveSettings)) {
                return ResponseVO.failure(PCSErrorCodeEnum.PEAK_CONCURRENCE_ERROR);
            }
        }
        //赠送资源,需要校验是否开通合同金额 > 0 + 状态必须是开通的套餐
        if (ResourceTypeEnum.DONATE.getCode().equals(inputVO.getResourceType())) {
            List<Integer> status = Lists.newArrayList(SalesOpportunitiesStatusConst.associate.getStatus());
            boolean flag = salesOpportunitiesRepository.existsByCustomerIdAndAmountGainedGreaterThanAndStatusIn(
                    inputVO.getCustomerId(), 0L, status);
            if (!flag) {
                return ResponseVO.failure(PCSErrorCodeEnum.UN_COMMIT_CUSTOMER);
            }
        }

        //只有当前是并发峰值方式的才能打峰值套餐
        if (BusinessTypeEnum.RESOURCE_POINT.getCode().equals(inputVO.getBusinessType())) {
            // 资源点判断是否已开启资源点开关
            SettlementConfigVO configVO = settlementConfigService.querySettlementConfig(inputVO.getCustomerId());
            if (configVO == null || !SwitchEnum.isY(configVO.getResourcePointSettlement())) {
                return ResponseVO.failure(PCSErrorCodeEnum.RESOURCE_POINT_NOT_ENABLED);
            }
        }
        //可用额度校验
        CommonResult<AmountCalculateResultVO> amountDetail = calculateAmount(buildAmountCalculateInputVO(inputVO));
        //额度不足
        if (CommonResult.isOk(amountDetail) && amountDetail.getData().getStatus() != 1) {
            return ResponseVO.failure(PCSErrorCodeEnum.BALANCE_NOT_ENOUGH);
        } else {
            inputVO.setAmountCalculateResultVO(amountDetail.getData());
        }
        return ResponseVO.success();
    }
    
    private ResponseVO<Object> fillAccountParamsByAddResource(AddResourceRequest inputVO) {
        if (AccountTypeEnum.GROUP2.getCode().equals(inputVO.getAccountType())) {
            ResponseVO<GroupAccountVO> groupAccountInfo = groupAccountServiceApi.getById(inputVO.getCustomerId());
            if (!groupAccountInfo.isSuccess() || Objects.isNull(groupAccountInfo.getData())) {
                return ResponseVO.failure(PCSErrorCodeEnum.GROUP_ACCOUNT_NOT_EXIST);
            }
            GroupAccountVO groupAccountVO = groupAccountInfo.getData();
            inputVO.setEmail(groupAccountVO.getEmail());
            inputVO.setCompany(groupAccountVO.getCompany());
        }
        if (AccountTypeEnum.NORMAL.getCode().equals(inputVO.getAccountType())) {
            UserMsgVO user = UserClient.getUserByUnionId(inputVO.getCustomerId());
            if (Objects.isNull(user)) {
                return ResponseVO.failure(PCSErrorCodeEnum.NORMAL_ACCOUNT_NOT_EXIST);
            }
            inputVO.setVodUserId(user.getUserId());
            inputVO.setLiveUserId(user.getLiveUserId());
            inputVO.setEmail(user.getEmail());
            inputVO.setCompany(user.getCompanyName());
        }
        return null;
    }
    
    //获取月可用额度信息(基础+当月额外+当月使用+销售信息)
    private AvailableAmountDetailVO getSaleAvailableAmount(String saleUserId, String effectiveMonth) {
        AvailableAmountDetailVO detailVO = new AvailableAmountDetailVO();
        BigDecimal personalLeftAmount = new BigDecimal(0.00);
        //基础额度
        SaleDepartmentBaseAmount baseAmountObj = cacheService.getBySaleUserId(saleUserId);
        if (Objects.nonNull(baseAmountObj)) {
            BigDecimal baseAmount = new BigDecimal(baseAmountObj.getBaseAmount()).divide(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION), 2, RoundingMode.HALF_UP);
            personalLeftAmount = personalLeftAmount.add(baseAmount);
            detailVO.setBaseAmount(baseAmount);
            detailVO.setSaleDepartmentType(baseAmountObj.getSaleDepartmentType());
            detailVO.setSaleUserName(baseAmountObj.getSaleUserName());
        }
        //额外额度
        List<SaleExtraAmountRecord> extraAmountList = saleExtraAmountRecordRepository.findBySaleUserIdAndEffectiveMonth(
                saleUserId, effectiveMonth);
        if (CollectionUtils.isNotEmpty(extraAmountList)) {
            Long extraAmount = extraAmountList.stream().mapToLong(i -> i.getExtraAmount()).sum();
            BigDecimal extraAmountBd = new BigDecimal(extraAmount).divide(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION), 2, RoundingMode.HALF_UP);
            personalLeftAmount = personalLeftAmount.add(extraAmountBd);
            detailVO.setExtraAmount(extraAmountBd);
        }
        //当月使用额度
        long monthTotalCost = examinationDonateResourceSpecificationRepository.getSaleMonthSumAmountCost(saleUserId,
                effectiveMonth);
        if (monthTotalCost > 0) {
            BigDecimal monthCostAmount = new BigDecimal(monthTotalCost).divide(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION), 2, RoundingMode.HALF_UP);
            personalLeftAmount = personalLeftAmount.subtract(monthCostAmount);
            detailVO.setMonthCostAmount(monthCostAmount);
        }
        detailVO.setMonthLeftAmount(personalLeftAmount);
        return detailVO;
    }
    @Override
    public CommonResult<AmountCalculateResultVO> calculateBillingConfigItem(AmountCalculateRequest request,
            AddResourceRequest inputVO) {
        try {
            return CommonResult.ok(packageAccountAmount(request,
                    this.resourceBillingItemService.calculateAmountByBillingConfig(request)));
        } catch (Exception e) {
            log.error("计算计费项消耗额度失败：", e);
            return CommonResult.fail(e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<Object> billingItemConfigPackage(AddResourceRequest inputVO) throws ClearingSystemException {
        if (Objects.isNull(inputVO.getFunctionRequest()) && Objects.isNull(inputVO.getNumberRequest())) {
            return ResponseVO.failure(BizErrorCodeEnum.PARAM_REQUIRE);
        }
        //赠送资源,需要校验是否开通合同金额 > 0 + 状态必须是开通的套餐
        if (ResourceTypeEnum.DONATE.getCode().equals(inputVO.getResourceType())) {
            List<Integer> status = Lists.newArrayList(SalesOpportunitiesStatusConst.associate.getStatus());
            boolean flag = salesOpportunitiesRepository.existsByCustomerIdAndAmountGainedGreaterThanAndStatusIn(
                    inputVO.getCustomerId(), 0L, status);
            if (!flag) {
                return ResponseVO.failure(PCSErrorCodeEnum.UN_COMMIT_CUSTOMER);
            }
        }
        //前置参数填充->账号信息
        fillAccountParamsByAddResource(inputVO);
        //可用额度校验：额度不足会直接异常终止该操作
        AmountCalculateRequest request = BeanUtil.parse(inputVO, AmountCalculateRequest.class);
        //填充计费项配置信息用于额度消耗计算
        fillBillingItemParamsByCalculateAmount(request, inputVO);
        inputVO.setAmountCalculateResultVO(billingItemAmount(request, inputVO));
        CommonResult addResult = CommonResult.fail();
        if (Objects.nonNull(inputVO.getNumberRequest())) {
            addResult = this.resourceBillingItemService.numberBillItem(inputVO);
        }
        if (Objects.nonNull(inputVO.getFunctionRequest())) {
            addResult = this.resourceBillingItemService.ruleUseTimeBillingItem(inputVO);
        }
        if (CommonResult.isNotOk(addResult)) {
            log.error("打计费项配置化资源失败：{}", addResult.getMsg());
            throw new ClearingSystemRuntimeException(addResult.getMsg());
        }
        //生成打套餐明细
        CommonResult result = createPackageRecord(inputVO);
        if (CommonResult.isNotOk(result)) {
            log.error("生成套餐明细失败：{}", result.getMsg());
            throw new ClearingSystemRuntimeException(result.getMsg());
        }
    
        return ResponseVO.success();
    }
    
    /**
     * 根据打套餐删除填充计算额度消耗
     */
    private void fillBillingItemParamsByCalculateAmount(AmountCalculateRequest request, AddResourceRequest inputVO) {
        BillingItem  billingItem;
        //前端无法获取code
        if (Objects.nonNull(inputVO.getNumberRequest())) {
            request.setNumber(inputVO.getNumberRequest().getNumber());
            //前端无法获取code
            billingItem = this.billingItemHelper.getBillItem(request.getCode(), request.getBusinessType());
            request.setCode(billingItem.getCode());
            request.setExpireDate(inputVO.getNumberRequest().getExpireDate());
            inputVO.getNumberRequest().setItemCode(billingItem.getCode());
            inputVO.setEndDate(cn.hutool.core.date.DateUtil.parseDate(inputVO.getNumberRequest().getExpireDate()));
            inputVO.setSpecification(request.getNumber() + billingItem.getItemConsumedUnit());
            inputVO.setResourceCode(billingItem.getResourceCode());
        }
        if (Objects.nonNull(inputVO.getFunctionRequest())) {
            request.setExpireDate(inputVO.getFunctionRequest().getExpireDate());
            request.setCode(inputVO.getFunctionRequest().getCode());
            inputVO.setEndDate(cn.hutool.core.date.DateUtil.parseDate(inputVO.getFunctionRequest().getExpireDate()));
            billingItem = this.billingItemHelper.getBillItem(request.getCode(), request.getBusinessType());
            inputVO.setSpecification(billingItem.getName());
            inputVO.setResourceCode(billingItem.getResourceCode());
        }
        inputVO.setStartDate(DateUtil.getCurrentDay());
    }
    
    private AmountCalculateResultVO billingItemAmount(AmountCalculateRequest request,AddResourceRequest inputVO) throws ClearingSystemException {
        CommonResult<AmountCalculateResultVO> result = calculateBillingConfigItem(request, inputVO);
        if(CommonResult.isNotOk(result)){
            throw  new  ClearingSystemException(result.getMsg());
        }
        //额度不足
        if (PcsEnumConst.YES.getValue()!= result.getData().getStatus() ) {
            throw  new  ClearingSystemException(PCSErrorCodeEnum.BALANCE_NOT_ENOUGH.getDesc());
        }
        log.info("本次开通消耗额度：{}", result.getData());
        return result.getData();
    }
}
