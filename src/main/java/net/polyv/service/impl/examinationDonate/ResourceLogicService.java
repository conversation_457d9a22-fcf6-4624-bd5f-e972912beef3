package net.polyv.service.impl.examinationDonate;

import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.service.pack.LiveOldPackageOperaService;
import net.polyv.service.pack.PeriodResourceSettingService;
import net.polyv.service.resource.ResourceDepositService;
import net.polyv.util.DateFormatUtil;
import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.oldpackage.OldPackageAdjustInputVO;
import net.polyv.web.model.oldpackage.ResourceDepositInputVO;
import net.polyv.web.model.pack.PeriodResourceSettingSaveVO;

/**
 * 资源逻辑服务
 * 包含->周期资源->资源变动
 * <AUTHOR>
 * @date 2022/7/26 9:18
 */
@Slf4j
@Component
public class ResourceLogicService {
    @Autowired
    private LiveOldPackageOperaService liveOldPackageOperaService;
    @Autowired
    private PeriodResourceSettingService periodResourceSettingService;
    @Autowired
    private ResourceDepositService resourceDepositService;
    /**
     * 添加直播套餐
     * @param livePrevBillingPlan:上一个套餐类型
     * @param livePrevPackageStart:上一个套餐开始时间戳
     * @param livePrevPackageEnd:上一个套餐结束时间戳
     * @param unionId
     * @param billingPlan
     * @param startDate
     * @param endDate
     * @param isForce
     * @param concurrences
     * @param duration
     * @param useDailyTest
     * @return
     */
    public CommonResult saveLivePackage(String livePrevBillingPlan,Long livePrevPackageStart,Long livePrevPackageEnd,
            String unionId,String billingPlan,String startDate,String endDate,
            int isForce,long concurrences,long duration,int useDailyTest,Integer resourceType){
        
        log.info("groupId = {},livePrevBillingPlan = {},livePrevPackageStart = {},livePrevPackageEnd = {}",
                unionId,livePrevBillingPlan,livePrevPackageStart,livePrevPackageEnd);
        OldPackageAdjustInputVO request = new OldPackageAdjustInputVO();
        request.setCustomerId(unionId);
        request.setBillingPlan(billingPlan);
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        request.setIsForce(isForce);
        request.setConcurrences(concurrences);
        request.setUseDailyTest(useDailyTest);
        request.setDuration(duration);
        request.setAccountType(AccountTypeEnum.GROUP2.getCode());
        request.setResourceType(resourceType);
        if(StringUtils.isNotBlank(livePrevBillingPlan)){
            request.setLivePrevBillingPlan(livePrevBillingPlan);
        }
        if(Objects.nonNull(livePrevPackageStart) && livePrevPackageStart.intValue() != 0){
            request.setLivePrevPackageStart(DateFormatUtil.longToDateStr(livePrevPackageStart,DateFormatUtil.FORMAT_DATE_NORMAL));
        }
        if(Objects.nonNull(livePrevPackageEnd) && livePrevPackageEnd.intValue() != 0){
            request.setLivePrevPackageEnd(DateFormatUtil.longToDateStr(livePrevPackageEnd,DateFormatUtil.FORMAT_DATE_NORMAL));
        }
        
        liveOldPackageOperaService.addCustomerPackage(request);
        return CommonResult.ok();
        
    }
    
    public CommonResult savePeriodResource(long value,  int period, String unionId, String resourceCode,
            Date startDate, Date endDate, String contractId, String accountType, Integer resourceType){
        
        PeriodResourceSettingSaveVO vo = new PeriodResourceSettingSaveVO();
        vo.setPeriod(period);
        vo.setResourceCode(resourceCode);
        vo.setCustomerId(unionId);
        vo.setContractId(contractId);
        vo.setStartDate(startDate);
        vo.setEndDate(endDate);
        if(ResourceCodeConst.space.name().equals(resourceCode)){
            vo.setPeriodAmount(value);
        }
        if(ResourceCodeConst.traffic.name().equals(resourceCode)){
            vo.setPeriodAmount(value * period);
        }
        vo.setAccountType(accountType);
        vo.setResourceType(resourceType);
        CommonOperateResultVO result =  periodResourceSettingService.savePeriodResourceSetting(vo);
        if(CommonOperateResultVO.isSuccess(result)){
            return CommonResult.ok();
        }
        else{
            return CommonResult.fail(result.getReason());
        }
    }
    
    /**
     * 添加资源变动
     * @param unionId
     * @param deposit
     * @param resourceCode
     * @param source
     * @param contractId
     * @param startDate
     * @param endDate
     * @return
     */
    public CommonResult saveResourceAlteration(String unionId, long deposit, String resourceCode, int source,
            String contractId, Date startDate, Date endDate, String accountType,
            Integer resourceType){
        ResourceDepositInputVO request = new ResourceDepositInputVO();
        request.setUnionId(unionId);
        request.setDeposit(deposit);
        request.setResourceCode(resourceCode);
        request.setSource(source);
        request.setContractId(contractId);
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        request.setAccountType(accountType);
        request.setResourceType(resourceType);
        request.setExpireDate(endDate);
        CommonOperateResultVO result = resourceDepositService.depositResource(request);
        if(CommonOperateResultVO.isSuccess(result)){
            return CommonResult.ok();
        }
        else{
            return CommonResult.fail(result.getReason());
        }
        
    }

}


