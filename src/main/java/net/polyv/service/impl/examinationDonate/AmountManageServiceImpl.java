package net.polyv.service.impl.examinationDonate;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import net.polyv.modules.common.util.JacksonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.common.PrecisionConst;
import net.polyv.constant.examinationDonate.BusinessTypeEnum;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.dao.primary.business.SystemDictionaryRepository;
import net.polyv.dao.primary.examinationDonate.ExaminationDonateResourceSpecificationRepository;
import net.polyv.dao.primary.examinationDonate.SaleDepartmentBaseAmountRepository;
import net.polyv.dao.primary.examinationDonate.SaleExtraAmountRecordRepository;
import net.polyv.model.entity.primary.business.SystemDictionary;
import net.polyv.model.entity.primary.examinationDonate.SaleDepartmentBaseAmount;
import net.polyv.model.entity.primary.examinationDonate.SaleExtraAmountRecord;
import net.polyv.modules.pcs.api.req.AmountListRequest;
import net.polyv.modules.pcs.api.req.AmountUseDetailRequest;
import net.polyv.modules.pcs.api.req.ExtraAmountAddRequest;
import net.polyv.modules.pcs.api.vo.AmountListResultVO;
import net.polyv.modules.pcs.api.vo.AmountUseDetailResultVO;
import net.polyv.modules.pcs.api.vo.SaleExtraAmountRecordVO;
import net.polyv.service.examinationDonate.AmountManageService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.JsonMapper;
import net.polyv.web.model.common.PageDataVO;

/**
 * 测试赠送额度管理service实现类
 * <AUTHOR>
 * @since 2022/06/23
 */
@Service
@Slf4j
public class AmountManageServiceImpl implements AmountManageService {


    @Autowired
    private SaleDepartmentBaseAmountRepository saleDepartmentBaseAmountRepository;

    @Autowired
    private SaleExtraAmountRecordRepository saleExtraAmountRecordRepository;

    @Autowired
    private ExaminationDonateResourceSpecificationRepository examinationDonateResourceSpecificationRepository;

    @Resource
    private SystemDictionaryRepository  systemDictionaryRepository;
    
    @Override
    public List<SaleDepartmentBaseAmount> getBySaleUserName(String saleUserName) {
        return saleDepartmentBaseAmountRepository.findBySaleUserNameContaining(saleUserName);
    }


    @Override
    public CommonResult addExtraAmount(ExtraAmountAddRequest inputVO) {
        SaleExtraAmountRecord saleExtraAmountRecord = new SaleExtraAmountRecord();
        saleExtraAmountRecord.setExtraAmount(new BigDecimal(inputVO.getExtraAmount()).
                multiply(new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION)).longValue());
        saleExtraAmountRecord.setSaleUserId(inputVO.getSaleUserId());
        saleExtraAmountRecord.setOperator(inputVO.getOperator());
        saleExtraAmountRecord.setCreateTime(new Date());
        saleExtraAmountRecord.setEffectiveMonth(DateFormatUtil.formatDateMonth(new Date()));
        saleExtraAmountRecord =  saleExtraAmountRecordRepository.save(saleExtraAmountRecord);
        if(Objects.nonNull(saleExtraAmountRecord)){
            return CommonResult.ok();
        }
        return CommonResult.fail("添加额外额度报错");
    }

    @Override
    public PageDataVO<AmountUseDetailResultVO> pageAmountUseDetail(AmountUseDetailRequest inputVO) {
        Pageable pageable =
                PageRequest.of(inputVO.getPage() - 1, inputVO.getPageSize());
        String queryMonths = inputVO.getDate();
        if(StringUtils.isNotBlank(queryMonths)){
            log.warn("use deprecated param 'date' query amount use detail, request: {}", JacksonUtil.writeAsString(inputVO));
        }
        List<String> currentMonths = StringUtils.isNotBlank(queryMonths)
                ? Arrays.asList(queryMonths.split(","))
                : Collections.singletonList(""); // 不确定还有没有其他地方会使用旧参数, 暂时还要兼容一下, 所以IN查询还去不掉, 使用新参数时set个空串进去避免SQL解析IN查询失败
        String dateStart = inputVO.getDateStart();
        String dateEnd = inputVO.getDateEnd();
        Page<Map<String,Object>> page = examinationDonateResourceSpecificationRepository.listAmountUseDetail(inputVO,pageable,currentMonths, dateStart, dateEnd);
        List<AmountUseDetailResultVO> resultVOList = Lists.newArrayList();
        if(page.hasContent()){
            page.getContent().forEach(map -> {
                AmountUseDetailResultVO amountUseDetailResultVO = JsonMapper.objectToBean(map,AmountUseDetailResultVO.class);
                resultVOList.add(amountUseDetailResultVO);
            } );
            transferAmountUseDetailResult(resultVOList);
        }
        return buildAmountUseDetailPageData(page,resultVOList);
    }

    @Override
    public PageDataVO<AmountListResultVO> pageAmountList(AmountListRequest inputVO) {
        Pageable pageable =
                PageRequest.of(inputVO.getPage() - 1, inputVO.getPageSize());
        int pageSize = inputVO.getPageSize();
        int page = inputVO.getPage();
        int totalPage = 0;
        List<Map<String,Object>> list = saleDepartmentBaseAmountRepository.amountList(inputVO);
        //组装数据
        List<AmountListResultVO> resultVOList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(list)){
            for(Map<String,Object> map : list){
                AmountListResultVO contractListResultVO = JsonMapper.objectToBean(map,AmountListResultVO.class);
                resultVOList.add(contractListResultVO);
            }
            transferAmountListResult(resultVOList,inputVO.getDate());
            //内存分页按使用额度从大到小排序
            resultVOList = resultVOList.stream()
                    .sorted(Comparator.comparing(AmountListResultVO :: getMonthUsedAmount).reversed())
                    .skip((page-1) * pageSize).limit(pageSize).collect(Collectors.toList());
            totalPage = pageSize == 0 ? 1 : (int)Math.ceil((double)list.size() / (double)pageSize);
        }
        return buildAmountListPageData(page,pageSize,totalPage,list.size(),resultVOList);
    }

    //处理结果
    private  List<AmountListResultVO> transferAmountListResult(List<AmountListResultVO> list,String currentMonth){
        for(AmountListResultVO inputVO : list){

            inputVO.setCurrentMonth(currentMonth);
            inputVO.setBaseAmount(inputVO.getBaseAmount().divide(new BigDecimal(
                    PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION),2, RoundingMode.HALF_UP));

            List<SaleExtraAmountRecord> extraRecords = saleExtraAmountRecordRepository.findBySaleUserIdAndEffectiveMonth
                    (inputVO.getSaleUserId(), currentMonth);
            if(CollectionUtils.isNotEmpty(extraRecords)){

                long extraAmount = extraRecords.stream().mapToLong(SaleExtraAmountRecord :: getExtraAmount).sum();
                inputVO.setExtraAmount(new BigDecimal(extraAmount).divide(new BigDecimal(
                        PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION),2, RoundingMode.HALF_UP));
                List<SaleExtraAmountRecordVO> recordVOS = Lists.newArrayList();
                extraRecords.forEach(saleExtraAmountRecord -> {
                    SaleExtraAmountRecordVO recordVO = new SaleExtraAmountRecordVO();
                    recordVO.setExtraAmount(new BigDecimal(saleExtraAmountRecord.getExtraAmount()).divide(new BigDecimal(
                            PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION),2, RoundingMode.HALF_UP));
                    recordVO.setOperator(saleExtraAmountRecord.getOperator());
                    recordVO.setCreateDate(DateFormatUtil.formatDateNormal(saleExtraAmountRecord.getCreateTime()));
                    recordVOS.add(recordVO);
                } );
                inputVO.setExtraAmountDetail(recordVOS);
            }

            //当月个人剩余额度
            inputVO.setMonthTotalAmount(inputVO.getExtraAmount().add(inputVO.getBaseAmount()));

            //当月使用额度

            //当月使用额度
            long monthTotalCost = examinationDonateResourceSpecificationRepository.getSaleMonthSumAmountCost(
                    inputVO.getSaleUserId(),currentMonth);
            if(monthTotalCost > 0){
                BigDecimal monthCostAmount = new BigDecimal(monthTotalCost).divide(
                        new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION),2,RoundingMode.HALF_UP);
                inputVO.setMonthUsedAmount(monthCostAmount);
            }
        }
        return list;
    }

    //处理结果
    private  List<AmountUseDetailResultVO> transferAmountUseDetailResult(List<AmountUseDetailResultVO> list){
        for (AmountUseDetailResultVO amountUseDetailInputVO : list){
            amountUseDetailInputVO.setBusinessTypeDesc(findBusinessType(amountUseDetailInputVO.getBusinessType()));
            amountUseDetailInputVO.setResourceTypeDesc(ResourceTypeEnum.getValue(amountUseDetailInputVO.getResourceType()));
            amountUseDetailInputVO.setCostAmount(amountUseDetailInputVO.getCostAmount().divide(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION)));
        }
        return list;
    }
    private  String  findBusinessType(Integer type){
        //因为businessType 为计费项id 主键
        String value = BusinessTypeEnum.getValue(type);
        if (StringUtils.isNotBlank(value)){
            return value;
        }
        Optional<SystemDictionary> system = this.systemDictionaryRepository.findById(Long.valueOf(type));
        if(system.isPresent()){
            return  system.get().getLabel();
        }
        return "";
    }
    
    
    private PageDataVO<AmountListResultVO> buildAmountListPageData(int pageNumber,int pageSize,int totalPage,long totalElements,List<AmountListResultVO> list) {
        // 组装
        return PageDataVO.<AmountListResultVO>builder()
                .pageNumber(pageNumber)
                .pageSize(pageSize)
                .totalPages(totalPage)
                .totalItems(totalElements)
                .contents(list)
                .build();
    }

    private PageDataVO<AmountUseDetailResultVO> buildAmountUseDetailPageData(Page<Map<String,Object>> page,List<AmountUseDetailResultVO> list) {
        // 组装
        return PageDataVO.<AmountUseDetailResultVO>builder()
                .pageNumber(page.getNumber() + 1)
                .pageSize(page.getSize())
                .totalPages(page.getTotalPages())
                .totalItems(page.getTotalElements())
                .contents(list)
                .build();
    }
}
