package net.polyv.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.group.ResourceAssignBusinessTypeEnum;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.dao.primary.groupAccount.GroupConcurrenceBillingUserFinishRepository;
import net.polyv.dao.primary.groupAccount.GroupConcurrenceBillingUserRecordRepository;
import net.polyv.dao.primary.groupAccount.GroupDurationBillingUserRecordRepository;
import net.polyv.model.data.examinationDonate.LiveDepositPackage;
import net.polyv.model.data.group.GroupResourceAssignableCalculateDTO;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.model.entity.primary.groupAccount.GroupConcurrenceBillingUserFinish;
import net.polyv.model.entity.primary.groupAccount.GroupConcurrenceBillingUserRecord;
import net.polyv.model.entity.primary.groupAccount.GroupDurationBillingUserRecord;
import net.polyv.modules.user.api.service.group.GroupUserServiceApi;
import net.polyv.modules.user.api.stereotype.GroupConfigKey;
import net.polyv.modules.user.api.vo.GroupConfigVO;
import net.polyv.rest.client.live.business.LiveInnerFinanceApiClient;
import net.polyv.service.CacheService;
import net.polyv.service.SystemEnvService;

import net.polyv.service.impl.examinationDonate.ResourceBillingItemService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import cn.hutool.core.date.DateUtil;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.SysTypeConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.GroupLiveChargeTypeEnum;
import net.polyv.constant.common.PrecisionConst;
import net.polyv.constant.customer.LiveBillingType;
import net.polyv.constant.deposit.AmountDepositTypeEnum;
import net.polyv.constant.deposit.AmountExpireTypeEnum;
import net.polyv.constant.examinationDonate.BusinessTypeEnum;
import net.polyv.constant.examinationDonate.LiveDailyBillingPlanEnum;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.constant.resource.ResourceSourceConst;
import net.polyv.dao.primary.GroupAccountConfigRepository;
import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.model.data.salesopportunities.ExtObjectDO;
import net.polyv.model.data.salesopportunities.UserFunctionDO;
import net.polyv.model.entity.primary.GroupAccountConfig;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.AddResourceRequest;
import net.polyv.modules.pcs.api.req.AmountRequest;
import net.polyv.modules.pcs.api.req.GuideDurationRequest;
import net.polyv.modules.pcs.api.req.LiveDailyConcurrenceRequest;
import net.polyv.modules.pcs.api.req.LiveDurationRequest;
import net.polyv.modules.pcs.api.req.LivePeakConcurrenceRequest;
import net.polyv.modules.pcs.api.req.MicDurationRequest;
import net.polyv.modules.pcs.api.req.PrtcRequest;
import net.polyv.modules.pcs.api.req.VodFlowRequest;
import net.polyv.modules.pcs.api.req.VodPackageRequest;
import net.polyv.modules.pcs.api.req.VodSpaceRequest;
import net.polyv.modules.user.api.req.GroupAccountPackageChannelsReq;
import net.polyv.modules.user.api.req.GroupConfigSaveReq;
import net.polyv.modules.user.api.service.group.GroupAccountServiceApi;
import net.polyv.rest.model.live.UserFunctionVO;
import net.polyv.rest.util.ResponseUtil;
import net.polyv.service.AccountDepositService;
import net.polyv.service.GroupAccountEstablishPackageService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.pack.LiveOldPackageOperaService;
import net.polyv.service.pack.PeriodResourceSettingService;
import net.polyv.service.resource.ResourceDepositService;
import net.polyv.service.so.SalesOpportunitiesService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.JsonMapper;
import net.polyv.util.RandomUtils;
import net.polyv.util.converter.UnitConverterUtil;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.account.DepositInputVO;
import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.oldpackage.OldPackageAdjustInputVO;
import net.polyv.web.model.oldpackage.ResourceDepositInputVO;
import net.polyv.web.model.pack.PeriodResourceSettingSaveVO;
import net.polyv.web.model.salesopportunities.input.SalesOpportEstablishInputVO;

import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import net.polyv.modules.user.api.vo.GroupUserRelationVO;

@Service
@Slf4j
public class GroupAccountEstablishPackageServiceImpl implements GroupAccountEstablishPackageService {
    @Autowired
    private PeriodResourceSettingService periodResourceSettingService;
    
    @Autowired
    private ResourceDepositService resourceDepositService;
    
    @Autowired
    private LiveOldPackageOperaService liveOldPackageOperaService;
    
    @Autowired
    private GroupAccountConfigRepository groupAccountConfigRepository;
    @Autowired
    private LiveInnerFinanceApiClient liveInnerFinanceApiClient;
    @Autowired
    private GroupAccountServiceApi groupAccountServiceApi;
    
    @Autowired
    private AccountDepositService accountDepositService;
    
    @Autowired
    private DingWarnRobot dingWarnRobot;
    
    @Autowired
    private SystemEnvService systemEnvService;
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private SalesOpportunitiesService salesOpportunitiesService;
    
    @Resource
    private CacheService cacheService;
    
    @Resource
    private LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository;
    
    @Resource
    private GroupUserServiceApi groupUserServiceApi;
    
    @Resource
    private GroupConcurrenceBillingUserRecordRepository groupConcurrenceBillingUserRecordRepository;
    
    @Resource
    private GroupConcurrenceBillingUserFinishRepository groupConcurrenceBillingUserFinishRepository;
    
    @Resource
    private GroupDurationBillingUserRecordRepository groupDurationBillingUserRecordRepository;

    @Resource
    private ResourceBillingItemService resourceBillingItemService;
    
    @Override
    public void statGroupDurationConcurrenceBillingUser(String startDate, String endDate) {
        log.info("statGroupDurationConcurrenceBillingUser,startDate == {},endDate == {}", startDate, endDate);
        try {
        
            //默认前一天0点
            Date sDate = net.polyv.util.DateUtil.getDateAfterDays(-1, net.polyv.util.DateUtil.getCurrentDay());
            Date eDate = sDate;
            if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                sDate = DateFormatUtil.parseDateStr(startDate);
                eDate = DateFormatUtil.parseDateStr(endDate);
            }
            if (sDate.getTime() > eDate.getTime()) {
                throw new ClearingSystemRuntimeException("校验异常");
            }
        
            //获取所有2.0主账号
            List<GroupAccountConfig> accountConfigList = groupAccountConfigRepository.findAll();
            if (!CollectionUtils.isEmpty(accountConfigList)) {
                while (DateUtil.compare(eDate, sDate) >= 0) {
                    Date finalSDate = sDate;
                    accountConfigList.forEach(accountConfig -> {
                        String groupId = accountConfig.getGroupId();
                        //获取主账号下所有分账号列表
                        ResponseVO<List<net.polyv.modules.user.api.vo.GroupUserRelationVO>> userListResponse =
                                groupUserServiceApi.listAll(
                                groupId, null, null);
                        if (userListResponse.isSuccess() && !CollectionUtils.isEmpty(userListResponse.getData())) {
                            List<GroupUserRelationVO> groupUserlist = userListResponse.getData();
                            Map<String, String> unionIdLiveUserIdMap = groupUserlist.stream()
                                    .collect(Collectors.toMap(GroupUserRelationVO::getUnionId,
                                            GroupUserRelationVO::getLiveUserId, (i, j) -> i));
                            List<String> durationUserList = createGroupConcurrenceBillingUserRecord(finalSDate, groupId,
                                    unionIdLiveUserIdMap);
                            createGroupDurationBillingUserRecord(finalSDate, groupId, durationUserList);
                        }
                    });
                
                    //统计完成记录表新增记录
                    GroupConcurrenceBillingUserFinish concurrenceBillingUserFinish =
                            new GroupConcurrenceBillingUserFinish();
                    concurrenceBillingUserFinish.setCreateTime(new Date());
                    concurrenceBillingUserFinish.setStatAt(sDate);
                    concurrenceBillingUserFinish = groupConcurrenceBillingUserFinishRepository.save(
                            concurrenceBillingUserFinish);
                    if (Objects.isNull(concurrenceBillingUserFinish)) {
                        throw new ClearingSystemRuntimeException("保存并发计费名单汇总完成表异常");
                    }
                    sDate = net.polyv.util.DateUtil.getDateAfterDays(1, finalSDate);
                
                
                }
            }
        } catch (Exception e) {
            dingWarnRobot.sendWarnMsg("【统计并发计费分账号名单异常】",
                    String.format("请人工检查，错误信息 == %s", e.getLocalizedMessage()));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    
    }
    
    private void createGroupDurationBillingUserRecord(Date sDate, String groupId, List<String> durationUserList) {
        if (!CollectionUtils.isEmpty(durationUserList)) {
            durationUserList.forEach(unionId -> {
                GroupDurationBillingUserRecord groupDurationBillingUserRecord = new GroupDurationBillingUserRecord();
                groupDurationBillingUserRecord.setUnionId(unionId);
                groupDurationBillingUserRecord.setStatAt(sDate);
                groupDurationBillingUserRecord.setGroupId(groupId);
                groupDurationBillingUserRecord.setCreateTime(new Date());
                groupDurationBillingUserRecordRepository.save(groupDurationBillingUserRecord);
            });
        }
    }
    
    /**
     * 创建并发计费用户记录
     * @param sDate
     * @param groupId
     * @param unionIdLiveUserIdMap
     * @return 分钟数计费用户
     */
    private List<String> createGroupConcurrenceBillingUserRecord(Date sDate, String groupId,
            Map<String, String> unionIdLiveUserIdMap) {
        if (!CollectionUtils.isEmpty(unionIdLiveUserIdMap)) {
            List<String> unionIdList = new ArrayList<>(unionIdLiveUserIdMap.keySet());
            List<String> durationBillingUserList = Lists.newArrayList();
            Date finalSDate = sDate;
            List<String> concurrenceUserIds = Lists.newArrayList();
            //获取结束日期 <= sDate的生效中并发
            unionIdList.forEach(unionId -> {
                log.info("createGroupConcurrenceBillingUserRecord,unionId == {} ,finalSDate == {}", unionId,
                        finalSDate);
                LiveCustomerConcurrenceSetting setting =
                        liveCustomerConcurrenceSettingRepository.findUnTestCustomerCurrentEffectiveSetting(
                        unionId, finalSDate);
                //有生效中的并发套餐 && 创建时间 < 当天0点，说明使用并发计费
                if (Objects.nonNull(setting) && setting.getCreateTime().getTime() <
                        net.polyv.util.DateUtil.getDateAfterDays(1, finalSDate).getTime()) {
                    concurrenceUserIds.add(unionId);
                }
                //分钟数计费
                else {
                    durationBillingUserList.add(unionId);
                }
            });
            
            if (!CollectionUtils.isEmpty(concurrenceUserIds)) {
                List<GroupConcurrenceBillingUserRecord> userList = Lists.newArrayList();
                
                concurrenceUserIds.forEach(concurrenceUserId -> {
                    GroupConcurrenceBillingUserRecord concurrenceBillingUserRecord =
                            new GroupConcurrenceBillingUserRecord();
                    String liveUserId = unionIdLiveUserIdMap.getOrDefault(concurrenceUserId, null);
                    if (StringUtils.isBlank(liveUserId)) {
                        dingWarnRobot.sendWarnMsg("【统计并发计费分账号名单告警】",
                                String.format("unionId == %s，找不到liveUserId", concurrenceUserId));
                        return;
                    }
                    concurrenceBillingUserRecord.setLiveUserId(liveUserId);
                    concurrenceBillingUserRecord.setGroupId(groupId);
                    concurrenceBillingUserRecord.setStatAt(finalSDate);
                    concurrenceBillingUserRecord.setCreateTime(new Date());
                    userList.add(concurrenceBillingUserRecord);
                });
    
                //批量保存并发计费用户
                groupConcurrenceBillingUserRecordRepository.saveAll(userList);
            }
            
            return durationBillingUserList;
            
        }
        return null;
    }
    
    @Override
    public CommonResult enabledFunctionAccess(SalesOpportunities salesOpportunities) {
        SalesOpportEstablishInputVO funcEnableInputVO = buildFunctionRequest(salesOpportunities);
        return enabledFunctionAccess(funcEnableInputVO);
    }
    
    private CommonResult enabledFunctionAccess(SalesOpportEstablishInputVO inputVO) {
        ExtObjectDO extObjectDO = inputVO.getExtObjectDO();
        // 开通功能
        try {
            if (!CollectionUtils.isEmpty(extObjectDO.getFunctionList())) {
                setFunctionEffect(inputVO.getSalesOpportunitiesId(), extObjectDO.getFunctionList(), inputVO.getCustomerId(), inputVO.getFunctionList());
            }
        } catch (Exception e) {
            log.error("集团主账号开通功能失败, customerId={}, addFunctionList={}, linkMicLimit={}", inputVO.getCustomerId(),
                    extObjectDO.getLinkMicLimit(), extObjectDO.getFunctionList(), e);
            dingWarnRobot.sendWarnMsg("【告警：开通功能异常】",
                    String.format("集团主账号开通功能失败, customerId=%s, addFunctionList=%s, linkMicLimit=%s, exception=%s", inputVO.getCustomerId(),
                            extObjectDO.getFunctionList(), extObjectDO.getLinkMicLimit(), e.getMessage()));
            throw new ClearingSystemRuntimeException("执行功能开通，异常信息：" + e.getMessage());
        }
        return CommonResult.ok();
    }
    
    
    /**
     * 功能设置
     *
     * @param id 对应的销售机会
     * @param functionDOList 设置的功能列表
     * @param customerId 客户id
     * @param queryCodes 修改成功后，需要查询的快照
     */
    public void setFunctionEffect(Long id, List<UserFunctionDO> functionDOList, String customerId, List<UserFunctionVO> queryCodes)
            throws Exception {
        if(!systemEnvService.isProdEnv() && !systemEnvService.isTestAllowUser(customerId)){
            return;
        }
        Map<SysTypeConst, List<UserFunctionDO>> functionMap = functionDOList.stream().collect(
                Collectors.groupingBy(UserFunctionDO::getSys, Collectors.toList()));
        // 目前直播、小班课、研讨会都在直播系统，因此需要合并
        UserFunctionDO liveScenes = null;
        UserFunctionDO meetingScenes = null;
        List<UserFunctionDO> liveUserFunction = Lists.newArrayList();
        if (functionMap.containsKey(SysTypeConst.Live)) {
            liveScenes = addListAndReturnScenes(functionMap.get(SysTypeConst.Live), liveUserFunction);
        }
        // TODO 等项目拆分
        if (functionMap.containsKey(SysTypeConst.Meeting)) {
            meetingScenes = addListAndReturnScenes(functionMap.get(SysTypeConst.Meeting), liveUserFunction);
        }
        // TODO 等项目拆分
        if (functionMap.containsKey(SysTypeConst.SmallClass))
            liveUserFunction.addAll(functionMap.get(SysTypeConst.SmallClass));
    
        if (functionMap.containsKey(SysTypeConst.Vod))
            liveUserFunction.addAll(functionMap.get(SysTypeConst.Vod));
    
        //集团2.0主账号特有功能开关
        if (functionMap.containsKey(SysTypeConst.GroupV2))
            liveUserFunction.addAll(functionMap.get(SysTypeConst.GroupV2));
        // scenes 合并
        if (null!=liveScenes && null!=meetingScenes) {
            liveScenes.setValueName(liveScenes.getValueName() + "," + meetingScenes.getValueName());
            liveScenes.setValue(liveScenes.getValue() + "," + meetingScenes.getValue());
            liveUserFunction.remove(meetingScenes);
        }
    
        // 集团2.0主账号功能开通
        enabledFunction(customerId,liveUserFunction);
        // 快照的用户功能
        if(!CollectionUtils.isEmpty(queryCodes)){
            List<UserFunctionVO> snapshotUserUserDOList = customerService.getGroupV2FunctionList(customerId, Lists.newArrayList(queryCodes));
            salesOpportunitiesService.updateSnapshotFunctionList(id, Lists.newArrayList(snapshotUserUserDOList));
        }
    }
    
    /**
     * 调用集团主账号接口开通功能
     */
    private void enabledFunction(String groupId,List<UserFunctionDO> liveUserFunction){
        Map<String,Object> configMaps = Maps.newHashMap();
        //先屏蔽掉点播的
        for(UserFunctionDO userFunctionDO : liveUserFunction){
            switch (userFunctionDO.getSys()) {
                case GroupV2:
                    String code = userFunctionDO.getCode().split("_")[1];
                    //暂时过滤掉频道数开关，在打分钟数套餐赠送频道数和功能开关同时开通的情况下，会出现频道数被覆盖的情况，后续优化
                    if (!GroupConfigKey.MAX_CHANNELS.getConfigKey().equals(code)) {
                        configMaps.put(code, userFunctionDO.getValue());
                    }
                    break;
            }
        }
        if(configMaps.size() > 0){
            GroupConfigSaveReq req = new GroupConfigSaveReq();
            req.setGroupId(groupId);
            req.setConfigMaps(configMaps);
            log.info("setGroupFunction 请求参数 == {}",req);
            ResponseVO responseVO = groupAccountServiceApi.setGroupFunction(req);
            if(Objects.isNull(responseVO) || !responseVO.isSuccess()){
                dingWarnRobot.sendWarnMsg("【设置主账号功能开关报错】",String.format("请求参数：%s", JsonMapper.jsonToString(req)));
                throw new ClearingSystemRuntimeException("setGroupFunction error");
            }
        }
    
    }
    /**
     * 从功能列表添加到指定列表，然后找出code为scenes的返回回来
     *
     * @param userFunctionDOList  功能列表
     * @param target 指定列表
     * @return code为scenes的UserFunctionDO
     */
    private UserFunctionDO addListAndReturnScenes(List<UserFunctionDO> userFunctionDOList, List<UserFunctionDO> target) {
        UserFunctionDO scenes = null;
        for (UserFunctionDO u : userFunctionDOList) {
            target.add(u);
            if (com.alibaba.druid.util.StringUtils.equals("scenes", u.getCode())) scenes = u;
        }
        return scenes;
    }
    /**
     * 构建功能开关开通请求参数
     * @param salesOpportunities
     * @return
     */
    private SalesOpportEstablishInputVO buildFunctionRequest(SalesOpportunities salesOpportunities){
        ExtObjectDO extObjectDO = JSON.parseObject(salesOpportunities.getExt(), ExtObjectDO.class);
        SalesOpportEstablishInputVO funcEnableInputVO = new SalesOpportEstablishInputVO();
        funcEnableInputVO.setSalesOpportunitiesId(salesOpportunities.getId());
        funcEnableInputVO.setCustomerId(salesOpportunities.getCustomerId());
        List<UserFunctionDO> snapshotFunctionList = extObjectDO.getSnapshotFunctionList();
        List<UserFunctionVO> userFunctionVOList = snapshotFunctionList == null ? Collections.emptyList() :
                snapshotFunctionList.stream().map(UserFunctionVO::new).collect(Collectors.toList());
        funcEnableInputVO.setFunctionList(userFunctionVOList);
        funcEnableInputVO.setSo(salesOpportunities);
        funcEnableInputVO.setExtObjectDO(extObjectDO);
        return funcEnableInputVO;
    }
    
    
    @Override
    public CommonResult addVodPackage(AddResourceRequest addResourceRequest) {
        GroupAccountConfig groupAccountConfig = groupAccountConfigRepository.findByGroupId(addResourceRequest.getCustomerId()).orElse(null);
        String contractId = RandomUtils.getRandStr(10);
        VodPackageRequest vodPackage = addResourceRequest.getVodPackageRequest();
        long space = UnitConverterUtil.GB2bytes(new BigDecimal(vodPackage.getPackageSpace()));
        long flow =  UnitConverterUtil.GB2bytes(new BigDecimal(vodPackage.getPackageMonthFlow()));
        Date startDate = DateFormatUtil.parseDateNormal(DateUtil.now());
        //添加空间
        CommonResult result = savePeriodResource(0L,space,1,addResourceRequest.getCustomerId(),ResourceCodeConst.space.name(),
                startDate,vodPackage.getExpireDate(),contractId,
                AccountTypeEnum.GROUP2.getCode(),addResourceRequest.getResourceType());
        if(CommonResult.isOk(result)){
            result =  savePeriodResource(flow,0L,1,addResourceRequest.getCustomerId(),ResourceCodeConst.traffic.name(),
                    startDate,vodPackage.getExpireDate(),contractId,
                    AccountTypeEnum.GROUP2.getCode(),addResourceRequest.getResourceType());
            if(CommonResult.isOk(result)) {
                //修改跟随主账号套餐到期的分账号时间
                customerService.updateGroupUserExpireDate(addResourceRequest.getCustomerId(),
                        BusinessTypeEnum.VOD_PACKAGE, vodPackage.getExpireDate());
                //重算主账号剩余可分配资源
                GroupResourceAssignableCalculateDTO spaceDto = GroupResourceAssignableCalculateDTO.builder()
                        .groupId(addResourceRequest.getCustomerId())
                        .businessType(ResourceAssignBusinessTypeEnum.SPACE.getCode())
                        .build();
                cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                        JsonMapper.jsonToString(spaceDto));
    
                GroupResourceAssignableCalculateDTO trafficDto = GroupResourceAssignableCalculateDTO.builder()
                        .groupId(addResourceRequest.getCustomerId())
                        .businessType(ResourceAssignBusinessTypeEnum.TRAFFIC.getCode())
                        .build();
                cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                        JsonMapper.jsonToString(trafficDto));
    
                return postProcess(groupAccountConfig, startDate, vodPackage.getExpireDate(), null, null, null, null,
                        vodPackage.getTestPackageName(), null);
            }
    
        }
        return result;
    }
    
    public CommonResult postProcess(GroupAccountConfig groupAccountConfig, Date vodStartDate, Date vodEndDate,
            Date liveStartDate, Date liveEndDate, String liveBillingPlan, String clearingPlan, String vodPackageName,
            String livePackageName) {
        if (Objects.nonNull(groupAccountConfig)) {
            if (StringUtils.isNotBlank(vodPackageName)) {
                groupAccountConfig.setVodPackageName(vodPackageName);
            }
            if (StringUtils.isNotBlank(livePackageName)) {
                groupAccountConfig.setLivePackageName(livePackageName);
            }
            if (Objects.nonNull(vodStartDate)) {
                groupAccountConfig.setVodStart(vodStartDate.getTime());
            }
            if (Objects.nonNull(vodEndDate)) {
                groupAccountConfig.setVodEnd(vodEndDate.getTime());
            }
            if (Objects.nonNull(liveStartDate)) {
                groupAccountConfig.setLiveStart(liveStartDate.getTime());
            }
            if (Objects.nonNull(liveEndDate)) {
                groupAccountConfig.setLiveEnd(liveEndDate.getTime());
            }
            if(StringUtils.isNotBlank(liveBillingPlan)){
                groupAccountConfig.setLiveConcurrenceType(liveBillingPlan);
                //当前是直播分钟数，才修改
                if(LiveBillingType.Duration.getValue().equals(liveBillingPlan)){
                    groupAccountConfig.setGroupLiveBillingPlan(GroupLiveChargeTypeEnum.MINUTES.getCode());
                }
                //结算类型不为空 + 并发包天
                if((LiveBillingType.Daily.getValue().equals(liveBillingPlan) || LiveBillingType.Prtc_Daily.getValue().equals(liveBillingPlan))
                        && StringUtils.isNotBlank(clearingPlan)){
                    groupAccountConfig.setGroupLiveBillingPlan(clearingPlan);
                }
            }
            
            
            groupAccountConfigRepository.save(groupAccountConfig);
        }
        return CommonResult.ok();
        
    }
    @Override
    public CommonResult addVodSpace(AddResourceRequest AddResourceRequest) {
        String contractId = RandomUtils.getRandStr(10);
        VodSpaceRequest spaceRequest = AddResourceRequest.getVodSpaceRequest();
        Date startDate = DateFormatUtil.parseDateNormal(DateUtil.now());
        long space = UnitConverterUtil.GB2bytes(new BigDecimal(spaceRequest.getVodSpace()));
        CommonResult result = saveResourceAlteration(AddResourceRequest.getCustomerId(), space,
                ResourceCodeConst.space.name(), ResourceSourceConst.TEMP, contractId, startDate,
                spaceRequest.getSpaceExpireDate(), AccountTypeEnum.GROUP2.getCode(),
                AddResourceRequest.getResourceType());
        if (CommonResult.isOk(result)) {
            //重算主账号剩余可分配资源
            GroupResourceAssignableCalculateDTO spaceDto = GroupResourceAssignableCalculateDTO.builder()
                    .groupId(AddResourceRequest.getCustomerId())
                    .businessType(ResourceAssignBusinessTypeEnum.SPACE.getCode())
                    .build();
            cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                    JsonMapper.jsonToString(spaceDto));
        }
        return result;
    
    }
    
    @Override
    public CommonResult addVodFlow(AddResourceRequest AddResourceRequest) {
        String contractId = RandomUtils.getRandStr(10);
        VodFlowRequest flowRequest = AddResourceRequest.getVodFlowRequest();
        Date startDate = DateFormatUtil.parseDateNormal(DateUtil.now());
        long flow = UnitConverterUtil.GB2bytes(new BigDecimal(flowRequest.getVodFlow()));
        int source = -1;
        //永久
        if ("4".equals(String.valueOf(flowRequest.getTrafficType()))) {
            source = ResourceSourceConst.PERMANENT;
        }
        CommonResult result = saveResourceAlteration(AddResourceRequest.getCustomerId(), flow,
                ResourceCodeConst.traffic.name(), source, contractId, startDate, flowRequest.getFlowExpireDate(),
                AccountTypeEnum.GROUP2.getCode(), AddResourceRequest.getResourceType());
        if (CommonResult.isOk(result)) {
            //重算主账号剩余可分配资源
            GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                    .groupId(AddResourceRequest.getCustomerId())
                    .businessType(ResourceAssignBusinessTypeEnum.TRAFFIC.getCode())
                    .build();
            cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                    JsonMapper.jsonToString(dto));
        }
        return result;
    }
    
    @Override
    public CommonResult addGuideDuration(AddResourceRequest AddResourceRequest) {
        GuideDurationRequest guideDurationRequest = AddResourceRequest.getGuideDurationRequest();
        CommonResult result = saveResourceAlteration(AddResourceRequest.getCustomerId(),
                guideDurationRequest.getDuration(), ResourceCodeConst.guide_duration.name(),
                ResourceSourceConst.PERMANENT, null, null, guideDurationRequest.getExpireDate(),
                AccountTypeEnum.GROUP2.getCode(), AddResourceRequest.getResourceType());
        if (CommonResult.isOk(result)) {
            //重算主账号剩余可分配资源
            GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                    .groupId(AddResourceRequest.getCustomerId())
                    .businessType(ResourceAssignBusinessTypeEnum.GUIDE_DURATION.getCode())
                    .build();
            cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                    JsonMapper.jsonToString(dto));
        }
        return result;
    }
    
    @Override
    public CommonResult addMicDuration(AddResourceRequest AddResourceRequest) {
        MicDurationRequest micDurationRequest = AddResourceRequest.getMicDurationRequest();
        CommonResult result = saveResourceAlteration(AddResourceRequest.getCustomerId(),
                micDurationRequest.getDuration(), ResourceCodeConst.mic_duration.name(), ResourceSourceConst.PERMANENT,
                null, null, micDurationRequest.getExpireDate(), AccountTypeEnum.GROUP2.getCode(),
                AddResourceRequest.getResourceType());
        if (CommonResult.isOk(result)) {
            //重算主账号剩余可分配资源
            GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                    .groupId(AddResourceRequest.getCustomerId())
                    .businessType(ResourceAssignBusinessTypeEnum.MIC_DURATION.getCode())
                    .build();
            cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                    JsonMapper.jsonToString(dto));
        }
        return result;
    }
    
    @Override
    public CommonResult addChannels(AddResourceRequest AddResourceRequest) {
        long channels = AddResourceRequest.getAddChannelsRequest().getChannels();
        if(Objects.nonNull(channels) && channels > 0){
            GroupAccountPackageChannelsReq increaseChannelReq = new GroupAccountPackageChannelsReq();
            increaseChannelReq.setChannels((int)channels);
            increaseChannelReq.setGroupId(AddResourceRequest.getCustomerId());
            log.info("addGroupAccountChannels request == {}",increaseChannelReq);
            ResponseVO<?> responseVO = groupAccountServiceApi.increasePackageChannels(increaseChannelReq);
            if(!responseVO.isSuccess()){
                return CommonResult.fail("increasePackageChannels error");
            }
        }
        return CommonResult.ok();
    }
    
    @Override
    public CommonResult addPrtcDuration(AddResourceRequest AddResourceRequest) {
        PrtcRequest prtcRequest = AddResourceRequest.getPrtcRequest();
        long duration = new BigDecimal(prtcRequest.getAmount()).divide(PrecisionConst.DEFAULT_PRTC_PRICE_PER_MINUTE).longValue();
        return saveResourceAlteration(AddResourceRequest.getCustomerId(),duration,ResourceCodeConst.prtc_duration.name(),
                ResourceSourceConst.PERMANENT,null,null,prtcRequest.getExpireDate(),
                AccountTypeEnum.GROUP2.getCode(),AddResourceRequest.getResourceType());
    }
    
    @Override
    public CommonResult addAmount(AddResourceRequest request){
        AmountRequest amountRequest = request.getAmountRequest();
        DepositInputVO depositRequest = new DepositInputVO();
        depositRequest.setCustomerId(request.getCustomerId());
        if(ResourceTypeEnum.TEST.getCode().equals(request.getResourceType())){
            depositRequest.setAmountDepositType(AmountDepositTypeEnum.TEST.getCode());
            depositRequest.setTestAmount(new BigDecimal(amountRequest.getAmount()).multiply(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION)).longValue());
            depositRequest.setPayUnpayEnabled(amountRequest.getPayUnpayEnabled());
        }
        if(ResourceTypeEnum.DONATE.getCode().equals(request.getResourceType())){
            depositRequest.setAmountDepositType(AmountDepositTypeEnum.DONATE.getCode());
            depositRequest.setDonate(new BigDecimal(amountRequest.getAmount()).multiply(
                    new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION)).longValue());
        }
        depositRequest.setAmountExpireDate(amountRequest.getExpireDate());
        depositRequest.setAmountExpireType(AmountExpireTypeEnum.POSTPONE.getCode());
        depositRequest.setAccountType(request.getAccountType());
        accountDepositService.depositTestAndDonateAmount(depositRequest);
    
        //修改跟随主账号套餐到期的分账号时间
        customerService.updateGroupUserExpireDate(request.getCustomerId(), BusinessTypeEnum.AMOUNT,
                amountRequest.getExpireDate());
    
        //重算主账号剩余可分配资源
        GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                .groupId(request.getCustomerId())
                .businessType(ResourceAssignBusinessTypeEnum.AMOUNT.getCode())
                .build();
        cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                JsonMapper.jsonToString(dto));
    
    
        return CommonResult.ok();
    }
    
    @Override
    public CommonResult addPeakConcurrence(AddResourceRequest addResourceRequest) {
        LivePeakConcurrenceRequest peakConcurrenceRequest = addResourceRequest.getLivePeakConcurrenceRequest();
        GroupAccountConfig accountConfig = groupAccountConfigRepository.findByGroupId(addResourceRequest.getCustomerId()).orElse(null);
        if(Objects.nonNull(accountConfig)){
            String livePrevBillingPlan = accountConfig.getLiveConcurrenceType();
            Long livePrevPackageStart = accountConfig.getLiveStart();
            Long livePrevPackageEnd = accountConfig.getLiveEnd();
            String startDate = DateFormatUtil.formatDateNormal(peakConcurrenceRequest.getLiveStartDate());
            String endDate = DateFormatUtil.formatDateNormal(peakConcurrenceRequest.getLiveEndDate());
    
            CommonResult result = saveLivePackage(livePrevBillingPlan,livePrevPackageStart,livePrevPackageEnd,
                    addResourceRequest.getCustomerId(),
                    LiveBillingType.Peak.getValue(), startDate,endDate, 0,peakConcurrenceRequest.getConcurrence(),0,0
                    ,addResourceRequest.getResourceType());
            if(CommonResult.isOk(result)) {
                //修改跟随主账号套餐到期的分账号时间
                customerService.updateGroupUserExpireDate(addResourceRequest.getCustomerId(),
                        BusinessTypeEnum.LIVE_PEAK_CONCURRENCE, peakConcurrenceRequest.getLiveEndDate());
                //重算主账号剩余可分配资源
                GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                        .groupId(addResourceRequest.getCustomerId())
                        .businessType(ResourceAssignBusinessTypeEnum.CONCURRENCE.getCode())
                        .build();
                cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                        JsonMapper.jsonToString(dto));
    
                return postProcess(accountConfig, null, null, DateFormatUtil.parseDateNormal(startDate),
                        DateFormatUtil.parseDateNormal(endDate), LiveBillingType.Peak.getValue(), null, null, null);
            }
        }
        return CommonResult.ok();
    }
    
    @Override
    public CommonResult addLiveDuration(AddResourceRequest addResourceRequest) {
        
        LiveDurationRequest durationRequest = addResourceRequest.getLiveDurationRequest();
        
        GroupAccountConfig accountConfig = groupAccountConfigRepository.findByGroupId(addResourceRequest.getCustomerId()).orElse(null);
        if(Objects.nonNull(accountConfig)){
            String livePrevBillingPlan = accountConfig.getLiveConcurrenceType();
            Long livePrevPackageStart = accountConfig.getLiveStart();
            Long livePrevPackageEnd = accountConfig.getLiveEnd();
            String startDate = DateFormatUtil.formatDateNormal(new Date());
            String endDate = DateFormatUtil.formatDateNormal(durationRequest.getExpireDate());
    
            CommonResult result = saveLivePackage(livePrevBillingPlan,livePrevPackageStart,livePrevPackageEnd,
                    addResourceRequest.getCustomerId(),
                    LiveBillingType.Duration.getValue(), startDate,endDate,
                    0,-1L,durationRequest.getDuration(),0,addResourceRequest.getResourceType());
            if(CommonResult.isOk(result)) {
                //修改跟随主账号套餐到期的分账号时间
                customerService.updateGroupUserExpireDate(addResourceRequest.getCustomerId(),
                        BusinessTypeEnum.LIVE_DURATION, durationRequest.getExpireDate());
                //重算主账号剩余可分配资源
                GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                        .groupId(addResourceRequest.getCustomerId())
                        .businessType(ResourceAssignBusinessTypeEnum.DURATION.getCode())
                        .build();
                cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                        JsonMapper.jsonToString(dto));
                //素材库资源对齐套餐时间
                String expiredDate = DateFormatUtil.formatDateNormal(durationRequest.getExpireDate());
                try {
                    resourceBillingItemService.addAndResetMaterialResource(addResourceRequest.getCustomerId(),"materialtraffic",10,expiredDate,new Date(),true);
                    resourceBillingItemService.addAndResetMaterialResource(addResourceRequest.getCustomerId(),"materialspace",10,expiredDate,new Date(),true);
                }catch (Exception ex){

                }

                return postProcess(accountConfig, null, null, DateFormatUtil.parseDateNormal(startDate),
                        DateFormatUtil.parseDateNormal(endDate), LiveBillingType.Duration.getValue(), null, null,
                        getNameByLivePackageId(durationRequest.getLiveTestPackageId()));
            }
        }
        return CommonResult.ok();
    }
    
    
    /**
     * 根据直播套餐id获取名字
     * @param packageId
     * @return
     */
    private String getNameByLivePackageId(Integer packageId) {
        ResponseEntity<WrappedResponse<?>> lpoResult = this.liveInnerFinanceApiClient.getDepositPackages();
        if (!ResponseUtil.isErrorResponse(lpoResult) && Objects.nonNull(lpoResult.getBody().getData())) {
            List<LiveDepositPackage> lpoList = JsonMapper.objectToComplicatedBean(lpoResult.getBody().getData(),
                    List.class, LiveDepositPackage.class);
            for (LiveDepositPackage lpo : lpoList) {
                if (lpo.getPackageId().equals(packageId)) {
                    return lpo.getName();
                }
            }
        }
        return null;
    }
    
    @Override
    public CommonResult addLiveDailyConcurrence(AddResourceRequest addResourceRequest) {
        LiveDailyConcurrenceRequest dailyRequest = addResourceRequest.getLiveDailyConcurrenceRequest();
        Integer billingPlanType = dailyRequest.getBillingPlanType();
        GroupAccountConfig accountConfig = groupAccountConfigRepository.findByGroupId(
                addResourceRequest.getCustomerId()).orElse(null);
        if (Objects.nonNull(accountConfig)) {
            String livePrevBillingPlan = accountConfig.getLiveConcurrenceType();
            Long livePrevPackageStart = accountConfig.getLiveStart();
            Long livePrevPackageEnd = accountConfig.getLiveEnd();
            String startDate = DateFormatUtil.formatDateNormal(dailyRequest.getLiveStartDate());
            String endDate = DateFormatUtil.formatDateNormal(dailyRequest.getLiveEndDate());
            Integer useDailyTest = 0 ;
            if(Objects.nonNull(billingPlanType) && LiveDailyBillingPlanEnum.TEST.getCode().equals(billingPlanType)){
                useDailyTest = 1;
            }
            String liveBillingType = LiveBillingType.Daily.getValue();
            BusinessTypeEnum liveBillingTypeEnum = BusinessTypeEnum.LIVE_DAILY_TEST;
            if(BusinessTypeEnum.LIVE_PRTC_DAILY_TEST.getCode() == addResourceRequest.getBusinessType()){
                liveBillingType = LiveBillingType.Prtc_Daily.getValue();
                liveBillingTypeEnum = BusinessTypeEnum.LIVE_PRTC_DAILY_TEST;

            }
            CommonResult result =  saveLivePackage(livePrevBillingPlan,livePrevPackageStart,livePrevPackageEnd,
                    addResourceRequest.getCustomerId(),
                    liveBillingType, startDate,endDate,
                    0,dailyRequest.getConcurrence(),0,useDailyTest,addResourceRequest.getResourceType());
            if(CommonResult.isOk(result)) {
                //修改跟随主账号套餐到期的分账号时间
                customerService.updateGroupUserExpireDate(addResourceRequest.getCustomerId(),
                        liveBillingTypeEnum, dailyRequest.getLiveEndDate());
                //重算主账号剩余可分配资源
                GroupResourceAssignableCalculateDTO dto = GroupResourceAssignableCalculateDTO.builder()
                        .groupId(addResourceRequest.getCustomerId())
                        .businessType(ResourceAssignBusinessTypeEnum.CONCURRENCE.getCode())
                        .build();
                cacheService.publishMessage(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE,
                        JsonMapper.jsonToString(dto));
                return postProcess(accountConfig, null, null, DateFormatUtil.parseDateNormal(startDate),
                        DateFormatUtil.parseDateNormal(endDate), liveBillingType,
                        dailyRequest.getClearingPlan(), null, null);
            }
        }
        return CommonResult.ok();
    }
    
    /**
     * 添加直播套餐
     * @param livePrevBillingPlan:上一个套餐类型
     * @param livePrevPackageStart:上一个套餐开始时间戳
     * @param livePrevPackageEnd:上一个套餐结束时间戳
     * @param unionId
     * @param billingPlan
     * @param startDate
     * @param endDate
     * @param isForce
     * @param concurrences
     * @param duration
     * @param useDailyTest
     * @return
     */
    private CommonResult saveLivePackage(String livePrevBillingPlan, Long livePrevPackageStart, Long livePrevPackageEnd,
            String unionId, String billingPlan, String startDate, String endDate, int isForce, long concurrences,
            long duration, Integer useDailyTest, Integer resourceType) {
        
        log.info("groupId = {},livePrevBillingPlan = {},livePrevPackageStart = {},livePrevPackageEnd = {}", unionId,
                livePrevBillingPlan, livePrevPackageStart, livePrevPackageEnd);
        OldPackageAdjustInputVO request = new OldPackageAdjustInputVO();
        request.setCustomerId(unionId);
        request.setBillingPlan(billingPlan);
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        request.setIsForce(isForce);
        request.setConcurrences(concurrences);
        request.setUseDailyTest(useDailyTest);
        request.setDuration(duration);
        request.setAccountType(AccountTypeEnum.GROUP2.getCode());
        request.setResourceType(resourceType);
        if(StringUtils.isNotBlank(livePrevBillingPlan)){
            request.setLivePrevBillingPlan(livePrevBillingPlan);
        }
        if(Objects.nonNull(livePrevPackageStart) && livePrevPackageStart.intValue() != 0){
            request.setLivePrevPackageStart(DateFormatUtil.longToDateStr(livePrevPackageStart,DateFormatUtil.FORMAT_DATE_NORMAL));
        }
        if(Objects.nonNull(livePrevPackageEnd) && livePrevPackageEnd.intValue() != 0){
            request.setLivePrevPackageEnd(DateFormatUtil.longToDateStr(livePrevPackageEnd,DateFormatUtil.FORMAT_DATE_NORMAL));
        }
        
        liveOldPackageOperaService.addCustomerPackage(request);
        return CommonResult.ok();
        
    }
    
    private CommonResult savePeriodResource(long flow, long space, int period, String unionId, String resourceCode,
            Date startDate, Date endDate, String contractId, String accountType, Integer resourceType) {
        
        PeriodResourceSettingSaveVO vo = new PeriodResourceSettingSaveVO();
        vo.setPeriod(period);
        vo.setResourceCode(resourceCode);
        vo.setCustomerId(unionId);
        vo.setContractId(contractId);
        vo.setStartDate(startDate);
        vo.setEndDate(endDate);
        if (ResourceCodeConst.space.name().equals(resourceCode)) {
            vo.setPeriodAmount(space);
        }
        if(ResourceCodeConst.traffic.name().equals(resourceCode)){
            vo.setPeriodAmount(flow * period);
        }
        vo.setAccountType(accountType);
        vo.setResourceType(resourceType);
        CommonOperateResultVO result =  periodResourceSettingService.savePeriodResourceSetting(vo);
        if(CommonOperateResultVO.isSuccess(result)){
            return CommonResult.ok();
        }
        else{
            return CommonResult.fail(result.getReason());
        }
    }
    
    /**
     * 添加资源变动
     * @param unionId
     * @param deposit
     * @param resourceCode
     * @param source
     * @param contractId
     * @param startDate
     * @param endDate
     * @return
     */
    private CommonResult saveResourceAlteration(String unionId, long deposit, String resourceCode, int source,
            String contractId, Date startDate, Date endDate, String accountType, Integer resourceType) {
        ResourceDepositInputVO request = new ResourceDepositInputVO();
        request.setUnionId(unionId);
        request.setDeposit(deposit);
        request.setResourceCode(resourceCode);
        request.setSource(source);
        request.setContractId(contractId);
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        request.setAccountType(accountType);
        request.setResourceType(resourceType);
        request.setExpireDate(endDate);
        CommonOperateResultVO result = resourceDepositService.depositResource(request);
        if(CommonOperateResultVO.isSuccess(result)){
            return CommonResult.ok();
        }
        else{
            return CommonResult.fail(result.getReason());
        }
        
    }
}
