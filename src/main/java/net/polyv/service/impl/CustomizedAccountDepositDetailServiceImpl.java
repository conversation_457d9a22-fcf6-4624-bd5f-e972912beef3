package net.polyv.service.impl;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.polyv.dao.primary.CustomizedAccountDepositDetailRepository;
import net.polyv.dao.primary.CustomizedAccountUnEffectiveDepositDetailRepository;
import net.polyv.model.entity.primary.CustomizedAccountDepositDetail;
import net.polyv.model.entity.primary.CustomizedAccountUnEffectiveDepositDetail;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.ResourceDepositListGetRequest;
import net.polyv.modules.pcs.api.stereotype.PCSErrorCodeEnum;
import net.polyv.modules.pcs.api.vo.ResourceDepositListResultVO;
import net.polyv.service.CustomizedAccountDepositDetailService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CustomizedAccountDepositDetailServiceImpl implements CustomizedAccountDepositDetailService {
    
    @Resource
    private CustomizedAccountDepositDetailRepository customizedAccountDepositDetailRepository;
    
    
    @Resource
    private CustomizedAccountUnEffectiveDepositDetailRepository customizedAccountUnEffectiveDepositDetailRepository;
    
    /**
     * 充值记录列表（已生效或未生效）
     * @param request
     * @return
     */
    @Override
    public ResponseVO<Map<String, List<ResourceDepositListResultVO>>> depositList(
            ResourceDepositListGetRequest request) {
        Map<String, List<ResourceDepositListResultVO>> result = Maps.newHashMap();
        if (StringUtils.isNotBlank(request.getCustomerIds())) {
            String[] customerIdArray = request.getCustomerIds().split(",");
            if (customerIdArray.length > 10) {
                return ResponseVO.failure(PCSErrorCodeEnum.CUSTOMER_COUNT_OVER_10);
            }
            for (String customerId : customerIdArray) {
                
                List<ResourceDepositListResultVO> resultVOList = Lists.newArrayList();
                //已生效
                List<CustomizedAccountDepositDetail> depositDetailList =
                        customizedAccountDepositDetailRepository.getDepositDetailList(
                        customerId, request.getBusinessType());
                if (CollectionUtils.isNotEmpty(depositDetailList)) {
                    depositDetailList.forEach(depositDetail -> {
                        ResourceDepositListResultVO resourceDepositListResultVO = new ResourceDepositListResultVO();
                        BeanUtils.copyProperties(depositDetail, resourceDepositListResultVO);
                        resultVOList.add(resourceDepositListResultVO);
                    });
                }
                
                //未生效
                List<CustomizedAccountUnEffectiveDepositDetail> unEffectiveDepositDetailList =
                        customizedAccountUnEffectiveDepositDetailRepository
                        .getDepositDetailList(customerId, request.getBusinessType());
                if (CollectionUtils.isNotEmpty(unEffectiveDepositDetailList)) {
                    unEffectiveDepositDetailList.forEach(depositDetail -> {
                        ResourceDepositListResultVO resourceDepositListResultVO = new ResourceDepositListResultVO();
                        BeanUtils.copyProperties(depositDetail, resourceDepositListResultVO);
                        resultVOList.add(resourceDepositListResultVO);
                    });
                }
                if (CollectionUtils.isNotEmpty(resultVOList)) {
                    result.put(customerId, resultVOList);
                }
            }
        }
        return ResponseVO.success(result);
    }
}
