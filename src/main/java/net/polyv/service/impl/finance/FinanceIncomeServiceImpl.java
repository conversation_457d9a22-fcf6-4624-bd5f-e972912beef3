package net.polyv.service.impl.finance;

import com.alibaba.excel.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.bill.BillStatusConst;
import net.polyv.constant.business.BusinessBillingPlanCodeEnum;
import net.polyv.constant.finance.ContractAssociationStatusEnum;
import net.polyv.constant.finance.ContractOperationCodeEnum;
import net.polyv.constant.finance.ContractOriginEnum;
import net.polyv.constant.finance.FinanceIncomeBelongEnum;
import net.polyv.constant.finance.FinanceIncomeDetailTagEnum;
import net.polyv.constant.finance.FinanceTaxRateConst;
import net.polyv.constant.finance.StatusValueEnum;
import net.polyv.constant.finance.SubCategoryEnum;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.dao.primary.AccountRechargeRecordRepository;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.business.BusinessPackageSpecificationRepository;
import net.polyv.dao.primary.difinition.BusinessEnumDefinitionRepository;
import net.polyv.dao.primary.finance.FinanceContractRepository;
import net.polyv.dao.primary.finance.FinanceContractSpecificationRepository;
import net.polyv.dao.primary.finance.FinanceIncomeDetailRepository;
import net.polyv.dao.primary.finance.FinanceIncomeRepository;
import net.polyv.dao.primary.finance.FinanceRefundRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.AccountRechargeRecord;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.CustomerBillingDaily;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.model.entity.primary.definition.BusinessEnumDefinition;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.model.entity.primary.finance.FinanceContractSpecificationEntity;
import net.polyv.model.entity.primary.finance.FinanceIncomeDetailEntity;
import net.polyv.model.entity.primary.finance.FinanceIncomeEntity;
import net.polyv.model.entity.primary.finance.FinanceRefundEntity;
import net.polyv.rest.client.dmp.LiveUserDurationClient;
import net.polyv.rest.client.group.GroupOneAccountClient;
import net.polyv.rest.client.vod.user.UnionUserClient;
import net.polyv.service.FinanceIncomeConfirmService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.bill.BillingConsumeDataService;
import net.polyv.service.bill.BillingDataService;
import net.polyv.service.bill.BillingService;
import net.polyv.service.finance.FinanceContractService;
import net.polyv.service.finance.FinanceIncomeService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.JsonMapper;
import net.polyv.util.MoneyUtil;
import net.polyv.web.model.finance.data.BillingItemAttributeDTO;
import net.polyv.web.model.finance.data.CurrentFinanceIncomeDTO;
import net.polyv.web.model.finance.input.Bills4FinanceQueryDO;
import net.polyv.web.model.finance.input.ContractSpecQueryDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_DOWN;
import static java.math.BigDecimal.ROUND_HALF_EVEN;
import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * @description: 财务收入服务实现
 * @author: Neo
 * @date: 2022-07-29
 */
@Slf4j
@Service
public class FinanceIncomeServiceImpl implements FinanceIncomeService {

    // 财务合同dao
    @Autowired
    private FinanceContractRepository contractRepository;


    // 合同收入dao
    @Autowired
    private FinanceIncomeRepository incomeRepository;

    // 合同收入明细dao
    @Autowired
    private FinanceIncomeDetailRepository incomeDetailRepository;

    // 账单service
    @Autowired
    private BillingService billingService;

    // 用户消耗service
    @Autowired
    private BillingConsumeDataService billingConsumeDataService;

    // 用户账单service
    @Autowired
    private BillingDataService billingDataService;

    // 退费dao
    @Autowired
    private FinanceRefundRepository refundRepository;

    // 计费项dao
    @Autowired
    private BillingItemRepository billingItemRepository;

    // 账单dao
    @Autowired
    private CustomerBillingDailyRepository customerBillingDailyRepository;

    // 合同规格dao
    @Autowired
    private FinanceContractSpecificationRepository contractSpecificationRepository;

    // 订单规格dao
    @Autowired
    private BusinessPackageSpecificationRepository businessSpecificationRepository;

    // 订单dao
    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;

    // 用户服务
    @Autowired
    private CustomerService customerService;

    // 直播查询接口
    @Autowired
    private LiveUserDurationClient liveUserDurationClient;

    // 集团账号1.0查询
    @Autowired
    private GroupOneAccountClient groupOneAccountClient;

    // 点播用户服务
    @Autowired
    private UnionUserClient unionUserClient;

    // 合同服务
    @Autowired
    private FinanceContractService contractService;

    // 收入确认服务
    @Autowired
    private FinanceIncomeConfirmService confirmService;

    // 用户充值dao
    @Autowired
    private AccountRechargeRecordRepository accountRechargeRecordRepository;

    // 计费项枚举定义dao
    @Autowired
    private BusinessEnumDefinitionRepository definitionRepository;

    @Resource
    private DingWarnRobot dingWarnRobot;


    @Override
    @Transactional(rollbackFor = {Exception.class, ClearingSystemException.class})
    public CommonResult initContractIncome(String contractId) throws ClearingSystemException {
        log.info("initContractIncome contractId == {}", contractId);
        if (StringUtils.isEmpty(contractId)) {
            return CommonResult.fail("初始化合同收入失败，合同ID为空");
        }
        FinanceContractEntity contractEntity = this.contractRepository.findByContractId(contractId);
        log.info("initContractIncome contractEntity == {}", contractEntity);
        if (Objects.isNull(contractEntity)) {
            return CommonResult.fail(String.format("初始化合同收入失败，合同ID{%s}对应合同实体为空", contractId));
        }

        try {
            String currentMonth = DateFormatUtil.formatDateMonth(contractEntity.getCreateTime());
            // 检查是否存在当前合同的当月收入数据
            FinanceIncomeEntity existsCurrentMonthIncome = this.incomeRepository.findByContractIdAndIncomeMonth(contractId, currentMonth);
            log.info("initContractIncome existsCurrentMonthIncome == {}", existsCurrentMonthIncome);
            if (Objects.isNull(existsCurrentMonthIncome)) {
                // 创建收入
                CommonResult<FinanceIncomeEntity> createIncomeEntityResult = this.createFinanceIncome(contractEntity);
                if (CommonResult.isNotOk(createIncomeEntityResult)) {
                    return createIncomeEntityResult;
                }
                FinanceIncomeEntity fiEntity = createIncomeEntityResult.getData();
                this.incomeRepository.save(fiEntity);
                // 创建收入明细
                CommonResult<FinanceIncomeDetailEntity> createIncomeDetailResult = this.createFinanceIncomeDetail(contractEntity);
                if (CommonResult.isNotOk(createIncomeDetailResult)) {
                    dingWarnRobot.sendWarnMsg("【创建收入明细失败】",
                            String.format("参数：%s，失败原因：%s", contractEntity, createIncomeDetailResult.getMsg()));
                    log.info("initContractIncome createIncomeDetailResult == {}", createIncomeDetailResult.getMsg());
                    return createIncomeDetailResult;
                }
                FinanceIncomeDetailEntity fide = createIncomeDetailResult.getData();
                this.incomeDetailRepository.save(fide);
            }
        } catch (Exception e) {
            log.error(String.format("an exception occurred initContractIncome %s", e.getCause().getMessage()), e);
            throw new ClearingSystemException(e);
        }
        return CommonResult.ok();
    }

    /**
     * 初始化创建合同收入对象
     */
    private CommonResult<FinanceIncomeEntity> createFinanceIncome(FinanceContractEntity contractEntity) {
        Date current = new Date();
        String currentMonth = DateFormatUtil.formatDateMonth(contractEntity.getCreateTime());
        // 查询存在
        FinanceIncomeEntity incomeEntity = this.incomeRepository.findByContractIdAndIncomeMonth(contractEntity.getContractId(), currentMonth);
        if (Objects.isNull(incomeEntity)) {
            incomeEntity = new FinanceIncomeEntity();
            incomeEntity.setCreateTime(current);
            incomeEntity.setContractId(contractEntity.getContractId());
            incomeEntity.setIncomeMonth(currentMonth);
            incomeEntity.setTotalIncome(0L);
            incomeEntity.setUsageIncome(0L);
            incomeEntity.setOtherIncome(0L);
            incomeEntity.setCumulativeIncome(0L);
            incomeEntity.setCurrentContractQuantity(contractEntity.getCurrentQuantity());
            incomeEntity.setCurrentContractAmount(contractEntity.getCurrentAmount());
            // 递延不含税
            BigDecimal realContractAmount = this.contractService.getRealContractAmount(contractEntity, current, FinanceTaxRateConst.NORMAL.getTaxRate());
            incomeEntity.setDeferredIncome(realContractAmount.longValue());
        }
        incomeEntity.setUpdateTime(current);
        return CommonResult.ok(incomeEntity);
    }

    /**
     * 创建收入明细实体
     */
    private CommonResult<FinanceIncomeDetailEntity> createFinanceIncomeDetail(FinanceContractEntity contractEntity) {
        CommonResult<String> contractBillingPlanCodeResult = this.contractService.getContractBillingPlanCode(contractEntity);
        if (CommonResult.isNotOk(contractBillingPlanCodeResult)) {
            return CommonResult.fail(String.format("合同 %s获取业务类别为空", contractEntity.getContractId()));
        }
        String billingPlanCode = contractBillingPlanCodeResult.getData();
        Date current = new Date();
        // 税率
        long currentTaxRate = 106;
        FinanceIncomeDetailEntity incomeDetailEntity = this.incomeDetailRepository.findContractLastIncomeDetail(contractEntity.getContractId(), contractEntity.getIncomeMonth());
        if (BusinessBillingPlanCodeEnum.HARD_ALL.getCode().equals(billingPlanCode)) {
            currentTaxRate = 113;
        }
        String currentMonth = DateFormatUtil.formatDateMonth(contractEntity.getCreateTime());
        if (Objects.isNull(incomeDetailEntity)) {
            incomeDetailEntity = new FinanceIncomeDetailEntity();
            incomeDetailEntity.setContractId(contractEntity.getContractId());
            incomeDetailEntity.setIncomeMonth(currentMonth);
            incomeDetailEntity.setTaxRate(currentTaxRate);
            incomeDetailEntity.setSubCategory(contractEntity.getSubCategory());
            // 收入归属，金额类型在确认收入会使用到
            incomeDetailEntity.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByOperationCode(contractEntity.getOperationCode()));
            // 收入单位，金额类型在确认收入会使用到
            incomeDetailEntity.setConsumeUnit(contractEntity.getQuantityUnit());
            incomeDetailEntity.setCurrentIncome(0L);
            incomeDetailEntity.setCumulativeIncome(0L);
            incomeDetailEntity.setCurrentConsume(0L);
            incomeDetailEntity.setCumulativeConsume(0L);
            incomeDetailEntity.setUnivalent(contractEntity.getIncomeUnivalent());
            incomeDetailEntity.setUnivalentUnit(contractEntity.getIncomeUnivalentUnit());
            incomeDetailEntity.setSettledStatus(StatusValueEnum.NO.getValue());
            incomeDetailEntity.setCreateTime(current);
        }
        incomeDetailEntity.setUpdateTime(current);
        incomeDetailEntity.setStatus(StatusValueEnum.YES.getValue());
        return CommonResult.ok(incomeDetailEntity);
    }

    @Override
    public CommonResult addFinanceIncome(FinanceIncomeEntity entity) {
        Date current = new Date();
        if (Objects.isNull(entity)) {
            return CommonResult.fail("实体不存在");
        }
        if (Objects.isNull(entity.getId())) {
            // 新增
            entity.setCreateTime(current);
        }
        entity.setUpdateTime(current);
        this.incomeRepository.save(entity);
        return CommonResult.ok();
    }

    @Override
    public CommonResult addFinanceIncomeDetail(FinanceIncomeDetailEntity entity) {
        Date current = new Date();
        if (Objects.isNull(entity)) {
            return CommonResult.fail("实体不存在");
        }
        if (Objects.isNull(entity.getId())) {
            // 新增
            entity.setCreateTime(current);
        }
        entity.setUpdateTime(current);
        entity.setStatus(StatusValueEnum.YES.getValue());
        this.incomeDetailRepository.save(entity);
        return CommonResult.ok();
    }

    @Override
    public CommonResult<FinanceIncomeEntity> getByContractIdAndIncomeMonth(String contractId, String incomeMonth) {
        if (StringUtils.isEmpty(contractId) || StringUtils.isEmpty(incomeMonth)) {
            return CommonResult.fail("参数条件非法");
        }
        FinanceIncomeEntity financeIncomeEntity = this.incomeRepository.findByContractIdAndIncomeMonth(contractId, incomeMonth);
        if (Objects.isNull(financeIncomeEntity)) {
            return CommonResult.notFound();
        }
        return CommonResult.ok(financeIncomeEntity);
    }

    @Override
    public CommonResult<FinanceIncomeEntity> getNearestByContractIdAndIncomeMonth(String contractId, String incomeMonth) {
        if (StringUtils.isEmpty(contractId) || StringUtils.isEmpty(incomeMonth)) {
            return CommonResult.fail("参数条件非法");
        }
        FinanceIncomeEntity nearestIncomeEntity = this.incomeRepository.findNearestByContractIdAndIncomeMonth(contractId, incomeMonth);
        if (Objects.isNull(nearestIncomeEntity)) {
            return CommonResult.notFound();
        }
        return CommonResult.ok(nearestIncomeEntity);
    }

    @Override
    public CommonResult<FinanceIncomeDetailEntity> getNearestDetailByBillingItemId(String contractId, String incomeMonth, Integer billingItemId) {
        if (StringUtils.isEmpty(contractId) || StringUtils.isEmpty(incomeMonth) || Objects.isNull(billingItemId)) {
            return CommonResult.fail("参数条件非法");
        }
        FinanceIncomeDetailEntity nearestIncomeDetailEntity = this.incomeDetailRepository.findNearestByBillingItemId(contractId, billingItemId, incomeMonth);
        if (Objects.isNull(nearestIncomeDetailEntity)) {
            return CommonResult.notFound();
        }
        return CommonResult.ok(nearestIncomeDetailEntity);
    }

    @Override
    public CommonResult<FinanceIncomeDetailEntity> getIncomeDetailByBillingItemId(String contractId, String incomeMonth,
            Integer billingItemId) {
        if (StringUtils.isEmpty(contractId) || StringUtils.isEmpty(incomeMonth) || Objects.isNull(billingItemId)) {
            return CommonResult.fail("参数条件非法");
        }
        FinanceIncomeDetailEntity incomeDetail =
                this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId, incomeMonth,
                        billingItemId);
        if (Objects.isNull(incomeDetail)) {
            return CommonResult.notFound();
        }
        return CommonResult.ok(incomeDetail);
    }
    
    /**
     * 由于直播流量是在第二天下午才出账，确认收入是在早上六点四十，这里需要改为查两天前的，不然会确认不到上个月最后一天账单
     * @param specifyDate
     * @return
     * @throws ClearingSystemException
     */
    @Override
    public CommonResult liveFlowStatistics(Date specifyDate) throws ClearingSystemException {
        if (Objects.isNull(specifyDate)) {
            specifyDate = new Date();
        }
        specifyDate = DateUtil.getDateAfterDays(-2, specifyDate);
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        //根据合同确认收入
        calcIncomeByContract(specifyDate);
        
        List<String> processOperationCodeList = Lists.newArrayList(ContractOperationCodeEnum.LIVE_FLOW.getCode());
        for (String operationCode : processOperationCodeList) {
            calcBillingDailyConsumedIncome(operationCode, specifyDate, currentMonth);
        }
        return CommonResult.ok();
    }
    
    @Override
    public CommonResult statistics(Date specifyDate) throws ClearingSystemException {
        if (Objects.isNull(specifyDate)) {
            specifyDate = new Date();
        }
        specifyDate = DateUtil.getDateAfterDays(-1, specifyDate);
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        // 处理退费调账、退费的合同，设置未完成状态
        this.confirmService.adjustUnfinishedContractStatus(specifyDate);
        // 处理本月关联的预开通和月结重新计算收入流程
        this.confirmService.confirmAssociatedContractIncome(specifyDate);
        // 处理本月退费的，但是合同套餐已经不在本周期确认收入的合同
        this.confirmService.confirmRefundContractIncome(specifyDate);
        // 处理时间节点之后的点播流量包、年流量的合同统计
        confirmVodYearlyIncomeAfterSpecifyDate(specifyDate);
    
        // 处理时间节点之后的直播套餐流量的合同统计
        //confirmLiveFlowIncomeAfterSpecifyDate(specifyDate);
        // 确认一次性收入
        this.confirmService.confirmOnetimeIncome(specifyDate);
        //根据合同确认收入
        calcIncomeByContract(specifyDate);
    
        List<String> processOperationCodeList = Lists.newArrayList(ContractOperationCodeEnum.LIVE_DURATION.getCode(),
                // 处理直播分钟数统计
                ContractOperationCodeEnum.LIVE_CONCURRENT_PEAK.getCode(), // 单独统计并发峰值
                ContractOperationCodeEnum.LIVE_GUIDE_DURATION.getCode(), // 单独统计导播台分钟数计费
                ContractOperationCodeEnum.PPT_PAGE_PAY.getCode(), // 单独统计ppt按页数计费
                ContractOperationCodeEnum.LIVE_MIC_DURATION.getCode(), // 单独统计连麦分钟数计费
                ContractOperationCodeEnum.VOD_PACKAGE_V1.getCode(), // 统计集团账号点播套餐
                ContractOperationCodeEnum.VOD_FLOW_V1.getCode(), // 统计集团账号点播流量
                ContractOperationCodeEnum.LIVE_MIC_DURATION_V1.getCode(), // 单独统计集团账号连麦分钟数计费
                ContractOperationCodeEnum.LIVE_DURATION_V1.getCode(),
                ContractOperationCodeEnum.LIVE_FLOW.getCode()
        );// 单独统计集团账号分钟数计费
        for (String operationCode : processOperationCodeList) {
            calcBillingDailyConsumedIncome(operationCode, specifyDate, currentMonth);
        }
        // 计算没有计入当月的0元结算
        // statsZeroFinanceInfo(specifyDate);
        // 执行补当月缺失明细数据
        this.confirmService.generateMissingIncomeDetail(specifyDate);
        return CommonResult.ok();
    }

    private void statsZeroFinanceInfo(Date specifyDate) {
        // 是否是当月收入类型
        Date endTime = DateUtil.getDateEnd(DateUtil.getLastDayOfMonth(specifyDate));
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        // 查询已有合同当月未有统计且未完成统计的合同
        List<FinanceContractEntity> zeroContractList = this.contractRepository.getZeroFinanceContract(currentMonth, endTime);
        if (CollectionUtils.isEmpty(zeroContractList)) {
            return;
        }
        for (FinanceContractEntity contract : zeroContractList) {
            Date startMonth = DateUtil.getFirstDayOfMonth(endTime);
            String contractWay = contract.getContractWay();
            if (StringUtils.isNotEmpty(contractWay) && ContractOriginEnum.ORIGIN_MONTHLY_SETTLE.getCode().equals(contractWay)) {
                // 月结不在这里结算
                continue;
            }
            if (contract.getCreateTime().getTime() > endTime.getTime()) {
                continue;
            }
            String operationCode = contract.getOperationCode();
            // 排除充值类型
            if (ContractOperationCodeEnum.DEPOSIT_AMOUNT.getCode().equals(operationCode)
                    || ContractOperationCodeEnum.OVER_AMOUNT_PAY.getCode().equals(operationCode)) {
                continue;
            }
            CommonResult<BusinessPackageSpecification> contractSpecResult = this.contractService.getContractSpec(contract, specifyDate);
            if (CommonResult.isNotOk(contractSpecResult)) {
                continue;
            }
            BusinessPackageSpecification contractSpec = contractSpecResult.getData();
            Date endT = contractSpec.getValidPeriodEndDate();
            FinanceIncomeEntity nearestIncome = this.incomeRepository.findNearestByContractIdAndIncomeMonth(contract.getContractId(), currentMonth);
            boolean hasLastFinance = (null != nearestIncome);
            if (hasLastFinance) {
                if (nearestIncome.getDeferredIncome() <= 0) {
                    // 完成统计
                    this.finishContractStatus(contract, currentMonth);
                    continue;
                }
            }

            CurrentFinanceIncomeDTO currentFinanceIncome = this.getCurrentFinanceIncome(contract, endTime);
            // 合同已经结算结束
            if (Objects.nonNull(endT) && endT.getTime() < startMonth.getTime()) {
                // 合同结算已经结束但是合同未结束
                if (!hasLastFinance) {
                    log.info("执行statsZeroFinanceInfo !hasLastFinance设置收入...");
                    this.setCurrentFinanceIncome(contract, currentFinanceIncome, BigDecimal.ZERO,
                            currentFinanceIncome.getContractAvailable(), BigDecimal.ZERO, currentFinanceIncome.getDeferredQuantity());
                }
                continue;
            }
            // 是否为最后一个月
            boolean isEnd = Objects.nonNull(endT) && endTime.getTime() > endT.getTime();

            BigDecimal currentUsed = isEnd ? currentFinanceIncome.getContractAvailable() : BigDecimal.ZERO;
            BigDecimal currentUsedQuantity = isEnd ? currentFinanceIncome.getDeferredQuantity() : BigDecimal.ZERO;
            log.info("执行statsZeroFinanceInfo设置收入...");
            this.setCurrentFinanceIncome(contract, currentFinanceIncome, BigDecimal.ZERO, currentUsed, BigDecimal.ZERO, currentUsedQuantity);
        }
    }


    // 根据合同确认收入
    private void calcIncomeByContract(Date specifyDate) {
        // 新版财务统计开始时间
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        String preMonth = DateFormatUtil.formatDateMonth(DateUtil.getFirstDayOfPreviousMonth(specifyDate));
        String financeStartDate = "2018-01-01 00:00:00";
        // 该方法处理的包含的合同业务类别
        List<String> includeOperationCodeList = Lists.newArrayList(ContractOperationCodeEnum.ONLINE_PACKAGE.getCode(),
                ContractOperationCodeEnum.VOD_MONTHLY_PACKAGE.getCode(),
                ContractOperationCodeEnum.COOL_MONTHLY_PACKAGE.getCode(),
                ContractOperationCodeEnum.LIVE_CONCURRENT_DAILY.getCode(),
                ContractOperationCodeEnum.LIVE_CONCURRENT_MONTHLY.getCode(),
                ContractOperationCodeEnum.LIVE_PRTC_CONCURRENT_DAILY.getCode(),
                ContractOperationCodeEnum.LIVE_PRTC_CONCURRENT_MONTHLY.getCode(),
                ContractOperationCodeEnum.LIVE_CONCURRENT_V1.getCode(),
                ContractOperationCodeEnum.DEPOSIT_AMOUNT.getCode(), ContractOperationCodeEnum.OVER_AMOUNT_PAY.getCode(),
                ContractOperationCodeEnum.PPT_LINE_PAY.getCode(),
                ContractOperationCodeEnum.RESOURCE_POINT.getCode()
        );
        ContractSpecQueryDO searchDO = ContractSpecQueryDO.builder()
                .businessStartDate(financeStartDate)
                .currentMonth(currentMonth)
                .operationCodeList(includeOperationCodeList)
                .build();
        List<String> allContractIdList = this.contractRepository.getContractIdBySearchDO(searchDO);
        // List<String> contractIdList = Lists.newArrayList("ed5b248130-RWxIJv9qlA");
        if (CollectionUtils.isEmpty(allContractIdList)) {
            return;
        }
        // contractIdList太大导致StackOverFlow
        List<List<String>> partitionContractIdList = Lists.partition(allContractIdList, 600);
        for (List<String> contractIdList : partitionContractIdList) {
            Map<String, FinanceContractEntity> contractMap = new HashMap<>();
            // 组装查询结果数据
            if (!CollectionUtils.isEmpty(contractIdList)) {
                contractIdList = contractIdList.stream().distinct().collect(Collectors.toList());
                List<FinanceContractEntity> contractList = this.contractRepository.findByContractIdIn(contractIdList);
                contractMap = contractList.stream().filter(f -> StringUtils.isNotEmpty(f.getContractId()))
                        .collect(Collectors.toMap(FinanceContractEntity::getContractId, Function.identity(), (o, n) -> n, HashMap::new));
            }
            for (String contractId : contractIdList) {
                // 合同实体
                FinanceContractEntity currentContract = contractMap.get(contractId);
                if (Objects.isNull(currentContract)) {
                    continue;
                }
                String contractWay = currentContract.getContractWay();
                if (StringUtils.isNotEmpty(contractWay) && ContractOriginEnum.ORIGIN_MONTHLY_SETTLE.getCode().equals(contractWay)) {
                    // 月结不在这里结算
                    continue;
                }
                // 合同规格
                CommonResult<BusinessPackageSpecification> contractSpecResult = this.contractService.getContractSpec(currentContract, specifyDate);
                if (CommonResult.isNotOk(contractSpecResult)) {
                    continue;
                }
                BusinessPackageSpecification contractSpec = contractSpecResult.getData();
                String operationCode = currentContract.getOperationCode();
                Date validPeriodStartDate = contractSpec.getValidPeriodStartDate();
                Date validPeriodEndDate = contractSpec.getValidPeriodEndDate();
                // 2 点播套餐
                try {
                    if (ContractOperationCodeEnum.VOD_MONTHLY_PACKAGE.getCode().equals(operationCode) ||
                            ContractOperationCodeEnum.COOL_MONTHLY_PACKAGE.getCode().equals(operationCode)) {
                        this.confirmService.confirmDailyIncome(currentContract, currentMonth, specifyDate, currentContract.getCreateTime(), validPeriodEndDate);
                    }

                    // 3 网校云套餐 、 ppt专享通道
                    if (ContractOperationCodeEnum.ONLINE_PACKAGE.getCode().equals(operationCode) ||
                            ContractOperationCodeEnum.PPT_LINE_PAY.getCode().equals(operationCode)) {
                        this.confirmService.confirmDailyIncome(currentContract, currentMonth, specifyDate,
                                currentContract.getCreateTime(),
                                contractSpec.getValidPeriodEndDate());
                        continue;
                    }
                    // 4 直播包天、直播包月、直播集团并发
                    if (ContractOperationCodeEnum.LIVE_CONCURRENT_DAILY.getCode().equals(operationCode) ||
                            ContractOperationCodeEnum.LIVE_CONCURRENT_MONTHLY.getCode().equals(operationCode) ||
                            ContractOperationCodeEnum.LIVE_PRTC_CONCURRENT_DAILY.getCode().equals(operationCode) ||
                            ContractOperationCodeEnum.LIVE_PRTC_CONCURRENT_MONTHLY.getCode().equals(operationCode) ||
                            ContractOperationCodeEnum.LIVE_CONCURRENT_V1.getCode().equals(operationCode)) {
                        this.confirmService.confirmConcurrentDailyIncome(currentContract, currentMonth, specifyDate, contractSpec.getValidPeriodStartDate(),
                                contractSpec.getValidPeriodEndDate());
                        continue;
                    }
                    // 金额
                    if (ContractOperationCodeEnum.DEPOSIT_AMOUNT.getCode().equals(operationCode) ||
                            ContractOperationCodeEnum.OVER_AMOUNT_PAY.getCode().equals(operationCode) ||
                            ContractOperationCodeEnum.RESOURCE_POINT.getCode().equals(operationCode)
                    ) {
                        this.confirmService.confirmDepositIncome(currentContract, specifyDate, currentMonth);
                    }
                } catch (Exception e) {
                    log.error(String.format("calcIncomeByContract occurred.contractId %s exception %s", contractId, e.getMessage()), e);
                }
            }
        }
    }


    private void finishContractStatus(FinanceContractEntity contract, String currentMonth) {
        // FinanceContractEntity exists = this.contractRepository.findByContractId(contract.getContractId());
        if (contract.getIncomeStatus().equals(StatusValueEnum.NO.getValue())) {
            contract.setIncomeStatus(StatusValueEnum.YES.getValue());
            contract.setIncomeMonth(currentMonth);
            this.contractService.saveContract(contract);
        }
    }


    @Override
    public void setCurrentAmountFinanceIncome(FinanceContractEntity contract, String currentMonth,
                                              Map<Integer, BigDecimal> itemAmountMap,
                                              Map<Integer, BigDecimal> itemConsumedMap,
                                              Map<Integer, BillingItemAttributeDTO> billingItemAttributeMap) {
        log.info(String.format("合同{%s}保存收入, itemAmountMap={%s}, itemConsumedMap={%s} , billingItemAttributeMap={%s}",
                contract.getContractId(), JsonMapper.jsonToString(itemAmountMap), JsonMapper.jsonToString(itemConsumedMap),
                JsonMapper.jsonToString(billingItemAttributeMap)));
        try {
            this.assembleCurrentAmountFinanceIncome(contract, currentMonth, itemAmountMap, itemConsumedMap, billingItemAttributeMap);
            // 重置精度
            this.resettingIncomePrecision(contract.getContractId(), currentMonth);
        } catch (Exception e) {
            log.error(String.format("setCurrentAmountFinanceIncome error. contract[%s],itemAmountMap[%s],itemConsumedMap[%s]errMsg[%s]",
                    JsonUtil.beanToString(contract), JsonUtil.beanToString(itemAmountMap), JsonUtil.beanToString(itemConsumedMap), e.getMessage()), e);
            String e1 = String.format("当前合同ID %s", contract.getContractId());
            String e2 = String.format("当前月份 %s", currentMonth);
            String e3 = String.format("当前收入信息 itemAmountMap %s", itemAmountMap);
            String e4 = String.format("当前收入信息 itemConsumedMap %s", itemConsumedMap);
            String e5 = String.format("异常信息 e %s", e.getMessage());
            dingWarnRobot.sendMsgToAtPeople("设置当前金额合同收入信息发生错误", Lists.newArrayList(e1, e2, e3, e4, e5), "");
        }
    }

    @Override
    public void setCurrentAmountFinanceIncomeForUpay(FinanceContractEntity contract, String currentMonth,
                                              Map<Integer, BigDecimal> itemAmountMap,
                                              Map<Integer, BigDecimal> itemConsumedMap,
                                              Map<Integer, BillingItemAttributeDTO> billingItemAttributeMap) {
        log.info(String.format("待支付合同{%s}保存收入, itemAmountMap={%s}, itemConsumedMap={%s} , billingItemAttributeMap={%s}",
                contract.getContractId(), JsonMapper.jsonToString(itemAmountMap), JsonMapper.jsonToString(itemConsumedMap),
                JsonMapper.jsonToString(billingItemAttributeMap)));
        try {
            this.assembleCurrentAmountFinanceIncome(contract, currentMonth, itemAmountMap, itemConsumedMap, billingItemAttributeMap,true);
            // 重置精度
            this.resettingIncomePrecision(contract.getContractId(), currentMonth);
        } catch (Exception e) {
            log.error(String.format("setCurrentAmountFinanceIncome error. contract[%s],itemAmountMap[%s],itemConsumedMap[%s]errMsg[%s]",
                    JsonUtil.beanToString(contract), JsonUtil.beanToString(itemAmountMap), JsonUtil.beanToString(itemConsumedMap), e.getMessage()), e);
            String e1 = String.format("当前合同ID %s", contract.getContractId());
            String e2 = String.format("当前月份 %s", currentMonth);
            String e3 = String.format("当前收入信息 itemAmountMap %s", itemAmountMap);
            String e4 = String.format("当前收入信息 itemConsumedMap %s", itemConsumedMap);
            String e5 = String.format("异常信息 e %s", e.getMessage());
            dingWarnRobot.sendMsgToAtPeople("设置当前金额合同收入信息发生错误", Lists.newArrayList(e1, e2, e3, e4, e5), "");
        }
    }

    private void assembleCurrentAmountFinanceIncome(FinanceContractEntity contract, String currentMonth,
                                                            Map<Integer, BigDecimal> itemAmountMap,
                                                            Map<Integer, BigDecimal> itemConsumedMap,
                                                            Map<Integer, BillingItemAttributeDTO> billingItemAttributeMap,boolean isUnpay){
        {
            Date current = new Date();
            // 获取结算月的上个月
            String lastMonth = DateFormatUtil.formatDateMonth(DateUtil.getAddMonthDate(DateFormatUtil.parseDateMonthNormal(currentMonth), -1));
            String unionId = contract.getUnionId();
            String contractId = contract.getContractId();
            String operationCode = contract.getOperationCode();
            Short waitPayStatus = contract.getWaitPayStatus();
            BigDecimal currentTaxRate = FinanceTaxRateConst.NORMAL.getTaxRate();
            if (Lists.newArrayList(ContractOperationCodeEnum.HARD_CABIN_BODY.getCode(),
                    ContractOperationCodeEnum.HARD_OTHER_SALE.getCode()).contains(operationCode)) {
                currentTaxRate = FinanceTaxRateConst.HARD.getTaxRate();
            }
            // 收入总额（在待支付合同情况下，如果为正数，则是增加待支付金额，如果为负数，则为偿还退收入）
            BigDecimal totalItemAmount = itemAmountMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
            // 先统计明细收入
            Set<Integer> itemIdSet = itemAmountMap.keySet();
            // 1.查询该合同上个月的所有明细数据
            List<FinanceIncomeDetailEntity> lastMonthDetailList = this.incomeDetailRepository.findContractIncomeDetail(contractId, lastMonth);
            // 上个月有的计费项
            // 查询出历史的对应计费项收入明细，目的为了计费项维度的total_income、cumulative_consume
            Map<Integer, Long> detailTotalMap = new HashMap<>();
            Map<Integer, Long> detailCumulativeMap = new HashMap<>();
            Set<Integer> zeroIncomeItemIdSet = new HashSet<>(); // 需要当月入0元收入的计费项
            if (!CollectionUtils.isEmpty(lastMonthDetailList)) {
                for (FinanceIncomeDetailEntity lastExists : lastMonthDetailList) {
                    Integer curIi = lastExists.getBillingItemId();
                    if (Objects.isNull(curIi)) {
                        continue;
                    }
                    if (itemIdSet.contains(curIi)) {
                        // 1.1 上个月有的计费项这个月也有（交集）
                    } else {
                        // 1.2 上个月有的计费项这个月没有
                        zeroIncomeItemIdSet.add(curIi);
                    }
                    BillingItemAttributeDTO curBia = new BillingItemAttributeDTO();
                    curBia.setUnivalentUnit(lastExists.getUnivalentUnit());
                    curBia.setUnivalent(lastExists.getUnivalent());
                    curBia.setConsumeUnit(lastExists.getConsumeUnit());
                    billingItemAttributeMap.putIfAbsent(lastExists.getBillingItemId(), curBia);
                    detailTotalMap.put(curIi, lastExists.getCumulativeIncome());
                    detailCumulativeMap.put(curIi, lastExists.getCumulativeConsume());
                }
                // 1.3 上个月没有的计费项这个月有
            }
            // 查询当月的收入明细
            List<FinanceIncomeDetailEntity> currentMonthDetailList = this.incomeDetailRepository.findContractIncomeDetail(contractId, currentMonth);
            Map<Integer, FinanceIncomeDetailEntity> currentMonthDetailMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(currentMonthDetailList)) {
                currentMonthDetailMap = currentMonthDetailList.stream().filter(f -> Objects.nonNull(f.getBillingItemId()))
                        .collect(Collectors.toMap(FinanceIncomeDetailEntity::getBillingItemId, Function.identity(), (o, n) -> n, HashMap::new));
            }
            // 查询计费项对应的细分
            List<Long> itemIdList = itemIdSet.stream().filter(Objects::nonNull).map(Integer::longValue).collect(Collectors.toList());
            for (Integer zeroItemId : zeroIncomeItemIdSet) {
                // 要把当月账单没有的上个月计费项也加上
                itemIdList.add(zeroItemId.longValue());
            }
            Map<Integer, String> itemIdSubCategoryMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(itemIdList)) {
                List<BusinessEnumDefinition> billingItemBedList = this.definitionRepository.findByBillItemIdList(itemIdList);
                if (!CollectionUtils.isEmpty(billingItemBedList)) {
                    itemIdSubCategoryMap = billingItemBedList.stream().filter(f -> Objects.nonNull(f.getBillItemId()))
                            .collect(Collectors.toMap(mk -> mk.getBillItemId().intValue(), BusinessEnumDefinition::getDetailSubCategory, (o, n) -> n, HashMap::new));
                }
            }
            // 获取过期计费项的ID
            BillingItem cleanupBi = this.billingItemRepository.findFirstByCode(ItemCodeConst.EXPIRED_CLEANUP.getCode());
            BigDecimal expireAmount = BigDecimal.ZERO;
            Integer expireAmountItemId = 0;
            if (Objects.nonNull(cleanupBi)) {
                expireAmountItemId = cleanupBi.getId();
            }
            // 处理明细数据
            List<FinanceIncomeDetailEntity> saveDetailList = new ArrayList<>();
            List<FinanceIncomeDetailEntity> delDetailList = new ArrayList<>();
            // 计算当月总收入（所有计费项收入之和）
            BigDecimal usageIncome = BigDecimal.ZERO;
            BigDecimal otherIncome = BigDecimal.ZERO;
            for (Map.Entry<Integer, BigDecimal> entry : itemAmountMap.entrySet()) {
                Integer billItemId = entry.getKey();
                BigDecimal amount = entry.getValue();
                // 待支付完全偿还之后的合同，cost总和为0，那么消耗量应当也为0
                BigDecimal consumed = itemConsumedMap.get(billItemId);
                if (amount.compareTo(BigDecimal.ZERO) == 0 || Objects.isNull(consumed)) {
                    consumed = BigDecimal.ZERO;
                }
                FinanceIncomeDetailEntity currentMonthDetail = currentMonthDetailMap.get(billItemId);
                if (Objects.isNull(currentMonthDetail)) {
                    // 当月新增的
                    currentMonthDetail = new FinanceIncomeDetailEntity();
                    currentMonthDetail.setContractId(contractId);
                    currentMonthDetail.setIncomeMonth(currentMonth);
                    currentMonthDetail.setBillingItemId(billItemId);
                    String itemSubCategory = itemIdSubCategoryMap.get(billItemId);
                    if (StringUtils.isNotEmpty(itemSubCategory)) {
                        currentMonthDetail.setSubCategory(itemSubCategory);
                    } else {
                        currentMonthDetail.setSubCategory(contract.getSubCategory());
                    }
                    currentMonthDetail.setCreateTime(current);
                    currentMonthDetail.setStatus(StatusValueEnum.YES.getValue());
                    BillingItem currentBillingItem = this.billingService.getById(billItemId);
                    if (Objects.nonNull(currentBillingItem)) {
                        currentMonthDetail.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByBillingItemCode(currentBillingItem.getCode()));
                    }
                    currentMonthDetail.setTaxRate(currentTaxRate.multiply(new BigDecimal("100")).longValue());
                    currentMonthDetail.setSettledStatus(StatusValueEnum.NO.getValue());
                }
                Long totalIncome = detailTotalMap.get(billItemId);
                Long cumulativeConsumed = detailCumulativeMap.get(billItemId);
                if (Objects.isNull(totalIncome)) {  // 上个月没有的计费项
                    totalIncome = 0L;
                }
                if (Objects.isNull(cumulativeConsumed)) {  // 上个月没有的计费项
                    cumulativeConsumed = 0L;
                }
                currentMonthDetail.setCurrentIncome(amount.longValue());
                currentMonthDetail.setCurrentConsume(consumed.longValue());
                currentMonthDetail.setCumulativeIncome(totalIncome + amount.longValue());
                currentMonthDetail.setCumulativeConsume(cumulativeConsumed + consumed.longValue());
                currentMonthDetail.setUpdateTime(current);
                BillingItemAttributeDTO billingItemAttr = billingItemAttributeMap.get(billItemId);
                if (Objects.nonNull(billingItemAttr)) {
                    currentMonthDetail.setConsumeUnit(billingItemAttr.getConsumeUnit());
                    currentMonthDetail.setUnivalent(billingItemAttr.getUnivalent());
                    currentMonthDetail.setUnivalentUnit(billingItemAttr.getUnivalentUnit());
                } else {
                    currentMonthDetail.setConsumeUnit(contract.getQuantityUnit());
                    currentMonthDetail.setUnivalent(contract.getIncomeUnivalent());
                    currentMonthDetail.setUnivalentUnit(contract.getIncomeUnivalentUnit());
                }
                // 过期计费项
                if (expireAmountItemId.equals(billItemId)) {
                    // 标记过期金额
                    currentMonthDetail.setConsumeUnit("-");
                    currentMonthDetail.setUnivalentUnit("-");
                    // 过期金额的消费量写死为1
                    currentMonthDetail.setCurrentConsume(1L);
                    currentMonthDetail.setCumulativeConsume(1L);
                    otherIncome = otherIncome.add(amount);
                } else {
                    usageIncome = usageIncome.add(amount);
                }
                saveDetailList.add(currentMonthDetail);
            }
            // 设置最后一条为确认收入
            if (!CollectionUtils.isEmpty(saveDetailList)) {
                FinanceIncomeDetailEntity lastR = saveDetailList.get(saveDetailList.size() - 1);
                lastR.setSettledStatus(StatusValueEnum.YES.getValue());
            }
            // 处理当月0元收入（需要过滤特殊计费项）
            if (!CollectionUtils.isEmpty(zeroIncomeItemIdSet)) {
                for (Integer itemId : zeroIncomeItemIdSet) {
                    // 上个月有这个月没有的收入数据
                    // 过滤特殊计费项
                    if (FinanceIncomeDetailTagEnum.getTags().contains(itemId)) {
                        continue;
                    }
                    if (Objects.nonNull(currentMonthDetailMap.get(itemId))) {
                        continue; // 本月0元已存在
                    }
                    FinanceIncomeDetailEntity zeroIncomeDetail = new FinanceIncomeDetailEntity();
                    zeroIncomeDetail.setContractId(contractId);
                    zeroIncomeDetail.setIncomeMonth(currentMonth);
                    zeroIncomeDetail.setBillingItemId(itemId);
                    zeroIncomeDetail.setCreateTime(current);
                    if (Objects.nonNull(itemId)) {
                        BillingItem currentBillingItem = this.billingService.getById(itemId);
                        if (Objects.nonNull(currentBillingItem)) {
                            zeroIncomeDetail.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByBillingItemCode(currentBillingItem.getCode()));
                        }
                    }
                    zeroIncomeDetail.setTaxRate(currentTaxRate.multiply(new BigDecimal("100")).longValue());
                    BillingItemAttributeDTO billingItemAttr = billingItemAttributeMap.get(itemId);
                    if (Objects.nonNull(billingItemAttr)) {
                        zeroIncomeDetail.setConsumeUnit(billingItemAttr.getConsumeUnit());
                        zeroIncomeDetail.setUnivalent(billingItemAttr.getUnivalent());
                        zeroIncomeDetail.setUnivalentUnit(billingItemAttr.getUnivalentUnit());
                    } else {
                        zeroIncomeDetail.setConsumeUnit(contract.getQuantityUnit());
                        zeroIncomeDetail.setUnivalent(contract.getIncomeUnivalent());
                        zeroIncomeDetail.setUnivalentUnit(contract.getIncomeUnivalentUnit());
                    }
                    String itemSubCategory = itemIdSubCategoryMap.get(itemId);
                    if (StringUtils.isNotEmpty(itemSubCategory)) {
                        zeroIncomeDetail.setSubCategory(itemSubCategory);
                    } else {
                        zeroIncomeDetail.setSubCategory(contract.getSubCategory());
                    }
                    zeroIncomeDetail.setCurrentIncome(0L);
                    zeroIncomeDetail.setCumulativeIncome(detailTotalMap.get(itemId));
                    zeroIncomeDetail.setCurrentConsume(0L);
                    zeroIncomeDetail.setCumulativeConsume(detailCumulativeMap.get(itemId));
                    zeroIncomeDetail.setSettledStatus(StatusValueEnum.NO.getValue());
                    zeroIncomeDetail.setCreateTime(current);
                    zeroIncomeDetail.setUpdateTime(current);
                    zeroIncomeDetail.setStatus(StatusValueEnum.YES.getValue());
                    saveDetailList.add(zeroIncomeDetail);
                }
            }
            // 统计收入数据
            // 查询上月收入数据
            FinanceIncomeEntity lastMonthIncome = this.incomeRepository.findByContractIdAndIncomeMonth(contractId, lastMonth);
            if(Objects.isNull(lastMonthIncome) && isUnpay){
                lastMonthIncome = this.incomeRepository.findNearestByContractIdAndIncomeMonth(contractId,lastMonth);
            }
            Long historyCumulativeIncome = 0L;
            BigDecimal calcHistoryCumulativeIncome = BigDecimal.ZERO;
            if (Objects.nonNull(lastMonthIncome)) {
                historyCumulativeIncome = Objects.nonNull(lastMonthIncome.getCumulativeIncome()) ? lastMonthIncome.getCumulativeIncome() : 0L;
                calcHistoryCumulativeIncome = new BigDecimal(historyCumulativeIncome);
            }

            // 查询合同退费（目的重新计算合同金额、合同充值量（不能直接取当前合同的，因为确认收入可以指定时间，合同对象是和此时此刻绑定的，不是指定时间的那个时刻绑定的））
            // 退费：合同金额变化、充值量变化、单价不变、递延扣减退收入部分
            // 查询合同当月退费
            Date queryRefundStartDate = DateUtil.getDateStart(DateUtil.getFirstDayOfMonth(DateFormatUtil.parseDateMonthNormal(currentMonth)));
            Date queryRefundEndDate = DateUtil.getDateEnd(DateUtil.getLastDayOfMonth(DateFormatUtil.parseDateMonthNormal(currentMonth)));
            Integer contractExistsCurrentMonthRefund = this.refundRepository.getContractExistsRefund(contractId, queryRefundStartDate, queryRefundEndDate);

            // 查询合同未来退费
            Date queryFutureRefundEndDate = DateUtil.getDateEnd(DateUtil.getLastDayOfMonth(DateFormatUtil.parseDateMonthNormal(currentMonth)));
            List<FinanceRefundEntity> contractFutureRefundRecords = this.refundRepository.getContractAfterThatRefund(contractId, queryFutureRefundEndDate);
            BigDecimal futureRefundAmount = BigDecimal.ZERO; // 未来退费金额
            if (!CollectionUtils.isEmpty(contractFutureRefundRecords)) {
                futureRefundAmount = contractFutureRefundRecords.stream().map(FinanceRefundEntity::getRefundAmount)
                        .filter(Objects::nonNull).map(BigDecimal::new)
                        .reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
            }

            BigDecimal calcContractAmount = Objects.nonNull(contract.getCurrentAmount()) ? new BigDecimal(contract.getCurrentAmount()) : BigDecimal.ZERO;
            // 当前合同金额 = 当前合同金额 + 未来退费金额
            Long currentMonthContractAmount = calcContractAmount.add(futureRefundAmount).longValue();
            Long currentMonthContractQuantity = currentMonthContractAmount;
            // 合同金额不含税
            BigDecimal currentContractAmount = new BigDecimal(currentMonthContractAmount).divide(currentTaxRate, 0, ROUND_HALF_EVEN);
            boolean isZeroContractAmount = currentContractAmount.equals(BigDecimal.ZERO);
            // 计算当前递延金额 递延 = 当前合同金额（new）/(1+税率）- 累计收入
            BigDecimal currentMonthDeferred = currentContractAmount.subtract(calcHistoryCumulativeIncome);
            if (currentMonthDeferred.compareTo(BigDecimal.ZERO) < 0) {
                if (Objects.nonNull(contractExistsCurrentMonthRefund) && contractExistsCurrentMonthRefund == 1) {
                    // 退费记录不空，则为退费，负数递延入非业务消耗
                    // 若新递延<0，则取 0，差值部分做退收入
                    // 查询当月退费收入数据
                    FinanceIncomeDetailEntity refundExtraIncome = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId, currentMonth,
                            FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode());
                    // 查询上月退费收入数据，用于获取累计收入
                    FinanceIncomeDetailEntity lastMonthRefundExtraIncome = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId, lastMonth,
                            FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode());
                    if (Objects.isNull(refundExtraIncome)) {
                        refundExtraIncome = new FinanceIncomeDetailEntity();
                        refundExtraIncome.setContractId(contractId);
                        refundExtraIncome.setIncomeMonth(currentMonth);
                        refundExtraIncome.setBillingItemId(FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode());
                        refundExtraIncome.setSubCategory(SubCategoryEnum.SC_REFUND_EXTRA.getCode());
                        refundExtraIncome.setTaxRate(FinanceTaxRateConst.NORMAL.getTaxRate().multiply(new BigDecimal("100")).longValue());
                        refundExtraIncome.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByOperationCode(operationCode));
                        refundExtraIncome.setConsumeUnit(contract.getQuantityUnit());
                        refundExtraIncome.setUnivalent(contract.getIncomeUnivalent());
                        refundExtraIncome.setUnivalentUnit(contract.getIncomeUnivalentUnit());
                        refundExtraIncome.setCreateTime(current);
                    }
                    // 计算递延尾差= 总可用金额-历史收入-当月收入
                    refundExtraIncome.setCurrentIncome(currentMonthDeferred.longValue());
                    refundExtraIncome.setCurrentConsume(0L);
                    Long totalRefundExtra = 0L;
                    if (Objects.nonNull(lastMonthRefundExtraIncome) && Objects.nonNull(lastMonthRefundExtraIncome.getCumulativeIncome())) {
                        totalRefundExtra = lastMonthRefundExtraIncome.getCumulativeIncome();
                    }
                    refundExtraIncome.setCumulativeIncome(totalRefundExtra + currentMonthDeferred.longValue());
                    refundExtraIncome.setCumulativeConsume(0L);
                    refundExtraIncome.setSettledStatus(StatusValueEnum.YES.getValue());
                    refundExtraIncome.setUpdateTime(current);
                    saveDetailList.add(refundExtraIncome);
                    otherIncome = otherIncome.add(currentMonthDeferred); // 退费收入计入非消耗收入
                } else {
                    // 退费记录为空，则为待支付最后一笔偿还，此时负数递延入业务消耗
                    // empty
                }
            }
            // 查询当前合同是否需要结入尾差（待支付合同永远都需要完全结清，不存在递延）
            // 计算尾差这里，如果是待支付最后一笔偿还，此时合同金额为0，退出之前所有确认的收入，此时尾差计算要排除这部分退收入的otherIncome
            // 如果是完全退费，尾差计算也要排除这部分退收入的otherIncome，不然明细和主表就会对不上，明细会生成一笔正数的尾差抵消掉退费的负收入
            Long tailExtraIncome = 0L;
            BigDecimal calcTailExtraIncome = BigDecimal.ZERO;
            boolean isRechargeZero = false;
            boolean isNeedTailExtra = false;
            if (StatusValueEnum.YES.getValue().equals(waitPayStatus) && totalItemAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 大于0的就是增加待支付，小于0则为偿还待支付，需要退出收入。只有在增加待支付的时候需要每次都完全确认收入
                // totalItemAmount小于0则为偿还待支付，在偿还的时候合同金额已经扣减了，所以不能拿现在的合同金额减过去的累计收入来补尾差
                // 而新增加的待支付，减过去的收入，需要使用尾差来补新增待支付的收入，依靠这个尾差来完全确认当前新合同金额的收入
                // 这里的累计收入，是以往变动的待支付合同的累计，所以不用加上
                isNeedTailExtra = true;
                // calcTailExtraIncome = currentContractAmount.subtract(calcHistoryCumulativeIncome).add(refundExtraAmount).subtract(usageIncome).subtract(otherIncome);
            } else {
                AccountRechargeRecord arr = this.accountRechargeRecordRepository.findOneRechargeRecordByCustomerIdAndContractId(unionId, contractId);
                if (Objects.nonNull(arr)) {
                    Long available = Objects.nonNull(arr.getAvailable()) ? arr.getAvailable() : 0L;
                    Long freezeAmount = Objects.nonNull(arr.getFreezeAmount()) ? arr.getFreezeAmount() : 0L;
                    // available + freeze_amount
                    if (available + freezeAmount == 0) {
                        isRechargeZero = true;
                    }
                }
                // 合同金额为0也需要退收入
                isNeedTailExtra = isRechargeZero || isZeroContractAmount;
                // tailExtraIncome = currentContractAmount.subtract(calcHistoryCumulativeIncome).add(refundExtraAmount).subtract(usageIncome).subtract(otherIncome).longValue();
            }
            calcTailExtraIncome = currentContractAmount.subtract(calcHistoryCumulativeIncome).subtract(usageIncome).subtract(otherIncome);
            if (isNeedTailExtra) {
                tailExtraIncome = calcTailExtraIncome.longValue();
            }
            boolean need2FinishContract = false;
            FinanceIncomeDetailEntity currentMonthExtraIncome = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId, currentMonth,
                    FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode());
            if (tailExtraIncome != 0) {
                if (Objects.isNull(currentMonthExtraIncome)) {
                    currentMonthExtraIncome = new FinanceIncomeDetailEntity();
                    currentMonthExtraIncome.setContractId(contractId);
                    currentMonthExtraIncome.setIncomeMonth(currentMonth);
                    currentMonthExtraIncome.setBillingItemId(FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode());
                    currentMonthExtraIncome.setSubCategory(SubCategoryEnum.SC_TAIL_EXTRA.getCode());
                    currentMonthExtraIncome.setTaxRate(FinanceTaxRateConst.NORMAL.getTaxRate().multiply(new BigDecimal("100")).longValue());
                    currentMonthExtraIncome.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByOperationCode(operationCode));
                    currentMonthExtraIncome.setConsumeUnit(contract.getQuantityUnit());
                    currentMonthExtraIncome.setUnivalentUnit(contract.getIncomeUnivalentUnit());
                    currentMonthExtraIncome.setCreateTime(current);
                    currentMonthExtraIncome.setStatus(StatusValueEnum.YES.getValue());
                }
                currentMonthExtraIncome.setUnivalent(contract.getIncomeUnivalent());
                // 计算递延尾差 = 总可用金额 - 历史收入- 当月收入
                currentMonthExtraIncome.setCurrentIncome(tailExtraIncome);
                currentMonthExtraIncome.setCurrentConsume(0L);
                FinanceIncomeDetailEntity lastMonthTailExtra = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId, lastMonth,
                        FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode());
                Long totalTailExtra = 0L;
                if (Objects.nonNull(lastMonthTailExtra) && Objects.nonNull(lastMonthTailExtra.getCumulativeIncome())) {
                    totalTailExtra = lastMonthTailExtra.getCumulativeIncome();
                }
                currentMonthExtraIncome.setCumulativeIncome(totalTailExtra + tailExtraIncome);
                currentMonthExtraIncome.setCumulativeConsume(0L);
                currentMonthExtraIncome.setSettledStatus(StatusValueEnum.YES.getValue());
                currentMonthExtraIncome.setUpdateTime(current);
                saveDetailList.add(currentMonthExtraIncome);
            } else {
                // 如果当月存在尾差了，则删除
                if (Objects.nonNull(currentMonthExtraIncome)) {
                    delDetailList.add(currentMonthExtraIncome);
                }
            }
            BigDecimal currentTotalIncome = usageIncome.add(otherIncome).add(new BigDecimal(tailExtraIncome));
            // 2.查询当月收入数据
            FinanceIncomeEntity currentMonthIncome = this.incomeRepository.findByContractIdAndIncomeMonth(contractId, currentMonth);
            if (Objects.isNull(currentMonthIncome)) {
                currentMonthIncome = new FinanceIncomeEntity();
                currentMonthIncome.setContractId(contractId);
                currentMonthIncome.setIncomeMonth(currentMonth);
                currentMonthIncome.setCreateTime(current);
            }
            currentMonthIncome.setCurrentContractAmount(currentMonthContractAmount);
            currentMonthIncome.setCurrentContractQuantity(currentMonthContractQuantity);
            currentMonthIncome.setTotalIncome(currentTotalIncome.longValue());
            currentMonthIncome.setUsageIncome(usageIncome.longValue());
            currentMonthIncome.setOtherIncome(otherIncome.longValue() + tailExtraIncome);
            currentMonthIncome.setCumulativeIncome(calcHistoryCumulativeIncome.add(currentTotalIncome).longValue());
            long currentDeferred = currentContractAmount.subtract(calcHistoryCumulativeIncome).subtract(currentTotalIncome).longValue();
            if (currentDeferred < 0) {
                // todo 告警
                currentDeferred = 0;
            }
            if (currentDeferred == 0) {
                this.finishContractStatus(contract, currentMonth);
            }
            currentMonthIncome.setDeferredIncome(currentDeferred);
            currentMonthIncome.setUpdateTime(current);
            // 保存主表数据
            this.incomeRepository.save(currentMonthIncome);
            // 保存明细数据
            for (FinanceIncomeDetailEntity sd : saveDetailList) {
                if (Objects.isNull(sd.getBillingItemId())) {
                    continue;
                }
                this.incomeDetailRepository.save(sd);
            }
            // 当月的计费项
            Set<Integer> currentMonthItemIds = currentMonthDetailMap.keySet();
            // 获取差集，设置当前计费项没有更新到的其他计费项
            if (!CollectionUtils.isEmpty(currentMonthItemIds)) {
                // 移除本次更新的
                currentMonthItemIds.removeAll(itemIdSet);
                if (!CollectionUtils.isEmpty(currentMonthItemIds)) {
                    for (Integer missItemId : currentMonthItemIds) {
                        if (missItemId <= 0) {
                            continue;
                        }
                        FinanceIncomeDetailEntity missing = currentMonthDetailMap.get(missItemId);
                        // 清0
                        Long missingCurrentAmount = missing.getCurrentIncome();
                        Long missingCurrentConsume = missing.getCurrentConsume();
                        missing.setCurrentIncome(0L);
                        missing.setCumulativeIncome(missing.getCumulativeIncome() - missingCurrentAmount);
                        missing.setCumulativeConsume(missing.getCumulativeConsume() - missingCurrentConsume);
                        missing.setCurrentConsume(0L);
                        this.incomeDetailRepository.save(missing);
                    }
                }
            }
            for (FinanceIncomeDetailEntity dd : delDetailList) {
                this.incomeDetailRepository.delete(dd);
            }
        }

    }

    private void assembleCurrentAmountFinanceIncome(FinanceContractEntity contract, String currentMonth,
                                                    Map<Integer, BigDecimal> itemAmountMap,
                                                    Map<Integer, BigDecimal> itemConsumedMap,
                                                    Map<Integer, BillingItemAttributeDTO> billingItemAttributeMap) {
        assembleCurrentAmountFinanceIncome(contract,currentMonth,itemAmountMap,itemConsumedMap,billingItemAttributeMap,false);
    }


    /**
     * 计算统计账单用量的收入
     *
     * @param operationCode 合同的操作编码
     * @param specifyDate   统计日期
     * @param currentMonth  当前月份
     * @return 发生的异常消息（期望之外的问题）
     */
    private void calcBillingDailyConsumedIncome(String operationCode, Date specifyDate, String currentMonth) {
        // 查询未确认收入合同的用户集合
        ContractSpecQueryDO customerSearchDO = ContractSpecQueryDO.builder()
                .operationCode(operationCode)
                .currentMonth(currentMonth).build();
        List<String> unionIdList = this.contractRepository.getContractUnionIdBySearchDO(customerSearchDO);
        // unionIdList = Lists.newArrayList("8841131796");
        if (!CollectionUtils.isEmpty(unionIdList)) {
            outerLoop:
            for (String unionId : unionIdList) {
                // 账号类型
                try {
                    this.confirmService.confirmQuantityContractIncome(operationCode, currentMonth, specifyDate, unionId);
                } catch (Exception e) {
                    log.error(String.format("calcBillingDailyConsumedIncome occurred.unionId %s exception %s", unionId, e.getMessage()), e);
                }
            }
        }
    }


    /**
     * 处理点播流量包、年流量的收入确认
     * operationStartDate = 2021-08-04 00:00:00
     * 之后的点播流量包、年流量
     */
    private void confirmVodYearlyIncomeAfterSpecifyDate(Date specifyDate) {
        // 处理【点播套餐-年流量、流量包】
        // 新的点播统计收入，从什么时候开始
        String operationStartDate = "2021-08-04 00:00:00";
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        String operationEndDate = DateFormatUtil.formatDateTimeNormal(DateUtil.getDateEnd(DateUtil.getCurrentMonthEnd(specifyDate)));
        ContractSpecQueryDO csq = ContractSpecQueryDO.builder()
                .businessStartDate(operationStartDate)
                .businessEndDate(operationEndDate)
                .operationCodeList(Lists.newArrayList(
                        ContractOperationCodeEnum.VOD_PACK_FLOW.getCode(),
                        ContractOperationCodeEnum.COOL_PACK_FLOW.getCode(),
                        ContractOperationCodeEnum.VOD_YEARLY_PACKAGE.getCode(),
                        ContractOperationCodeEnum.COOL_YEARLY_PACKAGE.getCode()))
                .currentMonth(currentMonth).build();
        // 查询未关联的合同
        List<String> contractIdList = this.contractRepository.getContractIdBySearchDO(csq);
        // contractIdList = Lists.newArrayList("ECklUZsabD");
        Map<String, FinanceContractEntity> contractMap = new HashMap<>();
        // 组装查询结果数据
        if (!CollectionUtils.isEmpty(contractIdList)) {
            contractIdList = contractIdList.stream().distinct().collect(Collectors.toList());
            List<FinanceContractEntity> contractList = this.contractRepository.findByContractIdIn(contractIdList);
            contractMap = contractList.stream().filter(f -> StringUtils.isNotEmpty(f.getContractId()))
                    .collect(Collectors.toMap(FinanceContractEntity::getContractId, Function.identity(), (o, n) -> n, HashMap::new));
        }
        for (String contractId : contractIdList) {
            FinanceContractEntity currentContract = contractMap.get(contractId);
            String contractWay = currentContract.getContractWay();
            if (StringUtils.isNotEmpty(contractWay) && ContractOriginEnum.ORIGIN_MONTHLY_SETTLE.getCode().equals(contractWay)) {
                // 月结不在这里结算
                continue;
            }
            try {
                this.confirmService.confirmVodFlowIncome(specifyDate, currentContract);
            } catch (Exception e) {
                log.error(String.format("calcVodFlowIncome occurred. contractId %s exception %s", contractId,
                        e.getMessage()), e);
            }
        }
    }

    /**
     * 直播套餐流量收入确认（按合同）
     * operationStartDate = 2023-05-01 00:00:00
     * 之后的点播流量包、年流量
     */
    public void confirmLiveFlowIncomeAfterSpecifyDate(Date specifyDate) {
        // 直播流量统计收入，从什么时候开始
        String operationStartDate = "2023-05-01 00:00:00";
        String currentMonth = DateFormatUtil.formatDateMonth(specifyDate);
        String operationEndDate = DateFormatUtil.formatDateTimeNormal(
                DateUtil.getDateEnd(DateUtil.getCurrentMonthEnd(specifyDate)));
        ContractSpecQueryDO csq = ContractSpecQueryDO.builder()
                .businessStartDate(operationStartDate)
                .businessEndDate(operationEndDate)
                .operationCodeList(Lists.newArrayList(ContractOperationCodeEnum.LIVE_FLOW.getCode()))
                .currentMonth(currentMonth)
                .build();
        // 查询未关联的合同
        List<String> contractIdList = this.contractRepository.getContractIdBySearchDO(csq);
        //contractIdList = Lists.newArrayList("AjrDVfjxhv");
        Map<String, FinanceContractEntity> contractMap = new HashMap<>();
        // 组装查询结果数据
        if (!CollectionUtils.isEmpty(contractIdList)) {
            contractIdList = contractIdList.stream().distinct().collect(Collectors.toList());
            List<FinanceContractEntity> contractList = this.contractRepository.findByContractIdIn(contractIdList);
            contractMap = contractList.stream()
                    .filter(f -> StringUtils.isNotEmpty(f.getContractId()))
                    .collect(Collectors.toMap(FinanceContractEntity::getContractId, Function.identity(), (o, n) -> n,
                            HashMap::new));
        }
        for (String contractId : contractIdList) {
            FinanceContractEntity currentContract = contractMap.get(contractId);
            String contractWay = currentContract.getContractWay();
            if (StringUtils.isNotEmpty(contractWay) &&
                    ContractOriginEnum.ORIGIN_MONTHLY_SETTLE.getCode().equals(contractWay)) {
                // 月结不在这里结算
                continue;
            }
            try {
                this.confirmService.confirmLiveFlowIncome(specifyDate, currentContract);
            } catch (Exception e) {
                log.error(String.format("calcVodFlowIncome occurred. contractId %s exception %s", contractId,
                        e.getMessage()), e);
            }
        }
    }

    /**
     * 判断合同是否单独点播年流量、流量包能否处理
     */
    private boolean isVodSpecifyDatePackageContract(FinanceContractEntity contract) {
        String specifyDate = "2021-08-04 00:00:00";
        String operationCode = contract.getOperationCode();
        Date createTime = contract.getCreateTime();
        return (ContractOperationCodeEnum.VOD_YEARLY_PACKAGE.getCode().equals(operationCode) ||
                ContractOperationCodeEnum.COOL_YEARLY_PACKAGE.getCode().equals(operationCode) ||
                ContractOperationCodeEnum.VOD_PACK_FLOW.getCode().equals(operationCode)) &&
                Objects.nonNull(createTime) && createTime.after(DateFormatUtil.parseDateTimeNormal(specifyDate));

    }

    /**
     * 获取当前合同收入信息（仅限非金额合同使用！！！）
     * 该方法必须严格适配传入的current参数获取到当时的合同信息
     * 只有每日确认收入的时候可以取contract的值、否则会重复计算关联差额、退费等逻辑
     * 该方法必须支持历史重算的逻辑，取current时刻的合同数值
     * 不能取当月的收入信息，应该取current月份之前的收入信息，
     * 因为确认收入的时候查询用量都是从当月的1号开始查的，如果取当月的数据已经包含之前统计过从1号开始的用量了
     * <p>
     * 注意：先算关联，再算退费！
     * 关联：合同金额变化、充值量不变、单价变化、差额部分入非消耗收入
     * 退费：合同金额变化、充值量变化、单价不变、递延扣减退收入部分
     */
    @Override
    public CurrentFinanceIncomeDTO getCurrentFinanceIncome(FinanceContractEntity contract, Date current) {
        CurrentFinanceIncomeDTO currentFinanceIncomeDTO = null;
        try {
            currentFinanceIncomeDTO = calcCurrentFinanceInfo(contract, current);
        } catch (Exception e) {
            log.error(String.format("getCurrentFinanceIncome error contract[%s],errMsg[%s]", JsonUtil.beanToString(contract), e.getCause()), e);
            String e1 = String.format("当前合同ID %s", contract.getContractId());
            String e2 = String.format("当月日期 %s", DateFormatUtil.formatDateNormal(current));
            String e3 = String.format("异常信息 %s", e.getMessage());
            dingWarnRobot.sendMsgToAtPeople("获取当前用量合同收入信息发生错误", Lists.newArrayList(e1, e2, e3), "");
        }
        return currentFinanceIncomeDTO;
    }

    private CurrentFinanceIncomeDTO calcCurrentFinanceInfo(FinanceContractEntity contract, Date current) {
        String contractId = contract.getContractId();
        String currentMonth = DateFormatUtil.formatDateMonth(current);
        String operationCode = contract.getOperationCode();
        // 合同金额
        BigDecimal contractAmount = null;
        // 合同可用金额
        BigDecimal contractAvailable = null;
        // 合同充值量（byte）
        Long contractQuantity = null;
        // 合同单价
        BigDecimal contractUnivalent = null;
        // 合同单价含税
        BigDecimal contractUnivalentWithTax = null;
        // 合同累计收入
        BigDecimal cumulativeIncome = null;
        // 合同累计消耗
        BigDecimal cumulativeConsumed = null;
        // 合同递延收入
        BigDecimal deferredIncome = null;
        // 合同退款退收入
        BigDecimal refundIncome = BigDecimal.ZERO;
        // 累计消耗量的差价（计入当月非消耗收入）
        BigDecimal differenceIncome = BigDecimal.ZERO;
        Long diffUnivalent = 0L;
        // 税率
        BigDecimal currentTaxRate = null;
        // 查询最近收入的月份参数
        String queryNearestMonth = currentMonth;
        if (ContractOriginEnum.ORIGIN_MONTHLY_SETTLE.getCode().equals(contract.getContractWay())) {
            // 月结自定义税率
            if (Objects.nonNull(contract.getContractSpecificationId())) {
                FinanceContractSpecificationEntity fcse = this.contractSpecificationRepository.getById(contract.getContractSpecificationId());
                if (Objects.nonNull(fcse) && Objects.nonNull(fcse.getTaxRate()) && fcse.getTaxRate() >= 100) {
                    currentTaxRate = new BigDecimal(fcse.getTaxRate()).divide(new BigDecimal("100"), 2, ROUND_DOWN);
                }
                if (StringUtils.isNotEmpty(fcse.getAccountPeriod()) &&
                        fcse.getAccountPeriod().compareTo(currentMonth) < 0) {
                    // 月结的账期，本月可以确认上月收入，但上月收入不能当做历史收入
                    queryNearestMonth = fcse.getAccountPeriod();
                }
            }
        }
        if (operationCode.equals(ContractOperationCodeEnum.HARD_CABIN_BODY.getCode()) ||
                operationCode.equals(ContractOperationCodeEnum.HARD_OTHER_SALE.getCode())) {
            currentTaxRate = FinanceTaxRateConst.HARD.getTaxRate();
        }
        if (Objects.isNull(currentTaxRate)) {
            currentTaxRate = FinanceTaxRateConst.NORMAL.getTaxRate();
        }

        // 获取不包括本月最新的收入数据
        FinanceIncomeEntity nearestIncome = this.incomeRepository.findNearestByContractIdAndIncomeMonth(contractId, currentMonth);
        // 获取不包括本月最新的收入明细
        FinanceIncomeDetailEntity nearestIncomeDetail = this.incomeDetailRepository.findNearestByContractIdAndIncomeMonth(contractId, currentMonth);

        // 合同的关联时间
        String contractAssociationMonth = DateFormatUtil.formatDateMonth(contract.getAssociationTime());
        // 是否当月关联（预开通/月结合同才需要）
        boolean isCurrentMonthAssociation =
                (ContractOriginEnum.ORIGIN_PRE_OPEN.getCode().equals(contract.getContractWay()) ||
                        ContractOriginEnum.ORIGIN_MONTHLY_SETTLE.getCode().equals(contract.getContractWay())) &&
                        ContractAssociationStatusEnum.ASSOCIATED.getCode().equals(contract.getAssociationStatus()) &&
                        StringUtils.isNotEmpty(contractAssociationMonth) && contractAssociationMonth.equals(currentMonth);
        // 当月关联，且关联金额不为空，且关联前原金额和关联后合同金额不一致，则计算关联差额
        Long associateContractAmount = null;
        String soId = contract.getSoId();
        if (isCurrentMonthAssociation) {
            SalesOpportunities so = this.salesOpportunitiesRepository.findBySoId(soId);
            if (Objects.nonNull(so)) {
                // 当月关联，获取关联金额
                associateContractAmount = so.getAmountGained();
            }
        }

        // 获取当月退费
        Date queryRefundStartDate = DateUtil.getDateStart(DateUtil.getFirstDayOfMonth(DateFormatUtil.parseDateMonthNormal(currentMonth)));
        Date queryRefundEndDate = DateUtil.getDateEnd(DateUtil.getLastDayOfMonth(DateFormatUtil.parseDateMonthNormal(currentMonth)));
        List<FinanceRefundEntity> refundList = this.refundRepository.getContractCurrentMonthRefund(contractId, queryRefundStartDate, queryRefundEndDate);
        // 退费金额
        BigDecimal refundMoney = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(refundList)) {
            // 计算当月退费的退费金额
            refundMoney = refundList.stream()
                    .filter(rf -> DateFormatUtil.formatDateMonth(rf.getCreateTime()).equals(currentMonth)) // 是否为当月退费
                    .map(FinanceRefundEntity::getRefundAmount)
                    .filter(Objects::nonNull)
                    .map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
        }
        // 计算的合同金额
        BigDecimal calcContractAmount = null;
        // 计算的合同充值量（GB）
        BigDecimal calcContractQuantity = null;
        // 计算的合同单价
        BigDecimal calcContractUnivalent = null;
        // 计算的合同累计消耗量
        BigDecimal calcContractCumulativeConsume = null;
        // 计算的合同累计使用金额
        BigDecimal calcContractCumulativeIncome = null;
        // 计算的合同递延金额
        BigDecimal calcContractDeferredIncome = null;
        // 年流量套餐处理流程
        boolean periodPackageType = (ContractOperationCodeEnum.VOD_YEARLY_PACKAGE.getCode().equals(operationCode) || ContractOperationCodeEnum.COOL_YEARLY_PACKAGE.getCode().equals(operationCode));
        if (Objects.nonNull(nearestIncome)) {
            // 操作前的单价（关联前的合同金额/关联前的用量）
            calcContractAmount = Objects.nonNull(nearestIncome.getCurrentContractAmount()) ? new BigDecimal(nearestIncome.getCurrentContractAmount()) : BigDecimal.ZERO;
            // 充值量需要转换，G、byte
            calcContractQuantity = this.contractService.getCalcContractQuantity(contractId, operationCode, nearestIncome.getCurrentContractQuantity());
            contractQuantity = nearestIncome.getCurrentContractQuantity();
            // 操作前的单价（不保留小数位截取）
            if (BigDecimal.ZERO.compareTo(calcContractAmount) == 0 && BigDecimal.ZERO.compareTo(calcContractQuantity) == 0) {
                calcContractUnivalent = BigDecimal.ZERO;
            } else {
                calcContractUnivalent = calcContractAmount.divide(calcContractQuantity, 0, RoundingMode.DOWN);
            }
            // 操作前的累计消耗（不含税）
            calcContractCumulativeIncome = new BigDecimal(nearestIncome.getCumulativeIncome());
            // 操作前的合同递延（不含税）
            calcContractDeferredIncome = new BigDecimal(nearestIncome.getDeferredIncome());
        } else {
            // 合同上月收入数据为空，则取当月收入数据（当月收入数据已经计算过关联差额与退费了，不能再重复计算）
            // 如果当月收入为空，则取当前合同初始数据
            calcContractAmount = new BigDecimal(contract.getInitialAmount());
            calcContractQuantity = this.contractService.getCalcContractQuantity(contractId, operationCode, contract.getInitialQuantity());
            contractQuantity = contract.getInitialQuantity();
            if (BigDecimal.ZERO.compareTo(calcContractAmount) == 0 && BigDecimal.ZERO.compareTo(calcContractQuantity) == 0) {
                calcContractUnivalent = BigDecimal.ZERO;
            } else {
                calcContractUnivalent = calcContractAmount.divide(calcContractQuantity, 0, RoundingMode.DOWN);
            }
            // 历史差额为0
            calcContractCumulativeIncome = BigDecimal.ZERO;
            // 历史递延为合同金额
            calcContractDeferredIncome = calcContractAmount.divide(currentTaxRate, 0, ROUND_HALF_EVEN);
        }
        // 获取累计消耗量
        if (Objects.nonNull(nearestIncomeDetail)) {
            // 操作前的累计消耗量
            calcContractCumulativeConsume = new BigDecimal(nearestIncomeDetail.getCumulativeConsume());
        } else {
            // 历史累计消耗为0
            calcContractCumulativeConsume = BigDecimal.ZERO;
        }
        if (periodPackageType) {
            // 查询历史年流量过期数据
            FinanceIncomeDetailEntity historyPeriodExpireDetail = this.incomeDetailRepository.getNearestMonthSpecifyItemIdDetail(contractId, currentMonth, FinanceIncomeDetailTagEnum.PERIOD_EXPIRED_INCOME_DETAIL_TAG.getCode());
            if (Objects.nonNull(historyPeriodExpireDetail)) {
                if (Objects.nonNull(historyPeriodExpireDetail.getCumulativeConsume())) {
                    // 周期流量过期用量
                    calcContractCumulativeConsume = calcContractCumulativeConsume.add(new BigDecimal(historyPeriodExpireDetail.getCumulativeConsume()));
                }
            }
        }

        // ===============================处理关联（合同金额变化、充值量不变、单价变化、差额部分入非消耗收入）===============================
        if (isCurrentMonthAssociation && Objects.nonNull(associateContractAmount)) {
            // 当月之前的收入信息
            // 关联后的单价（不保留小数位截取）
            BigDecimal afterAssociationUnivalent = new BigDecimal(associateContractAmount).divide(calcContractQuantity, 0, ROUND_DOWN);
            // 是否需要转换为GB
            Boolean isGbConverted = this.contractService.isGbConvertedContract(contract);
            // 获取累计消耗量的差价（计入当月非消耗收入）（含税）= 累计消耗量 * ( 新单价 - 旧单价)
            diffUnivalent = afterAssociationUnivalent.longValue() - calcContractUnivalent.longValue();
            if (Objects.nonNull(isGbConverted) && isGbConverted) {
                differenceIncome = calcContractCumulativeConsume
                        .multiply(new BigDecimal(diffUnivalent))
                        .divide(new BigDecimal("1024").pow(3), 2, ROUND_HALF_UP).divide(currentTaxRate, 0, RoundingMode.HALF_EVEN);
            } else {
                differenceIncome = calcContractCumulativeConsume
                        .multiply(new BigDecimal(diffUnivalent))
                        .divide(currentTaxRate, 0, RoundingMode.HALF_EVEN);
            }
            // 合同金额 = 关联后合同金额
            contractAmount = new BigDecimal(associateContractAmount);
            // 递延金额 =
            deferredIncome = contractAmount.divide(currentTaxRate, 0, ROUND_HALF_EVEN);
            // 合同单价使用新单价（不保留小数位截取）
            contractUnivalent = new BigDecimal(afterAssociationUnivalent.longValue());
            // 关联的前后合同累计收入不变
            cumulativeIncome = calcContractCumulativeIncome;

            calcContractAmount = contractAmount;
            calcContractUnivalent = contractUnivalent;
        }
        // ===============================处理退款（合同金额变化、充值量变化、单价不变、递延扣减退收入部分）===============================
        // 当前充值量 = 当前合同金额（new）/ 收入单价
        if (refundMoney.compareTo(BigDecimal.ZERO) > 0) {
            // 当前合同金额
            contractAmount = calcContractAmount.subtract(refundMoney);
            // 当前充值量 = 当前合同金额（new）/ 收入单价
            calcContractQuantity = contractAmount.divide(calcContractUnivalent, 0, ROUND_DOWN);
            // 单位转换
            contractQuantity = this.contractService.getStoreContractQuantity(contractId, operationCode, calcContractQuantity);
            // 在这里扣减合同的递延金额，其他地方会处理生成退收入情况
            deferredIncome = contractAmount.divide(currentTaxRate, 0, ROUND_HALF_EVEN).subtract(calcContractCumulativeIncome).subtract(differenceIncome);
        }
        // 合同金额
        if (Objects.isNull(contractAmount)) {
            contractAmount = calcContractAmount;
        }
        // 单价不含税计算
        if (Objects.isNull(contractUnivalent)) {
            contractUnivalentWithTax = calcContractUnivalent;
            contractUnivalent = new BigDecimal(calcContractUnivalent.divide(currentTaxRate, 0, RoundingMode.DOWN).longValue());
        } else {
            contractUnivalentWithTax = new BigDecimal(contractUnivalent.toPlainString());
            contractUnivalent = new BigDecimal(contractUnivalent.divide(currentTaxRate, 0, RoundingMode.DOWN).longValue());
        }
        // 累计收入量
        if (Objects.isNull(cumulativeIncome)) {
            cumulativeIncome = calcContractCumulativeIncome;
        }
        // 累计消耗量
        cumulativeConsumed = calcContractCumulativeConsume;
        // 递延金额
        if (Objects.isNull(deferredIncome)) {
            deferredIncome = calcContractDeferredIncome;
        } else {
            if (deferredIncome.compareTo(BigDecimal.ZERO) < 0) {
                // 如果当前递延小于0，则需要产生退费退收入记录
                refundIncome = deferredIncome.abs();
            }
        }
        // 合同金额不含税
        BigDecimal contractAmountExcludingTax = contractAmount.divide(currentTaxRate, 0, ROUND_HALF_EVEN);
        // 当前合同可用金额（不含税） = 当前合同金额（不含税）- 累计使用（不含税） + 关联差额（不含税）
        contractAvailable = contractAmountExcludingTax.subtract(calcContractCumulativeIncome).subtract(differenceIncome);
        CurrentFinanceIncomeDTO currentFinanceInfoDTO = CurrentFinanceIncomeDTO.builder()
                .contractId(contractId)
                .currentMonth(currentMonth)
                .contractAmount(contractAmount)
                .contractAmountExcludingTax(contractAmountExcludingTax)
                .contractQuantity(contractQuantity)
                .calcContractQuantity(calcContractQuantity)
                .contractUnivalent(contractUnivalent)
                .contractUnivalentWithTax(contractUnivalentWithTax)
                .cumulativeIncome(cumulativeIncome)
                .cumulativeConsumed(cumulativeConsumed)
                .diffAmount(differenceIncome)
                .diffUnivalent(diffUnivalent)
                .deferredIncome(deferredIncome)
                .refundIncome(refundIncome)
                .contractAvailable(contractAvailable)
                .deferredQuantity(new BigDecimal(contractQuantity).subtract(calcContractCumulativeConsume))
                .taxRate(currentTaxRate)
                .build();
        return currentFinanceInfoDTO;
    }


    /**
     * 设置并更新当前收入信息
     *
     * @param contract            合同实体
     * @param cfi                 当前收入实体
     * @param usageIncomeAmount   业务消耗金额（不含税）
     * @param otherIncomeAmount   其他消耗金额（不含税）
     * @param usageIncomeQuantity 业务消耗用量
     * @param otherIncomeQuantity 其他消耗用量
     */
    @Override
    public void setCurrentFinanceIncome(FinanceContractEntity contract, CurrentFinanceIncomeDTO cfi,
                                        BigDecimal usageIncomeAmount, BigDecimal otherIncomeAmount,
                                        BigDecimal usageIncomeQuantity, BigDecimal otherIncomeQuantity) {
        log.info(String.format("合同{%s}保存收入, 当前收入数据{%s} usageIncomeAmount={%s}, otherIncomeAmount={%s}, usageIncomeQuantity={%s}，otherIncomeQuantity={%s}",
                contract.getContractId(), JsonMapper.jsonToString(cfi), usageIncomeAmount.toPlainString(), otherIncomeAmount.toPlainString(),
                usageIncomeQuantity.toPlainString(), otherIncomeQuantity.toPlainString()));
        try {
            addFinanceIncome(contract, cfi, usageIncomeAmount, otherIncomeAmount, usageIncomeQuantity, otherIncomeQuantity);
            // 重置精度
            resettingIncomePrecision(contract.getContractId(), cfi.getCurrentMonth());
        } catch (Exception e) {
            log.error(String.format("setCurrentFinanceIncome error. CurrentFinanceIncomeDTO[%s],errMsg[%s]", JsonUtil.beanToString(cfi), e.getMessage()), e);
            String e1 = String.format("当前合同ID %s", contract.getContractId());
            String e2 = String.format("当前月份 %s", cfi.getCurrentMonth());
            String e3 = String.format("当前收入信息实体 %s", cfi);
            String e4 = String.format("当前收入信息 usageIncomeAmount %s", usageIncomeAmount);
            String e5 = String.format("当前收入信息 otherIncomeAmount %s", otherIncomeAmount);
            String e6 = String.format("当前收入信息 usageIncomeQuantity %s", usageIncomeQuantity);
            String e7 = String.format("当前收入信息 otherIncomeQuantity %s", otherIncomeQuantity);
            String e8 = String.format("异常信息 e %s", e.getMessage());
            dingWarnRobot.sendMsgToAtPeople("设置当前用量合同收入信息发生错误", Lists.newArrayList(e1, e2, e3, e4, e5, e6, e7), "");
        }
    }

    private void addFinanceIncome(FinanceContractEntity contract, CurrentFinanceIncomeDTO cfi,
                                  BigDecimal usageIncomeAmount, BigDecimal otherIncomeAmount,
                                  BigDecimal usageIncomeQuantity, BigDecimal otherIncomeQuantity) {
        Date current = new Date();
        String contractId = contract.getContractId();
        String currentMonth = cfi.getCurrentMonth();
        String operationCode = contract.getOperationCode();
        // 指定计费项，金额收入使用
        Integer specifyBillingItemId = cfi.getSpecifyBillingItemId();
        // 合同充值量（已经扣减退款后的充值量）
        Long contractQuantity = cfi.getContractQuantity();
        // 合同历史消耗量
        BigDecimal cumulativeConsumed = cfi.getCumulativeConsumed();
        // 保存字段
        BigDecimal currentContractAmount = cfi.getContractAmount();
        // 当月总消耗=业务消耗+非业务消耗
        BigDecimal totalIncome = usageIncomeAmount.add(otherIncomeAmount);
        // 当月累计收入 = 历史累计收入 + 当月收入
        BigDecimal cumulativeIncome = totalIncome.add(cfi.getCumulativeIncome());
        // 当月递延收入 = 历史递延收入 - 当月总收入
        // BigDecimal deferredIncome = cfi.getDeferredIncome().subtract(totalIncome);
        // 当月递延收入 = 合同金额（不含税） - 历史累计收入
        BigDecimal deferredIncome = cfi.getContractAmountExcludingTax().subtract(cumulativeIncome);

        BillingItem currentBillingItem = null;
        // 获取当月收入信息
        FinanceIncomeEntity fi = this.incomeRepository.findByContractIdAndIncomeMonth(contractId, currentMonth);
        // 获取当月收入明细
        FinanceIncomeDetailEntity fid = null;
        if (Objects.nonNull(specifyBillingItemId)) {
            currentBillingItem = this.billingService.getById(specifyBillingItemId);
            fid = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId, currentMonth, specifyBillingItemId);
        } else {
            fid = this.incomeDetailRepository.findContractLastIncomeDetail(contractId, currentMonth);
        }
        if (Objects.isNull(fi)) {
            fi = new FinanceIncomeEntity();
            fi.setCreateTime(current);
            fi.setContractId(contractId);
            fi.setIncomeMonth(currentMonth);
            fi.setTotalIncome(0L);
            fi.setUsageIncome(0L);
            fi.setOtherIncome(0L);
        }
        Long historyDetailAmount = 0L;
        Long historyDetailQuantity = 0L;
        // 查询历史数据
        if (Objects.isNull(fid)) {
            fid = new FinanceIncomeDetailEntity();
            fid.setBillingItemId(specifyBillingItemId);
            fid.setCreateTime(current);
            fid.setStatus(StatusValueEnum.YES.getValue());
            fid.setContractId(contractId);
            fid.setIncomeMonth(currentMonth);
            if (Objects.nonNull(currentBillingItem)) {
                fid.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByBillingItemCode(currentBillingItem.getCode()));
            }
        }
        if (Objects.nonNull(specifyBillingItemId)) {
            // 查询上一条指定计费项收入明细数据
            FinanceIncomeDetailEntity nearestBillingItemIncomeDetail = this.incomeDetailRepository.findNearestByBillingItemId(contractId, specifyBillingItemId, currentMonth);
            fid.setBillingItemId(specifyBillingItemId);
            if (Objects.nonNull(nearestBillingItemIncomeDetail)) {
                fid.setSubCategory(nearestBillingItemIncomeDetail.getSubCategory());
                fid.setTaxRate(nearestBillingItemIncomeDetail.getTaxRate());
                fid.setIncomeBelong(nearestBillingItemIncomeDetail.getIncomeBelong());
                fid.setConsumeUnit(nearestBillingItemIncomeDetail.getConsumeUnit());
                historyDetailAmount = nearestBillingItemIncomeDetail.getCumulativeIncome();
                historyDetailQuantity = nearestBillingItemIncomeDetail.getCumulativeConsume();
            }
        } else {
            fid.setSubCategory(contract.getSubCategory());
            fid.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByOperationCode(operationCode));
            fid.setConsumeUnit(contract.getQuantityUnit());
            fid.setUnivalent(cfi.getContractUnivalentWithTax().longValue());
            fid.setUnivalentUnit(contract.getIncomeUnivalentUnit());
            FinanceIncomeDetailEntity nearestItemIncomeDetail = this.incomeDetailRepository.getNearestMonthNullItemIdDetail(contractId, currentMonth);
            if (Objects.nonNull(nearestItemIncomeDetail)) {
                historyDetailAmount = nearestItemIncomeDetail.getCumulativeIncome();
                historyDetailQuantity = nearestItemIncomeDetail.getCumulativeConsume();
            }
        }

        // 组装收入数据
        fi.setCurrentContractAmount(currentContractAmount.longValue());
        fi.setCurrentContractQuantity(contractQuantity);
        fi.setTotalIncome(totalIncome.longValue());
        fi.setUsageIncome(usageIncomeAmount.longValue());
        fi.setOtherIncome(otherIncomeAmount.longValue());
        fi.setCumulativeIncome(cumulativeIncome.longValue());
        fi.setDeferredIncome(deferredIncome.longValue());
        fi.setUpdateTime(current);
        this.incomeRepository.save(fi);

        // 组装收入明细数据
        fid.setCurrentIncome(usageIncomeAmount.longValue());
        fid.setCumulativeIncome(historyDetailAmount + usageIncomeAmount.longValue());
        long usageIncomeQuantityLongValue = Objects.nonNull(usageIncomeQuantity) ? usageIncomeQuantity.longValue() : 0;
        fid.setCurrentConsume(usageIncomeQuantityLongValue);
        fid.setCumulativeConsume(usageIncomeQuantityLongValue + historyDetailQuantity);

        if (deferredIncome.compareTo(BigDecimal.ZERO) <= 0) {
            // 如果递延为0，则修改合同确认收入状态
            this.finishContractStatus(contract, currentMonth);
            fid.setSettledStatus(StatusValueEnum.YES.getValue());
        } else {
            fid.setSettledStatus(StatusValueEnum.NO.getValue());
        }
        if (Objects.nonNull(cfi.getTaxRate())) {
            fid.setTaxRate(MoneyUtil.enlargeNumericalHundred(cfi.getTaxRate().toPlainString()));
        } else {
            if (Lists.newArrayList(ContractOperationCodeEnum.HARD_CABIN_BODY.getCode(),
                    ContractOperationCodeEnum.HARD_OTHER_SALE.getCode()).contains(operationCode)) {
                fid.setTaxRate(FinanceTaxRateConst.HARD.getTaxRate().multiply(new BigDecimal("100")).longValue());
            } else {
                fid.setTaxRate(FinanceTaxRateConst.NORMAL.getTaxRate().multiply(new BigDecimal("100")).longValue());
            }
        }
        fid.setUpdateTime(current);
        this.incomeDetailRepository.save(fid);
        // 处理特殊收入
        this.addSpecialIncomeDetail(contract, cfi, fi, usageIncomeAmount, otherIncomeAmount, usageIncomeQuantity, otherIncomeQuantity);
    }

    /**
     * 保存特殊收入为一条独立的收入明细数据
     * 4种情况：
     * 1.合同到期的剩余金额确认收入
     * 2.合同消耗完毕的递延尾差
     * 3.合同退费产生的退收入
     * 4.合同关联产生的收入差额
     *
     * @param contract            合同实体
     * @param cfi                 收入实体
     * @param fi                  当月收入实体，用于更新递延
     * @param usageIncomeAmount   消耗收入金额
     * @param otherIncomeAmount   非消耗收入金额
     * @param usageIncomeQuantity 消耗量
     * @param otherIncomeQuantity 非消耗量
     */
    @Override
    public void addSpecialIncomeDetail(FinanceContractEntity contract, CurrentFinanceIncomeDTO cfi, FinanceIncomeEntity fi,
                                       BigDecimal usageIncomeAmount, BigDecimal otherIncomeAmount,
                                       BigDecimal usageIncomeQuantity, BigDecimal otherIncomeQuantity) {
        String contractId = contract.getContractId();
        String currentMonth = cfi.getCurrentMonth();
        String lastMonth = DateFormatUtil.formatDateMonth(DateUtil.getAddMonthDate(DateFormatUtil.parseDateMonthNormal(currentMonth), -1));
        String operationCode = contract.getOperationCode();
        Date current = new Date();
        // 合同历史消耗量
        BigDecimal cumulativeConsumed = cfi.getCumulativeConsumed();
        // 合同历史累计收入
        BigDecimal cumulativeIncome = cfi.getCumulativeIncome();
        // 预开通合同关联产生的差额
        BigDecimal diffAmount = cfi.getDiffAmount();
        Long diffUnivalent = cfi.getDiffUnivalent();
        // 合同退费退收入
        BigDecimal refundIncome = cfi.getRefundIncome();
        // 合同充值量（已经扣减退款后的充值量）
        Long contractQuantity = cfi.getContractQuantity();
        // 合同总金额（含税）
        BigDecimal contractAmount = cfi.getContractAmount();
        // 合同单价
        BigDecimal contractUnivalentWithTax = cfi.getContractUnivalentWithTax();
        // 合同总金额（不含税）
        BigDecimal contractAmountExcludingTax = cfi.getContractAmountExcludingTax();
        // 合同可用金额
        BigDecimal contractAvailable = cfi.getContractAvailable();
        // 税率
        BigDecimal taxRate = cfi.getTaxRate();
        Long saveTaxRate = 106L;
        if (Objects.nonNull(taxRate)) {
            saveTaxRate = MoneyUtil.enlargeNumericalHundred(cfi.getTaxRate().toPlainString());
        }
        // 年流量套餐处理流程
        Long historyPeriodExpireCumulativeConsume = 0L;
        boolean periodPackageType = (ContractOperationCodeEnum.VOD_YEARLY_PACKAGE.getCode().equals(operationCode) || ContractOperationCodeEnum.COOL_YEARLY_PACKAGE.getCode().equals(operationCode));
        if (periodPackageType) {
            // 查询历史年流量过期数据
            FinanceIncomeDetailEntity historyPeriodExpireDetail = this.incomeDetailRepository.getNearestMonthSpecifyItemIdDetail(contractId, currentMonth, FinanceIncomeDetailTagEnum.PERIOD_EXPIRED_INCOME_DETAIL_TAG.getCode());
            if (Objects.nonNull(historyPeriodExpireDetail)) {
                if (Objects.nonNull(historyPeriodExpireDetail.getCumulativeConsume())) {
                    historyPeriodExpireCumulativeConsume = historyPeriodExpireDetail.getCumulativeConsume();
                }
            }
            // 查询出本月的年流量过期数据
            FinanceIncomeDetailEntity currentMonthPeriodExpireDetail = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId, currentMonth,
                    FinanceIncomeDetailTagEnum.PERIOD_EXPIRED_INCOME_DETAIL_TAG.getCode());
            // 1.处理周期过期数据
            if (otherIncomeQuantity.compareTo(BigDecimal.ZERO) > 0) {
                if (Objects.isNull(currentMonthPeriodExpireDetail)) {
                    currentMonthPeriodExpireDetail = new FinanceIncomeDetailEntity();
                    currentMonthPeriodExpireDetail.setContractId(contractId);
                    currentMonthPeriodExpireDetail.setIncomeMonth(currentMonth);
                    currentMonthPeriodExpireDetail.setBillingItemId(FinanceIncomeDetailTagEnum.PERIOD_EXPIRED_INCOME_DETAIL_TAG.getCode());
                    currentMonthPeriodExpireDetail.setSubCategory(SubCategoryEnum.SC_EXPIRED_CLEAR.getCode());
                    currentMonthPeriodExpireDetail.setTaxRate(saveTaxRate);
                    currentMonthPeriodExpireDetail.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByOperationCode(operationCode));
                    currentMonthPeriodExpireDetail.setConsumeUnit(contract.getQuantityUnit());
                    currentMonthPeriodExpireDetail.setUnivalentUnit(contract.getIncomeUnivalentUnit());
                    currentMonthPeriodExpireDetail.setCreateTime(current);
                    currentMonthPeriodExpireDetail.setStatus(StatusValueEnum.YES.getValue());
                }
                currentMonthPeriodExpireDetail.setUnivalent(contractUnivalentWithTax.longValue());
                currentMonthPeriodExpireDetail.setCurrentIncome(otherIncomeAmount.longValue());
                currentMonthPeriodExpireDetail.setCurrentConsume(otherIncomeQuantity.longValue());
                currentMonthPeriodExpireDetail.setCumulativeIncome(otherIncomeAmount.longValue());

                currentMonthPeriodExpireDetail.setCumulativeConsume(historyPeriodExpireCumulativeConsume + otherIncomeQuantity.longValue());
                currentMonthPeriodExpireDetail.setSettledStatus(StatusValueEnum.NO.getValue());
                currentMonthPeriodExpireDetail.setUpdateTime(current);
                this.incomeDetailRepository.save(currentMonthPeriodExpireDetail);
            } else {
                // 如果不存在了需要删除
                if (Objects.nonNull(currentMonthPeriodExpireDetail)) {
                    this.incomeDetailRepository.delete(currentMonthPeriodExpireDetail);
                }
            }
        }

        // 查询出该合同当前收入明细的这些特殊收入
        List<FinanceIncomeDetailEntity> currentContractExtraIncomeList = this.incomeDetailRepository.getCurrentMonthSpecifyItemIdDetailList(contractId,
                currentMonth, Lists.newArrayList(
                        FinanceIncomeDetailTagEnum.ASSOCIATION_EXTRA_INCOME_DETAIL_TAG.getCode(),
                        FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode(),
                        FinanceIncomeDetailTagEnum.EXPIRED_INCOME_DETAIL_TAG.getCode(),
                        FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode()));
        // 查询上月份特殊收入数据，用于计算累计收入（关联，过期没有多次）
        List<FinanceIncomeDetailEntity> lastMonthExtraIncomeList = this.incomeDetailRepository.getCurrentMonthSpecifyItemIdDetailList(contractId,
                lastMonth, Lists.newArrayList(
                        FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode(),
                        FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode()));

        Map<Integer, FinanceIncomeDetailEntity> currentContractExtraIncomeMap = new HashMap<>();
        Map<Integer, FinanceIncomeDetailEntity> lastMonthExtraIncomeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(currentContractExtraIncomeList)) {
            currentContractExtraIncomeMap = currentContractExtraIncomeList.stream().collect(Collectors.toMap(FinanceIncomeDetailEntity::getBillingItemId,
                    Function.identity(), (o, n) -> n, HashMap::new));
        }
        if (!CollectionUtils.isEmpty(lastMonthExtraIncomeList)) {
            lastMonthExtraIncomeMap = lastMonthExtraIncomeList.stream().collect(Collectors.toMap(FinanceIncomeDetailEntity::getBillingItemId,
                    Function.identity(), (o, n) -> n, HashMap::new));
        }
        // 合同历史视同+当月消耗量+当月费消耗量+年流量套餐历史过期
        boolean isTotalConsumedOut = cumulativeConsumed.add(usageIncomeQuantity).add(otherIncomeQuantity).compareTo(new BigDecimal(contractQuantity)) >= 0;
        // 合同到期：如果历史消耗+当前消耗+当前其他消耗 = 合同总充值量 并且其他消耗大于0：到期剩余收入 （年流量套餐有自己单独的周期过期，不走合同过期）
        FinanceIncomeDetailEntity expireIncome = null;
        if (otherIncomeQuantity.compareTo(BigDecimal.ZERO) > 0 && isTotalConsumedOut && !periodPackageType) {
            expireIncome = currentContractExtraIncomeMap.get(FinanceIncomeDetailTagEnum.EXPIRED_INCOME_DETAIL_TAG.getCode());
            if (Objects.isNull(expireIncome)) {
                expireIncome = new FinanceIncomeDetailEntity();
                expireIncome.setContractId(contractId);
                expireIncome.setIncomeMonth(currentMonth);
                expireIncome.setBillingItemId(FinanceIncomeDetailTagEnum.EXPIRED_INCOME_DETAIL_TAG.getCode());
                expireIncome.setSubCategory(SubCategoryEnum.SC_EXPIRED_CLEAR.getCode());
                expireIncome.setTaxRate(saveTaxRate);
                expireIncome.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByOperationCode(operationCode));
                expireIncome.setConsumeUnit(contract.getQuantityUnit());
                expireIncome.setUnivalentUnit(contract.getIncomeUnivalentUnit());
                expireIncome.setCreateTime(current);
                expireIncome.setStatus(StatusValueEnum.YES.getValue());
            }
            expireIncome.setUnivalent(contractUnivalentWithTax.longValue());
            expireIncome.setCurrentIncome(otherIncomeAmount.longValue());
            expireIncome.setCurrentConsume(otherIncomeQuantity.longValue());
            expireIncome.setCumulativeIncome(otherIncomeAmount.longValue());
            expireIncome.setCumulativeConsume(otherIncomeQuantity.longValue());
            expireIncome.setSettledStatus(StatusValueEnum.YES.getValue());
            expireIncome.setUpdateTime(current);
        }
        FinanceIncomeDetailEntity associationExtraIncome = null;
        if (diffAmount.compareTo(BigDecimal.ZERO) != 0) {
            // 如果关联差额收入大于0：关联差额收入记录
            associationExtraIncome = currentContractExtraIncomeMap.get(FinanceIncomeDetailTagEnum.ASSOCIATION_EXTRA_INCOME_DETAIL_TAG.getCode());
            if (Objects.isNull(associationExtraIncome)) {
                associationExtraIncome = new FinanceIncomeDetailEntity();
                associationExtraIncome.setContractId(contractId);
                associationExtraIncome.setIncomeMonth(currentMonth);
                associationExtraIncome.setBillingItemId(FinanceIncomeDetailTagEnum.ASSOCIATION_EXTRA_INCOME_DETAIL_TAG.getCode());
                associationExtraIncome.setSubCategory(SubCategoryEnum.SC_ASSOCIATION_EXTRA.getCode());
                associationExtraIncome.setTaxRate(saveTaxRate);
                associationExtraIncome.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByOperationCode(operationCode));
                associationExtraIncome.setConsumeUnit(contract.getQuantityUnit());
                associationExtraIncome.setUnivalentUnit(contract.getIncomeUnivalentUnit());
                associationExtraIncome.setCreateTime(current);
                associationExtraIncome.setStatus(StatusValueEnum.YES.getValue());
            }
            // 计算递延尾差= 总可用金额-历史收入-当月收入
            associationExtraIncome.setCurrentIncome(diffAmount.longValue());
            associationExtraIncome.setUnivalent(diffUnivalent);
            associationExtraIncome.setCumulativeIncome(diffAmount.longValue());
            associationExtraIncome.setCurrentConsume(cumulativeConsumed.longValue());
            associationExtraIncome.setCumulativeConsume(cumulativeConsumed.longValue());
            associationExtraIncome.setSettledStatus(StatusValueEnum.YES.getValue());
            associationExtraIncome.setUpdateTime(current);
        }
        FinanceIncomeDetailEntity refundExtraIncome = null;
        if (refundIncome.compareTo(BigDecimal.ZERO) > 0) {
            // 如果退费收入大于0：增加退费退收入
            refundExtraIncome = currentContractExtraIncomeMap.get(FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode());
            if (Objects.isNull(refundExtraIncome)) {
                refundExtraIncome = new FinanceIncomeDetailEntity();
                refundExtraIncome.setContractId(contractId);
                refundExtraIncome.setIncomeMonth(currentMonth);
                refundExtraIncome.setBillingItemId(FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode());
                refundExtraIncome.setSubCategory(SubCategoryEnum.SC_REFUND_EXTRA.getCode());
                refundExtraIncome.setTaxRate(saveTaxRate);
                refundExtraIncome.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByOperationCode(operationCode));
                refundExtraIncome.setConsumeUnit(contract.getQuantityUnit());
                refundExtraIncome.setUnivalentUnit(contract.getIncomeUnivalentUnit());
                refundExtraIncome.setCreateTime(current);
                refundExtraIncome.setStatus(StatusValueEnum.YES.getValue());
            }
            // 计算递延尾差= 总可用金额-历史收入-当月收入
            refundExtraIncome.setUnivalent(contractUnivalentWithTax.longValue());
            refundExtraIncome.setCurrentIncome(refundIncome.negate().longValue());
            refundExtraIncome.setCurrentConsume(0L);
            FinanceIncomeDetailEntity lastMonthRefundExtra = lastMonthExtraIncomeMap.get(FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode());
            Long totalRefundExtra = 0L;
            if (Objects.nonNull(lastMonthRefundExtra) && Objects.nonNull(lastMonthRefundExtra.getCumulativeIncome())) {
                totalRefundExtra = lastMonthRefundExtra.getCumulativeIncome();
            }
            refundExtraIncome.setCumulativeIncome(totalRefundExtra + refundIncome.negate().longValue());
            refundExtraIncome.setCumulativeConsume(0L);
            refundExtraIncome.setSettledStatus(StatusValueEnum.YES.getValue());
            refundExtraIncome.setUpdateTime(current);
        }
        // 如果历史消耗 + 当前消耗收入= 合同总充值量 并且 历史收入 + 当月收入 != 总金额 当前：尾差收入
        FinanceIncomeDetailEntity tailExtraIncome = null;
        // contractAvailable里面已经加入了关联差额，需要减去
        if (isTotalConsumedOut && usageIncomeAmount.add(otherIncomeAmount).subtract(refundIncome).compareTo(contractAvailable) != 0) {
            // 计算尾差 计算「累计收入」与「当前合同金额」的差值，若有差额，更新至当月的收入快照的非消耗收入
            tailExtraIncome = currentContractExtraIncomeMap.get(FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode());
            if (Objects.isNull(tailExtraIncome)) {
                tailExtraIncome = new FinanceIncomeDetailEntity();
                tailExtraIncome.setContractId(contractId);
                tailExtraIncome.setIncomeMonth(currentMonth);
                tailExtraIncome.setBillingItemId(FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode());
                tailExtraIncome.setSubCategory(SubCategoryEnum.SC_TAIL_EXTRA.getCode());
                tailExtraIncome.setTaxRate(saveTaxRate);
                tailExtraIncome.setIncomeBelong(FinanceIncomeBelongEnum.getBelongByOperationCode(operationCode));
                tailExtraIncome.setConsumeUnit(contract.getQuantityUnit());
                tailExtraIncome.setUnivalentUnit(contract.getIncomeUnivalentUnit());
                tailExtraIncome.setCreateTime(current);
                tailExtraIncome.setStatus(StatusValueEnum.YES.getValue());
            }
            tailExtraIncome.setUnivalent(contractUnivalentWithTax.longValue());
            // 计算递延尾差 = 总可用金额 - 历史收入- 当月收入
            // 这个公司参考上面那个if判断条件
            BigDecimal tailExtra = contractAvailable.add(refundIncome).subtract(usageIncomeAmount).subtract(otherIncomeAmount);
            tailExtraIncome.setCurrentIncome(tailExtra.longValue());
            tailExtraIncome.setCurrentConsume(0L);
            FinanceIncomeDetailEntity lastMonthTailExtra = lastMonthExtraIncomeMap.get(FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode());
            Long totalTailExtra = 0L;
            if (Objects.nonNull(lastMonthTailExtra) && Objects.nonNull(lastMonthTailExtra.getCumulativeIncome())) {
                totalTailExtra = lastMonthTailExtra.getCumulativeIncome();
            }
            tailExtraIncome.setCumulativeIncome(totalTailExtra + tailExtra.longValue());
            tailExtraIncome.setCumulativeConsume(0L);
            tailExtraIncome.setSettledStatus(StatusValueEnum.YES.getValue());
            tailExtraIncome.setUpdateTime(current);
        }
        boolean needFinishContract = false;
        BigDecimal updateOtherIncome = BigDecimal.ZERO;
        // 保存数据
        // 2023-05-25 防止重跑收入，需要把缺失的明细删掉
        if (Objects.nonNull(expireIncome)) {
            this.incomeDetailRepository.save(expireIncome);
            needFinishContract = true;
        } else {
            if (Objects.nonNull(currentContractExtraIncomeMap.get(FinanceIncomeDetailTagEnum.EXPIRED_INCOME_DETAIL_TAG.getCode()))) {
                FinanceIncomeDetailEntity needDelTailExtraIncome = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId,
                        currentMonth, FinanceIncomeDetailTagEnum.EXPIRED_INCOME_DETAIL_TAG.getCode());
                if (Objects.nonNull(needDelTailExtraIncome)) {
                    this.incomeDetailRepository.delete(needDelTailExtraIncome);
                }
            }
        }
        if (Objects.nonNull(tailExtraIncome)) {
            this.incomeDetailRepository.save(tailExtraIncome);
            needFinishContract = true;
        } else {
            // 如果是空，则代表合同该月没有尾差，如果存在需要清理
            // 在合同发生变化，比如关联之前有尾差，关联后无尾差则需清理
            if (Objects.nonNull(currentContractExtraIncomeMap.get(FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode()))) {
                FinanceIncomeDetailEntity needDelTailExtraIncome = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId,
                        currentMonth, FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode());
                if (Objects.nonNull(needDelTailExtraIncome)) {
                    this.incomeDetailRepository.delete(needDelTailExtraIncome);
                }
            }
        }

        if (Objects.nonNull(associationExtraIncome)) {
            this.incomeDetailRepository.save(associationExtraIncome);
            updateOtherIncome = updateOtherIncome.add(new BigDecimal(associationExtraIncome.getCurrentIncome()));
        } else {
            if (Objects.nonNull(currentContractExtraIncomeMap.get(FinanceIncomeDetailTagEnum.ASSOCIATION_EXTRA_INCOME_DETAIL_TAG.getCode()))) {
                FinanceIncomeDetailEntity needDelTailExtraIncome = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId,
                        currentMonth, FinanceIncomeDetailTagEnum.ASSOCIATION_EXTRA_INCOME_DETAIL_TAG.getCode());
                if (Objects.nonNull(needDelTailExtraIncome)) {
                    this.incomeDetailRepository.delete(needDelTailExtraIncome);
                }
            }
        }
        if (Objects.nonNull(refundExtraIncome)) {
            this.incomeDetailRepository.save(refundExtraIncome);
            updateOtherIncome = updateOtherIncome.add(new BigDecimal(refundExtraIncome.getCurrentIncome()));
        } else {
            if (Objects.nonNull(currentContractExtraIncomeMap.get(FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode()))) {
                FinanceIncomeDetailEntity needDelTailExtraIncome = this.incomeDetailRepository.findByContractIdAndIncomeMonthAndBillingItemId(contractId,
                        currentMonth, FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode());
                if (Objects.nonNull(needDelTailExtraIncome)) {
                    this.incomeDetailRepository.delete(needDelTailExtraIncome);
                }
            }
        }
        if (needFinishContract) {
            if (Objects.nonNull(fi)) {
                // 递延已经完结，直接将递延入非消耗
                Long fiDeferredIncome = fi.getDeferredIncome();
                Long fiOtherIncome = fi.getOtherIncome();
                Long fiTotalIncome = fi.getTotalIncome();
                Long fiCumulativeIncome = fi.getCumulativeIncome();
                if (fiDeferredIncome != 0) {
                    // 合同应该完结了
                    fi.setOtherIncome(fiOtherIncome + fiDeferredIncome);
                    fi.setTotalIncome(fiTotalIncome + fiDeferredIncome);
                    fi.setCumulativeIncome(fiCumulativeIncome + fiDeferredIncome);
                    fi.setDeferredIncome(0L);
                    this.incomeRepository.save(fi);
                }
            }
            // 修改合同确认收入状态
            this.finishContractStatus(contract, currentMonth);
        }
        if (updateOtherIncome.compareTo(BigDecimal.ZERO) != 0) {
            if (Objects.nonNull(fi)) {
                Long fiDeferredIncome = fi.getDeferredIncome();
                Long fiOtherIncome = fi.getOtherIncome();
                Long fiTotalIncome = fi.getTotalIncome();
                Long fiCumulativeIncome = fi.getCumulativeIncome();
                if (fiDeferredIncome != 0) {
                    fi.setOtherIncome(fiOtherIncome + updateOtherIncome.longValue());
                    fi.setTotalIncome(fiTotalIncome + updateOtherIncome.longValue());
                    fi.setCumulativeIncome(fiCumulativeIncome + updateOtherIncome.longValue());
                    fi.setDeferredIncome(fiDeferredIncome - updateOtherIncome.longValue());
                    this.incomeRepository.save(fi);
                }
            }
            // 修改合同确认收入状态
            // this.finishContractStatus(contract, currentMonth);
        }
    }


    @Override
    public void statisticsSpecialStage(Date specifyDate) {
        Date current = new Date();
        if (Objects.isNull(specifyDate)) {
            specifyDate = current;
        }
        // 连麦分钟数的billId，需要特殊处理
        List<Integer> micItemIdList = Lists.newArrayList(4, 5, 45, 46, 47, 48, 49);
        // 上个月的第一天
        Date previousMonthFirstDay = DateUtil.getFirstDayOfPreviousMonth(specifyDate);
        // 上个月份
        String currentMonth = DateFormatUtil.formatDateMonth(previousMonthFirstDay);
        // 当月的第二天和下一个月的第一天（出账日期是隔天出账）
        Date billStartDate = DateUtil.getDateStart(DateUtil.getSecondDayOfMonth(previousMonthFirstDay));
        // 获取上月结束时间
        Date billEndDate = DateUtil.getDateEnd(DateUtil.getFirstDayOfMonth(specifyDate));
        String billStartDateStr = DateFormatUtil.formatDateNormal(billStartDate);
        String billEndDateStr = DateFormatUtil.formatDateNormal(billEndDate);
        // 偿还开始、结束时间
        Date billReplyStartDate = DateUtil.getDateStart(previousMonthFirstDay);
        Date billReplyEndDate = DateUtil.getDateEnd(DateUtil.getLastDayOfMonth(previousMonthFirstDay));
        String billReplyStartDateStr = DateFormatUtil.formatDateTimeNormal(billReplyStartDate);
        String billReplyEndDateStr = DateFormatUtil.formatDateTimeNormal(billReplyEndDate);


        // 查询上个月偿还待支付的用户
        Bills4FinanceQueryDO replyAndUnpaidQueryDO = new Bills4FinanceQueryDO();
        replyAndUnpaidQueryDO.setStatAtStartDate(billStartDateStr);
        // 补统计下个月2号的连麦数据
        Date micStatAtDate = DateUtil.getSecondDayOfMonth(specifyDate);
        replyAndUnpaidQueryDO.setStatAtEndDate(DateFormatUtil.formatDateNormal(micStatAtDate));
        replyAndUnpaidQueryDO.setRepayOperationStartTime(billReplyStartDateStr);
        replyAndUnpaidQueryDO.setRepayOperationEndTime(billReplyEndDateStr);
        // 获取用户偿还的待支付账单
        List<String> replyBillCustomerIdList = this.customerBillingDailyRepository.queryCustomerReplyOrUnpaidBills4Finance(replyAndUnpaidQueryDO);
        replyBillCustomerIdList = replyBillCustomerIdList.stream().distinct().collect(Collectors.toList());
        // 计费项：计费项属性
        Map<Integer, BillingItemAttributeDTO> billingItemAttributeMap = new HashMap<>();
        List<FinanceContractEntity> saveContractList = new ArrayList<>();
        // replyBillCustomerIdList = Lists.newArrayList("06832b9e3a");
        if (!CollectionUtils.isEmpty(replyBillCustomerIdList)) {
            for (String customerId : replyBillCustomerIdList) {
                try {
                    boolean isCreatedWaitPayContract = false;
                    // 计费项-待支付金额Map
                    // Map<Integer, BigDecimal> unpaidBillCostMap = new HashMap<>();
                    // 计费项-已支付金额Map
                    // Map<Integer, BigDecimal> paidBillCostMap = new HashMap<>();
                    // 偿还计费项的偿还待支付账单ID
                    Map<Integer, Long> itemSplitFromMap = new HashMap<>();
                    // 计费项金额
                    Map<Integer, BigDecimal> itemAmountMap = new HashMap<>();
                    // 计费项消耗
                    Map<Integer, BigDecimal> itemConsumedMap = new HashMap<>();
                    // 查询用户待支付合同
                    CommonResult<FinanceContractEntity> waitPayContractResult = this.contractService.getWaitPayContractByUnionId(customerId);
                    FinanceContractEntity waitPayContract = null;
                    if (CommonResult.isOk(waitPayContractResult)) {
                        waitPayContract = waitPayContractResult.getData();
                    } else if (CommonResult.isNotFound(waitPayContractResult)) {
                        // 创建用户待支付
                        CommonResult<FinanceContractEntity> createWaitPayResult = this.contractService.addWaitPayContract(customerId, BigDecimal.ZERO, previousMonthFirstDay);
                        waitPayContract = createWaitPayResult.getData();
                        isCreatedWaitPayContract = true;
                    } else {
                        continue;
                    }
                    if (Objects.isNull(waitPayContract)) {
                        continue;
                    }

                     /*
                         1.使用待支付合同，循环周期内账单
                    */
                    Integer waitPayContractExistsChange = this.customerBillingDailyRepository.queryWaitPayContractExistsChangeBill(customerId, waitPayContract.getContractId(), billReplyStartDate, billReplyEndDate);
                    if (Objects.nonNull(waitPayContractExistsChange)) {
                        // 待支付合同存在变动，查询合同所有账单
                        Bills4FinanceQueryDO waitPayContractBillQueryDO = new Bills4FinanceQueryDO();
                        waitPayContractBillQueryDO.setContractId(waitPayContract.getContractId());
                        waitPayContractBillQueryDO.setCustomerId(customerId);
                        waitPayContractBillQueryDO.setRepayOperationStartTime(billReplyStartDateStr);
                        waitPayContractBillQueryDO.setRepayOperationEndTime(billReplyEndDateStr);
                        List<CustomerBillingDaily> waitPayContractBillList = this.customerBillingDailyRepository.queryWaitPayContractBills(waitPayContractBillQueryDO);

                        if (!CollectionUtils.isEmpty(waitPayContractBillList)) {
                            for (CustomerBillingDaily waitPayBill : waitPayContractBillList) {
                                Integer itemId = waitPayBill.getItemId();
                                Date statAt = waitPayBill.getStatAt();
                                if (Objects.isNull(itemId)) {
                                    continue;
                                }
                                BigDecimal waitPayCost = new BigDecimal(waitPayBill.getCost());
                                Integer status = waitPayBill.getStatus();
                                itemAmountMap.merge(itemId, waitPayCost, BigDecimal::add);
                                if (BillStatusConst.UNPAID == status) {
                                    // 当前依然为待支付的计费项
                                    itemSplitFromMap.putIfAbsent(itemId, waitPayBill.getId());
                                } else {
                                    itemSplitFromMap.putIfAbsent(itemId, waitPayBill.getSplitFrom());
                                }
                                BillingItemAttributeDTO itemAttr = new BillingItemAttributeDTO();
                                if (Objects.nonNull(waitPayBill.getUnivalence())) {
                                    itemAttr.setUnivalent(waitPayBill.getUnivalence());
                                }
                                if (Objects.nonNull(waitPayBill.getItemConsumedUnit())) {
                                    itemAttr.setConsumeUnit(waitPayBill.getItemConsumedUnit());
                                }
                                if (Objects.nonNull(waitPayBill.getUnivalenceUnit())) {
                                    itemAttr.setUnivalentUnit(waitPayBill.getUnivalenceUnit());
                                }
                                billingItemAttributeMap.putIfAbsent(itemId, itemAttr);
                            }
                        }
                    }

                    Bills4FinanceQueryDO customerUnpaidReplyBillQueryDO = new Bills4FinanceQueryDO();
                    customerUnpaidReplyBillQueryDO.setCustomerId(customerId);
                    customerUnpaidReplyBillQueryDO.setContractId(waitPayContract.getContractId());
                    customerUnpaidReplyBillQueryDO.setStatAtStartDate(billStartDateStr);
                    customerUnpaidReplyBillQueryDO.setStatAtEndDate(billEndDateStr);
                    customerUnpaidReplyBillQueryDO.setUnpaidStatus(BillStatusConst.UNPAID);
                    customerUnpaidReplyBillQueryDO.setReplyStatus(BillStatusConst.UNRELATED);
                    List<CustomerBillingDaily> customerUnpaidReplyBills = this.customerBillingDailyRepository.queryCustomerCurrentMonthUnpaidBills(customerUnpaidReplyBillQueryDO);

                    Bills4FinanceQueryDO micCustomerUnpaidReplyBillQueryDO = new Bills4FinanceQueryDO();
                    micCustomerUnpaidReplyBillQueryDO.setCustomerId(customerId);
                    micCustomerUnpaidReplyBillQueryDO.setContractId(waitPayContract.getContractId());
                    micCustomerUnpaidReplyBillQueryDO.setStatAtStartDate(DateFormatUtil.formatDateNormal(micStatAtDate));
                    micCustomerUnpaidReplyBillQueryDO.setItemIdList(micItemIdList);
                    micCustomerUnpaidReplyBillQueryDO.setUnpaidStatus(BillStatusConst.UNPAID);
                    micCustomerUnpaidReplyBillQueryDO.setReplyStatus(BillStatusConst.UNRELATED);
                    List<CustomerBillingDaily> micCustomerUnpaidReplyBills = this.customerBillingDailyRepository.queryMicCustomerCurrentMonthUnpaidBills(micCustomerUnpaidReplyBillQueryDO);
                    customerUnpaidReplyBills.addAll(micCustomerUnpaidReplyBills);


                    // 1.循环账单
                    for (CustomerBillingDaily unpaidReplyBill : customerUnpaidReplyBills) {
                        Integer itemId = unpaidReplyBill.getItemId();
                        if (Objects.isNull(itemId)) {
                            continue;
                        }
                        Date statAt = unpaidReplyBill.getStatAt();
                        BigDecimal unpaidReplyCost = new BigDecimal(unpaidReplyBill.getCost());
                        if (micItemIdList.contains(itemId) && DateFormatUtil.formatDateNormal(statAt).equals(billStartDateStr)) {
                            // 上个月第一天，排除连麦数据
                            continue;
                        }
                        Integer status = unpaidReplyBill.getStatus();
                        itemAmountMap.merge(itemId, unpaidReplyCost, BigDecimal::add);
                        if (BillStatusConst.UNPAID == status) {
                            // 当前依然为待支付的计费项
                            itemSplitFromMap.putIfAbsent(itemId, unpaidReplyBill.getId());
                        } else {
                            itemSplitFromMap.putIfAbsent(itemId, unpaidReplyBill.getSplitFrom());
                        }
                        BillingItemAttributeDTO itemAttr = new BillingItemAttributeDTO();
                        if (Objects.nonNull(unpaidReplyBill.getUnivalence())) {
                            itemAttr.setUnivalent(unpaidReplyBill.getUnivalence());
                        }
                        if (Objects.nonNull(unpaidReplyBill.getItemConsumedUnit())) {
                            itemAttr.setConsumeUnit(unpaidReplyBill.getItemConsumedUnit());
                        }
                        if (Objects.nonNull(unpaidReplyBill.getUnivalenceUnit())) {
                            itemAttr.setUnivalentUnit(unpaidReplyBill.getUnivalenceUnit());
                        }
                        billingItemAttributeMap.putIfAbsent(itemId, itemAttr);
                    }

                    // 查询用户历史待支付金额合同
                    BigDecimal currentCustomerUnpaidAmount = itemAmountMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
                    BigDecimal historyUnpaidContractAmount = BigDecimal.ZERO;
                    FinanceIncomeEntity historyIncome = this.incomeRepository.findNearestByContractIdAndIncomeMonth(waitPayContract.getContractId(), currentMonth);
                    if (Objects.nonNull(historyIncome)) {
                        historyUnpaidContractAmount = Objects.nonNull(historyIncome.getCurrentContractAmount()) ? new BigDecimal(historyIncome.getCurrentContractAmount()) : BigDecimal.ZERO;
                    }
                    // 更新待支付合同金额
                    if (isCreatedWaitPayContract) {
                        waitPayContract.setInitialAmount(currentCustomerUnpaidAmount.longValue());
                        waitPayContract.setInitialQuantity(currentCustomerUnpaidAmount.longValue());
                    }
                    waitPayContract.setCurrentAmount(historyUnpaidContractAmount.longValue() + currentCustomerUnpaidAmount.longValue());
                    waitPayContract.setCurrentQuantity(historyUnpaidContractAmount.longValue() + currentCustomerUnpaidAmount.longValue());
                    waitPayContract.setUpdateTime(current);
                    // saveContractList.add(waitPayContract);
                    this.contractService.saveContract(waitPayContract);
                    log.info(String.format("更新待支付合同金额.当前月份：%s  历史待支付金额 %s 当月待支付金额 %s", currentMonth,
                            historyUnpaidContractAmount.toPlainString(), currentCustomerUnpaidAmount.toPlainString()));
                    // 计算偿还的用量比例
                    Map<Long, CustomerBillingDaily> unpaidBillMap = new HashMap<>();
                    List<Long> billIdList = new ArrayList<>(itemSplitFromMap.values());
                    if (!CollectionUtils.isEmpty(billIdList)) {
                        List<CustomerBillingDaily> unpaidBills = this.customerBillingDailyRepository.queryCustomerUnpaidBillsById(customerId, billIdList);
                        unpaidBillMap = unpaidBills.stream().collect(Collectors.toMap(CustomerBillingDaily::getId, Function.identity(), (o, n) -> n, HashMap::new));
                    }
                    for (Integer key : itemAmountMap.keySet()) {
                        BigDecimal value = itemAmountMap.get(key);
                        Long splitFrom = itemSplitFromMap.get(key);
                        if (Objects.nonNull(splitFrom)) {
                            CustomerBillingDaily unpaidBill = unpaidBillMap.get(splitFrom);
                            // 消耗量 = 当前金额*总金额/充值量
                            BigDecimal consumed = value.divide(new BigDecimal(unpaidBill.getCost()), 5, ROUND_HALF_UP)
                                    .multiply(new BigDecimal(unpaidBill.getOriginalItemConsumed()))
                                    .divide(new BigDecimal(unpaidBill.getLastAdjustRatio()).divide(new BigDecimal(100), 2, ROUND_HALF_UP), 5, ROUND_HALF_UP)
                                    .setScale(0, RoundingMode.HALF_UP);
                            itemConsumedMap.put(key, consumed);
                        }
                        // 计算金额的税率
                        BigDecimal currentAmount = value.divide(FinanceTaxRateConst.NORMAL.getTaxRate(), 0, ROUND_HALF_EVEN);
                        itemAmountMap.put(key, currentAmount);
                    }
                    // 执行确认收入
                    if (itemAmountMap.isEmpty()) {
                        // 清除待支付合同收入
                        List<FinanceIncomeDetailEntity> clearIncomeDetailList = this.incomeDetailRepository.findContractIncomeDetail(waitPayContract.getContractId(), currentMonth);
                        if (!CollectionUtils.isEmpty(clearIncomeDetailList)) {
                            for (FinanceIncomeDetailEntity clearDetail : clearIncomeDetailList) {
                                Integer clearBillItemId = clearDetail.getBillingItemId();
                                if (Objects.nonNull(clearBillItemId) && clearBillItemId > 0) {
                                    itemAmountMap.putIfAbsent(clearBillItemId, BigDecimal.ZERO);
                                    itemConsumedMap.putIfAbsent(clearBillItemId, BigDecimal.ZERO);
                                    BillingItemAttributeDTO bia = new BillingItemAttributeDTO();
                                    bia.setConsumeUnit(clearDetail.getConsumeUnit());
                                    bia.setUnivalent(clearDetail.getUnivalent());
                                    bia.setUnivalentUnit(clearDetail.getUnivalentUnit());
                                    billingItemAttributeMap.putIfAbsent(clearBillItemId, bia);
                                }
                            }
                        }
                    } else {
                        this.setCurrentAmountFinanceIncomeForUpay(waitPayContract, currentMonth, itemAmountMap, itemConsumedMap, billingItemAttributeMap);
                    }
                } catch (Exception e) {
                    log.error(String.format("statisticsSpecialStage error. current customerId %s.msg %s", customerId, e.getMessage()), e);
                    dingWarnRobot.sendMsgToAtPeople("同步待支付账单收入", Lists.newArrayList(String.format("当前用户%s，错误信息%s", customerId, e.getMessage())), "");
                }
            }
        }
    }


    @Override
    public CommonResult statisticsMicDuration(Date specifyDate) throws ClearingSystemException {
        if (Objects.isNull(specifyDate)) {
            specifyDate = new Date();
        }
        Date currentDate = DateUtil.getFirstDayOfPreviousMonth(specifyDate);
        // 获取上一个月作为计算当月
        String currentMonth = DateFormatUtil.formatDateMonth(currentDate);
        // 获取当月最后一天
        Date endDate = DateUtil.getCurrentMonthEnd(currentDate);
        // 单独统计连麦分钟数计费
        calcBillingDailyConsumedIncome(ContractOperationCodeEnum.LIVE_MIC_DURATION.getCode(), endDate, currentMonth);
        // 单独统计集团账号连麦分钟数计费
        calcBillingDailyConsumedIncome(ContractOperationCodeEnum.LIVE_MIC_DURATION_V1.getCode(), endDate, currentMonth);
        return CommonResult.ok();
    }

    @Override
    public void resettingIncomePrecision(String contractId, String incomeMonth) {
        if (StringUtils.isEmpty(contractId) || StringUtils.isEmpty(incomeMonth)) {
            return;
        }
        // 收入明细
        List<FinanceIncomeDetailEntity> incomeDetailList = this.incomeDetailRepository.findContractIncomeDetail(contractId, incomeMonth);
        if (CollectionUtils.isEmpty(incomeDetailList)) {
            return;
        }
        BigDecimal thousand = new BigDecimal("1000");
        // 特殊收入明细计费项，归入非消耗
        List<Integer> specialBillingItemIdList = Lists.newArrayList(
                FinanceIncomeDetailTagEnum.ASSOCIATION_EXTRA_INCOME_DETAIL_TAG.getCode(),
                FinanceIncomeDetailTagEnum.REFUND_EXTRA_INCOME_DETAIL_TAG.getCode(),
                FinanceIncomeDetailTagEnum.EXPIRED_INCOME_DETAIL_TAG.getCode(),
                FinanceIncomeDetailTagEnum.TAIL_EXTRA_INCOME_DETAIL_TAG.getCode(),
                FinanceIncomeDetailTagEnum.PERIOD_EXPIRED_INCOME_DETAIL_TAG.getCode());
        BillingItem cleanupBi = this.billingItemRepository.findFirstByCode(ItemCodeConst.EXPIRED_CLEANUP.getCode());
        if (Objects.nonNull(cleanupBi)) {
            specialBillingItemIdList.add(cleanupBi.getId());
        }
        BigDecimal incomeAmount = BigDecimal.ZERO;
        // 特殊计费项全部归入非消耗收入
        BigDecimal otherIncomeAmount = BigDecimal.ZERO;
        try {
            for (FinanceIncomeDetailEntity fid : incomeDetailList) {
                Integer billingItemId = fid.getBillingItemId();
                Long currentIncome = fid.getCurrentIncome();
                if (Objects.nonNull(currentIncome) && currentIncome != 0) {
                    // 除1000再乘1000
                    BigDecimal after = new BigDecimal(currentIncome).divide(thousand, 0, RoundingMode.HALF_EVEN).multiply(thousand);
                    fid.setCurrentIncome(after.longValue());
                    if (Objects.nonNull(billingItemId) && specialBillingItemIdList.contains(billingItemId)) {
                        otherIncomeAmount = otherIncomeAmount.add(after);
                    } else {
                        incomeAmount = incomeAmount.add(after);
                    }
                }
            }
            // 查询收入主表
            FinanceIncomeEntity income = this.incomeRepository.findByContractIdAndIncomeMonth(contractId, incomeMonth);
            income.setUsageIncome(incomeAmount.longValue());
            income.setOtherIncome(otherIncomeAmount.longValue());
            income.setTotalIncome(incomeAmount.add(otherIncomeAmount).longValue());
            this.incomeRepository.save(income);
            // 保存收入明细
            this.incomeDetailRepository.saveByBatch(incomeDetailList);
        } catch (Exception e) {
            log.error("resettingIncomePrecision occurred an exception." + e.getMessage(), e);
        }
    }
}
