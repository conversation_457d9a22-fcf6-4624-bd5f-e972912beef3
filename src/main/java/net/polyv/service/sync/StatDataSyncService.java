package net.polyv.service.sync;

import net.polyv.common.CommonResult;
import net.polyv.web.model.stat.StatQueryRequest;

/**
 * 统计数据同步服务
 * <AUTHOR>
 * @date 2022/8/24 18:08
 */
public interface StatDataSyncService {
    
    /**
     * 初始化同步数据
     * @param request 统计请求参数
     * @return {@link CommonResult}
     * <AUTHOR>
     * @date 2022/8/25
     */
    CommonResult initDataSync(StatQueryRequest request);
    
    /**
     * 增量数据同步
     * @param request 请求参数
     * @return {@link CommonResult}
     * <AUTHOR>
     * @date 2022/8/25
     */
    CommonResult incrementalDataSync(StatQueryRequest request);
    
    
}
