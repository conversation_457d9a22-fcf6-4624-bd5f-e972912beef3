package net.polyv.service.sync;

import java.util.Date;

import net.polyv.common.CommonResult;

/**
 * 同步计费项与表格配置服务
 * <AUTHOR>
 * @date 2022/7/27 14:07
 */
public interface SyncItmeDataService {
    
    /**
     * 同步数据表
     * @param statTime 统计时间
     * @param tableName 临时表格
     * @return 操作成功
     * <AUTHOR>
     * @date 2022/7/27
     */
    CommonResult syncData(Date statTime, String tableName);
    
    /**
     * 预处理表名
     * @param tableName
     * @return
     */
    String preHandleTableName(String tableName);
    
    /**
     * 预处理数据（比如把明细表的数据同步到汇总表）
     * @param statTime
     * @param tableName
     * @return
     */
    void preHandleData(Date statTime, String tableName);
}
