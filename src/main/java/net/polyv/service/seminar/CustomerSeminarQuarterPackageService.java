package net.polyv.service.seminar;

import net.polyv.common.CommonResult;
import net.polyv.web.model.seminar.data.AddSeminarQuarterResult;
import net.polyv.web.model.seminar.data.SeminarQuarterAvailableStatusResult;
import net.polyv.web.model.seminar.input.AddSeminarQuarterRequest;
import net.polyv.web.model.seminar.input.SeminarQuarterAvailableStatusRequest;
import net.polyv.web.model.seminar.input.SeminarQuarterPackageDisableRequest;

import java.util.Date;
import java.util.List;

/**
 * @description: 用户研讨会季度包服务
 * @author: Neo
 * @date: 2022-05-12
 */
public interface CustomerSeminarQuarterPackageService {

    /**
     * 用户充值研讨会季度包
     */
    CommonResult<List<AddSeminarQuarterResult>> addSeminarQuarterPackage(List<AddSeminarQuarterRequest> addRequestList);

    /**
     * 查询用户研讨会季度包可用状态（批量）
     */
    CommonResult<List<SeminarQuarterAvailableStatusResult>> querySeminarQuarterPackageAvailableStatus(List<SeminarQuarterAvailableStatusRequest> requestList);

    /**
     * 查询用户研讨会季度包可用状态
     */
    CommonResult<SeminarQuarterAvailableStatusResult> getSeminarQuarterPackageAvailableStatus(SeminarQuarterAvailableStatusRequest request);

    /**
     * 停用用户研讨会季度包
     */
    CommonResult disableSeminarQuarterPackage(SeminarQuarterPackageDisableRequest request);

    /**
     * 获取当前研讨会季度包可用用户集合
     */
    CommonResult<List<String>> getSeminarQuarterPackageAvailableCustomerIdList(Date specifyDate);

    /**
     * 获取昨天研讨会季度包可用用户集合
     */
    CommonResult<List<String>> getNeed2ClearingSeminarPackageCustomerIdList();
}
