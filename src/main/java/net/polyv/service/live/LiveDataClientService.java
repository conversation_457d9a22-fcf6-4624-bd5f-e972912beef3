package net.polyv.service.live;

import net.polyv.rest.model.dmp.Paginator;
import net.polyv.rest.model.dmp.input.CustomerPdConsumePageInput;
import net.polyv.rest.model.dmp.input.SmallClassAndSeminarDTO;
import net.polyv.rest.model.dmp.input.SmallClassAndSeminarRecordDTO;
import net.polyv.rest.model.dmp.input.VodSearchDTO;
import net.polyv.rest.model.dmp.vod.VodSpaceDTO;
import net.polyv.rest.model.dmp.vod.VodTrafficDTO;
import net.polyv.rest.model.live.LiveConcurrenceVO;
import net.polyv.rest.model.live.LiveGuideDurationVO;
import net.polyv.rest.model.live.LiveMicDurationVO;
import net.polyv.rest.model.live.SeminarVO;
import net.polyv.rest.model.live.SmallClassAndSeminarRecordVO;
import net.polyv.rest.model.live.SmallClassVO;

/**
 * 直播数据查询接口
 * <AUTHOR> href="mailto:liu<PERSON>ant<PERSON>@polyv.net">liujiant<PERSON></a>
 * created by 2021/8/6
 */
public interface LiveDataClientService {



    /**
     * 研讨会时长列表
     * @param searchDTO 检索入参
     * @return 研讨会时长列表
     */
    Paginator<SeminarVO> listUserSeminarDuration(SmallClassAndSeminarDTO searchDTO);
    
    
    Paginator<LiveConcurrenceVO> listLiveConcurrence(CustomerPdConsumePageInput input);
    
    Paginator<LiveMicDurationVO> listLiveMicDuration(CustomerPdConsumePageInput input);
    
    Paginator<LiveGuideDurationVO> listLiveGuideDuration(CustomerPdConsumePageInput input);
}
