package net.polyv.service;

import net.polyv.constant.GlobalConfigConst;
import net.polyv.exception.config.ConfigKeyNotFoundException;

/**
 * 全局配置服务
 * <AUTHOR>
 * @since 07/05/2020
 */
public interface GlobalConfigService {

    /**
     * 通过key查找配置
     * @param key key 具体的参见{@link GlobalConfigConst}
     * @param clazz value的类型
     * @param <T> 想要的value的类型
     * @return value
     * @throws ConfigKeyNotFoundException 配置key找不到
     * @throws ClassCastException 类型转换失败
     */
    <T> T getByKey(String key, Class<T> clazz) throws ConfigKeyNotFoundException, ClassCastException;
}
