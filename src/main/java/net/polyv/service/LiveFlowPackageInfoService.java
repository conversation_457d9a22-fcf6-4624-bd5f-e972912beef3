package net.polyv.service;

/**
 * 直播流量套餐service
 * <AUTHOR>
 * @since 2023/05/10
 */
public interface LiveFlowPackageInfoService {
    /**
     * 修改是否直播流量计费
     * @param customerId
     * @param isLiveFlowBilling
     */
    void updateIsLiveFlowBilling(String customerId, int isLiveFlowBilling);
    
    /**
     * 清除点播流量套餐过期资源
     */
    void clearLiveFlowPackageExpiredResource();
    
    /**
     * 是否直播流量计费
     * @param customerId
     * @return
     */
    Boolean isLiveFlowBilling(String customerId);
    
}
