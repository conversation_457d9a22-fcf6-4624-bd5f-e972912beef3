package net.polyv.service.marketing;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;

/**
 * @description: 市场营销线索对接服务接口
 * @author: Neo
 * @date: 2022-04-29
 */
public interface MarketingClueService {

    /**
     * 同步青鸟线索到纷享销客
     * <p>
     * 青鸟返回状态对照参考：https://open.oceanengine.com/labels/7/docs/1696710760866831
     */
    CommonResult syncQingniaoClue2Fxk() throws ClearingSystemException;

    /**
     * 同步腾讯线索到纷享销客
     */
    CommonResult syncTencentClue2Fxk() throws ClearingSystemException;


    /**
     * 定时执行获取青鸟授权信息
     * <p>
     * 定时任务，由于access_token仅一天有效期，故而至少一天刷新一次，才可保证token有效访问。若担心临界时间点，可设置一天刷新两次。
     * 获取到的access_token与refresh_token均需要进行保存，刷新时取最近的access_token进行刷新操作，使用后即失效。
     * refresh_token有效期30天，过期后需重新授权（使用后会生成新的refresh_token，新的token会重置有效期，依然有30天有效期），若正常刷新不会出现refresh_token过期的情况。
     */
    CommonResult refreshQingniaoTokenSchedule() throws ClearingSystemException;
}
