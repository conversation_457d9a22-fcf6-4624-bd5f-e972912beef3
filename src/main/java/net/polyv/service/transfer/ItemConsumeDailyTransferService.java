package net.polyv.service.transfer;

import java.util.Date;

import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.web.model.consume.input.SyncDmpToPcsVO;

/**
 * 资源消耗量传输服务
 * <AUTHOR>
 * @since 2020/6/18
 */
public interface ItemConsumeDailyTransferService {
    
    /**
     * 从dmp同步并发数据
     */
    void dmpConcurrenceSyncToClearing(SyncDmpToPcsVO vo);
    
    /**
     * 从dmp同步连麦分钟数数据
     */
    void dmpMicDurationSyncToClearing(SyncDmpToPcsVO vo);
    
    /**
     * 从dmp同步导播台分钟数数据
     */
    void dmpGuideDurationSyncToClearing(SyncDmpToPcsVO vo);
    
    /**
     * 同步课件重制时长数据
     */
    void pptCompositeSyncToClearing(SyncDmpToPcsVO vo);

    /**
     * 同步 点播流量
     */
    void dmpTrafficSyncToClearing(SyncDmpToPcsVO vo);

    /**
     * 同步 点播存储空间
     */
    void dmpSpaceSyncToClearing(SyncDmpToPcsVO vo);
    
    
    /**
     * 同步研讨会分钟数
     */
    void dmpSeminarSyncToClearing(SyncDmpToPcsVO vo);


}
