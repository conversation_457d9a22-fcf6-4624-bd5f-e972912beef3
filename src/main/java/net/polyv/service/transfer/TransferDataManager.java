package net.polyv.service.transfer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.PcsConstant;
import net.polyv.constant.SyncTaskStatusEnum;
import net.polyv.dao.primary.SyncFinanceDataRecordTaskRepository;
import net.polyv.model.dto.TransferDataDTO;
import net.polyv.model.entity.primary.SyncFinanceDataRecordTask;
import net.polyv.util.DingWarnRobot;

/**
 * 转移数据调度管理者
 * <AUTHOR>
 * @date 2022/9/20 14:23
 */
@Component
@Slf4j
public class TransferDataManager {
    @Resource
    private SyncFinanceDataRecordTaskRepository syncFinanceDataRecordTaskRepository;
    @Autowired
    private List<TransferDateService> transferDateServiceList;
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private ThreadPoolExecutor threadPoolExecutor;
    
    
    public void invoker(TransferDataDTO dto) {
        invoker(dto, null);
    }
    
    public void invoker(TransferDataDTO dto, Long primaryKey) {
        log.info("转移数据调度管理者 调用参数：{},id:{}", JsonUtil.beanToString(dto).orElse(""), primaryKey);
        if (StringUtils.isEmpty(dto.getTaskId())) {
            return;
        }
        // StopWatch stop = new StopWatch("转移数据任务");
        Optional.ofNullable(this.transferDateServiceList.stream()).ifPresent(v -> {
            Optional<TransferDateService> first = v.filter(obj -> obj.isAbleInvoke(dto.getTaskId())).findFirst();
            //    stop.start("执行" + dto.getTaskId());
            first.ifPresent(service -> {
                try {
                    service.transferData(dto);
                    if (Objects.nonNull(primaryKey)){
                        this.syncFinanceDataRecordTaskRepository.updateStatusById(Lists.newArrayList(primaryKey),
                                SyncTaskStatusEnum.SUCCESS.getStatus());
                    }
                } catch (Exception e) {
                    tryException(dto, primaryKey, e);
                }
            });
        });
    }
    
    public String transferAll() {
        List<SyncFinanceDataRecordTask> waitTaskList =
                this.syncFinanceDataRecordTaskRepository.findByTaskStatusEqualsOrderByTaskOrderAsc(
                SyncTaskStatusEnum.WAIT.getStatus());
        if (CollectionUtils.isEmpty(waitTaskList)) {
            return PcsConstant.SUCCESS;
        }
        List<Long> ids = waitTaskList.stream().map(SyncFinanceDataRecordTask::getId).collect(Collectors.toList());
        //异步线程池处理
        this.syncFinanceDataRecordTaskRepository.updateStatusById(ids, SyncTaskStatusEnum.RUNNING.getStatus());
        //   waitTaskList.forEach(v -> this.threadPoolExecutor.submit(() -> this.invoker(v.getJsonBean(), v.getId())));
        waitTaskList.forEach(v -> this.invoker(v.getJsonBean(), v.getId()));
        return PcsConstant.SUCCESS;
    }
    
    public String againTryInvoker(Long id) {
        Optional<SyncFinanceDataRecordTask> optional = this.syncFinanceDataRecordTaskRepository.findById(id);
        optional.ifPresent(syncFinanceDataRecordTask -> this.invoker(syncFinanceDataRecordTask.getJsonBean(), id));
        return PcsConstant.SUCCESS;
    }
    
    
    private void tryException(TransferDataDTO dto, Long primaryKey, Throwable e) {
        dto.setErrorMsg(e.getMessage());
        String s = JsonUtil.beanToString(dto).orElse("");
        log.error("当前调度参数内容：{}", s);
        log.error("转移数据异常：", e);
        //将异常时间点的参数回填至数据库
        SyncFinanceDataRecordTask syncFinanceDataRecordTask = new SyncFinanceDataRecordTask(dto.getTaskId(), s,
                SyncTaskStatusEnum.FAIL.getStatus(), new Date());
        syncFinanceDataRecordTask.setId(primaryKey);
        this.syncFinanceDataRecordTaskRepository.save(syncFinanceDataRecordTask);
        List<String> msg = new ArrayList<>();
        msg.add("调用参数：" + s);
        dingWarnRobot.sendMsgToAtPeople("【转移数据异常】", msg, "");
    }
    
}
