package net.polyv.service.transfer;

import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.GrayTestService;
import net.polyv.web.model.transfer.BaseTransferRequest;

/**
 * 传输服务
 * <AUTHOR>
 * @date 2022/9/2 16:36
 */
@Slf4j
public abstract class AbstractTransferDateService implements TransferDateService {
     
     @Resource
     private GrayTestService grayTestService;
     
     protected boolean pageResult(BaseTransferRequest request) {
          return false;
     }
     
     /**
      * 调度
      */
     protected  void scheduling(BaseTransferRequest request)  throws Exception {
          request.setPageSize(200);
          boolean hasNext = pageResult(request);
          //获取下一次执行 分页数据操作
          while (hasNext) {
               request.setPage(request.getPage() + 1);
               hasNext = pageResult(request);
          }
     }
     
//     /**
//      * 获取灰度用户
//      * @return
//      */
//     @Deprecated
//     public List<String> getGrayUser(){
//          return  this.grayTestService.getUserIdList();
//     }
}
