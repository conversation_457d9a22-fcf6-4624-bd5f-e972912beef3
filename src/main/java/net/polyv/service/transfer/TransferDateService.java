package net.polyv.service.transfer;

import net.polyv.common.CommonResult;
import net.polyv.model.dto.TransferDataDTO;

/**
 * 财务合同传输服务
 * <AUTHOR>
 * @date 2022/9/2 16:36
 */
public interface TransferDateService {
     /**
      * 获取任务名称
      * @return {@link String}
      * <AUTHOR>
      * @date 2022/9/20
      */
     String taskName();
     
     /**
      * 是否可以调用
      * @return true 可以调用
      * @param  taskId 可以调用
      * <AUTHOR>
      * @date 2022/9/20
      */
     boolean isAbleInvoke(String taskId);
     
     /**
      * 执行转移数据
      * @param req 请求参数
      * @return  转移数据结果
      * <AUTHOR>
      * @date 2022/9/20
      */
     CommonResult<TransferDataDTO> transferData(TransferDataDTO req) throws Exception;
}
