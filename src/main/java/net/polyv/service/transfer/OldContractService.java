package net.polyv.service.transfer;

import net.polyv.model.entity.oldFinance.BusinessOperation;
import net.polyv.model.entity.oldFinance.Contract;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;

/**
 * 旧合同服务
 * <AUTHOR>
 * @date 2022/10/27 14:10
 */
public interface OldContractService extends TransferContractProcessorService {
    
    /**
     * 获取合同创建方式
     * @param operation 规格配置
     * @param oldContract 旧合同信息
     * @return 创建方式
     */
    String getContractWay(BusinessOperation operation, Contract oldContract);
    
    /**
     * 解释：方法内部实现了 对月结、预开通会生成规格配置信息
     * 组装合同基本信息
     * 需要将其规格配置和客户信息 进行保存生成数据
     * @param operation 规格配置信息
     * @param oldContract 合同数据
     * @param contract 新合同数据
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2022/10/28
     */
    boolean fillPackageParams(BusinessOperation operation, Contract oldContract, FinanceContractEntity contract);
    
}
