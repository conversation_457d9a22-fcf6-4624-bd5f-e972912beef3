package net.polyv.service.transfer;

import net.polyv.web.model.consume.input.SyncDmpToPcsVO;

import java.util.Date;

/**
 * 资源消耗量传输服务
 * <AUTHOR>
 * @since 2020/10/14
 */
public interface ItemConsumeDailyTransferServiceV2 {
    
    /**
     * 从dmp同步国内分钟数数据
     */
    void dmpChinaDurationSyncToPCS(SyncDmpToPcsVO vo);

    /**
     * 从dmp同步海外分钟数数据

     */
    void dmpOverseasDurationSyncToPCS(SyncDmpToPcsVO vo);
    
    /**
     * 从dmp同步PRTC 国内分钟数数据
     */
    void dmpPRTCChinaDurationSyncToPCS(SyncDmpToPcsVO vo);
    
    /**
     * 从dmp同步PRTC 海外分钟数数据
     */
    void dmpPRTCOverseasDurationSyncToPCS(SyncDmpToPcsVO vo);

    /**
     * 同步dmp集团账号的国内分钟数数据
     * @param vo
     */
    void dmpGroupChinaDurationSyncToPCS(SyncDmpToPcsVO vo);

    /**
     * 同步dmp集团账号的海外分钟数数据
     * @param vo
     */
    void dmpGroupOverseasDurationSyncToPCS(SyncDmpToPcsVO vo);

    /**
     * 同步dmp集团账号的导播台分钟数数据
     * @param vo
     */
    void dmpGuideDurationSyncToPCS(SyncDmpToPcsVO vo);

    /**
     * 同步dmp集团账号的并发数据
     * @param vo
     */
    void dmpGroupConcurrenceSyncToPCS(SyncDmpToPcsVO vo);


    /**
     * 同步dmp集团账号的普通连麦分钟数数据
     * @param vo
     */
    void dmpGroupMicDurationSyncToClearing(SyncDmpToPcsVO vo);

    /**
     * 从dmp同步PRTC 国内分钟数数据
     * @param vo
     */
    void dmpGroupPRTCChinaDurationSyncToPCS(SyncDmpToPcsVO vo);

    /**
     * 从dmp同步PRTC 海外分钟数数据
     * @param vo
     */
    void dmpGroupPRTCOverseasDurationSyncToPCS(SyncDmpToPcsVO vo);

    /**
     * 从dmp同步点播流量数据
     * @param vo
     */
    void dmpGroupTrafficSyncToClearing(SyncDmpToPcsVO vo);

    /**
     * 从dmp同步点播空间用量数据
     * @param vo
     */
    void dmpGroupSpaceSyncToClearing(SyncDmpToPcsVO vo);

    /**
     * 从dmp同步直播研讨会分钟数用量数据
     * @param vo
     */
    void dmpGroupSeminarDurationSyncToClearing(SyncDmpToPcsVO vo);
    
    
}
