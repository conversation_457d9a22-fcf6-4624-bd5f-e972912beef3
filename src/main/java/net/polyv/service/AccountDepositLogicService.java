package net.polyv.service;

import net.polyv.model.data.deposit.DepositPeriodDO;
import net.polyv.rest.model.finance.AddContractResultVO;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.account.DepositInputVO;

/**
 * 顾客账户余额相关
 * <AUTHOR>
 * @since 07/05/2020
 */
public interface AccountDepositLogicService {
    
    /**
     * 保存充值记录到数据库
     * @param inputVO 输入参数
     */
    void doDeposit(DepositInputVO inputVO);

    /**
     * 保存资源点充值记录到数据库
     * @param inputVO 输入参数
     */
    void doDepositResourcePoint(DepositInputVO inputVO);
    
    /**
     * 计算当前顾客的储值周期开始时间
     * @param inputVO 储值入参
     * @return 当前顾客的储值周期开始时间
     */
    DepositPeriodDO calcCurrentPeriod(DepositInputVO inputVO);
    
    /**
     * 添加合同
     */
    AddContractResultVO doAddContract(DepositInputVO inputVO);

    /**
     * 添加合同
     */
    AddContractResultVO doAddContractResourcePoint(DepositInputVO inputVO);
    
    /**
     * 判断顾客有无充值过，赠送过金额或者当前授信额度是否>0
     * @param customerId 顾客信息
     * @return 返回判断结果
     */
    boolean hasDepositOrCredit(String customerId);

    /**
     * 是否开启资源点开关
     * @param customerId 顾客信息
     * @return 返回判断结果
     */
    boolean hasResourcePoint(String customerId);
    
    /**
     * 计算顾客的可用授信额度
     * @param inputVO 顾客信息
     * @return 顾客的可用授信额度
     */
    long calcCustomerValidCredit(CustomerInfoGetInputVO inputVO);
}
