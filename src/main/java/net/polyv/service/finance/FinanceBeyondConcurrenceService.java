package net.polyv.service.finance;

import net.polyv.model.entity.primary.stat.StatBeyondConcurrent;
import net.polyv.modules.common.vo.ResponseVO;

/**
 * 超并发service
 * <AUTHOR>
 * @since 2023/02/06
 */
public interface FinanceBeyondConcurrenceService {
    /**
     * 同步历史超并发数据
     */
    void syncHistoryBeyondConcurrence();
    
    /**
     * 定时生成超并发记录
     */
    void scheduleCreateBeyondConcurrence();
    
    /**
     * 创建超并发应收记录
     * @param statBeyondConcurrent
     */
    void createBeyondConcurrenceReceivable(StatBeyondConcurrent statBeyondConcurrent);
    
    /**
     * 创建超并发应收记录
     * @param id
     */
    ResponseVO<String> createBeyondConcurrenceReceivableById(long id);
    
    /**
     * 更新超并发记录应收id
     * @param beyondConcurrenceId
     * @param receivableId
     */
    void updateBeyondConcurrenceReceivableId(Long beyondConcurrenceId, String receivableId);
    
    ResponseVO<String> syncCreateAllBeyondConcurrenceReceivable();
    
    
}
