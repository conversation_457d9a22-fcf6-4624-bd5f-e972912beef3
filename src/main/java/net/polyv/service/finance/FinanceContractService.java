package net.polyv.service.finance;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.modules.pcs.api.vo.finance.FinanceContractInfoVO;
import net.polyv.rest.model.finance.AddContractResultVO;
import net.polyv.rest.model.finance.PreOpenAndMonthlyContractSpecResultVO;
import net.polyv.web.model.finance.input.AddAmountContractDTO;
import net.polyv.web.model.finance.input.AddOpenContractDTO;
import net.polyv.web.model.finance.input.AddPreOpenContractDTO;
import net.polyv.web.model.finance.input.DealThawBillsDTO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 财务合同服务
 * @author: Neo
 * @date: 2022-07-28
 */
public interface FinanceContractService {

    /**
     * 获取用户待支付合同金额（一个用户一条）
     * notice:该方法返回ok也存在数据为null情况
     */
    CommonResult<FinanceContractEntity> getWaitPayContractByUnionId(String unionId);


    /**
     * 预开通、月结创建合同
     * <p>
     * 1.页面调用接口，执行保存规格配置信息（business_package_specification）
     * 2.将保存的business_package_specification主键id、unionId、accountType组装apoc创建合同
     * <p>
     * 返回：创建的合同ID
     */
    CommonResult<String> addOriginContract(AddPreOpenContractDTO apoc) throws ClearingSystemException;

    /**
     * 添加业务开通流程合同
     */
    CommonResult<String> addOpenBusinessContract(AddOpenContractDTO aoc);

    /**
     * 添加金额充值合同
     */
    CommonResult<AddContractResultVO> addAmountContract(AddAmountContractDTO aac);

    CommonResult<AddContractResultVO> addTestAmountContract(AddAmountContractDTO aac);

    /**
     * 添加金额充值合同
     */
    CommonResult<AddContractResultVO> addAmountContractResourcePoint(AddAmountContractDTO aac);

    /**
     * 处理偿还待支付账单
     */
    CommonResult<String> dealThawBills(DealThawBillsDTO dtb) throws ClearingSystemException;

    /**
     * 创建用户待支付合同
     */
    CommonResult<FinanceContractEntity> addWaitPayContract(String unionId, BigDecimal contractAmount, Date createTime);

    /**
     * 获取合同金额
     */
    BigDecimal getRealContractAmount(FinanceContractEntity contract, Date current, BigDecimal taxRate);

    /**
     * 根据开始时间和结束时间计算使用周期数
     */
    Integer calcYearlyPackagePeriod(Date startTime, Date endTime, Integer period);

    /**
     * 获取合同规格配置信息
     * 无论是否关联，只要是预开通、月结，都取合同规格
     */
    CommonResult<BusinessPackageSpecification> getContractSpec(FinanceContractEntity contract, Date specifyDate);

    /**
     * 获取合同业务规格编码
     */
    CommonResult<String> getContractBillingPlanCode(FinanceContractEntity contract);

    /**
     * 获取计算充值量：部分合同计算用GB
     * 因为充值量和计算充值量单位存在GB和byte的转换
     */
    BigDecimal getCalcContractQuantity(String contractId, String operationCode, Long quantity);

    /**
     * 获取存储充值量：合同存储用byte
     * 因为充值量和计算充值量单位存在GB和byte的转换
     */
    Long getStoreContractQuantity(String contractId,String operationCode, BigDecimal quantity);

    /**
     * 判断合同是否是需要经过单位转换
     * 部分合同充值量存储的时候是使用单位：byte
     * 但计算单价的时候需要使用GB，单位：元/GB
     * 故在计算单价的时候需要获取GB，在确认使用量的时候需要使用byte
     */
    Boolean isGbConvertedContract(FinanceContractEntity fc);

    /**
     * 获取预开通合同信息
     */
    CommonResult<FinanceContractInfoVO> getContractInfo(String contractId);

    /**
     * 更新合同到款状态
     *
     * @param soId          订单ID
     * @param paymentStatus 到款状态：0未到款，1完全到款
     * @return 操作结果
     */
    CommonResult updateContractPaymentStatus(String soId, Short paymentStatus);

    /**
     * 根据合同ID获取预开通、月结的规格配置信息
     *
     * @param contractIdList 合同ID
     * @return 规格信息实体
     * @throws ClearingSystemException 日期转换异常
     */
    CommonResult<List<PreOpenAndMonthlyContractSpecResultVO>> getPreOpenOrMonthlyContractSpec(List<String> contractIdList) throws ClearingSystemException;


    /**
     * 保存合同操作
     */
    void saveContract(FinanceContractEntity contractEntity);

    /**
     * 触发合同修改时间
     */
    void triggerContractModifyEvent(String contractId);


    /**
     * 获取合同指定月份指定计费项的权重税率
     * 权重为计费项消耗金额/总消耗金额
     * 即每个不同税率的计费项的消耗总量占用该合同当前消耗总量的比例计算加权税率
     *
     * @param contractId    合同ID
     * @param specifyMonth  指定月份
     * @param billingItemId 指定计费项 @todo 计费项维度未实现
     * @return 权重税率
     */
    BigDecimal getContractWeightedTaxRate(String contractId, String specifyMonth, Integer billingItemId);


    /**
     * 更新合同责任销售信息信息
     *
     * @param contractId   合同ID
     * @param saleUserId   责任销售ID
     * @param saleUserName 责任销售名称
     * @return 操作结果
     */
    CommonResult updateContractSaleUser(String contractId, String saleUserId, String saleUserName);

    /**
     * 删除合同
     */
    CommonResult deleteMonthlySettleContract(String contracntId);

}
