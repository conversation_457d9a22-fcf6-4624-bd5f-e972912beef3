package net.polyv.service.finance;

import net.polyv.exception.ClearingSystemException;
import net.polyv.web.model.finance.input.AddContractOperationLogDTO;

/**
 * @description: 合同操作日志服务
 * @author: Neo
 * @date: 2022-09-20
 */
public interface FinanceContractOperationLogService {

    /**
     * 添加合同操作日志
     * 合同生命流程：
     * 1.创建：业务开通，预开通，月结
     * 2.变更：关联、退费
     *
     * @param addDTO 添加日志实体
     */
    void addContractLog(AddContractOperationLogDTO addDTO) throws ClearingSystemException;

    /**
     * 添加合同操作日志-异步
     */
    void addContractLogAsync(AddContractOperationLogDTO addDTO);
}
