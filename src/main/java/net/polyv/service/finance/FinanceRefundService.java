package net.polyv.service.finance;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.modules.pcs.api.req.finance.AddRefundDTO;
import net.polyv.modules.pcs.api.vo.finance.ContractRefundApplyVO;
import net.polyv.modules.pcs.api.vo.finance.ContractRefundVO;

import java.util.List;

/**
 * @description: 财务退款服务
 * @author: Neo
 * @date: 2022-08-30
 */
public interface FinanceRefundService {

    /**
     *  发起合同退款框信息查询
     */
    CommonResult<ContractRefundApplyVO> applyRefund(String contractId);

    /**
     * 保存合同退款
     */
    CommonResult addRefund(AddRefundDTO ar) throws ClearingSystemException;

    /**
     * 展示合同退款记录
     */
    CommonResult<List<ContractRefundVO>> displayRefundRecords(String contractId);
}
