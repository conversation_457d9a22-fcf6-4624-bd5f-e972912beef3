package net.polyv.service.finance;

import net.polyv.common.CommonResult;
import net.polyv.model.entity.primary.definition.ContractEnumDefinition;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.rest.model.finance.FinanceContractAssembleRequest;

/**
 * @description: 历史合同转换服务
 * @author: Neo
 * @date: 2022-09-28
 */
public interface FinanceHistoryContractConvertService {

    /**
     * 合同数据转换
     *  @apiNote  请注意：当assembleContract()，传递一个合同对象时会对其内容填充，如果不传则为一个新的对象
     */
    CommonResult<FinanceContractEntity> assembleContract(FinanceContractAssembleRequest fcar,FinanceContractEntity entity);
    
    /**
    *  获取合同枚举 根据财务数据
    * @param operationName business_operation-operationName
    * @param trafficType business_operation-trafficType
    * @param operationType business_operation-operationType
    * @param vodIsYearly business_operation-vodIsYearly
    * @return 合同枚举
    * <AUTHOR>
    * @date 2022/10/11
    */
     ContractEnumDefinition findContractEnumByOperationName(String operationName, String trafficType,
            String operationType, String vodIsYearly);
}
