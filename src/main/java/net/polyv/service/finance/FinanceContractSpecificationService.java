package net.polyv.service.finance;

import net.polyv.common.CommonResult;
import net.polyv.modules.pcs.api.req.finance.FinanceBusinessOrderOpenDTO;

/**
 * @description: 财务合同规格配置服务接口
 * @author: Neo
 * @date: 2022-08-26
 */
public interface FinanceContractSpecificationService {

    /**
     * 添加财务合同规格配置
     *
     * @param addSpecDTO           添加对象
     * @param orderSpecificationId 普通业务类别将业务套餐信息存在订单的规格配置里，将ID保存过来
     * @return 返回新增的合同规格配置表ID
     */
    CommonResult<Long> addContractSpec(FinanceBusinessOrderOpenDTO addSpecDTO, Long orderSpecificationId);
}
