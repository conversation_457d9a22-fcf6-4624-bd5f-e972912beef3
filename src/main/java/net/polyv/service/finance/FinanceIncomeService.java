package net.polyv.service.finance;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.model.entity.primary.finance.FinanceIncomeDetailEntity;
import net.polyv.model.entity.primary.finance.FinanceIncomeEntity;
import net.polyv.web.model.finance.data.BillingItemAttributeDTO;
import net.polyv.web.model.finance.data.CurrentFinanceIncomeDTO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * @description: 财务收入服务
 * @author: Neo
 * @date: 2022-07-29
 */
public interface FinanceIncomeService {
    
    
    /**
     * 直播流量用量收入确认
     * @param specifyDate
     * @return
     * @throws ClearingSystemException
     */
    CommonResult liveFlowStatistics(Date specifyDate) throws ClearingSystemException;
    
    /**
     * 保存金额合同合同收入
     * 1.各个计费项按照金额和消耗量确认收入
     * 2.金额合同存在退费的情况，收入主表的合同金额需要获取上个月的收入主表减去当月的合同退款金额获取
     * 3.退费扣减递延
     *
     * @param contract                待支付合同
     * @param currentMonth            统计月份
     * @param itemAmountMap           key:itemId value:金额
     * @param itemConsumedMap         key:itemId value:消耗
     * @param billingItemAttributeMap key:itemId value:属性
     */
    void setCurrentAmountFinanceIncome(FinanceContractEntity contract, String currentMonth, Map<Integer, BigDecimal> itemAmountMap,
            Map<Integer, BigDecimal> itemConsumedMap, Map<Integer, BillingItemAttributeDTO> billingItemAttributeMap);
    
    /**
     * 获取当前收入信息
     */
    CurrentFinanceIncomeDTO getCurrentFinanceIncome(FinanceContractEntity contract, Date current);
    
    /**
     * 设置并更新当前收入信息
     *
     * @param contract            合同实体
     * @param cfi                 当前收入实体
     * @param usageIncomeAmount   业务消耗金额（不含税）
     * @param otherIncomeAmount   其他消耗金额（不含税）
     * @param usageIncomeQuantity 业务消耗用量
     * @param otherIncomeQuantity 其他消耗用量
     */
    void setCurrentFinanceIncome(FinanceContractEntity contract, CurrentFinanceIncomeDTO cfi, BigDecimal usageIncomeAmount, BigDecimal otherIncomeAmount,
            BigDecimal usageIncomeQuantity, BigDecimal otherIncomeQuantity);
    
    /**
     * 保存特殊收入为一条独立的收入明细数据
     * 4种情况：
     * 1.合同到期的剩余金额确认收入
     * 2.合同消耗完毕的递延尾差
     * 3.合同退费产生的退收入
     * 4.合同关联产生的收入差额
     *
     * @param contract            合同实体
     * @param cfi                 收入实体
     * @param fi                  当月收入实体，用于更新递延
     * @param usageIncomeAmount   消耗收入金额
     * @param otherIncomeAmount   非消耗收入金额
     * @param usageIncomeQuantity 消耗量
     * @param otherIncomeQuantity 非消耗量
     */
    void addSpecialIncomeDetail(FinanceContractEntity contract, CurrentFinanceIncomeDTO cfi, FinanceIncomeEntity fi,
            BigDecimal usageIncomeAmount, BigDecimal otherIncomeAmount, BigDecimal usageIncomeQuantity, BigDecimal otherIncomeQuantity);
    
    /**
     * 根据合同ID初始化收入信息
     */
    CommonResult initContractIncome(String contractId) throws ClearingSystemException;
    
    /**
     * 保存收入实体
     */
    CommonResult addFinanceIncome(FinanceIncomeEntity entity);
    
    /**
     * 保存收入明细实体
     */
    CommonResult addFinanceIncomeDetail(FinanceIncomeDetailEntity entity);
    
    /**
     * 根据合同ID和月份查询收入
     */
    CommonResult<FinanceIncomeEntity> getByContractIdAndIncomeMonth(String contractId, String incomeMonth);
    
    /**
     * 根据合同ID和月份查询最邻近@incomeMonth的收入数据
     */
    CommonResult<FinanceIncomeEntity> getNearestByContractIdAndIncomeMonth(String contractId, String incomeMonth);
    
    /**
     * 通过合同ID和业务细分获取临近@incomeMonth的收入明细数据
     */
    CommonResult<FinanceIncomeDetailEntity> getNearestDetailByBillingItemId(String contractId, String incomeMonth, Integer billingItemId);
    
    /**
     * 通过合同ID和业务细分查询收入数据
     */
    CommonResult<FinanceIncomeDetailEntity> getIncomeDetailByBillingItemId(String contractId, String incomeMonth, Integer billingItemId);
    
    /**
     * 执行确认收入
     */
    CommonResult statistics(Date specifyDate) throws ClearingSystemException;
    
    
    /**
     * 每月第一天(6点半)执行上个月特殊的合同数据
     */
    void statisticsSpecialStage(Date specifyDate);
    
    
    /**
     * 每月第1到第2天中午12点执行上个月连麦分钟数的合同数据
     */
    CommonResult statisticsMicDuration(Date specifyDate) throws ClearingSystemException;
    
    
    /**
     * 重置收入精度
     * @param contractId 合同ID
     * @param incomeMonth 收入月份
     * @param scale 默认为5
     */
    void resettingIncomePrecision(String contractId, String incomeMonth);
    
    // 处理时间节点之后的直播套餐流量的合同统计
    void confirmLiveFlowIncomeAfterSpecifyDate(Date specifyDate);

    /**
     * 保存金额合同合同收入(待支付的逻辑)
     * 1.各个计费项按照金额和消耗量确认收入
     * 2.金额合同存在退费的情况，收入主表的合同金额需要获取上个月的收入主表减去当月的合同退款金额获取
     * 3.退费扣减递延
     *
     * @param contract                待支付合同
     * @param currentMonth            统计月份
     * @param itemAmountMap           key:itemId value:金额
     * @param itemConsumedMap         key:itemId value:消耗
     * @param billingItemAttributeMap key:itemId value:属性
     */
    void setCurrentAmountFinanceIncomeForUpay(FinanceContractEntity contract, String currentMonth, Map<Integer, BigDecimal> itemAmountMap,
                                       Map<Integer, BigDecimal> itemConsumedMap, Map<Integer, BillingItemAttributeDTO> billingItemAttributeMap);
    
}
