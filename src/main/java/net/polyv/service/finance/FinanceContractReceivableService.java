package net.polyv.service.finance;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.finance.FinanceReceivableEntity;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.finance.ContractReceivableRequest;
import net.polyv.modules.pcs.api.vo.finance.ContractReceivableVO;


import java.util.Date;
import java.util.List;

/**
 * @description: 合同应收服务接口
 * @author: Neo
 * @date: 2023-02-20
 */
public interface FinanceContractReceivableService {

    /**
     * 创建合同生成合同应收
     *
     * @param contractId         合同ID
     * @param contractCreateTime 合同创建时间
     * @return 结果
     */
    CommonResult generateReceivableContractCreated(String contractId, Date contractCreateTime);

    /**
     * 生成合同应收
     *
     * @param specifyMonth 指定日期 yyyy-MM
     * @param contractList 指定合同
     * @return 结果
     */
    CommonResult generateReceivableScheduling(String specifyMonth, List<String> contractList);

    /**
     *  生成待支付合同应收
     * @param specifyMonth 指定日期 yyyy-MM
     * @return 结果
     */
    void generateWaitPayContractReceivableScheduling(String specifyMonth);


    /**
     * 查询合同应收分页数据
     *
     * @param crRequest 查询参数请求
     * @return 分页结果
     */
    CommonResult<Pager<ContractReceivableVO>> pagingContractReceivableData(ContractReceivableRequest crRequest);

    /**
     * 处理合同完成应收月份
     * 完成应收月份条件：合同关联的回款金额 ≥ 当前合同金额 && 合同当月递延为0
     *
     * @param contractId       合同ID
     * @param specifyMonth     指定月份
     * @param receivableEntity 当月应收实体
     * @return 操作结果
     * @throws ClearingSystemException 执行异常
     */
    CommonResult processContractReceivableMonth(String contractId, String specifyMonth, FinanceReceivableEntity receivableEntity) throws ClearingSystemException;

    /**
     * 组装导出数据
     *
     * @param crList 元数据
     * @return 结果
     */
    CommonResult<List<ContractReceivableVO>> assembleReceivableExportList(List<ContractReceivableVO> crList);

}
