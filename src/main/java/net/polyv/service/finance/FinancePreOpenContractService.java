package net.polyv.service.finance;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.modules.pcs.api.req.finance.FinanceBusinessOrderOpenDTO;
import net.polyv.modules.pcs.api.vo.finance.FinanceAssociationDisplayVO;

import java.util.Map;

/**
 * @description: 预开通合同接口
 * @author: Neo
 * @date: 2022-08-26
 */
public interface FinancePreOpenContractService {

    /**
     * 添加预开通、月结合同规格配置内容，创建合同
     * 该方法没有更新操作，只有创建操作（每次执行都生成新数据）
     */
    CommonResult<String> createPreOpenContract(FinanceBusinessOrderOpenDTO addSpecDTO) throws ClearingSystemException;

    /**
     * 展示合同关联订单列表
     *
     * @param contractId 合同ID
     * @return 展示内容
     */
    CommonResult<FinanceAssociationDisplayVO> displayAssociationInfo(String contractId);

    /**
     * 待关联合同关联订单
     */
    CommonResult associationContractCheck(String contractId, String soId, String operatorId);


    /**
     * 更新待关联合同状态
     *
     * @param contractId 合同ID
     * @param soId       订单ID
     * @param operatorId 操作人
     * @return 更新结果
     */
    CommonResult updateAssociatedStatus(String contractId, String soId, String operatorId);

    /**
     * 关联审核结果操作
     *
     * @param auditStatus StatusValueEnum
     */
    CommonResult associationContractOperation(String contractId, String soId, Short auditStatus);

    /**
     * 预开通/月结 直接关联的预显示订单信息
     *
     * @param unionId         unionId
     * @param businessType    业余类别
     * @param billingPlanCode 产品编码
     * @return 结果
     */
    CommonResult<FinanceAssociationDisplayVO> displayDirectAssociateInfo(String unionId, Integer businessType, String billingPlanCode);

    /**
     * 预开通/月结 直接关联的预显示订单信息
     *
     * @param soId         soId
     * @param businessType    业余类别
     * @param billingPlanCode 产品编码
     * @return 结果
     */
    CommonResult<FinanceAssociationDisplayVO> queryAssociateBySoId(String soId, Integer businessType, String billingPlanCode);

    /**
     * 预开通/月结 直接关联的预显示订单信息
     *
     * @param soId         soId
     * @return 结果
     */
    CommonResult<FinanceAssociationDisplayVO> queryAssociateByOnlySoId(String soId);

    CommonResult<FinanceAssociationDisplayVO> queryAssociateBySoIdContractId(String soId, String contractId);


    /**
     * 合同关联后更新纷享销客字段
     *
     * @param soId            订单ID
     * @param updateFieldsMap 字段更新
     * @return 操作结果
     * @description: 业务开始时间：field_R9g40__c、业务结束时间：field_g07y2__c、field_m5BG0__c：业务开通状态、field_1SJCx__c：财务ID
     */
    CommonResult updateFxkOrderStatusAfterAssociated(String soId, Map<String, Object> updateFieldsMap);


    /**
     * 回滚预月结合同创建
     * 月结合同才可以回滚，预开通因为会打套餐，不能回滚，不然会导致查不到相关信息
     *
     * @param contractId 合同ID
     * @return 结果
     */
    CommonResult rollbackMonthlySettleContractCreated(String contractId);

}
