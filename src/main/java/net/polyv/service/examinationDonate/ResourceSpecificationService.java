package net.polyv.service.examinationDonate;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.AddResourceRequest;
import net.polyv.modules.pcs.api.req.AmountCalculateRequest;
import net.polyv.modules.pcs.api.req.PackageSpecificationGetRequest;
import net.polyv.modules.pcs.api.vo.AmountCalculateResultVO;
import net.polyv.modules.pcs.api.vo.PackageSpecificationGetResultVO;
import net.polyv.modules.pcs.api.vo.ResourceAndBusinessTypeGetResultVO;


/**
 * 测试赠送套餐规格service
 * <AUTHOR>
 * @since 2022/06/14
 */
public interface ResourceSpecificationService {
    
    CommonResult<PackageSpecificationGetResultVO> getPackageSpecification(PackageSpecificationGetRequest inputVO);
    
    
    /**
     * 获取资源类型和业务类型下拉
     * @param resourceTypeCode :资源类型code，数据库配置
     * @param accountType：账号类型
     * @return
     */
    CommonResult<ResourceAndBusinessTypeGetResultVO> getResourceAndBusinessTypeList(String resourceTypeCode,
            String accountType);
    
    /**
     * 添加测试或赠送套餐
     * @param inputVO
     * @return
     */
    ResponseVO<Object> addResource(AddResourceRequest inputVO);
    
    /**
     * 额度计算
     * @param inputVO
     * @return
     */
    CommonResult<AmountCalculateResultVO> calculateAmount(AmountCalculateRequest inputVO);
    
    /**
     * 计算配置化消耗数据
     * @param request 请求参数
     * @param inputVO 请求参数
     * @return 计算消耗
     * <AUTHOR>
     * @date 2022/7/29
     */
    CommonResult<AmountCalculateResultVO> calculateBillingConfigItem(AmountCalculateRequest request,AddResourceRequest inputVO);
    
    /**
     * 打计费项配置化测试赠送、资源
     * @param inputVO 请求参数
     * @return CommonResult：ok 添加成功
     * <AUTHOR>
     * @date 2022/7/29
     */
    ResponseVO<Object> billingItemConfigPackage(AddResourceRequest inputVO) throws ClearingSystemException;
     
     
}
