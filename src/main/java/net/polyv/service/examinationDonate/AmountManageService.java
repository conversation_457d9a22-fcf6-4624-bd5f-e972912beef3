package net.polyv.service.examinationDonate;


import net.polyv.common.CommonResult;
import net.polyv.model.entity.primary.examinationDonate.SaleDepartmentBaseAmount;
import net.polyv.modules.pcs.api.req.AmountListRequest;
import net.polyv.modules.pcs.api.req.AmountUseDetailRequest;
import net.polyv.modules.pcs.api.req.ExtraAmountAddRequest;
import net.polyv.modules.pcs.api.vo.AmountListResultVO;
import net.polyv.modules.pcs.api.vo.AmountUseDetailResultVO;
import net.polyv.web.model.common.PageDataVO;
import net.polyv.web.model.examinationDonate.*;

import java.util.List;

/**
 * 测试赠送额度管理service
 * <AUTHOR>
 * @since 2022/06/23
 */
public interface AmountManageService {

    /**
     * 根据销售名字模糊查询
     * @param saleUserName
     * @return
     */
    List<SaleDepartmentBaseAmount> getBySaleUserName(String saleUserName);

    /**
     * 额度管理列表
     * @param inputVO
     * @return
     */
    PageDataVO<AmountListResultVO> pageAmountList(AmountListRequest inputVO);

    /**
     * 额度使用明细列表
     * @param inputVO
     * @return
     */
    PageDataVO<AmountUseDetailResultVO> pageAmountUseDetail(AmountUseDetailRequest inputVO);

    CommonResult addExtraAmount(ExtraAmountAddRequest inputVO);

}
