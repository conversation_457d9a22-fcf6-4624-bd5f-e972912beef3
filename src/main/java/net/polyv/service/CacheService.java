package net.polyv.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.CustomerEffectiveScheduleJob;
import net.polyv.model.entity.primary.business.SystemDictionary;
import net.polyv.model.entity.primary.examinationDonate.SaleDepartmentBaseAmount;
import net.polyv.model.entity.primary.finance.FinanceIncomeDetailEntity;
import net.polyv.modules.pcs.api.req.crm.GetUserLast7DayConsumedRequest;
import net.polyv.modules.pcs.api.vo.crm.GetGrayUserResourceByZeroResponse;
import net.polyv.modules.pcs.api.vo.crm.GetUserLast7DayConsumedResponse;
import net.polyv.rest.model.vod.VodPackageVo;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.cache.CleanLiveResourceCacheInputVO;

public interface CacheService {
    
    /**
     * 清理资源可用量缓存和是否可用缓存
     * @param customerId unionid
     */
    void cleanLiveRestrictPcsResult(String customerId);
    
    /**
     * 清理资源可用量缓存和是否可用缓存
     * @param inputVO 输入参数
     */
    void cleanLiveRestrictPcsResult(CleanLiveResourceCacheInputVO inputVO);
    
    /**
     * 清理结算资源可用量缓存和是否可用缓存
     * @param inputVO 输入参数
     */
    void cleanPcsCache(CleanLiveResourceCacheInputVO inputVO);
    
    /**
     * 清理pcs，直播，点播缓存
     * @param customerId
     * @param code
     */
    void cleanAllCacheByCustomerIdAndCode(String customerId, String code);
    
    /**
     * 清理结算缓存
     * @param customerId
     */
    void cleanPcsCache(String customerId);
    
    /**
     * 清理直播用户缓存
     * @param customerId unionid
     */
    void cleanLiveUserCache(String customerId);
    
    /**
     * 清理点播用户缓存
     * @param customerId unionId
     */
    WrappedResponse cleanVodUserCache(String customerId);
    
    /**
     * 清理直播用户缓存（异步）
     * @param customerId 用户id
     * @return 结果
     * @throws ClearingSystemException 异常
     */
    CommonResult asyncCleanLiveUserCache(String customerId) throws ClearingSystemException;
    
    /**
     * 清理点播用户缓存（异步）
     * @param customerId 用户id
     * @return 结果
     * @throws ClearingSystemException 异常
     */
    CommonResult asyncCleanVodUserCache(String customerId) throws ClearingSystemException;
    
    /**
     * 根据getBySaleUserId获取基础额度信息
     * @param saleUserId
     * @return
     */
    SaleDepartmentBaseAmount getBySaleUserId(String saleUserId);
    
    
    /**
     * 获取字典数据
     * @param dictType 字典类型
     * @param label 字典名称
     * @return {@link SystemDictionary}
     * <AUTHOR>
     * @date 2022/9/16
     */
    SystemDictionary getValueByDictTypeAndLabel(String dictType, String label);
    
    
    /**
     * 从缓存中获取上一个收入明细数据
     * @param contractId 合同id
     * @param itemId 计费项id
     * @return 返回上一个的收入明细数据
     * <AUTHOR>
     * @date 2022/10/19
     */
    FinanceIncomeDetailEntity getCacheBeforeMonthIncomeByContractId(String contractId, Long itemId);
    
    /**
     * 将收入明细数据设置进缓存中
     * @param entity 收入明细数据
     * @return true 设置成功
     * <AUTHOR>
     * @date 2022/10/19
     */
    boolean setCacheBeforeMonthIncomeByContractId(FinanceIncomeDetailEntity entity);
    
    /**
     * 发布消息到redis list，订阅者自行消费
     * @param topic：队列名字
     * @param message:消息内容
     */
    void publishMessage(String topic, String message);
    
    /**
     * 缓存集团分账号
     * @param customerId
     * @return
     */
    void cacheGroupSubUser(String customerId);
    
    /**
     * 是否集团分账号
     * @param customerId
     * @return
     */
    boolean isGroupSubUser(String customerId);
    
    /**
     * 缓存1小时
     * 设置空间为0用户
     */
    void setZeroUsers(List<String> users, String key);
    
    /**
     * 获取空间为0用户
     */
    List<String> getSpaceZeroUsers();
    
    /**
     * 获取流量为0用户
     */
    List<String> getFlowZeroUsers();
    
    /**
     * 获取套餐流量缓存
     */
    String getPackageFlowSize();
    
    void setPackageFlow(List<GetGrayUserResourceByZeroResponse> list);
    
    /**
     * 获取近七天消耗量
     */
    List<GetUserLast7DayConsumedResponse> getLastConsumedBy7(GetUserLast7DayConsumedRequest request);
    
    /**
     * 设置近七天消耗量
     */
    void setLastConsumedBy7(List<GetUserLast7DayConsumedResponse> consumed, String resourceCode);
    
    /**
     * 获取套餐流量缓存
     */
    String getPackageSpaceSize();
    
    /**
     * 设置套餐空间
     */
    void setPackageSpace(List<GetGrayUserResourceByZeroResponse> list);
    
    void setLiveBillingType(String unionId, String billingType);
    
    String getLiveBillingType(String unionId);
    
    /**
     * 获取客户套餐最早待开通时间，放到缓存
     * @param list：待开通套餐列表
     */
    void setPackageEarliestOpenTime(List<CustomerEffectiveScheduleJob> list);
    
    /**
     * 获取客户套餐最早待开通时间
     * @param unionIds
     * @param type
     * @return key：unionId，value：最早开通时间
     */
    Map<String, Date> getPackageEarliestOpenTime(List<String> unionIds, String type);
    
    void setVodSpaceTrafficAvailable(List<VodPackageVo> list);
    
    List<VodPackageVo> getVodSpaceTrafficAvailable(List<String> unionIds);
    
    void setConcurrenceLimit(String liveUserId, Integer limit);
    
    Integer getConcurrenceLimit(String liveUserId);
    
    /**
     * 是否赠送过ai资源
     * @param unionId
     * @return
     */
    
    
    /**
     * 客户是否成交
     * @param unionId
     * @return
     */
    boolean isCustomerDeal(String unionId);
    
    void cleanBillingItemCache(String unionId);
}
