package net.polyv.service.billingconfig;

import java.util.List;

import net.polyv.model.data.bilingConfig.OtherBillConfigItemDO;
import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;

/**
 * <AUTHOR>
 * @date 2022/7/20 15:00
 */

public interface BillingConfigService {
    /**
     * 赠送、测试-客户配置的计费项
     * @param customerConfig 配置信息
     * <AUTHOR>
     * @date 2022/7/26
     */
    void saveConfig(BillingCustomerConfig customerConfig);
    
    /**
     * 获取所有其他计费项
     * @return {@link java.util.List<net.polyv.model.data.bilingConfig.OtherBillConfigItemDO>}
     * <AUTHOR>
     * @date 2022/7/28
     */
    List<OtherBillConfigItemDO> listOpenBillItemExt();
    
    
    /**
     * 获取其他计费项的显示数据 根据销售机会
     * @param soId 销售机会id
     * @return {@link OtherBillConfigItemDO}
     * <AUTHOR>
     * @date 2022/11/2
     */
    List<OtherBillConfigItemDO> getOtherItemDataBySoId(String soId);
}
