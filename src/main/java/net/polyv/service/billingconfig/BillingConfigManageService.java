package net.polyv.service.billingconfig;

import java.util.Date;
import java.util.List;

import net.polyv.common.CommonResult;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.billingconfig.GetBillingItemRequest;
import net.polyv.modules.pcs.api.req.billingconfig.PageBillingItemRequest;
import net.polyv.modules.pcs.api.req.billingconfig.SaveBillingItemRequest;
import net.polyv.modules.pcs.api.req.billingconfig.UpdateBillingItemRequest;
import net.polyv.modules.pcs.api.vo.billingconfig.BillingConfigVO;
import net.polyv.modules.pcs.api.vo.billingconfig.BillingItemBaseVO;
import net.polyv.modules.pcs.api.vo.billingconfig.BillingItemInfoVO;
import net.polyv.modules.pcs.api.vo.billingconfig.BillingItemPageVO;
import net.polyv.web.model.user.OtherBillingItemVO;

/**
 * 计费项配置化 管理服务
 * <AUTHOR>
 * @date 2022/7/20 15:00
 */
public interface BillingConfigManageService {
    /**
     * 修改计费项
     * @param request 保存参数
     * @return {@link CommonResult}
     * <AUTHOR>
     * @date 2022/7/20
     */
    CommonResult update(UpdateBillingItemRequest request);
    /**
     * 新增计费项
     * @param request 保存参数
     * @return {@link CommonResult}
     * <AUTHOR>
     * @date 2022/7/20
     */
    CommonResult save(SaveBillingItemRequest request);
    
    /**
     * 根据计费项id获取计费项信息
     * @param request 查询参数
     * @return {@link BillingItemInfoVO}
     * <AUTHOR>
     * @date 2022/7/20
     */
    BillingItemInfoVO getByBillItemId(GetBillingItemRequest request);
    
    /**
     * 根据条件查询计费项列表
     * @param request 参数参数
     * @return {@link  BillingItemPageVO}
     * <AUTHOR>
     * @date 2022/7/20
     */
    Pager<BillingItemPageVO> pageBillingItem(PageBillingItemRequest request);
    
    /**
     * 获取计费项配置化相关参数
     * @return {@link  BillingConfigVO}
     * <AUTHOR>
     * @date 2022/7/20
     */
    BillingConfigVO configList();
    
    /**
     * 获取计费项列表
     * @param typeId 方式类型id
     * @return {@link List<BillingItemBaseVO>}
     * <AUTHOR>
     * @date 2022/7/29
     */
    List<BillingItemBaseVO> listConfigItemByType(Integer typeId);
    
    /**
     * 获取全部的配置化计费项
     * @return {@link List< BillingItemBaseVO>}
     * <AUTHOR>
     * @date 2022/8/2
     */
    List<BillingItemBaseVO> listByAll();
    
    /**
     * 获取客户配置化信息的
     * @param customerId 客户id
     * @return {@link List<OtherBillingItemVO>}
     * <AUTHOR>
     * @date 2022/8/2
     */
    List<OtherBillingItemVO> getCustomerBillItemConfigs(String customerId);
    
    /**
     * 获取客户的可用资源情况
     * @param customerId 客户id
     * @param resourceCode 计费资源编码
     * @return {@link OtherBillingItemVO}
     * <AUTHOR>
     * @date 2022/11/1
     */
    OtherBillingItemVO getCustomerAvailableByOther(String customerId, String resourceCode, Date baseDate);
    
    /**
     * 根据类别获取客户的可用资源情况
     * @param customerId 客户id
     * @param resourceCode 计费资源编码
     * @return {@link OtherBillingItemVO}
     * <AUTHOR>
     * @date 2022/11/1
     */
    OtherBillingItemVO getCustomerAvailableByOther(String customerId, String resourceCode, String type, Date baseDate);
    
    
}
