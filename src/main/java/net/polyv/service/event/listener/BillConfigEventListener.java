package net.polyv.service.event.listener;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.PcsEnumConst;
import net.polyv.constant.event.BillingEventEnum;
import net.polyv.constant.finance.FinanceTaxRateConst;
import net.polyv.dao.primary.billingConfig.BillingWayConfigRepository;
import net.polyv.dao.primary.business.DictionaryTypeRepository;
import net.polyv.dao.primary.business.SystemDictionaryRepository;
import net.polyv.dao.primary.difinition.BusinessEnumDefinitionRepository;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.model.entity.primary.billingconfig.BillingItemExt;
import net.polyv.model.entity.primary.billingconfig.BillingWayConfig;
import net.polyv.model.entity.primary.business.DictionaryType;
import net.polyv.model.entity.primary.business.SystemDictionary;
import net.polyv.model.entity.primary.definition.BusinessEnumDefinition;
import net.polyv.model.event.BillingConfigEvent;
import net.polyv.modules.pcs.api.stereotype.DictionaryCodeEnum;
import net.polyv.service.business.SystemDictionaryService;
import net.polyv.util.MoneyUtil;

/**
 * 计费项配置化 事件监听器
 * 异步处理
 * <AUTHOR>
 * @date 2022/7/25 15:48
 */
@Component
@Slf4j
public class BillConfigEventListener {
    @Autowired
    private SystemDictionaryService systemDictionaryService;
    @Autowired
    private DictionaryTypeRepository dictionaryTypeRepository;
    @Autowired
    private SystemDictionaryRepository systemDictionaryRepository;
    @Autowired
    private BillingWayConfigRepository billingWayConfigRepository;
    @Resource
    private BusinessEnumDefinitionRepository businessEnumDefinitionRepository;
    
    @Value("${pcs.dictCode.resource}")
    private String resourceCode;
    @Value("${pcs.dictCode.business}")
    private String businessCode;
    @Value("${pcs.dictCode.subCategory:subCategory}")
    private String subCategory;
    
    
    @Value("${pcs.config.numberBillWayId}")
    private Integer numberBillWayId;
    
    @Value("${pcs.config.usageAndUseTimeWayId:7}")
    private Integer usageAndUseTimeWayId;
    
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void listener(BillingConfigEvent event) {
        log.info("收到计费项事件：{}", JsonUtil.beanToString(event).orElse(""));
        if (BillingEventEnum.SAVE.name().equals(event.getEventEnum().name())) {
            log.info("插入枚举定义表-开始！");
            this.businessEnumDefinitionRepository.save(buildDefinition(event));
            log.info("插入枚举定义表-成功！");
            this.systemDictionaryService.saveOrUpdateSystemDict(buildSystemDict(event));
        }
        log.info("收到计费项事件-处理成功！");
    }
    
    private BusinessEnumDefinition buildDefinition(BillingConfigEvent event) {
        BillingItem billingItem = event.getBillingItem();
        BillingItemExt billingItemExt = event.getBillingItemExt();
        //获取收入归属
        SystemDictionary incomeBelong = this.systemDictionaryRepository.getByDictTypeCodeAndLabel(
                DictionaryCodeEnum.INCOME_BELONG_TYPE.getCode(), billingItem.getProduction());
        SystemDictionary product = this.systemDictionaryRepository.getByDictTypeCodeAndLabel(
                DictionaryCodeEnum.PRODUCT_NAME.getCode(), billingItem.getProduction());
        return BusinessEnumDefinition.builder()
                .billItemCategory(billingItem.getCategory())
                .billItemCode(billingItem.getCode())
                .billItemName(billingItem.getName())
                .billItemId(billingItem.getId().longValue())
                .incomeBelong(incomeBelong.getValue())
                .incomeBelongName(incomeBelong.getLabel())
                .detailSubCategory("SC_" + billingItem.getCode())
                .detailSubCategoryName(billingItemExt.getSubCategory())
                .product(product.getValue())
                .productName(product.getLabel())
                .taxRatio(MoneyUtil.enlargeNumericalHundred(FinanceTaxRateConst.NORMAL.getTaxRate().toString()))
                .createTime(new Date())
                .updateTime(new Date())
                .build();
    }
    
    // TODO: 2023/5/9  不建议生成 layout code config 数据
    
    
    
    private List<SystemDictionary> buildSystemDict(BillingConfigEvent event) {
        //根据新增的 计费方式为按次数计费 的类型，新增测试/赠送的选项，选项命名规则：$产品中文名$-$计费项名称$
        if (Objects.isNull(event)) {
            return null;
        }
        List<SystemDictionary> list = new ArrayList<>();
        BillingItemExt billingItemExt = event.getBillingItemExt();
        //插入细分
        DictionaryType subCategoryDict = dictionaryTypeRepository.findByCodeAndStatus(this.subCategory,
                (short) PcsEnumConst.YES.getValue());
        SystemDictionary systemDictionary = new SystemDictionary();
        systemDictionary.setDictTypeId(subCategoryDict.getId());
        systemDictionary.setLabel(billingItemExt.getSubCategory());
        systemDictionary.setStatus((short) PcsEnumConst.YES.getValue());
        systemDictionary.setPId(null);
        systemDictionary.setValue("SC_"+billingItemExt.getItemCode());
        list.add(systemDictionary);
        if (!saveEnable(event)) {
            int i = this.systemDictionaryRepository.deletedByDictTypeCodeAndValue(this.businessCode,
                    event.getBillingItem().getId() + "");
            log.info("逻辑删除数据：code={},value={},deleted={}", this.businessCode, event.getBillingItem().getId() + "", i);
            return list;
        }
        DictionaryType resourceDict = dictionaryTypeRepository.findByCodeAndStatus(this.resourceCode,
                (short) PcsEnumConst.YES.getValue());
        DictionaryType businessDict = dictionaryTypeRepository.findByCodeAndStatus(this.businessCode,
                (short) PcsEnumConst.YES.getValue());
        List<SystemDictionary> dictList = systemDictionaryRepository.findByDictTypeIdAndStatus(resourceDict.getId(),
                (short) PcsEnumConst.YES.getValue());
        
        String format = String.format("%s-%s", event.getBillingItem().getProduction(),
                event.getBillingItem().getName());
        for (SystemDictionary dict : dictList) {
            SystemDictionary system = new SystemDictionary();
            system.setDictTypeId(businessDict.getId());
            system.setLabel(format);
            system.setStatus((short) PcsEnumConst.YES.getValue());
            system.setPId(dict.getId());
            system.setValue(event.getBillingItem().getId() + "");
            list.add(system);
        }
        log.info("准备将数据插入字典表：{}", JsonUtil.beanToString(list).orElse(""));
        return list;
    
    }
    
    private boolean saveEnable(BillingConfigEvent event) {
        Integer billingWayId = event.getBillingItemExt().getBillingWayId();
        Optional<BillingWayConfig> billWay = billingWayConfigRepository.findById(billingWayId);
        if (!billWay.isPresent() ||
                (!billingWayId.equals(this.numberBillWayId) && !billingWayId.equals(this.usageAndUseTimeWayId))) {
            log.warn("无法满足生成测试、赠送的字典数据条件！");
            return false;
        }
        return true;
    }
    
}
