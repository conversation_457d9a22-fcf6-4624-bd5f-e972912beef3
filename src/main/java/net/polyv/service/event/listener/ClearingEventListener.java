package net.polyv.service.event.listener;

import net.polyv.model.event.ClearingEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 结算事件监听者
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/6/8
 */
@Component
public class ClearingEventListener {

    @Async
    @EventListener
    public void OnClearingEventPublish(ClearingEvent event) {

    }

}
