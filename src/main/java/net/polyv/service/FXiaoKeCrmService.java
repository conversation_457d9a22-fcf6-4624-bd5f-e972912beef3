package net.polyv.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;

import lombok.extern.slf4j.Slf4j;
import net.polyv.dao.primary.CustomerBillingDailyRepository;
import net.polyv.dao.primary.finance.FinanceContractRepository;
import net.polyv.model.dto.ReceivableMessageDTO;
import net.polyv.model.entity.primary.CustomerBillingDaily;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.modules.common.util.StringUtil;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.third.constant.FXiaoKeRespCodeEnum;
import net.polyv.modules.third.service.crm.FXiaoKeService;
import net.polyv.modules.third.stereotype.FXiaoKeObjectTypeEnum;
import net.polyv.modules.third.stereotype.FXiaoKeOperatorEnum;
import net.polyv.modules.third.vo.fxiaoke.FXiaoKeBaseResponse;
import net.polyv.service.subscribe.FXiaoKeReceivableCreateSubscribeService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DingWarnRobot;

@Slf4j
@Component
public class FXiaoKeCrmService {
    
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private FXiaoKeService fXiaoKeService;
    @Resource
    private FXiaoKeReceivableCreateSubscribeService fXiaoKeReceivableCreateSubscribeService;
    @Resource
    private CustomerBillingDailyRepository customerBillingDailyRepository;
    @Resource
    private FinanceContractRepository financeContractRepository;
    
    private boolean success(ResponseVO<FXiaoKeBaseResponse> vo) {
        if (Objects.isNull(vo)) {
            return false;
        }
        return vo.getData().getErrorCode() == FXiaoKeRespCodeEnum.SUCCESS.getValue();
        
    }
    
    //objectType 对象类型 custom:自定义 default:默认
    public List<Map<String, Object>> getListByCondition(List<Map<String, Object>> filters, String apiName,
            String objectType) {
        Map<String, Object> searchQueryInfoMap = Maps.newHashMap();
        Map<String, Object> dataMap = Maps.newHashMap();
        Map<String, Object> requestMap = Maps.newHashMap();
        searchQueryInfoMap.put("offset", 0); // 偏移量
        searchQueryInfoMap.put("limit", 100); // 步长
        searchQueryInfoMap.put("filters", filters);
        dataMap.put("dataObjectApiName", apiName);
        dataMap.put("search_query_info", searchQueryInfoMap);
        requestMap.put("data", dataMap);
        ResponseVO<FXiaoKeBaseResponse> responseVO = null;
        if (FXiaoKeObjectTypeEnum.DEFAULT.getCode().equals(objectType)) {
            responseVO = fXiaoKeService.queryPresetsObjCustomizeField(requestMap);
        }
        if (FXiaoKeObjectTypeEnum.CUSTOM.getCode().equals(objectType)) {
            responseVO = fXiaoKeService.queryCustomObjCustomizeField(requestMap);
        }
        if (this.success(responseVO)) {
            Map<String, Object> resultMap = responseVO.getData().getData();
            if (!CollectionUtils.isEmpty(resultMap)) {
                return (List) resultMap.get("dataList");
            }
        }
        return null;
    }
    
    //根据条件获取某个值
    public Object getSingleValueByCondition(String fieldName, String fieldValue, String returnField, String apiName,
            String objectType) {
        if (StringUtils.isEmpty(fieldValue)) {
            return null;
        }
        List<Map<String, Object>> filters = Lists.newArrayList();
        Map<String, Object> conditionMap = Maps.newHashMap();
        conditionMap.put("field_name", fieldName);
        conditionMap.put("field_values", fieldValue);
        conditionMap.put("operator", FXiaoKeOperatorEnum.EQ.getValue());
        filters.add(conditionMap);
        List<Map<String, Object>> list = this.getListByCondition(filters, apiName, objectType);
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, Object> data = list.get(0);
            return (data == null || data.get(returnField) == null) ? null : data.get(returnField);
        }
        return null;
    }
    
    /**
     * 同步历史待支付账单到纷享销客生成应收记录
     */
    public void syncUnpaidBilling(String startDate, String endDate) {
        int pageIndex = 0;
        int pageSize = 500;
        long totalSize = 0L;
        Page<CustomerBillingDaily> page;
        while (true) {
            page = customerBillingDailyRepository.findUnpaidBillingList(DateFormatUtil.parseDateStr(startDate),
                    DateFormatUtil.parseDateStr(endDate),
                    PageRequest.of(pageIndex, pageSize, Sort.by("createTime").descending()));
            if (CollectionUtils.isEmpty(page.getContent())) {
                break;
            }
            pushUnpaidBillingToRedis(page.getContent());
            totalSize += page.getContent().size();
            if (page.getContent().size() == pageSize) {
                pageIndex++;
            } else {
                break;
            }
        }
        log.info("startDate = {},endDate = {},syncUnpaidBilling totalSize == {}", startDate, endDate, totalSize);
    }
    
    /**
     * 同步历史预开通合同到纷享销客生成应收记录
     */
    public void syncPreOpenContract(String startDate, String endDate) {
        int pageIndex = 0;
        int pageSize = 500;
        long totalSize = 0L;
        Page<FinanceContractEntity> page;
        while (true) {
            page = financeContractRepository.findPreOpenContract(DateFormatUtil.parseDateStr(startDate),
                    DateFormatUtil.parseDateStr(endDate),
                    PageRequest.of(pageIndex, pageSize, Sort.by("createTime").descending()));
            if (CollectionUtils.isEmpty(page.getContent())) {
                break;
            }
            pushPreOpenContractToRedis(page.getContent());
            totalSize += page.getContent().size();
            if (page.getContent().size() == pageSize) {
                pageIndex++;
            } else {
                break;
            }
        }
        log.info("startDate = {},endDate = {},syncPreOpenContract totalSize == {}", startDate, endDate, totalSize);
    }
    
    private void pushUnpaidBillingToRedis(List<CustomerBillingDaily> list) {
        if (!CollectionUtils.isEmpty(list)) {
    
            RateLimiter rateLimiter = RateLimiter.create(2);
            list.forEach(customerBillingDaily -> {
                rateLimiter.acquire();
                log.info("pushUnpaidBillingToRedis 获取到令牌.....");
                ReceivableMessageDTO messageDTO = new ReceivableMessageDTO();
                messageDTO.setCreateTime(customerBillingDaily.getCreateTime());
                messageDTO.setCustomerId(customerBillingDaily.getCustomerId());
                messageDTO.setReceivableType(2);
                messageDTO.setReceivableAmount(customerBillingDaily.getUnpaid());
                try {
        
                    fXiaoKeReceivableCreateSubscribeService.pushReceivableToRedis(messageDTO);
                } catch (Exception e) {
                    log.error("pushUnpaidBillingToRedis error", e);
                    e.printStackTrace();
                    dingWarnRobot.sendWarnMsg("【待支付账单生成应收记录失败】",
                            String.format("customerBillingDaily == %s", customerBillingDaily));
                }
            });
        }
    }
    
    private void pushPreOpenContractToRedis(List<FinanceContractEntity> list) {
        if (!CollectionUtils.isEmpty(list)) {
            //纷享销客限制1分钟只能调用180次接口
            RateLimiter rateLimiter = RateLimiter.create(2);
            list.forEach(financeContractEntity -> {
                rateLimiter.acquire();
                log.info("pushPreOpenContractToRedis 获取到令牌.....");
                ReceivableMessageDTO messageDTO = new ReceivableMessageDTO();
                messageDTO.setCreateTime(financeContractEntity.getCreateTime());
                messageDTO.setCustomerId(financeContractEntity.getUnionId());
                messageDTO.setReceivableType(4);
                messageDTO.setReceivableAmount(financeContractEntity.getCurrentAmount());
                messageDTO.setSaleUserId(financeContractEntity.getOwnerId());
                try {
                    fXiaoKeReceivableCreateSubscribeService.pushReceivableToRedis(messageDTO);
                } catch (Exception e) {
                    log.error("pushPreOpenContractToRedis error", e);
                    e.printStackTrace();
                    dingWarnRobot.sendWarnMsg("【预开通合同生成应收记录失败】",
                            String.format("financeContractEntity == %s", financeContractEntity));
                }
            });
        }
    }
}
