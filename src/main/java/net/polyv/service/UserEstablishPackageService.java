package net.polyv.service;

import net.polyv.common.CommonResult;
import net.polyv.model.data.deposit.DepositResourceDTO;
import net.polyv.modules.pcs.api.req.AddResourceRequest;
import net.polyv.modules.pcs.api.req.DepositFlowPackageRequest;
import net.polyv.modules.pcs.api.req.DepositResourceRequest;

/**
 * 普通 账号| 集团2.0分账号打套餐接口service
 * <AUTHOR>
 * @since 2022/09/29
 */
public interface UserEstablishPackageService {


    /**
     * 点播流量包
     * @param request
     * @return
     */
    CommonResult depositVodFlowPackage(DepositResourceDTO request);

    /**
     * 点播增容空间
     * @param request
     * @return
     */
    CommonResult depositVodTempSpace(DepositResourceDTO request);

    /**
     * 充值金额
     * @param request
     * @return
     */
    CommonResult depositAmount(DepositResourceDTO request);

    /**
     * 充值连麦分钟数
     * @param request
     * @return
     */
    CommonResult depositMicDuration(DepositResourceDTO request);

    /**
     * 充值直播分钟数
     * @param request
     * @return
     */
    CommonResult depositLiveDuration(DepositResourceDTO request);

    /**
     * 充值直播并发（需要重算并发数和过期时间）
     * @param request
     * @return
     */
    CommonResult depositLiveConcurrence(DepositResourceDTO request);

    /**
     * 充值直播并发（不需要重算并发数和过期时间）
     * @param request
     * @return
     */
    CommonResult depositLiveConcurrenceWithoutRecalculate(DepositResourceDTO request);

    /**
     * 充值导播台分钟数
     * @param request
     * @return
     */
    CommonResult depositGuideDuration(DepositResourceDTO request);

    CommonResult depositChannels(DepositResourceDTO request);

    /**
     * 充值素材空间
     * @param request
     * @return
     */
    CommonResult depositMaterialSpace(DepositResourceDTO request);

    /**
     * 充值素材库流量
     * @param request
     * @return
     */
    CommonResult depositMaterialTraffic(DepositResourceDTO request);

    CommonResult depositAIPPTVideo(DepositResourceDTO request);

    CommonResult depositAIPPTVideoWithDigital(DepositResourceDTO request);
}


