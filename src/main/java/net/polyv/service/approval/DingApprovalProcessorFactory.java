package net.polyv.service.approval;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;
import net.polyv.exception.ClearingSystemRuntimeException;

/**
 * 钉钉审批处理器的工厂类
 * <AUTHOR>
 * @since 2020/7/15
 */
@Slf4j
@Service
public class DingApprovalProcessorFactory {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private static final Map<String, DingApprovalProcessor> PROCESSOR_CACHE_MAP = new HashMap<>();
    
    @PostConstruct
    public synchronized void cacheDingApprovalProcessor() {
        
        Map<String, DingApprovalProcessor> processorMap = this.applicationContext.getBeansOfType(DingApprovalProcessor.class);
        
        processorMap.forEach((key, value) -> PROCESSOR_CACHE_MAP.put(value.getProcessCode(), value));
        
        log.info("load dingding approval processor success, current processor list is {}",
                JSON.toJSONString(processorMap.keySet()));
    }
    
    /**
     * 根据钉钉审批模板processCode获取审批处理器
     * @param processCode 类型
     * @return 处理器
     */
    public DingApprovalProcessor get(String processCode) {
        
        if (processCode == null) {
            log.error("processCode is null.");
            throw new IllegalArgumentException("processCode不能为空");
        }
    
        DingApprovalProcessor processor = PROCESSOR_CACHE_MAP.get(processCode);
        
        if (processor == null) {
            log.error("Can't found processor by code: {}", processCode);
        }
        
        return processor;
    }
}
