package net.polyv.service.pack;

import static net.polyv.constant.item.ResourceCodeConst.duration;
import static net.polyv.constant.item.ResourceCodeConst.live_flow;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.ConcurrenceModeConst;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.OperateResultConst;
import net.polyv.constant.ResourceAlterTypeConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.common.ZeroOneConst;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.item.OldPackItemCodeConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.constant.resource.ResourceSourceConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.dao.primary.LiveFlowPackageInfoRepository;
import net.polyv.dao.primary.custom.CustomerConfigDao;
import net.polyv.dao.primary.custom.ResourceAlterationRecordDao;
import net.polyv.model.data.concurrence.BalanceChangeState;
import net.polyv.model.entity.primary.CustomerConfig;
import net.polyv.model.entity.primary.CustomerConfigConfig;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.model.entity.primary.LiveFlowPackageInfo;
import net.polyv.rest.model.live.LivePackageMsgVO;
import net.polyv.rest.service.live.LivePackageClientService;
import net.polyv.service.CacheService;
import net.polyv.service.LiveFlowPackageInfoService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.bill.BillingDataService;
import net.polyv.service.bill.BillingService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.JsonMapper;
import net.polyv.web.model.bill.BillClearingInputVO;
import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.examinationDonate.ResourceTypeExpireDateVO;
import net.polyv.web.model.oldpackage.OldPackageAdjustInputVO;
import net.polyv.web.model.resource.CleanResourceVO;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 旧套餐操作服务
 * <AUTHOR>
 * @since 2020/6/8
 */
@Service
@Slf4j
public class LiveOldPackageOperaService {
    
    @Resource
    private LiveFlowPackageInfoService liveFlowPackageInfoService;
    @Resource
    private LiveFlowPackageInfoRepository liveFlowPackageInfoRepository;
    @Autowired
    private LiveCustomerConcurrenceSettingRepository concurrRepository;
    @Autowired
    private BillingItemRepository billingItemRepository;
    @Autowired
    private BillingService billingService;
    @Autowired
    private CustomerConfigDao customerConfigDao;
    @Autowired
    private DingWarnRobot dingWarnRobot;
    @Autowired
    private BillingDataService billingDataService;
    @Autowired
    @Lazy
    private ResourceAlterationRecordDao resourceAlterationRecordDao;
    @Autowired
    private CustomerResourceService customerResourceService;
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private LivePackageClientService livePackageClientService;
    
    @Resource
    private CustomerService customerService;
    
    /**
     * ！强调一点，当天生效或者未来生效的旧版套餐设置只能同时存在一个！
     */
    @Transactional
    public void addCustomerPackage(OldPackageAdjustInputVO inputVO) {
        log.info("customerId == {},addCustomerPackage inputVO == {}", inputVO.getCustomerId(), inputVO);
        if (checkValidPackage(inputVO) != 0) {
            // 假设同步过来的套餐有冲突，跳过处理（一般是超管后台设置，套餐没有任何变动的时候引起的异常）
            log.warn("并发时段冲突，跳过处理, inputVO={}", inputVO);
            return;
        }
        String billingPlan = inputVO.getBillingPlan();
        Date now = DateUtil.getCurrentDay();
        
        if (!checkDataInDb(inputVO)) {
            return;
        }
        
        // 设置上一个套餐的信息
        CustomerConfig config = customerConfigDao.getOrCreateCustomerConfig(inputVO.getCustomerId());
        CustomerConfigConfig configExt = config.getConfig();
        //是否添加并发套餐
        boolean isAddConcurrPack = OldPackItemCodeConst.daily.name().equals(billingPlan) ||
                OldPackItemCodeConst.monthly.name().equals(billingPlan) || OldPackItemCodeConst.prtc_monthly.name().equals(billingPlan) ||
                OldPackItemCodeConst.prtc_daily.name().equals(billingPlan) ||
                OldPackItemCodeConst.peak.name().equals(billingPlan);
        //是否添加流量套餐
        boolean isAddFlowPack = OldPackItemCodeConst.live_flow.name().equals(billingPlan);
        //是否添加分钟数套餐
        boolean isAddDurationPack = OldPackItemCodeConst.duration.name().equals(billingPlan);
        boolean isGroupAccount = AccountTypeEnum.GROUP2.getCode().equals(inputVO.getAccountType());
        
        //集团2.0主账号
        if (isGroupAccount) {
            
            setPackageDatesFromInputVO(inputVO, configExt);
        }
        //普通账号打直播流量套餐，是不会同步到直播的，所以打分钟数或并发套餐时，上一个套餐信息，不能直接从直播传过来的参数获取，需要先判断当前是不是流量套餐
        else {
            if (isAddConcurrPack || isAddDurationPack) {
                LivePackageMsgVO livePackageMsg = livePackageClientService.getLivePackageMsg(inputVO.getCustomerId());
                log.info("customerId == {},livePackageMsg == {}", inputVO.getCustomerId(), livePackageMsg);
                if (Objects.nonNull(livePackageMsg) &&
                        OldPackItemCodeConst.live_flow.getBillingPlan().equals(livePackageMsg.getBillingPlan())) {
                    configExt.setLivePrevBillingPlan(livePackageMsg.getBillingPlan());
                    //上一个套餐生效时间
                    if (StringUtils.isNotBlank(livePackageMsg.getPackageStart())) {
                        configExt.setLivePrevPackageStart(
                                DateFormatUtil.parseDateStr(livePackageMsg.getPackageStart()).getTime());
                    }
                    if (StringUtils.isNotBlank(livePackageMsg.getPackageEnd())) {
                        configExt.setLivePrevPackageEnd(
                                DateFormatUtil.parseDateStr(livePackageMsg.getPackageEnd()).getTime());
                    }
                } else {
                    setPackageDatesFromInputVO(inputVO, configExt);
                }
            }
            //直播流量套餐，上一个套餐信息从InputVO获取
            else if (isAddFlowPack) {
                setPackageDatesFromInputVO(inputVO, configExt);
            }
        }
        
        
        //分钟数套餐 变为 并发套餐或流量套餐，需要记录上一次分钟数套餐的信息,并发套餐过期打回分钟数套餐需要这些信息
        if ((isAddConcurrPack || isAddFlowPack) &&
                OldPackItemCodeConst.duration.name().equals(configExt.getLivePrevBillingPlan())) {
            configExt.setLivePrevDurationPackStart(configExt.getLivePrevPackageStart());
            configExt.setLivePrevDurationPackEnd(configExt.getLivePrevPackageEnd());
        }
        
        //直播流量套餐 变为 并发套餐或分钟数套餐，需要记录上一次流量套餐的信息,并发套餐过期打回流量套餐需要这些信息
        if ((isAddConcurrPack || isAddDurationPack) &&
                OldPackItemCodeConst.live_flow.name().equals(configExt.getLivePrevBillingPlan())) {
            configExt.setLivePrevFlowPackStart(configExt.getLivePrevPackageStart());
            configExt.setLivePrevFlowPackEnd(configExt.getLivePrevPackageEnd());
        }
        log.info("customerId == {},config == {}", inputVO.getCustomerId(), config);
        customerConfigDao.saveCustomerConfig(config);
    
    
        // 获取用户当前生效的年峰值并发设置记录
        List<LiveCustomerConcurrenceSetting> effectivePeakList = concurrRepository.findCustomerCurrentEffectiveSettings(
                inputVO.getCustomerId(), now, ConcurrenceModeConst.PEAK);
    
        if (effectivePeakList.size() > 1) {
            dingWarnRobot.sendWarnMsg("直播并发设置数据异常",
                    "数据库存在多条当前或者未来生效的年峰值并发设置, customerId=" + inputVO.getCustomerId());
        }
    
        // 清0年峰值的可用余额
        if (!OldPackItemCodeConst.peak.name().equals(billingPlan)) {
            CleanResourceVO cleanResourceVO = CleanResourceVO.builder()
                    .resourceCode(ResourceCodeConst.concurrence.name()).customerId(inputVO.getCustomerId()).build();
            customerResourceService.cleanItemResource(cleanResourceVO);

//            if(!effectivePeakList.isEmpty()){
//                customerResourceService.writeLatestPcsResourceAvailable2LiveDb(inputVO.getCustomerId(),
//                ResourceCodeConst.concurrence.name());
//            }
        }
    
        processAddPackage(inputVO, effectivePeakList);
        if (AccountTypeEnum.NORMAL.getCode().equals(inputVO.getAccountType())) {
        
            cacheService.cleanLiveRestrictPcsResult(inputVO.getCustomerId());
            cacheService.cleanVodUserCache(inputVO.getCustomerId());
        } else if (AccountTypeEnum.GROUP2.getCode().equals(inputVO.getAccountType())) {
            cacheService.cleanPcsCache(inputVO.getCustomerId());
        }
    }
    
    private void setPackageDatesFromInputVO(OldPackageAdjustInputVO inputVO, CustomerConfigConfig configExt) {
        configExt.setLivePrevBillingPlan(inputVO.getLivePrevBillingPlan());
        setPackageDates(inputVO.getLivePrevPackageStart(), inputVO.getLivePrevPackageEnd(), configExt);
    }
    
    private void setPackageDates(String packageStart, String packageEnd, CustomerConfigConfig configExt) {
        if (StringUtils.isNotBlank(packageStart)) {
            configExt.setLivePrevPackageStart(DateFormatUtil.parseDateStr(packageStart).getTime());
        }
        if (StringUtils.isNotBlank(packageEnd)) {
            configExt.setLivePrevPackageEnd(DateFormatUtil.parseDateStr(packageEnd).getTime());
        }
    }
    
    private void processAddPackage(OldPackageAdjustInputVO inputVO,
            List<LiveCustomerConcurrenceSetting> effectivePeakList) {
        //如果有businessId传过来，需要获取redis的数据
        if (StringUtils.isNotBlank(inputVO.getBusinessId())) {
            
            Object businessInfo = redisTemplate.opsForValue()
                    .get(String.format(GlobalConfigConst.ESTABLISH_PACKAGE_BUSINESS_KEY, inputVO.getBusinessId()));
            if (Objects.nonNull(businessInfo)) {
                ResourceTypeExpireDateVO resourceTypeExpireDateVO = JsonMapper.stringToBean(businessInfo.toString(),
                        ResourceTypeExpireDateVO.class);
                log.info("customerId ={},resourceTypeExpireDateVO = {}", inputVO.getCustomerId(),
                        resourceTypeExpireDateVO);
                //判断传过来的值是否在枚举范围内
                String resourceTypeDesc = ResourceTypeEnum.getValue(resourceTypeExpireDateVO.getResourceType());
                if (StringUtils.isNotBlank(resourceTypeDesc)) {
                    inputVO.setResourceType(resourceTypeExpireDateVO.getResourceType());
                }
                if (Objects.nonNull(resourceTypeExpireDateVO.getExpireDate())) {
                    inputVO.setEndDate(resourceTypeExpireDateVO.getExpireDate());
                }
                if (StringUtils.isNotBlank(resourceTypeExpireDateVO.getContractId())) {
                    inputVO.setContractId(resourceTypeExpireDateVO.getContractId());
                }
            }
        }
        String billingPlan = inputVO.getBillingPlan();
        String startDateStr = inputVO.getStartDate();
        String endDateStr = inputVO.getEndDate();
        Date startDate = DateFormatUtil.parseDateNormal(startDateStr);
        Date endDate = DateFormatUtil.parseDateNormal(endDateStr);
        Date now = DateUtil.getCurrentDay();
        String accountType = inputVO.getAccountType();
        boolean needProcessClearing = false;
        LiveCustomerConcurrenceSetting needClearingConcurrenceSetting = null;
        //打并发套餐
        if (OldPackItemCodeConst.daily.name().equals(billingPlan) ||
                OldPackItemCodeConst.monthly.name().equals(billingPlan) ||
                OldPackItemCodeConst.prtc_daily.name().equals(billingPlan) ||
                OldPackItemCodeConst.prtc_monthly.name().equals(billingPlan) ||
                OldPackItemCodeConst.peak.name().equals(billingPlan)) {
    
            OldPackItemCodeConst codeConst = OldPackItemCodeConst.findByBillingPlan(billingPlan);
            if(codeConst==null){
                dingWarnRobot.sendWarnMsg("打套餐，找不到系统设置的billingPlan.", "billingPlan非法，请检查, inputVo=" + inputVO);
                log.error("通过billingPlan找不到计费套餐，inputVO={}", inputVO);
                return;
            }
            String code = codeConst.getItemCodeConst().getCode();
            int mode = codeConst.getMode();
            if(OldPackItemCodeConst.peak.equals(codeConst)){
                // 添加用量变动记录
                resourceAlterationRecordDao.saveResourceAlteration(null, inputVO.getCustomerId(),
                        ResourceCodeConst.concurrence.name(), ResourceAlterTypeConst.deposit,
                        inputVO.getConcurrences(), ResourceSourceConst.PERMANENT,inputVO.getContractId(),inputVO.getResourceType(),
                        DateFormatUtil.parseDateNormal(inputVO.getEndDate()));
                if(StringUtils.isNotBlank(inputVO.getEndDate())){
                    resourceAlterationRecordDao.updateExpireDateByCustomerIdAndResourceCodeAndType(
                            DateFormatUtil.parseDateNormal(inputVO.getEndDate()),inputVO.getCustomerId(),
                            ResourceCodeConst.concurrence.name(),ResourceAlterTypeConst.deposit.name()
                    );
                }
            }
        
            if (!effectivePeakList.isEmpty() && OldPackItemCodeConst.peak.name().equals(billingPlan)) {
                // 之前是年峰值并发，打的套餐也是年峰值并发
                LiveCustomerConcurrenceSetting setting = effectivePeakList.get(0);
                setting.setConcurrence(setting.getConcurrence() + inputVO.getConcurrences());
                if (endDate != null) {
                    setting.setEndTime(endDate);
                }
                setting.setUpdateTime(new Date());
                concurrRepository.save(setting);
                //修改为非直播流量计费
                liveFlowPackageInfoService.updateIsLiveFlowBilling(inputVO.getCustomerId(), 0);
                return;
            }
        
            //1.删除今天或者未来生效的旧并发设置
            concurrRepository.deleteByCustomerIdAndStartTimeGreaterThanEqualAndIsOldPackage(inputVO.getCustomerId(),
                    now, 1);
            //2.修改start<=now, end>now的旧套餐的end为now
            List<LiveCustomerConcurrenceSetting> oldList = concurrRepository.findSpecialOldSettings(
                    inputVO.getCustomerId(), DateUtils.addDays(now, -1));
        
            if(!oldList.isEmpty()){
                LiveCustomerConcurrenceSetting oldSetting = oldList.get(0);
                log.info("将旧套餐的结束时间改为昨日, customerId={}, startDate={}, endDate={}", oldSetting.getCustomerId(),
                        oldSetting.getStartTime(), oldSetting.getEndTime());
                oldSetting.setUpdateTime(new Date());
                oldSetting.setEndTime(DateUtils.addDays(now, -1));
                oldSetting.setDays(getDiff(oldSetting.getStartTime(), DateUtils.addDays(now, -1)));
                concurrRepository.save(oldSetting);
    
                // 假如旧套餐是并发包月或者年峰值并发，被提前终止，需要出账
                needProcessClearing = Arrays.asList(ConcurrenceModeConst.MONTHLY, ConcurrenceModeConst.PEAK,ConcurrenceModeConst.PRTC_MONTHLY)
                        .contains(oldSetting.getMode());
                needClearingConcurrenceSetting = oldSetting;
            }
    
            //3.添加旧版并发时段
            int itemId = billingItemRepository.findByCode(code).get(0).getId();
    
            long concurrences = inputVO.getConcurrences() == null ? -1 : inputVO.getConcurrences();
            //生成并发时段
            addLiveCustomerConcurrSetting(inputVO.getCustomerId(), mode, startDate, endDate, itemId, concurrences,
                    inputVO.getUseDailyTest(), accountType);
            //修改为非直播流量计费
            liveFlowPackageInfoService.updateIsLiveFlowBilling(inputVO.getCustomerId(), 0);
    
        } else if (OldPackItemCodeConst.duration.name().equals(billingPlan) ||
                OldPackItemCodeConst.live_flow.name().equals(billingPlan)) {
            //打分钟数 || 流量套餐
            if (inputVO.getDuration() != 0) {
                // 记录资源变动记录
                resourceAlterationRecordDao.saveResourceAlteration(null, inputVO.getCustomerId(), duration.name(),
                        ResourceAlterTypeConst.deposit, inputVO.getDuration(), ResourceSourceConst.PERMANENT,
                        inputVO.getContractId(), inputVO.getResourceType(),
                        DateFormatUtil.parseDateNormal(inputVO.getEndDate()));
                //结束时间不为空 && 非定制账号（定制账号资源独立维护）
                if (StringUtils.isNotBlank(inputVO.getEndDate()) &&
                        !customerResourceService.isCustomizedGroupIdByUnionId(inputVO.getCustomerId())) {
                    resourceAlterationRecordDao.updateExpireDateByCustomerIdAndResourceCodeAndType(
                            DateFormatUtil.parseDateNormal(inputVO.getEndDate()), inputVO.getCustomerId(),
                            duration.name(), ResourceAlterTypeConst.deposit.name());
                }
            }
            if (inputVO.getLiveFlow() != 0) {
                // 记录资源变动记录
                resourceAlterationRecordDao.saveResourceAlteration(null, inputVO.getCustomerId(), live_flow.name(),
                        ResourceAlterTypeConst.deposit, inputVO.getLiveFlow(), ResourceSourceConst.PERMANENT,
                        inputVO.getContractId(), inputVO.getResourceType(),
                        DateFormatUtil.parseDateNormal(inputVO.getEndDate()));
                //结束时间不为空 && 非定制账号（定制账号资源独立维护）
                if (StringUtils.isNotBlank(inputVO.getEndDate()) &&
                        !customerResourceService.isCustomizedGroupIdByUnionId(inputVO.getCustomerId())) {
                    resourceAlterationRecordDao.updateExpireDateByCustomerIdAndResourceCodeAndType(
                            DateFormatUtil.parseDateNormal(inputVO.getEndDate()), inputVO.getCustomerId(),
                            live_flow.name(), ResourceAlterTypeConst.deposit.name());
                }
    
                // 修改用户状态为正常（分钟数和并发在直播已经修改用户为正常状态了）
                customerService.setStatusNormal(inputVO.getCustomerId());
            }
            //删除今天或者未来生效的旧并发设置
            concurrRepository.deleteByCustomerIdAndStartTimeGreaterThanEqualAndIsOldPackage(inputVO.getCustomerId(),
                    now, 1);
            //分钟数修改为非直播流量计费
            if (OldPackItemCodeConst.duration.name().equals(billingPlan)) {
                liveFlowPackageInfoService.updateIsLiveFlowBilling(inputVO.getCustomerId(), 0);
            }
            //流量修改为直播流量计费
            if (OldPackItemCodeConst.live_flow.name().equals(billingPlan)) {
                this.addLiveFlowPackageInfo(inputVO);
            }
            //如果存在生效中的并发套餐（说明直播的计费类型也是并发），但是直播传的上一个计费类型又是分钟数或流量，那就告警提醒
            LiveCustomerConcurrenceSetting activeSetting = concurrRepository.findCustomerCurrentEffectiveSetting(
                    inputVO.getCustomerId(), now);
            if (activeSetting == null) {
                return;
            }
    
            if (inputVO.getLivePrevBillingPlan().equals(billingPlan)) {
                dingWarnRobot.sendWarnMsg("直播套餐不一致告警",
                        "业务后台上一个套餐信息和结算系统不一致，请检查, inputVo=" + inputVO);
            }
        
            activeSetting.setEndTime(DateUtil.getYesterday());
            activeSetting.setDays(getDiff(activeSetting.getStartTime(), activeSetting.getEndTime()));
            concurrRepository.save(activeSetting);
    
            // 假如旧套餐是并发包月或者年峰值并发，被提前终止，需要出账
            needProcessClearing = Arrays.asList(ConcurrenceModeConst.MONTHLY, ConcurrenceModeConst.PEAK,ConcurrenceModeConst.PRTC_MONTHLY)
                    .contains(activeSetting.getMode());
            needClearingConcurrenceSetting = activeSetting;
        } else {
            log.error("billingPlan error, billingPlan={}", billingPlan);
            return;
        }
    
        if (needProcessClearing) {
            // 结算并发账单
            this.processClearing(inputVO, needClearingConcurrenceSetting);
        }
    }
    
    /**
     * 添加直播流量套餐信息
     * @param inputVO
     */
    private void addLiveFlowPackageInfo(OldPackageAdjustInputVO inputVO) {
        String customerId = inputVO.getCustomerId();
        List<LiveFlowPackageInfo> list = liveFlowPackageInfoRepository.findByCustomerId(inputVO.getCustomerId());
        LiveFlowPackageInfo liveFlowPackageInfo;
        //如果之前没有打过直播流量套餐，直接新增记录
        if (CollectionUtils.isEmpty(list)) {
            liveFlowPackageInfo = new LiveFlowPackageInfo();
            liveFlowPackageInfo.setCustomerId(customerId);
            liveFlowPackageInfo.setPackageId(inputVO.getLiveFlowPackageId());
            liveFlowPackageInfo.setPackageName(inputVO.getLiveFlowPackageName());
            liveFlowPackageInfo.setStartDate(DateFormatUtil.parseDateNormal(inputVO.getStartDate()));
            liveFlowPackageInfo.setEndDate(DateFormatUtil.parseDateNormal(inputVO.getEndDate()));
            liveFlowPackageInfo.setIsLiveFlowBilling(1);
            liveFlowPackageInfo.setCreateTime(new Date());
        } else {
            //之前打过直播流量套餐，
            liveFlowPackageInfo = list.get(0); //只会存在一条记录
            //如果当前是直播流量计费 && 今天没有打过直播流量套餐，需要记录上一次套餐的开始和结束日期（直播流量出账的时候需要判断前一天是否流量计费）
            if (ZeroOneConst.ONE.getValue().equals(liveFlowPackageInfo.getIsLiveFlowBilling()) &&
                    liveFlowPackageInfo.getStartDate().compareTo(DateUtil.getCurrentDay()) != 0) {
                liveFlowPackageInfo.setLastStartDate(liveFlowPackageInfo.getStartDate());
                liveFlowPackageInfo.setLastEndDate(liveFlowPackageInfo.getEndDate());
                log.info("customerId：{}，上一个套餐是直播流量计费，重新打了流量套餐,lastStartDate={},lastEndDate={}",
                        customerId, liveFlowPackageInfo.getLastStartDate(), liveFlowPackageInfo.getLastEndDate());
            }
            liveFlowPackageInfo.setPackageId(inputVO.getLiveFlowPackageId());
            liveFlowPackageInfo.setPackageName(inputVO.getLiveFlowPackageName());
            liveFlowPackageInfo.setStartDate(DateFormatUtil.parseDateNormal(inputVO.getStartDate()));
            liveFlowPackageInfo.setEndDate(DateFormatUtil.parseDateNormal(inputVO.getEndDate()));
            liveFlowPackageInfo.setIsLiveFlowBilling(1);
            liveFlowPackageInfo.setUpdateTime(new Date());
        }
    
    
        //添加或修改直播流量套餐信息表
        liveFlowPackageInfoRepository.save(liveFlowPackageInfo);
    }
    
    private void processClearing(OldPackageAdjustInputVO inputVO, LiveCustomerConcurrenceSetting activeSetting) {
        String itemCode = "";
        if(ConcurrenceModeConst.MONTHLY == activeSetting.getMode()){
            itemCode = ItemCodeConst.concur_monthly.getCode();
        }else if(ConcurrenceModeConst.PRTC_MONTHLY == activeSetting.getMode()){
            itemCode = ItemCodeConst.prtc_concur_monthly.getCode();
        }else{
            itemCode = ItemCodeConst.concur_peak.getCode();
        }
                        ;
        Date now = DateUtil.getCurrentDay();
        if (!billingDataService.existsBillingData(now, itemCode, null)) {
            // 假设已经出账，需要补上这一笔账单，假如未出账，则直接交给结算器定时结算
            return;
        }
        BillClearingInputVO billClearingInputVO = new BillClearingInputVO();
        billClearingInputVO.setCustomerIdArrStr(inputVO.getCustomerId());
        billClearingInputVO.setConsumeEndDate(activeSetting.getEndTime());
        
        Assert.notNull(activeSetting.getMode(), "并发模式不能为空");
        
        billClearingInputVO.setItemCode(itemCode);
        
        log.info("并发套餐被中止，今日账单已出，需要补结算账单，clearingInput={}", billClearingInputVO);
        CommonOperateResultVO resultVO = billingService.clearing(billClearingInputVO);

        if (resultVO.getResult() == null || OperateResultConst.SUCCESS != resultVO.getResult()) {
            log.error("顾客切换旧套餐触发结算失败, 返回参数={}", JSON.toJSONString(resultVO));
        }
    }
    
    /**
     * inputVo.getBillingPlan()只处理daily, monthly, duration的情况
     * 开始和结束时间要大于当前日期
     * 非法情况：
     * 1.billingPlan = daily or monthly：
     * 时间段和现有的并发套餐有时间冲突，
     * 2.billingPlan = duration：(打分钟数套餐是立马生效的)
     * 客户存在正在生效的并发时间段，并且操作时非强制性的，
     */
    public int checkValidPackage(OldPackageAdjustInputVO inputVo) {
        String startDateStr = inputVo.getStartDate();
        String endDateStr = inputVo.getEndDate();
        Date startDate = DateFormatUtil.parseDateNormal(startDateStr);
        Date endDate = DateFormatUtil.parseDateNormal(endDateStr);
        Date now = DateUtil.getCurrentDay();
        //TODO 不确定要不要做
        /*UserMsgVO userMsgVO = UserClient.getUserByUnionId(inputVo.getCustomerId());
        if("Y".equalsIgnoreCase(userMsgVO.getIsGroup())){
            log.info("客户为集团套餐，跳过套餐检查, customerId={}", inputVo.getCustomerId());
            return 0;
        }*/
        if (startDate.before(now)) {
            return 1;
        }
        
        if (startDate.after(endDate)) {
            return 2;
        }
        String billingPlan = inputVo.getBillingPlan();
        if (OldPackItemCodeConst.daily.name().equals(billingPlan) ||
                OldPackItemCodeConst.monthly.name().equals(billingPlan) ||
                OldPackItemCodeConst.prtc_daily.name().equals(billingPlan) ||
                OldPackItemCodeConst.prtc_monthly.name().equals(billingPlan)
        ) {
            
            if (!CollectionUtils.isEmpty(
                    concurrRepository.findSettingByCustomerIdAndDateAndIsOldPackage(inputVo.getCustomerId(), startDate,
                            endDate, 0))) {
                log.info("concurrence conflict, customerId={}, startDate={}, endDate={}", inputVo.getCustomerId(),
                        inputVo.getStartDate(), inputVo.getEndDate());
                return 3;
            }
        } else if (OldPackItemCodeConst.duration.name().equals(billingPlan)) {
            //判断当前是否为并发时段套餐
            LiveCustomerConcurrenceSetting activeSetting = concurrRepository.findNewCustomerCurrentEffectiveSetting(
                    inputVo.getCustomerId(), now);
            if (inputVo.getIsForce() != 1 && activeSetting != null) {
                return 4;
            }
        } else if (OldPackItemCodeConst.live_flow.name().equals(billingPlan)) {
            //判断当前是否有客户后台购买的并发时段套餐
            LiveCustomerConcurrenceSetting activeSetting = concurrRepository.findNewCustomerCurrentEffectiveSetting(
                    inputVo.getCustomerId(), now);
            if (inputVo.getIsForce() != 1 && activeSetting != null) {
                return 5;
            }
        }
        
        return 0;
    }
    
    private void addLiveCustomerConcurrSetting(String customerId, int mode, Date startDate, Date endDate, int itemId,
            Long concurrences, Integer useDailyTest,String accountType) {
        LiveCustomerConcurrenceSetting setting = new LiveCustomerConcurrenceSetting();
        
        setting.setCustomerId(customerId);
        setting.setStartTime(startDate);
        setting.setEndTime(endDate);
        setting.setConcurrence(Long.MAX_VALUE);
        setting.setItemId(itemId);
        setting.setMode(mode);
        setting.setDays(getDiff(startDate, endDate));
        setting.setIsOldPackage(1);
        setting.setCreateUserId("api");
        setting.setFreezeState(new BalanceChangeState());
        setting.setConcurrence(concurrences);
        setting.setCreateTime(new Date());
        setting.setUpdateTime(new Date());
        setting.setIsDailyTest(useDailyTest);
        setting.setAccountType(accountType);
        concurrRepository.save(setting);
    }
    
    private int getDiff(Date startDate, Date endDate) {
        return (int) ((endDate.getTime() - startDate.getTime()) / 1000 / 60 / 60 / 24) + 1;
    }
    
    /**
     * 检查旧版套餐当前或者未来生效的设置是否存在多个，存在多个属于异常现象
     */
    private boolean checkDataInDb(OldPackageAdjustInputVO inputVO){
        Date now = DateUtil.getCurrentDay();
        
        List<LiveCustomerConcurrenceSetting> oldList = concurrRepository.findSpecialOldSettings(
                inputVO.getCustomerId(), DateUtils.addDays(now, -1));
    
        if (oldList.size() > 1) {
            dingWarnRobot.sendWarnMsg("数据库数据异常告警",
                    "客户存在多条正在生效的并发设置，请检查, inputVo=" + inputVO);
            log.error("数据有异常，同时存在多条旧套餐信息，暂不往下处理，inputVO={}, oldSettings={}", inputVO,
                    JsonMapper.jsonToString(oldList));
            return false;
        }
        return true;
    }
}
