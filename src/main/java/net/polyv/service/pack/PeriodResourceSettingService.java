package net.polyv.service.pack;

import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.pack.PeriodGetVO;
import net.polyv.web.model.pack.PeriodResourceSettingSaveVO;
import net.polyv.web.model.pack.PeriodResourceSettingUpdateVO;
import net.polyv.web.model.pack.PeriodTimeVO;

/**
 * 套餐操作逻辑
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/6/2
 */
public interface PeriodResourceSettingService {

    /**
     * 打套餐
     * @param inputVO 打套餐入参
     * @return 打套餐结果
     */
    CommonOperateResultVO savePeriodResourceSetting(PeriodResourceSettingSaveVO inputVO);

    /**
     * 更新套餐信息
     * @param inputVO 更新套餐
     * @return 更新套餐结果
     */
    CommonOperateResultVO updatePeriodResourceSetting(PeriodResourceSettingUpdateVO inputVO);

    /**
     * 获取周期时间信息
     * @param getVO 获取入参
     * @return 周期时间信息
     */
    PeriodTimeVO getPeriod(PeriodGetVO getVO);
}
