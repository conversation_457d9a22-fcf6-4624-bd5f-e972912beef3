package net.polyv.service.subscribe;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.finance.StatusValueEnum;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.business.BusinessPaymentRepository;
import net.polyv.model.data.business.PaymentContractIdUpdateDTO;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.business.BusinessPayment;
import net.polyv.service.SystemEnvService;
import net.polyv.service.finance.FinanceContractService;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.JsonMapper;
import net.polyv.util.RandomUtils;

/**
 * 回款合同id更新
 * <AUTHOR>
 * @since 2022/11/23
 */
@Component
@Slf4j
public class PaymentContractIdUpdateSubscribeService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private BusinessPaymentRepository businessPaymentRepository;
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private SystemEnvService systemEnvService;
    private ExecutorService executorService = Executors.newFixedThreadPool(5);
    
    
    @PostConstruct
    public void init() {
        if (systemEnvService.isLocalEnv()) {
            return;
        }
        //pushTestData();
        //这里也可以多线程处理，根据实际业务需要
        executorService.execute(new SubscribeThread());
    }
    
    //造测试数据
    public void pushTestData() {
        PaymentContractIdUpdateDTO dto = new PaymentContractIdUpdateDTO();
        dto.setSoId("6239448ec383200001e24dca");
        dto.setContractId(RandomUtils.getRandStr(10));
        redisTemplate.opsForList()
                .leftPush(GlobalConfigConst.UPDATE_PAYMENT_CONTRACT_ID_QUEUE, JsonMapper.jsonToString(dto));
        
        PaymentContractIdUpdateDTO dto2 = new PaymentContractIdUpdateDTO();
        dto2.setSoId("5SmcfIGdt590ike14HQ2");
        dto2.setContractId(RandomUtils.getRandStr(10));
        redisTemplate.opsForList()
                .leftPush(GlobalConfigConst.UPDATE_PAYMENT_CONTRACT_ID_QUEUE, JsonMapper.jsonToString(dto2));
    }
    
    class SubscribeThread implements Runnable {
        @Override
        public void run() {
            log.info("start to update payment contractId....");
            while (true) {
                String message = null;
                try {
                    message = redisTemplate.opsForList().rightPop(GlobalConfigConst.UPDATE_PAYMENT_CONTRACT_ID_QUEUE);
                    if (StringUtils.isNotBlank(message)) {
                        //业务逻辑处理
                        log.info("pull message : {} to handle", message);
                        PaymentContractIdUpdateDTO dto = JsonMapper.stringToBean(message,
                                PaymentContractIdUpdateDTO.class);
                        updatePaymentContractId(dto);
                    } else {
                        Thread.sleep(5000);
                    }
                } catch (Exception e) {
                    log.error("request == {},update payment contractId fail", message);
                    dingWarnRobot.sendWarnMsg("【更新回款合同id失败】", String.format("request = %s", message));
                }
            }
        }
        
        void updatePaymentContractId(PaymentContractIdUpdateDTO dto) {
            String orderNo = dto.getSoId();
            String contractId = dto.getContractId();
            if (StringUtils.isNotBlank(orderNo) && StringUtils.isNotBlank(contractId)) {
                List<BusinessPayment> paymentList = businessPaymentRepository.findBySoId(orderNo);
                if (CollectionUtils.isNotEmpty(paymentList)) {
                    paymentList.forEach(businessPayment -> {
                        businessPayment.setContractId(contractId);
                        businessPayment.setUpdateTime(new Date());
                    });
                    businessPaymentRepository.saveAll(paymentList);
                }
            }
        }
    }
}
