package net.polyv.service.subscribe;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.stat.StatBeyondConcurrentRepository;
import net.polyv.model.dto.FXiaoKeReceivableDTO;
import net.polyv.model.dto.ReceivableMessageDTO;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.stereotype.SalesOpportunitiesStatusConst;
import net.polyv.modules.third.service.crm.FXiaoKeService;
import net.polyv.modules.third.stereotype.FXiaoKeApiNameEnum;
import net.polyv.modules.third.stereotype.FXiaoKeObjectTypeEnum;
import net.polyv.modules.third.vo.fxiaoke.FXiaoKeBaseResponse;
import net.polyv.modules.user.api.service.CrmApi;
import net.polyv.modules.user.api.service.group.GroupAccountServiceApi;
import net.polyv.service.FXiaoKeCrmService;
import net.polyv.service.GroupAccountOneService;
import net.polyv.service.SystemEnvService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.finance.FinanceBeyondConcurrenceService;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.JsonMapper;
import net.polyv.web.model.account.GroupAccountAndNormalUserVO;

/**
 * 应收记录创建
 * <AUTHOR>
 * @since 2023/01/04
 */
@Component
@Slf4j
public class FXiaoKeReceivableCreateSubscribeService {
    
    @Resource
    private GroupAccountOneService groupAccountOneService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private FXiaoKeCrmService fXiaoKeCrmService;
    @Resource
    private FXiaoKeService fXiaoKeService;
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private CustomerService customerService;
    @Resource
    private CrmApi crmApi;
    @Resource
    private GroupAccountServiceApi groupAccountServiceApi;
    @Resource
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    @Resource
    private StatBeyondConcurrentRepository statBeyondConcurrentRepository;
    @Resource
    private FinanceBeyondConcurrenceService financeBeyondConcurrenceService;
    private ExecutorService executorService = Executors.newFixedThreadPool(1);
    @Resource
    private SystemEnvService systemEnvService;
    
    @PostConstruct
    public void init() {
        if (systemEnvService.isLocalEnv()) {
            return;
        }
        //这里也可以多线程处理，根据实际业务需要
        executorService.execute(new SubscribeThread());
    }
    
    //造测试数据
    public void pushTestData() {
        FXiaoKeReceivableDTO dto = new FXiaoKeReceivableDTO();
    
        dto.setCustomerName("客户1");
        dto.setReceivableAmount(BigDecimal.valueOf(66));
        dto.setReceivableType(4);
        dto.setCustomerId("fd93d0bae1");
        dto.setSaleUserId("1254");
        redisTemplate.opsForList()
                .leftPush(GlobalConfigConst.FXIAOKE_RECEIVABLE_CREATE_QUEUE, JsonMapper.jsonToString(dto));
    
        FXiaoKeReceivableDTO dto2 = new FXiaoKeReceivableDTO();
        dto2.setCustomerName("客户2");
        dto2.setReceivableAmount(BigDecimal.valueOf(77));
        dto2.setReceivableType(2);
        dto2.setCustomerId("fd93d0bae1");
        dto2.setSaleUserId("1254");
        redisTemplate.opsForList()
                .leftPush(GlobalConfigConst.FXIAOKE_RECEIVABLE_CREATE_QUEUE, JsonMapper.jsonToString(dto2));
    }
    
    public FXiaoKeReceivableDTO getFXiaoKeReceivableDTO(ReceivableMessageDTO messageDTO) {
        FXiaoKeReceivableDTO dto = new FXiaoKeReceivableDTO();
        String customerId = messageDTO.getCustomerId();
        Integer receivableType = messageDTO.getReceivableType();
        long receivableAmount = messageDTO.getReceivableAmount();
        String saleUserId = messageDTO.getSaleUserId();
        Long beyondConcurrenceId = messageDTO.getBeyondConcurrenceId();
        //待支付账单需要排除集团2.0分账号和未成交客户
        if (receivableType.intValue() == 2) {
            List<Integer> status = Lists.newArrayList(SalesOpportunitiesStatusConst.associate.getStatus());
            boolean flag = salesOpportunitiesRepository.existsByCustomerIdAndAmountGainedGreaterThanAndStatusIn(
                    customerId, 0L, status);
            if (customerService.isGroupSubAccount(customerId) || !flag) {
                return null;
            }
        }
        GroupAccountAndNormalUserVO userVO = customerService.getUserInfoByCustomerId(customerId);
        dto.setReceivableType(receivableType);
        dto.setCustomerId(customerId);
        dto.setReceivableAmount(BigDecimal.valueOf(receivableAmount).divide(BigDecimal.valueOf(100000)));
        dto.setCustomerName(userVO.getCompany());
        //比如预开通责任销售是界面传的
        if (StringUtils.isNotBlank(saleUserId)) {
            dto.setSaleUserId(saleUserId);
        } else {
            dto.setSaleUserId(userVO.getSaleUserId());
        }
        dto.setBeyondConcurrenceId(beyondConcurrenceId);
        return dto;
    }
    
    public void pushReceivableToRedis(ReceivableMessageDTO messageDTO) {
        
        FXiaoKeReceivableDTO dto = getFXiaoKeReceivableDTO(messageDTO);
        if (Objects.nonNull(dto)) {
            redisTemplate.opsForList()
                    .leftPush(GlobalConfigConst.FXIAOKE_RECEIVABLE_CREATE_QUEUE, JsonMapper.jsonToString(dto));
        }
        
    }
    
    /**
     * 创建应收记录，返回分享的应收记录id
     * @param dto
     * @return
     */
    public String createReceivable(FXiaoKeReceivableDTO dto) {
        Map<String, Object> outerMap = Maps.newHashMap();
        Map<String, Object> dataMap = Maps.newHashMap();
        Map<String, Object> objectMap = Maps.newHashMap();
        Object owner = fXiaoKeCrmService.getSingleValueByCondition("field_7Hf4C__c", dto.getSaleUserId(), "owner",
                FXiaoKeApiNameEnum.PERSONAL.getValue(), FXiaoKeObjectTypeEnum.DEFAULT.getCode());
        if (Objects.isNull(owner)) {
            log.warn("saleUserId == {}，在纷享销客系统中没有找到对应的负责人，不创建应收记录", dto.getSaleUserId());
            return null;
        }
        List<String> ownerList = (List<String>) owner;
        objectMap.put("dataObjectApiName", "accounts_receivable_Obj__c");
        objectMap.put("receivable_type__c", dto.getReceivableType());
        objectMap.put("owner", ownerList);
        objectMap.put("customer_name__c", dto.getCustomerName());
        objectMap.put("unionid__c", dto.getCustomerId());
        objectMap.put("payment_plan__c", null);
        objectMap.put("receivable_amount__c", String.valueOf(dto.getReceivableAmount()));
        objectMap.put("original_receivable_amount__c", String.valueOf(dto.getReceivableAmount()));
        objectMap.put("receivable_status__c", dto.getReceivableStatus());
        objectMap.put("payment_amount__c", dto.getPaymentAmount());
        objectMap.put("unpaid_amount__c", String.valueOf(dto.getReceivableAmount().subtract(dto.getPaymentAmount())));
        objectMap.put("is_count__c", 1);
    
        dataMap.put("object_data", objectMap);
        outerMap.put("data", dataMap);
        ResponseVO<FXiaoKeBaseResponse> responseVO = fXiaoKeService.createCustomObjCustomizeField(outerMap);
        log.info("createReceivableRecord result == {}", responseVO);
        if (!responseVO.isSuccess() || !responseVO.getData().isSuccess()) {
            dingWarnRobot.sendWarnMsg("【调用纷享销客创建应收记录createCustomObjCustomizeField接口失败】",
                    String.format("request = %s", outerMap));
            return null;
        }
        FXiaoKeBaseResponse responseData = responseVO.getData();
        return responseData.getDataId();
    }
    
    
    class SubscribeThread implements Runnable {
        @Override
        public void run() {
            log.info("start to create receivable....");
            
            while (true) {
                String message = null;
                try {
                    message = redisTemplate.opsForList().rightPop(GlobalConfigConst.FXIAOKE_RECEIVABLE_CREATE_QUEUE);
                    if (StringUtils.isBlank(message)) {
                        Thread.sleep(60000);
                    } else {
                        //业务逻辑处理
                        log.info("pull message : {} to handle receivable", message);
                        FXiaoKeReceivableDTO dto = JsonMapper.stringToBean(message, FXiaoKeReceivableDTO.class);
                        String receivableId = createReceivable(dto);
                        //超并发，更新会应收记录id
                        if (StringUtils.isNotBlank(receivableId) && dto.getReceivableType() == 3) {
                            financeBeyondConcurrenceService.updateBeyondConcurrenceReceivableId(
                                    dto.getBeyondConcurrenceId(), receivableId);
                        }
                    }
                } catch (Exception e) {
                    log.error("request == {},createReceivable fail", message);
                    log.error("createReceivable error msg", e);
                    dingWarnRobot.sendWarnMsg("【处理应收记录异常告警】", String.format("request = %s", message));
                }
            }
        }
    }
}
