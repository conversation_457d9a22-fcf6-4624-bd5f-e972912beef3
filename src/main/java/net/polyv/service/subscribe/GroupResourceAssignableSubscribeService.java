package net.polyv.service.subscribe;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.model.data.group.GroupResourceAssignableCalculateDTO;
import net.polyv.service.GroupResourceAssignableService;
import net.polyv.service.SystemEnvService;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.JsonMapper;

/**
 * 主账号剩余可分配资源订阅处理
 * <AUTHOR>
 * @since 2022/12/22
 */
@Component
@Slf4j
public class GroupResourceAssignableSubscribeService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Resource
    private GroupResourceAssignableService groupResourceAssignableService;
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private SystemEnvService systemEnvService;
    private ExecutorService executorService = Executors.newFixedThreadPool(5);
    
    
    @PostConstruct
    public void init() {
        if (systemEnvService.isLocalEnv()) {
            return;
        }
        //pushTestData();
        //这里也可以多线程处理，根据实际业务需要
        executorService.execute(new SubscribeThread());
    }
    
    //造测试数据
    public void pushTestData() {
        GroupResourceAssignableCalculateDTO dto = new GroupResourceAssignableCalculateDTO();
        dto.setGroupId("0a564a07fb");
        dto.setBusinessType(1);
        redisTemplate.opsForList()
                .leftPush(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE, JsonMapper.jsonToString(dto));
        
        GroupResourceAssignableCalculateDTO dto2 = new GroupResourceAssignableCalculateDTO();
        dto.setGroupId("0a564a07fb");
        dto.setBusinessType(2);
        redisTemplate.opsForList()
                .leftPush(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE, JsonMapper.jsonToString(dto2));
    }
    
    class SubscribeThread implements Runnable {
        @Override
        public void run() {
            log.info("start to handle group assignable resource....");
            while (true) {
                String message = null;
                try {
                    message = redisTemplate.opsForList()
                            .rightPop(GlobalConfigConst.RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE);
                    if (StringUtils.isNotBlank(message)) {
                        //业务逻辑处理
                        log.info("pull message : {} to handle", message);
                        GroupResourceAssignableCalculateDTO dto = JsonMapper.stringToBean(message,
                                GroupResourceAssignableCalculateDTO.class);
                        handleAssignableResource(dto);
                    } else {
                        Thread.sleep(5000);
                    }
                } catch (Exception e) {
                    log.error("request == {},recalculateGroupResource fail", message);
                    dingWarnRobot.sendWarnMsg("【重算集团主账号剩余可分配资源失败】", String.format("orderNo = %s", message));
                }
            }
        }
        
        void handleAssignableResource(GroupResourceAssignableCalculateDTO dto) {
            if (StringUtils.isNotBlank(dto.getGroupId()) && Objects.nonNull(dto.getBusinessType())) {
                groupResourceAssignableService.recalculateGroupResource(dto.getGroupId(), dto.getBusinessType());
            }
        }
    }
}
    

