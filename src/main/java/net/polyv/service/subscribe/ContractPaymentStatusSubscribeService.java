package net.polyv.service.subscribe;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.finance.StatusValueEnum;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.business.BusinessPaymentRepository;
import net.polyv.dao.primary.finance.FinanceContractRepository;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.service.SystemEnvService;
import net.polyv.service.finance.FinanceContractService;
import net.polyv.util.DingWarnRobot;

/**
 * 合同回款状态订阅处理
 * <AUTHOR>
 * @since 2022/11/21
 */
@Component
@Slf4j
public class ContractPaymentStatusSubscribeService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private BusinessPaymentRepository businessPaymentRepository;
    @Resource
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    @Resource
    private FinanceContractService financeContractService;
    @Resource
    private DingWarnRobot dingWarnRobot;
    
    @Resource
    private SystemEnvService systemEnvService;
    
    @Resource
    private FinanceContractRepository financeContractRepository;
    
    private ExecutorService executorService = Executors.newFixedThreadPool(5);
    
    
    @PostConstruct
    public void init() {
        if (systemEnvService.isLocalEnv()) {
            return;
        }
        //这里也可以多线程处理，根据实际业务需要
        executorService.execute(new SubscribeThread());
    }
    
    class SubscribeThread implements Runnable {
        @Override
        public void run() {
            log.info("start to handle contract payment status....");
            while (true) {
                String message = null;
                try {
                    message = redisTemplate.opsForList().rightPop(GlobalConfigConst.CONTRACT_PAYMENT_STATUS_QUEUE);
                    if (StringUtils.isNotBlank(message)) {
                        //业务逻辑处理
                        log.info("pull message : {} to handle", message);
                        handlePaymentStatus(message);
                    } else {
                        Thread.sleep(5000);
                    }
                } catch (Exception e) {
                    log.error("orderNo == {},update contract payment status fail", message);
                    dingWarnRobot.sendWarnMsg("【更新合同回款状态失败】", String.format("orderNo = %s", message));
                }
            }
        }
        
        void handlePaymentStatus(String orderNo) {
            Long sumPayment = businessPaymentRepository.findSoSumPayment(orderNo);
            FinanceContractEntity contractEntity = financeContractRepository.findBySoId(orderNo);
            Long amountGained = Objects.nonNull(contractEntity) ? contractEntity.getCurrentAmount() : null;
            if (Objects.nonNull(sumPayment) && Objects.nonNull(amountGained)) {
                boolean existsFullPayment = sumPayment.equals(amountGained);
                log.info("updateContractPaymentStatus orderNo = {},existsFullPayment = {}", orderNo, existsFullPayment);
                CommonResult result = financeContractService.updateContractPaymentStatus(orderNo,
                        existsFullPayment ? StatusValueEnum.YES.getValue() : StatusValueEnum.NO.getValue());
                if (CommonResult.isNotOk(result)) {
                    log.error("orderNo == {},update contract payment status fail", orderNo);
                    dingWarnRobot.sendWarnMsg("【更新合同回款状态失败】", String.format("orderNo = %s", orderNo));
                }
            }
        }
    }
    
}
