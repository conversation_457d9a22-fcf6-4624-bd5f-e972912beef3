package net.polyv.service.account;

import net.polyv.common.CommonResult;
import net.polyv.constant.SysTypeConst;
import net.polyv.constant.examinationDonate.BusinessTypeEnum;
import net.polyv.model.data.customer.CrmUserSearchDO;
import net.polyv.model.data.salesopportunities.UserFunctionDO;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.vo.CustomerExtraInfoVO;
import net.polyv.modules.pcs.api.vo.VodPackageState;
import net.polyv.modules.user.api.vo.GroupUserRelationVO;
import net.polyv.rest.model.live.UserFunctionVO;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.rest.model.vod.QiKeBaoContactVO;
import net.polyv.rest.model.vod.QiKeBaoCustomerUnionIdVO;
import net.polyv.rest.model.vod.QiKeBaoCustomerVO;
import net.polyv.web.model.account.AccountAmountLessThanResultVO;
import net.polyv.web.model.account.GetCustomerStateInputVO;
import net.polyv.web.model.account.GroupAccountAndNormalUserVO;
import net.polyv.web.model.user.CustomerPackageInfoVO;
import net.polyv.web.model.user.CustomerStateVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户服务
 * <AUTHOR>
 * @since 2020/6/18
 */
public interface CustomerService {
    
    Map<String, String> getLiveUnionIdMap(List<String> liveUserIdList);
    
    Map<String, String> getUnionLiveIdMap(List<String> liveUserIdList);
    
    CustomerStateVO getCustomerState(GetCustomerStateInputVO inputVO);
    
    CustomerPackageInfoVO getCustomerPackageInfo(String customerId);
    
    /**
     * 根据直播userid/email获取用户信息
     * 直播userid优先级 > email
     */
    UserMsgVO getUserMsgVo(GetCustomerStateInputVO inputVO);
    
    /**
     * 更新点播的客户信息
     * @param list
     */
    void updateCustomerFromVodDb(List<QiKeBaoCustomerVO> list);
    
    /**
     * 更新点播的联系人信息
     * @param list
     */
    void updateContactFromVodDb(List<QiKeBaoContactVO> list);
    
    /**
     * 查找crm客户-对应联系人的vo list
     * @param customerIdList
     * @return
     */
    List<QiKeBaoCustomerUnionIdVO> listCrmCustomerUnionId(List<String> customerIdList);
    
    /**
     * 获取指定用户功能列表
     *
     * @param userMsgVO 用户信息
     * @param codes 功能编码，多个用逗号隔开
     * @param sys 系统(vod-点播, live-直播)
     * @return 功能列表
     */
    List<UserFunctionVO> getUserFunctionList(UserMsgVO userMsgVO, List<String> codes, SysTypeConst sys);
    
    /**
     * 根据code获取集团2.0主账号的功能列表
     *
     * @param groupId 集团2.0主账号id
     * @param functionDOList 功能编码
     * @return 功能列表
     */
    List<UserFunctionVO> getGroupV2FunctionList(String groupId, List<UserFunctionDO> functionDOList);
    
    /**
     * 根据code获取普通用户的功能列表
     *
     * @param liveUserId 直播用户id
     * @param functionDOList 功能编码
     * @return 功能列表
     */
    List<UserFunctionVO> getUserFunctionList(String liveUserId, List<UserFunctionDO> functionDOList);
    
    List<UserFunctionVO> getGroupV2FunctionList(String groupId, List<String> codes, SysTypeConst sys);
    
    /**
     * 设置用户状态为正常
     * @return
     */
    boolean setStatusNormal(String customerId);
    
    /**
     * 金额不足记录
     * @param amount
     * @return
     */
    List<AccountAmountLessThanResultVO> amountNotEnoughList(Long amount);
    
    /**
     *
     * @param amount
     * @return
     */
    List<AccountAmountLessThanResultVO> listAmountLessThan(Long amount);
    
    /**
     * 添加同步任务到执行队列，按照FIFO的顺序
     * @param searchDO 同步参数
     */
    void addSyncTaskToQueue(CrmUserSearchDO searchDO);
    
    /**
     * 立即执行同步任务，该方法优先于队列方法，会马上执行
     * @param searchDO 同步参数
     */
    void runCrmCustomerSyncTask(CrmUserSearchDO searchDO);
    
    /**
     * 根据邮箱和账号类型获取用户信息
     * @param email
     * @param accountType
     * @return
     */
    GroupAccountAndNormalUserVO getByEmailAndAccountType(String email,String accountType);
    
    /**这个接口只返回normal或group_v2
     * 根据客户id获取账号类型
     * @param customerId：unionId
     * @return String :{@link net.polyv.constant.business.AccountTypeEnum}
     */
    String getAccountType(String customerId);
    
    /**这个接口只返回normal或group_v2
     * 根据客户id获取账号类型
     * @param liveUserId：直播用户id
     * @return String :{@link net.polyv.constant.business.AccountTypeEnum}
     */
    String getAccountTypeByLiveUserId(String liveUserId);
    
    /** 这个接口会返回所有的账号类型
     * 根据客户id获取账号类型
     * @param customerId
     * @return String :{@link net.polyv.constant.business.AccountTypeEnum}
     */
    String getTotalAccountType(String customerId);

    void updateTempSpaceExpireDate(List<String> unionIds, Date expireDate);
    
    /**
     * 是否集团账号1.0的分账号
     */
    boolean isGroupV1SubAccount(String customerId);

    /**
     * 是否是集团分账号
     * @param customerId
     * @return
     */
    boolean isGroupSubAccount(String customerId);
    
    /**
     * 是否集团2.0主账号
     * @param customerId
     * @return
     */
    boolean isGroupAccount(String customerId);
    
    /**
     * 获取点播套餐信息
     * @param accountType : 账号类型
     * @param customerId：unionId或集团主账号id
     * @return
     */
    VodPackageState getVodPackage(String accountType, String customerId);
    
    /**
     * 获取普通客户信息
     * @param customerId
     * @return
     */
    CustomerExtraInfoVO getCustomerInfo(String customerId);
    
    /**
     * 获取用户列表
     * @param autoIds 批量polyvAutoIds
     * @return
     */
    Map<String, UserMsgVO> getUserMap(List<String> autoIds);
    
    
    /**
     * 更新跟随集团主账号过期分账号套餐过期时间
     * @param groupId : 集团主账号id
     * @param businessTypeEnum :业务编码 {@link BusinessTypeEnum}
     * @param expireDate : 套餐过期时间(金额 | 点播 | 直播)
     */
    void updateGroupUserExpireDate(String groupId, BusinessTypeEnum businessTypeEnum, Date expireDate);
    
    /**
     * 根据分账号获取所属主账号信息
     * @param unionId
     * @return
     */
    GroupUserRelationVO getGroupBySubUser(String unionId);
    
    CustomerStateVO getCustomerSimpleState(GetCustomerStateInputVO inputVO);
    
    GroupAccountAndNormalUserVO getUserInfoByCustomerId(String customerId);
    
    GroupAccountAndNormalUserVO getUserInfoByCustomerIdAndAccountType(String customerId, String accountType);
    
    /**
     * 同步点播用户到customer
     * @param unionIds
     * @return
     */
    List<String> syncVodUserByUnionIds(List<String> unionIds);
    
    /**
     * 根据开通功能开关初始化billing_customer_config
     * 解决：https://pm.igeeker.org/browse/POP-4449
     *
     * @param customerId              用户ID
     * @param activatedItemCodeList   开通功能开关
     * @param deactivatedItemCodeList 开关闭功能开关
     * @return 结果
     */
    CommonResult initItemCodeConfig(String customerId, List<String> activatedItemCodeList, List<String> deactivatedItemCodeList);
    
    /**
     * 获取需要执行初始化的计费项编码
     *
     * @return 计费项编码
     */
    CommonResult<List<String>> getNeedInitConfigItemCode();
    
    
    /**
     * 批量初始化用户默认配置
     * @param customerIdList 用户ID
     * @param activatedItemCode 功能开关
     * @return 执行结果
     */
    CommonResult initBatchItemCodeConfig(List<String> customerIdList, String activatedItemCode);
    
    
    /**
     * 根据unionId获取客户基准码率
     * @param unionId
     */
    Integer getCustomerBaseCodeRate(String unionId);
    ResponseVO<CustomerExtraInfoVO> getCustomerDealStatusAndAIResource(String liveUserId, String itemCode);
    
}
