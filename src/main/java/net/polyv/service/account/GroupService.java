package net.polyv.service.account;

import net.polyv.web.model.account.GetCustomerStateInputVO;
import net.polyv.web.model.account.group.GroupOverviewResultVO;
import net.polyv.web.model.account.group.GroupSearchCommonInputVO;
import net.polyv.web.model.user.CustomerStateVO;

/**
 * 集团账号业务实现
 */
public interface GroupService {

    /**
     * 获取集团账号概览
     * @param groupId 集团账号ID
     * @return 概览结果对象
     */
    GroupOverviewResultVO overview(String groupId);
    CustomerStateVO getGroupAccountState(GetCustomerStateInputVO inputVO);
}
