package net.polyv.service.account;

import java.util.Date;
import java.util.List;

/**
 * 并发计费分账号并发汇总service
 */
public interface GroupConcurrenceBillingUserStatService {
    /**
     * 获取并发计费的主账号名单
     * @param date：账单使用日期
     * @return
     */
    List<String> getConcurrenceBillingGroupIds(Date date);
    
    /**
     * 获取主账号并发消耗
     * @param groupId
     * @param date
     * @return
     */
    long getGroupConcurrence(String groupId, Date date);
    
    /**
     * 获取主账号指定日期的最高并发
     * @param groupId
     * @param startDate
     * @param endDate
     * @return
     */
    long getGroupMaxConcurrenceBetweenDate(String groupId, Date startDate, Date endDate);
    
}
