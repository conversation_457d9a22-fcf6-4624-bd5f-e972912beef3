package net.polyv.service.account;

import java.util.Date;
import java.util.List;

import net.polyv.web.model.group.CustomerWithGroup;

/**
 * 分钟数计费分账号记录service
 */
public interface GroupDurationBillingUserRecordService {
    
    /**
     * 获取分钟数计费的主账号名单
     * @param date：账单使用日期
     * @param itemId :计费项Id
     * @return
     */
    List<String> getDurationBillingGroupIds(List<CustomerWithGroup> groupIds, Date date, Integer itemId);
    
    /**
     * sum(主账号下分钟数计费用户分钟数消耗）
     * @param groupId : 主账号id
     * @param date : 使用日期
     * @param itemId : 计费项id
     * @return
     */
    long sumGroupUserDurationConsume(String groupId, Date date, Integer itemId);
}
