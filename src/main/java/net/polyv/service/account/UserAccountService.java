package net.polyv.service.account;

import java.util.List;

import net.polyv.common.CommonResult;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.model.data.customer.BaseCustomer;
import net.polyv.model.data.customer.CustomerInfoWithFunctionListVO;
import net.polyv.model.entity.primary.LiveFlowPackageInfo;
import net.polyv.modules.pcs.api.req.crm.GetTestOrFormalUserRequest;
import net.polyv.modules.pcs.api.vo.crm.GetLiveFlowInfoResponse;
import net.polyv.modules.pcs.api.vo.crm.GetRechargeOrCreditResponse;
import net.polyv.modules.pcs.api.vo.crm.GetTestOrFormalUserResponse;
import net.polyv.web.model.WrappedResponse;

/**
 * 用户账号服务
 * <AUTHOR>
 * @date 2022/9/19 15:29
 */

public interface UserAccountService {
    /**
     *  根据邮箱和账号 获取账号信息
     * @param email  邮箱
     * @param accountType 账号类型
     * @return 账号信息
     * <AUTHOR>
     * @date 2022/9/19
     */
    WrappedResponse<Object>  getByEmailAndAccount(String email, String accountType);
    

    
    /**
     * 根据邮箱和账号类型获取账号信息和默认功能开关
     * 账号类型现在保存后可以切换了，如果订单ext已经保存，查询时账号类型不匹配
     * 需要返回的时候清除旧ext内容显示
     *
     * @param email       邮箱
     * @param accountType 账号类型
     * @return 结果
     */
    CommonResult<CustomerInfoWithFunctionListVO> getByEmailWithDefaultFunctionList(String soId, String email, String accountType);
    
    
    /**
     * 根据类型和unionId
     * @param unionIds unionIds
     * @param userType 用户类型
     * @return 基础客户信息列表
     * <AUTHOR>
     * @date 2022/8/25
     */
    List<BaseCustomer> findByUnionIdsAndType(List<String> unionIds, AccountTypeEnum userType);
    
    /**
     * 这个接口只返回normal或group_v2
     * 根据客户id获取账号类型
     * @param customerId
     * @return String :{@link net.polyv.constant.business.AccountTypeEnum}
     */
    String getAccountType(String customerId);
    
    /**
     * 获取批量客户的充值与授信数据
     * @param customerIds 客户id列表
     * @return 客户授信与金额信息列表
     * <AUTHOR>
     * @date 2022/11/23
     */
    List<GetRechargeOrCreditResponse> getRechargeOrCredit(List<String> customerIds);

    /**
     * 获取批量客户的充值与授信数据
     * @param customerIds 客户id列表
     * @return 客户授信与金额信息列表
     * <AUTHOR>
     * @date 2022/11/23
     */
    List<GetRechargeOrCreditResponse> getRechargeResourcePoint(List<String> customerIds);
    
    /**
     * 获取测试和正式用户名单
     * @param request 请求参数
     * @return 客户充值or授信金额名单
     * <AUTHOR>
     * @date 2022/11/21
     */
    List<GetTestOrFormalUserResponse> getTestOrFormalUsers(GetTestOrFormalUserRequest request);
    
    /**
     * 是否集团2.0分账号
     * @param customerId
     * @return
     */
    boolean isGroup2SubUser(String customerId);

    /**
     * 查询指定时间内还未过期的直播套餐信息
     * @param limitDays
     */
    List<GetLiveFlowInfoResponse> listLiveFlowUsers(Integer limitDays);
}
