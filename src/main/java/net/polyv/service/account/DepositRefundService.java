package net.polyv.service.account;

import net.polyv.exception.deposit.refund.RefundOperationNotAllowException;
import net.polyv.web.model.account.refund.DepositRefundInputVO;
import net.polyv.web.model.account.refund.DepositRefundResultVO;

/**
 * 储值退款服务
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/22
 */
public interface DepositRefundService {

    /**
     * 退款
     * @param inputVO 储值退费服务
     * @return 储值退费结果
     * @throws RefundOperationNotAllowException 退款操作不被允许，可能是该充值有存在冻结金额
     */
    DepositRefundResultVO refund(DepositRefundInputVO inputVO) throws RefundOperationNotAllowException;

    /**
     * 退资源点
     * @param inputVO
     * @return
     */
    DepositRefundResultVO refundResourcePoint(DepositRefundInputVO inputVO) throws RefundOperationNotAllowException;
}
