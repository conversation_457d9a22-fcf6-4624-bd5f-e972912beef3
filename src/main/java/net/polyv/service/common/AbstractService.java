package net.polyv.service.common;

import net.polyv.dao.primary.custom.AuditLogDao;
import net.polyv.model.entity.primary.AuditLog;
import net.polyv.util.UserInfoThreadLocal;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 通用的服务层父类
 * <AUTHOR>
 * @since 08/05/2020
 */
public abstract class AbstractService {

    protected AuditLogDao auditLogDao;

    /**
     * 获取基础的操作日志实体
     * @return 基础的操作日志实体
     */
    public AuditLog getBaseAuditLog() {
        AuditLog auditLog = new AuditLog();
        auditLog.setCreateUserIp(UserInfoThreadLocal.getUserIp());
        return auditLog;
    }

    /**
     * 记录成功日志
     * @param auditLog 操作日志
     * @return 持久化后带id的操作日志
     */
    public AuditLog logSuccess(AuditLog auditLog) {
        return this.auditLogDao.logSuccess(auditLog);
    }

    /**
     * 记录失败日志
     * @param auditLog 操作日志
     * @return 持久化后带id的操作日志
     */
    public AuditLog logFail(AuditLog auditLog) {
        return this.auditLogDao.logFail(auditLog);
    }

    // 可选依赖，如果不需要使用 log 功能的时候，该依赖可选
    @Autowired
    public void setAuditLogDao(AuditLogDao auditLogDao) {
        this.auditLogDao = auditLogDao;
    }
}
