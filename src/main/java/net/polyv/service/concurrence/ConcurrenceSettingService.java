package net.polyv.service.concurrence;

import io.swagger.annotations.ApiOperation;
import net.polyv.exception.concurrence.ConcurrenceSettingException;
import net.polyv.exception.deposit.AccountDepositNoEnoughException;
import net.polyv.model.data.concurrence.ConcurrenceOverstepMessageDTO;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.vo.stat.PeakConcurrenceWarnVO;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.common.PageDataVO;
import net.polyv.web.model.concurrence.ConcurrenceOverstepRequest;
import net.polyv.web.model.concurrence.ConcurrenceSettingAddVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingCancelVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingCountVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingFreezeAmountCalcInputVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingFreezeAmountCalcResultVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingListVO;
import net.polyv.web.model.concurrence.ConcurrenceSettingVO;

import java.util.Date;
import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 并发时段相关服务
 * <AUTHOR>
 * @since 2020/5/14
 */
public interface ConcurrenceSettingService {
    
    /**
     * 添加并发时段
     * @param addVO 添加参数
     * @return 操作结果
     * @throws ConcurrenceSettingException  并发时段添加失败异常
     * @throws AccountDepositNoEnoughException  账户额度不足无法创建并发时段
     */
    CommonOperateResultVO add(ConcurrenceSettingAddVO addVO) throws ConcurrenceSettingException, AccountDepositNoEnoughException;

    /**
     * 取消并发时段设置
     * @param cancelVO 取消参数
     * @return 操作结果
     */
    CommonOperateResultVO cancel(ConcurrenceSettingCancelVO cancelVO);

    /**
     * 列表方法时段
     * @param listVO 列表入参
     * @return 分页结果
     */
    PageDataVO<ConcurrenceSettingVO> list(ConcurrenceSettingListVO listVO);

    /**
     * 计算并发时段需要冻结的金额
     * @param inputVO 并发时段入参
     * @return 需要冻结的金额结果
     */
    ConcurrenceSettingFreezeAmountCalcResultVO calcFreezeAmount(ConcurrenceSettingFreezeAmountCalcInputVO inputVO);

    /**
     * 统计用户的生效中、待生效并发时段
     * @param inputVO 用户信息
     * @return 用户的生效中、待生效并发时段信息
     */
    ConcurrenceSettingCountVO count(CustomerInfoGetInputVO inputVO);

    /**
     * 根据客户id获取所有并发时段
     * @param customerId 客户id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 并发时段记录
     */
    List<ConcurrenceSettingVO> listAll(String customerId, Date startDate, Date endDate);

    /**
     * 根据日期和并发模式查找对应的客户id
     * @param currentDay 指定日期
     * @param mode 并发模式
     * @return 客户id列表
     */
    List<String> listCurrentEffectiveUserIds(Date currentDay, Integer mode);

    PageDataVO<ConcurrenceSettingVO> page(ConcurrenceSettingListVO listVO);


    /**
     * POP-599 超并发查询
     * <p>
     * 统计昨日并发
     * 机制一：超并发提醒收费
     * <p>
     * 客户实际并发大于并发套餐预设并发
     * 客户实际并发大于峰值并发中剩余并发
     * @param coq 请求参数
     * @return 提醒实体类
     */
    List<ConcurrenceOverstepMessageDTO> concurrenceOverstepRemind(ConcurrenceOverstepRequest coq);
    
    /**
     * 普通用户并发生效（往直播打套餐）
     * @param setting
     * @param syncClearing : 直播是否同步结算添加并发接口  0：否 1：是
     */
    void startConcurrence(LiveCustomerConcurrenceSetting setting, int syncClearing);
    
    
    /**
     * 并发结束
     * @param setting
     */
    void endConcurrence(LiveCustomerConcurrenceSetting setting);
    
    /**
     * 并发是否可用
     * @param unionId
     * @return
     */
    boolean isConcurrenceAvailable(String unionId, String liveUserId) throws Exception;
    
    /**
     * 获取峰值并发用量不足告警数据
     * @param date
     * @return
     */
    ResponseVO<List<PeakConcurrenceWarnVO>> getPeakInsufficientList(@RequestParam("date") String date);
    
    /**
     * 获取峰值并发可用量告警数据
     * @return
     */
    ResponseVO<List<PeakConcurrenceWarnVO>> getPeakAvailableList();
}
