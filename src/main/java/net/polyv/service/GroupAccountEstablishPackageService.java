package net.polyv.service;

import org.springframework.transaction.annotation.Transactional;

import net.polyv.common.CommonResult;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.modules.pcs.api.req.AddResourceRequest;

/**
 * 集团主账号打套餐接口service
 * <AUTHOR>
 * @since 2022/06/22
 */
public interface GroupAccountEstablishPackageService {
    /**
     * 添加点播套餐
     * @param AddResourceRequest
     * @return
     */
    CommonResult addVodPackage(AddResourceRequest AddResourceRequest);

    CommonResult addVodSpace(AddResourceRequest AddResourceRequest);

    CommonResult addVodFlow(AddResourceRequest AddResourceRequest);

    CommonResult addLiveDuration(AddResourceRequest AddResourceRequest);

    CommonResult addLiveDailyConcurrence(AddResourceRequest AddResourceRequest);

    CommonResult addGuideDuration(AddResourceRequest AddResourceRequest);

    CommonResult addMicDuration(AddResourceRequest AddResourceRequest);
    
    CommonResult addChannels(AddResourceRequest AddResourceRequest);
    
    CommonResult addPeakConcurrence(AddResourceRequest AddResourceRequest);
    
    CommonResult addPrtcDuration(AddResourceRequest AddResourceRequest);
    
    CommonResult addAmount(AddResourceRequest AddResourceRequest);
    
    CommonResult enabledFunctionAccess(SalesOpportunities salesOpportunities);
    
    @Transactional(rollbackFor = Exception.class)
    void statGroupDurationConcurrenceBillingUser(String startDate, String endDate);
}


