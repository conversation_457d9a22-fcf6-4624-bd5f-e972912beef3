package net.polyv.service;

import com.alibaba.excel.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.polyv.config.PcsConfig;
import net.polyv.rest.client.vod.user.UnionUserClient;
import net.polyv.rest.model.vod.gray.OperateVodGrayInputVO;
import net.polyv.rest.model.vod.user.CustomerSearchDTO;
import net.polyv.rest.model.vod.user.UserDetail;
import net.polyv.service.vod.VodAmountBillListService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 灰度测试 逻辑
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 *         created by 2021/6/4
 */
@Service
@Slf4j
public class GrayTestService {
    
    public static final String GRAY_TEST_CUSTOMER_ID_TEST_KEY = "pcs:cache:grayTest:customerId";
    
    public static final String GRAY_TEST_CUSTOMER_ID_TEST_KEY_SET = "pcs:cache:grayTest:customerId:set";
    
    @Autowired
    StringRedisTemplate redis;
    
    public static final int ADD_GRAY_USER = 1;
    public static final int DEL_GRAY_USER = 0;
    
    /**
     * 按首字符切换灰度开关是否开启,默认关闭
     */
    @Value("${pcs.switch.grayUser.firstCharacter:false}")
    private boolean firstCharacterGrayUserSwitch;
    /**
     * 分批切换灰度，区别于ADD_GRAY_USER，按用户首字符切换，缓存只保留首字符
     */
    public static final int SWITCH_GRAY_USER = 2;
    
    private final ValueOperations<String, String> valueOperations;
    private final UnionUserClient unionUserClient;
    
    
    @Autowired
    public GrayTestService(RedisTemplate<String, String> redisTemplate, UnionUserClient unionUserClient) {
        this.valueOperations = redisTemplate.opsForValue();
        this.unionUserClient = unionUserClient;
    }
    
    @Resource
    private PcsConfig pcsConfig;
    @Resource
    private VodAmountBillListService vodAmountBillListService;
    
    @Deprecated
    public List<String> getUserIdList() {
        
        String listStr = valueOperations.get(GRAY_TEST_CUSTOMER_ID_TEST_KEY);
        if (StringUtils.isBlank(listStr)) {
            return Collections.emptyList();
        }
        
        return Arrays.asList(listStr.split(","));
    }
    
    public boolean isFirstCharacterGrayUserSwitch() {
        return firstCharacterGrayUserSwitch;
    }
    
    public Set<String> getGrayUserIdList() {
        return redis.opsForSet().members(GRAY_TEST_CUSTOMER_ID_TEST_KEY_SET);
    }
    
    /**
     * 判断用户是否在灰度名单中
     * 注意：这里需要兼容首字符和完整的用户id场景
     * @param customerId
     * @return
     */
    private boolean isGrayUser(String customerId) {
    
        if (StringUtils.isBlank(customerId)) {
            return false;
        }
    
        Long size = redis.opsForSet().size(GRAY_TEST_CUSTOMER_ID_TEST_KEY_SET);
        if (size == null || size <= 0) {
            return false;
        }
    
        Boolean isMember;
        //首字符开关开启，如果首字符命中，则直接返回，否则再查询一次完整名单
        if (firstCharacterGrayUserSwitch) {
            //判断首字符
            String firstCharacter = customerId.substring(0, 1);
            isMember = redis.opsForSet().isMember(GRAY_TEST_CUSTOMER_ID_TEST_KEY_SET, firstCharacter);
            if (isMember != null && isMember) {
                return true;
            }
        }
        //完整的用户id
        isMember = redis.opsForSet().isMember(GRAY_TEST_CUSTOMER_ID_TEST_KEY_SET, customerId);
        return isMember != null && isMember;
    }
    
    public void addGrayUser(String customerId) {
        redis.opsForSet().add(GRAY_TEST_CUSTOMER_ID_TEST_KEY_SET, customerId);
    }
    
    public void batchAddGrayUser(String... customerIds) {
        redis.opsForSet().add(GRAY_TEST_CUSTOMER_ID_TEST_KEY_SET, customerIds);
    }
    
    public void removeGrayUser(String customerId) {
        redis.opsForSet().remove(GRAY_TEST_CUSTOMER_ID_TEST_KEY_SET, customerId);
    }
    
    public void batchRemoveGrayUser(String... customerIds) {
        if (Objects.isNull(customerIds)) {
            return;
        }
        redis.opsForSet().remove(GRAY_TEST_CUSTOMER_ID_TEST_KEY_SET, customerIds);
    }
    
    public boolean isUseAmount(String customerId) {
        return vodAmountBillListService.isUseAmountBill(customerId);
    }
    
    public boolean isGrayTestUser(String customerId) {
        return pcsConfig.isGrayUserOpen() || this.isGrayUser(customerId);
    }

//    /**
//     * 操作灰度名单
//     * @param operateType 操作类型 {@link #ADD_GRAY_USER}添加, {@link #DEL_GRAY_USER}删除
//     * @param customerIds 顾客id
//     */
//    @Deprecated
//    public void operateGrayUserList(Integer operateType, String... customerIds) {
//
//        OperateVodGrayInputVO operateVodGrayInputVO = new OperateVodGrayInputVO();
//        operateVodGrayInputVO.setOperation(operateType);
//
//        for (String customerId : customerIds) {
//            String unionId = customerId2VodUserId(customerId);
//            if (StringUtils.isEmpty(unionId)) {
//                throw new IllegalArgumentException(String.format("查询用户不存在，customerIds=%s", customerId));
//            }
//            operateVodGrayInputVO.setUserId(unionId);
//            Set<String> customerIdSet = new HashSet<>(this.getUserIdList());
//            if (operateType.equals(ADD_GRAY_USER)) {
//                log.info("添加灰度名单，customerId={}", customerId);
//                customerIdSet.add(customerId);
//            } else if (operateType.equals(DEL_GRAY_USER)) {
//                log.info("删除灰度名单，customerId={}", customerId);
//                customerIdSet.remove(customerId);
//            } else {
//                throw new IllegalArgumentException(String.format("操作类型错误，operateType=%s, customerIds=%s", operateType,
//                        Arrays.toString(customerIds)));
//            }
//            unionUserClient.operateVodGrayList(operateVodGrayInputVO);
//            valueOperations.set(GRAY_TEST_CUSTOMER_ID_TEST_KEY, String.join(",", customerIdSet));
//        }
//
//    }

    /**
     * 操作灰度名单
     * @param operateType 操作类型 {@link #ADD_GRAY_USER}添加, {@link #DEL_GRAY_USER}删除
     * @param customerIds 顾客id
     */
    public void operateGrayUserListNew(Integer operateType, String... customerIds) {
    
        //点播全量灰度开关开启后，不允许操作灰度名单
        if (pcsConfig.isGrayUserOpen()) {
            log.info("点播全量灰度开关开启后，不允许操作灰度名单,optType={},customerIds={}", operateType, customerIds);
            return;
        }
    
        OperateVodGrayInputVO operateVodGrayInputVO = new OperateVodGrayInputVO();
        operateVodGrayInputVO.setOperation(operateType);
    
        for (String customerId : customerIds) {
            //分批切换灰度不需要查询用户id，默认用customerId，首字符
            String unionId = operateType.equals(SWITCH_GRAY_USER) ? customerId : customerId2VodUserId(customerId);
            if (StringUtils.isEmpty(unionId)) {
                throw new IllegalArgumentException(String.format("查询用户不存在，customerIds=%s", customerId));
            }
    
            operateVodGrayInputVO.setUserId(unionId);
            if (operateType.equals(ADD_GRAY_USER)) {
                log.info("添加灰度名单，customerId={}", customerId);
                this.addGrayUser(customerId);
            } else if (operateType.equals(DEL_GRAY_USER)) {
                log.info("删除灰度名单，customerId={}", customerId);
                this.removeGrayUser(customerId);
            } else if (operateType.equals(SWITCH_GRAY_USER)) {
                log.info("分配切换灰度名单，首字符={}", customerId);
                //添加首字符
                this.addGrayUser(customerId);
            } else {
                throw new IllegalArgumentException(
                        String.format("操作类型错误，operateType=%s, customerIds=%s", operateType,
                                Arrays.toString(customerIds)));
            }

            unionUserClient.operateVodGrayList(operateVodGrayInputVO);
        }

    }

    public void copyGrayUserToSet() {

        List<String> oldList =  getUserIdList() ;
        if(CollectionUtils.isEmpty(oldList)){
            return ;
        }

        List<List<String>> lists = Lists.partition(oldList , 1000) ;
        for(List<String> list : lists){
            if(CollectionUtils.isEmpty(list)){
                return ;
            }
            this.batchAddGrayUser(list.toArray(new String[list.size()]));
        }

    }
    
    
    private String customerId2VodUserId(String customerId) {
        
        CustomerSearchDTO searchDTO = new CustomerSearchDTO();
        searchDTO.setUnionIds(customerId);
        List<UserDetail> detailList = unionUserClient.listCustomer(searchDTO).getData();
        if (CollectionUtils.isEmpty(detailList)) {
            return "";
        }
        return detailList.get(0).getUserId();
    }
}
