package net.polyv.service;


import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.ResourceExpireRecordAddRequest;
import net.polyv.modules.pcs.api.req.ResourceExpireRecordGetRequest;
import net.polyv.modules.pcs.api.vo.ResourceExpireRecordResultVO;

import java.util.List;
import java.util.Map;

/**
 * 资源充值过期记录
 *
 * <AUTHOR>
 * @since 2022/08/09
 */
public interface ResourceDepositExpireRecordService {

    boolean add(ResourceExpireRecordAddRequest request);

    ResponseVO<Map<String, List<ResourceExpireRecordResultVO>>>  getExpireRecord(ResourceExpireRecordGetRequest request);
}
