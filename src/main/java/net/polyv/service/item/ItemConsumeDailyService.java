package net.polyv.service.item;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/6/21
 */
public interface ItemConsumeDailyService {
    
    /**
     * 判断消耗量数据是否存在
     * @param date
     * @param itemId
     * @return
     */
    boolean isDataExist(Date date, Integer itemId);

    boolean isDataExistWithGroupAccount(Date date, Integer itemId,String isGroupAccount);
    
    boolean isDataExist(Date date, String itemCode, String itemScaleCode);
}
