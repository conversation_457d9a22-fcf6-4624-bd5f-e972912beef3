package net.polyv.service.item;

import java.util.Date;
import java.util.List;

import net.polyv.web.model.account.CustomerItemStateDetailVO;
import net.polyv.web.model.account.CustomerItemStateResultVO;
import net.polyv.web.model.account.item.CurrentCustomerItemStateDetailInputVO;
import net.polyv.web.model.account.item.CustomerItemInfoGetInputVO;

/**
 * 计费项目相关逻辑
 * <AUTHOR>
 * @since 12/05/2020
 */
public interface ItemService {

    /**
     * 获取用户的某个计费项目的可用状态
     * 剩余用量+授信额度+余额 都已经不足则返回不可用状态
     * @param inputVO 检索入参
     * @return 用户的某个计费项目的可用状态
     */
    CustomerItemStateResultVO getCustomerItemState(CustomerItemInfoGetInputVO inputVO);
    
    /**
     * 批量获取特定用户特定计费项的可用状态
     */
    List<CustomerItemStateDetailVO> batchGetCustomerItemStateDetail(CurrentCustomerItemStateDetailInputVO input);
    
    /**
     * 直播资源是否可用
     * @param unionId
     * @param liveUserId
     * @return
     */
    Boolean isLiveResourceAvailable(String unionId, String liveUserId);
    
    /**
     * 获取点播套餐过期时间
     */
    Date getVodPackageExpireDate(String customerId);

    long getDailySpaceAlterationByUnionId(String unionId);
}
