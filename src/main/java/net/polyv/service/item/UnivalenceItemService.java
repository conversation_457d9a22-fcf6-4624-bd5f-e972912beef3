package net.polyv.service.item;

import java.util.List;

import net.polyv.web.model.account.GetCustomerStateInputVO;
import net.polyv.web.model.itemsetting.input.ItemUnivalenceGetInput;
import net.polyv.web.model.salesopportunities.data.BillingItemVO;

/**
 * 计费项单价相关服务
 * <AUTHOR>
 * @since 2020/6/4
 */
public interface UnivalenceItemService {
    
    /**
     * 根据客户id获取用户所有计费项的数据
     * @param customerId 客户的id
     * @param itemIds 计费项目id列表
     * @return 计费项列表
     */
    List<BillingItemVO> getUserBillingItems(String customerId, List<Integer> itemIds);
    
    
    /**
     * 根据客户id获取用户所有资源点计费项的数据
     * @param customerId 客户的id
     * @param itemIds 计费项目id列表
     * @return 计费项列表
     */
    List<BillingItemVO> getUserResourcePointBillingItems(String customerId, List<Integer> itemIds);
    
    /**
     * 获取客户当前生效的计费项设置，海外分钟数系数和7人以上连麦分钟数系数做了特殊的覆盖
     * 用于销售机会修改单价页面展示使用
     * @param customerId 客户id
     * @return 计费项列表
     */
    List<BillingItemVO> getUserBillingItemsWithSpecialConig(String customerId);
    
    /**
     * 根据直播id/email获取用户所有计费项的数据
     * 直播id优先级 > email
     */
    List<BillingItemVO> getUserBillingItems(GetCustomerStateInputVO inputVO);
    
    /**
     * 根据直播id/email，以及计费项ID，获取计费项数据
     * 直播id优先级 > email
     * @param itemIds 计费项ID列表，如果为空，则不限制
     */
    List<BillingItemVO> getUserBillingItems(GetCustomerStateInputVO inputVO, List<Integer> itemIds);
    
    /**
     * 根据直播id/email获取用户资源点计费项的数据
     * 直播id优先级 > email
     */
    List<BillingItemVO> getUserResourcePointBillingItems(GetCustomerStateInputVO inputVO);
    
    
    
    /**
     * 根据条件获取计费项的单价
     * @param input 输入条件
     * @return 计费项单价
     */
    Long getItemUnivalence(ItemUnivalenceGetInput input);
    
    /**
     * 根据resourceCode获取客户的基础计费项
     * @param customerId 客户id
     * @param resourceCode 资源code
     * @return 计费项
     */
    BillingItemVO getUserBaseItemByResourceCode(String customerId, String resourceCode);
    
    
    /**
     * 资源点结算排除的主营业务resourceCode列表
     * @return
     */
    List<String> getResourcePointSettlementResourceCodeExcluded();

    /**
     * 是否使用资源点结算的业务
     * @param resourceCode
     * @return
     */
    boolean isResourcePointSettlementResourceCode(String resourceCode);
}
