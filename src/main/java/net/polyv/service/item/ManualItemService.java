package net.polyv.service.item;

import java.util.List;

import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.itemsetting.data.ManualItemVO;
import net.polyv.web.model.itemsetting.input.ManualItemAlterationInputVO;
import net.polyv.web.model.itemsetting.input.ManualItemGetInputVO;

/**
 * 自定义计费项的服务
 * <AUTHOR>
 * @since 2020/7/15
 */
public interface ManualItemService {
    
    CommonOperateResultVO saveManualItems(ManualItemAlterationInputVO inputVO);
    
    List<ManualItemVO> listManualItems();
    
    /**
     * 根据条件获取自定义计费项
     */
    ManualItemVO getManualItem(ManualItemGetInputVO inputVO);
    
    /**
     * 根据条件获取全部计费项
     */
    ManualItemVO getItem(ManualItemGetInputVO inputVO);
}
