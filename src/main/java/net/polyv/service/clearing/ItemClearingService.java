package net.polyv.service.clearing;

import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.item.ResourceCodeConst;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/19
 */
public interface ItemClearingService {
    
    void clearing();
    
    /**
     * @param resourceCodeConsts
     * @param isGroup:是否集团主账号 Y:是 N:否
     */
    void clearingWithResourceCodes(List<ResourceCodeConst> resourceCodeConsts, String isGroup);
    
    void clearingWithItemCodes(List<ItemCodeConst> itemCodeConsts, String isGroup);
}
