package net.polyv.service;

import java.util.List;

import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.VodFlowPackageLimitRequest;
import net.polyv.modules.pcs.api.req.VodUnlimitedFlowPackageListRequest;
import net.polyv.modules.pcs.api.vo.VodUnlimitedFlowPackageVO;

/**
 * 点播无限版流量套餐service
 * <AUTHOR>
 * @since 2023/03/12
 */
public interface VodUnlimitedFlowPackageService {
    Pager<VodUnlimitedFlowPackageVO> list(VodUnlimitedFlowPackageListRequest request);
    
    ResponseVO<Object> markIsLimit(VodFlowPackageLimitRequest request);
    
    void preStatisticVodUnlimitedPackage();
    
    /**
     * 点播无限版套餐阈值告警数据列表
     * @return
     */
    List<VodUnlimitedFlowPackageVO> periodFlowWarnNotifyList();
    
    /**
     * 点播无限版流量包告警数据列表
     * @return
     */
    List<VodUnlimitedFlowPackageVO> permanentFlowWarnNotifyList();
}
