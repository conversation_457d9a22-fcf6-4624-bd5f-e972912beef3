package net.polyv.service.business;

import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessGetWaitOpenRequest;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenRequest;
import net.polyv.modules.pcs.api.vo.OnlineBusinessGetWaitOpenVO;

import java.util.List;

/**
 * 在线业务
 */
public interface OnlineBusinessService {


    ResponseVO<Void> open(OnlineBusinessOpenRequest request);
    
    /**
     * 获取待开通的在线业务
     */
    List<OnlineBusinessGetWaitOpenVO> getWaitOpenRecord(OnlineBusinessGetWaitOpenRequest request);
}
