package net.polyv.service.business;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.business.BusinessOrderAddSpecDTO;
import net.polyv.model.data.business.BusinessPackageSpecificationDTO;
import net.polyv.model.data.business.BusinessPackageSpecificationInfo;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.modules.pcs.api.req.finance.FinanceBusinessOrderOpenDTO;
import net.polyv.rest.model.finance.GetPackageInfoDataVO;

import java.util.Date;
import java.util.List;

/**
 * @description: 商机套餐规格服务
 * @author: Neo
 * @date: 2021-11-03
 */
public interface BusinessPackageSpecificationService {


    /**
     * 通过商机id查询商机规格信息
     *
     * @param soId 订单号
     * @return 规格信息
     * @throws ClearingSystemException 异常
     */
    CommonResult<BusinessPackageSpecificationInfo> getPackageSpecInfo(String soId) throws ClearingSystemException;


    /**
     * 保存商机套餐规格配置
     *
     * @param boas          规格配置信息实体
     * @param operateUserId 操作人
     * @return 保存结果
     * @throws ClearingSystemException 异常
     */
    CommonResult savePackageSpecification(BusinessOrderAddSpecDTO boas, String operateUserId) throws ClearingSystemException;

    /**
     * 显示商机套餐规格配置
     *
     * @param soId 商机id
     * @return 结果
     * @throws ClearingSystemException 异常
     */
    CommonResult<BusinessPackageSpecificationDTO> showBpsInfo(String soId) throws ClearingSystemException;

    /**
     * 保存财务套餐规格配置
     *
     * @param boas          财务套餐信息
     * @param operateUserId 操作用户id
     * @return {@link CommonResult}
     * @throws ClearingSystemException 异常
     * <AUTHOR>
     * @date 2022/8/8
     */
    CommonResult<BusinessPackageSpecification> saveFinancePackageSpecification(FinanceBusinessOrderOpenDTO boas, String operateUserId) throws ClearingSystemException;

    /**
     * 保存财务基础的套餐规格设置
     * 时间，用户
     *
     * @param bps           保存实体
     * @param operateUserId 操作人
     * @return 结果
     * @throws ClearingSystemException 异常
     */
    CommonResult<BusinessPackageSpecification> saveBasicFinancePackageSpecification(BusinessPackageSpecification bps, String operateUserId) throws ClearingSystemException;

    /**
     * 获取套餐下拉数据
     *
     * @param billingPlanCode 产品编码
     * @param bpsInfo         业务规格vo 可为null
     * @return 业务规格vo
     * <AUTHOR>
     * @date 2022/9/19
     */
    CommonResult<BusinessPackageSpecificationInfo> getSelectOption(String billingPlanCode, BusinessPackageSpecificationInfo bpsInfo);


    /**
     * 代替旧财务获取销售机会套餐信息
     *
     */
    List<GetPackageInfoDataVO> getSoPackageInfo(List<String> soIdList);


    /**
     * 组装套餐信息显示
     *
     * @param so  销售机会
     * @param bps 规格配置
     * @return 套餐信息
     */
    String getPackageDesc(SalesOpportunities so, BusinessPackageSpecification bps);
    
    
    /**
     * 更新规格配置的合同ID
     */
    void updateContractIdById(BusinessPackageSpecification bps, String contractId);
    
    /**
     * 获取用户生效中的点播无限版套餐
     * @param customerId
     * @param today
     * @return
     */
    BusinessPackageSpecification findVodUnlimitedFlowPackageByCustomerId(String customerId, Date today);
    
    /**
     * 获取所有用户生效中的点播无限版套餐
     * @return
     */
    List<BusinessPackageSpecification> findVodUnlimitedFlowPackage();
}
