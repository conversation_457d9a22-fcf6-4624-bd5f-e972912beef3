package net.polyv.service.business;

import java.util.List;
import java.util.Map;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.dict.SystemDictionaryVO;
import net.polyv.model.entity.primary.business.SystemDictionary;

/**
 * @description: 系统数据字典服务
 * @author: Neo
 * @date: 2021-11-04
 */
public interface SystemDictionaryService {
    
    
    /**
     * 根据枚举类code获取字典值
     * @param dictCode 数据字典code
     * @return 数据字典值
     * @throws ClearingSystemException 异常
     */
    CommonResult<List<SystemDictionary>> getDictListByCod(String dictCode) throws ClearingSystemException;
    
    /**
     * 根据枚举类code获取字典值
     * @param dictCodes 数据字典code 逗号分割
     * @return 数据字典值
     * @throws ClearingSystemException 异常
     */
    CommonResult<Map<String, List<SystemDictionaryVO>>> getDictListByCodes(String dictCodes)
            throws ClearingSystemException;
    
    /**
     * 根据枚举类code和pid获取字典值
     * 级联使用
     *
     * @param dictCode 数据字典code
     * @param pid      父id
     * @return 数据字典值
     * @throws ClearingSystemException 异常
     */
    CommonResult<List<SystemDictionary>> getDictListByCodAndPid(String dictCode, Long pid) throws ClearingSystemException;

    /**
    * 保存或更新字典表
    * @param systemDictionary 字典
    * @return {@link SystemDictionary}
    * <AUTHOR>
    * @date 2022/7/25
    */
    List<SystemDictionary>   saveOrUpdateSystemDict(List<SystemDictionary> systemDictionary);
    
    
}
