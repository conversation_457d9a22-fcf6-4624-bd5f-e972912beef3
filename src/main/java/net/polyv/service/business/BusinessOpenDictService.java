package net.polyv.service.business;

import net.polyv.modules.pcs.api.vo.dict.PreOpenLayoutCodeVO;

/**
 * 业务开通
 * <AUTHOR>
 * @date 2022/9/1 9:17
 */
public interface BusinessOpenDictService {
 
    /**
    *  根据字典获取layoutCode
    * @param accountType 账号类型 {@see AccountTypeEnum}>
     * @param businessType 业务类型
    * @param businessOpenType 业务开通类型
    * @param productType 产品类型
    * @return {@link String}
    * <AUTHOR>
    * @date 2022/9/1
    */
    PreOpenLayoutCodeVO getLayoutCodeByDict(String accountType,String businessType,String businessOpenType,String productType);

}
