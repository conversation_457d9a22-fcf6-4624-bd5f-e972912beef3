package net.polyv.service.business;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.SysTypeConst;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.model.data.salesopportunities.UserFunctionDO;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.modules.common.stereotype.SwitchEnum;

/**
 * 业务功能服务-专用于调用业务系统
 * <AUTHOR>
 * @date 2022/11/11 15:08
 */
@Component
@Slf4j
public class BusinessFunctionService {
    @Resource
    private BillingItemRepository billingItemRepository;
    
    
    public List<UserFunctionDO> functionProcess(List<BillingItem> allByCodeIn, boolean isOpen) {
        if (CollectionUtils.isEmpty(allByCodeIn)) {
            return null;
        }
   /*     Map<String, String>  codeMap = allByCodeIn.stream()
                .collect(Collectors.toMap(BillingItem::getCode, BillingItem::getProduction));*/
        List<UserFunctionDO> dos = new ArrayList<>();
        allByCodeIn.forEach(item -> {
            /*     String production = codeMap.get(code);*/
            UserFunctionDO userFunctionDO = new UserFunctionDO();
            userFunctionDO.setValue(isOpen ? SwitchEnum.Y.getCode() : SwitchEnum.N.getCode());
            userFunctionDO.setCode(item.getCode());
            userFunctionDO.setExtraValue("");
            userFunctionDO.setSys(SysTypeConst.findByLabel(item.getProduction()));
            dos.add(userFunctionDO);
        });
        return dos;
    }
}
