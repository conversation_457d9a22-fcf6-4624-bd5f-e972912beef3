package net.polyv.service.business;


import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.BusinessChangeRecordSaveRequest;
import net.polyv.modules.pcs.api.req.ContractExpireDateChangeApproveRequest;
import net.polyv.modules.pcs.api.req.ContractExpireDateChangeRequest;

/**
 * 业务变更service
 * <AUTHOR>
 * @date 2023-12-04
 */

public interface BusinessChangeRecordService {
    
    /**
     * 合同到期时间变更预检
     * @param request
     * @return
     */
    ResponseVO<String> changeContractExpireDatePreCheck(ContractExpireDateChangeRequest request);
    
    /**
     * 保存业务变更记录
     * @param request
     * @return
     */
    ResponseVO<String> save(BusinessChangeRecordSaveRequest request);
    
    /**
     * 变更合同到期时间审批回调处理
     * @param request
     * @return
     */
    ResponseVO<String> changeContractExpireDateApprove(ContractExpireDateChangeApproveRequest request);
    
    
}
