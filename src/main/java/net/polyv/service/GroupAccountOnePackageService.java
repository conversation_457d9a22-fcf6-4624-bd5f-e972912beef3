package net.polyv.service;

import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.BusinessBillingPlanCodeEnum;
import net.polyv.constant.examinationDonate.ResourceTypeEnum;
import net.polyv.constant.finance.StatusValueEnum;
import net.polyv.model.dto.GroupAccountOnePackageDTO;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.model.entity.primary.finance.FinanceContractSpecificationEntity;
import net.polyv.modules.pcs.api.stereotype.ResourceEnum;
import net.polyv.rest.client.group.GroupOneOtherPackageClient;
import net.polyv.rest.client.group.GroupOnePackageClient;
import net.polyv.service.impl.examinationDonate.ResourceLogicService;
import net.polyv.web.model.WrappedResponse;

/**
 * 集团账号1.0 服务
 * <AUTHOR>
 * @date 2022/8/4 15:20
 */
@Slf4j
@Component
public class GroupAccountOnePackageService {
    @Resource
    private GroupOneOtherPackageClient groupOneOtherPackageClient;
    @Resource
    private GroupOnePackageClient groupOnePackageClient;
    @Resource
    private ResourceLogicService resourceLogicService;
    
    //支持： 直播分钟数套餐 +直播并发套餐+点播套餐 + 连麦分钟数
    public CommonResult addGroupOneResource(FinanceContractSpecificationEntity entity,
            BusinessPackageSpecification packageSpecification, String contractId) {
        String accountType = packageSpecification.getAccountType();
        String billingPlanCode = entity.getBillingPlanCode();
        if (!AccountTypeEnum.GROUP1.getCode().equals(accountType)) {
            return CommonResult.fail("非集团账号1.0不可执行打套餐！");
        }
        boolean already = StatusValueEnum.YES.getValue().equals(packageSpecification.getIsAlreadyActivated());
        if (already) {
            return CommonResult.ok("已经开通过，不执行开通！");
        }
        GroupAccountOnePackageDTO packageDTO = buildParamsByBillPlanCode(billingPlanCode, packageSpecification);
        if (Objects.isNull(packageDTO)) {
            return CommonResult.fail("无法根据业务编码创建套餐参数：billingPlanCode={}", billingPlanCode);
        }
        //转换一下单位
        GroupAccountOnePackageDTO.convertUnit(packageDTO);
        WrappedResponse response;
        //设置一下合同id给规格配置，让他在创建套餐信息时填充合同id
        packageSpecification.setContractId(contractId);
        log.info("调用集团账号1.0套餐接口参数：{}", JsonUtil.beanToString(packageDTO).orElse(""));
        if (BusinessBillingPlanCodeEnum.LIVE_MIC_DURATION.getCode().equals(billingPlanCode)) {
            response = this.groupOnePackageClient.addGroupMicPackage(packageDTO.getGroupId(), packageDTO.getDuration());
        } else if (BusinessBillingPlanCodeEnum.ONLINE_PACKAGE.getCode().equals(billingPlanCode) ||
                BusinessBillingPlanCodeEnum.VOD_PACKAGE.getCode().equals(billingPlanCode) ||
                BusinessBillingPlanCodeEnum.LIVE_DURATION.getCode().equals(billingPlanCode) ||
                BusinessBillingPlanCodeEnum.LIVE_CONCURRENT.getCode().equals(billingPlanCode)) {
            response = this.groupOneOtherPackageClient.addGroupPackage(packageDTO);
            vodResourceSetToPcs(entity, packageSpecification);
        } else {
            return CommonResult.fail("无法支持{},打集团1.0套餐", billingPlanCode);
        }
        log.info("打集团账号1.0套餐结果：{}", response);
        return response.isSuccess() ? CommonResult.ok() : CommonResult.fail(response.getMessage());
    }
    
    public void vodResourceSetToPcs(FinanceContractSpecificationEntity entity,
            BusinessPackageSpecification specification) {
        String billingPlanCode = entity.getBillingPlanCode();
        String accountType = specification.getAccountType();
        if (BusinessBillingPlanCodeEnum.ONLINE_PACKAGE.getCode().equals(billingPlanCode) ||
                BusinessBillingPlanCodeEnum.VOD_PACKAGE.getCode().equals(billingPlanCode)) {
            if (Objects.nonNull(specification.getVodFlow())) {
                CommonResult commonResult = this.resourceLogicService.savePeriodResource(specification.getVodFlow(),
                        specification.getVodPackageFlowMonthsOfYear(), specification.getCustomerId(),
                        ResourceEnum.traffic.name(), specification.getValidPeriodStartDate(),
                        specification.getValidPeriodEndDate(), specification.getContractId(), accountType,
                        ResourceTypeEnum.DEPOSIT.getCode());
                log.info("充值集团1.0账号的流量数据结果：{}", CommonResult.isOk(commonResult));
            }
            if (Objects.nonNull(specification.getVodSpace())) {
                CommonResult commonResult = this.resourceLogicService.savePeriodResource(specification.getVodSpace(),
                        specification.getVodPackageFlowMonthsOfYear(), specification.getCustomerId(),
                        ResourceEnum.space.name(), specification.getValidPeriodStartDate(),
                        specification.getValidPeriodEndDate(), specification.getContractId(), accountType,
                        ResourceTypeEnum.DEPOSIT.getCode());
                log.info("充值集团1.0账号的空间数据结果：{}", CommonResult.isOk(commonResult));
            }
            
        }
    }
    
    
    private GroupAccountOnePackageDTO buildParamsByBillPlanCode(String billingPlanCode,
            BusinessPackageSpecification packageSpecification) {
        if (BusinessBillingPlanCodeEnum.VOD_PACKAGE.getCode().equals(billingPlanCode)) {
            return GroupAccountOnePackageDTO.vodPackage(packageSpecification);
        }
        if (BusinessBillingPlanCodeEnum.LIVE_CONCURRENT.getCode().equals(billingPlanCode)) {
            return GroupAccountOnePackageDTO.liveConcurrency(packageSpecification);
        }
        if (BusinessBillingPlanCodeEnum.LIVE_DURATION.getCode().equals(billingPlanCode)) {
            return GroupAccountOnePackageDTO.livePackage(packageSpecification);
        }
        if (BusinessBillingPlanCodeEnum.LIVE_MIC_DURATION.getCode().equals(billingPlanCode)) {
            return GroupAccountOnePackageDTO.liveMicDuration(packageSpecification);
        }
        if (BusinessBillingPlanCodeEnum.ONLINE_PACKAGE.getCode().equals(billingPlanCode)) {
            return GroupAccountOnePackageDTO.onlinePackage(packageSpecification);
        }
        
        return null;
    }
}
