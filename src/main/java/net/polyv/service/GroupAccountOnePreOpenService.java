package net.polyv.service;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.finance.ContractOriginEnum;
import net.polyv.constant.finance.ContractTypeEnum;
import net.polyv.dao.primary.finance.FinanceContractSpecificationRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.model.entity.primary.finance.FinanceContractSpecificationEntity;
import net.polyv.modules.pcs.api.req.finance.FinanceBusinessOrderOpenDTO;
import net.polyv.service.business.BusinessPackageSpecificationService;
import net.polyv.service.finance.FinanceContractService;
import net.polyv.service.finance.FinanceContractSpecificationService;
import net.polyv.util.BeanUtil;
import net.polyv.web.model.finance.input.AddPreOpenContractDTO;

/**
 * 集团1.0逻辑
 * <AUTHOR>
 * @date 2022/8/4 18:22
 */
@Slf4j
@Component
public class GroupAccountOnePreOpenService {
    @Resource
    private FinanceContractService financeContractService;
    @Resource
    private BusinessPackageSpecificationService businessPackageSpecificationService;
    @Resource
    private GroupAccountOnePackageService groupAccountOnePackageService;
    @Resource
    private FinanceContractSpecificationRepository financeContractSpecificationRepository;
    @Resource
    private FinanceContractSpecificationService contractSpecificationService;
    
    //预开通集团账号1.0套餐
    @Transactional(rollbackFor = Exception.class)
    public void preOpenLogic(FinanceBusinessOrderOpenDTO openDTO) throws ClearingSystemException {
        //创建订单规格配置
        CommonResult<BusinessPackageSpecification> result = this.businessPackageSpecificationService.saveFinancePackageSpecification(
                openDTO, "Pre-Open");
        if (CommonResult.isNotOk(result)){
            log.warn("集团账号1.0预开通业务保存规格,失败：{}",result.getMsg());
            throw new ClearingSystemException("预开通失败："+result.getMsg());
        }
        BusinessPackageSpecification businessPackage = result.getData();

        // 创建合同规格配置（参数需要订单业务规格的ID，返回合同业务规格的ID）
        CommonResult<Long> addContractSpecResult = this.contractSpecificationService.addContractSpec(openDTO, businessPackage.getId());
        if (CommonResult.isNotOk(addContractSpecResult)) {
            log.warn("集团账号1.0预开通业务保存合同规格,失败：{}",addContractSpecResult.getMsg());
            throw new ClearingSystemException("预开通失败："+addContractSpecResult.getMsg());
        }
        //创建待关联合同
        AddPreOpenContractDTO parse = BeanUtil.parse(openDTO, AddPreOpenContractDTO.class);
        parse.setContractSpecificationId(addContractSpecResult.getData());
        parse.setContractWay(ContractOriginEnum.ORIGIN_PRE_OPEN.getCode());
        parse.setOrderContractType(ContractTypeEnum.NONE.getDesc());
        parse.setEmail(openDTO.getEmail());
        parse.setCompany(openDTO.getCompany());
        parse.setAccountType(openDTO.getAccountType());
        parse.setOperatorId(openDTO.getOperatorId());
        CommonResult<String>  createContract = this.financeContractService.addOriginContract(parse);
        if (CommonResult.isNotOk(createContract)){
            log.warn("集团账号1.0预开通创建预开财务合同,失败：{}",createContract.getMsg());
            throw new ClearingSystemException("预开通创建合同失败："+createContract.getMsg());
        }
        //从结果中获取合同id
        String contractId = createContract.getData();
        //获取预开通规格配置信息
        FinanceContractSpecificationEntity specification =
                financeContractSpecificationRepository.findByBusinessSpecificationId(businessPackage.getId());
        CommonResult addResourceResult = this.groupAccountOnePackageService.addGroupOneResource(specification, businessPackage,
                contractId);
        if (CommonResult.isNotOk(addResourceResult)){
            log.warn("打集团套餐失败！");
            throw  new ClearingSystemException("预开通创建集团1.0合同失败："+ addResourceResult.getMsg());
        }
        log.info("执行开通成功:{} {}", JsonUtil.beanToString(specification), JsonUtil.beanToString(businessPackage));
       }
}
