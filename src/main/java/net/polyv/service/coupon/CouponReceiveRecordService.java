package net.polyv.service.coupon;

import net.polyv.model.entity.primary.CouponReceiveRecord;
import net.polyv.web.model.common.PageDataVO;
import net.polyv.web.model.coupon.data.CustomerReceiveCouponAggDataVO;
import net.polyv.web.model.coupon.input.PageCouponReceiveRecordInput;

import java.util.Date;
import java.util.List;

public interface CouponReceiveRecordService {

    /**
     * 分页获取客户-优惠券领取 记录
     * @param input
     * @return
     */
    PageDataVO<CustomerReceiveCouponAggDataVO> pageCouponReceiveRecords(PageCouponReceiveRecordInput input);

    /**
     * 根据客户id获取对应的优惠券领取记录
     * @param customerId
     * @return
     */
    CustomerReceiveCouponAggDataVO getCouponReceiveRecordsByCustomerId(String customerId);

    /**
     * 核销 客户领取的优惠券
     * @param couponReceiveRecordId
     */
    void writeOffCouponReceiveRecord(Integer couponReceiveRecordId);

    /**
     * 获取指定日期过期的优惠券领取记录
     */
    List<CouponReceiveRecord> listExpireRecords(Date date);
}
