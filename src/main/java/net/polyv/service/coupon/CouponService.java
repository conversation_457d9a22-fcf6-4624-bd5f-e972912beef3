package net.polyv.service.coupon;

import net.polyv.web.model.coupon.data.CouponDataVO;
import net.polyv.web.model.coupon.input.CouponReceiveInput;

import java.util.List;

/**
 * 优惠券相关业务方法
 */
public interface CouponService {

    /**
     * 获取上架的优惠券
     */
    List<CouponDataVO> listReleaseCoupon();

    /**
     * 领取优惠券
     * @param input 输入参数
     */
    void receive(CouponReceiveInput input);
}
