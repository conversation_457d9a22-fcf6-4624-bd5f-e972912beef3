package net.polyv.exception.balance;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
 * 余额异常
 * <AUTHOR>
 * @since 2020/5/18
 */
public class BalanceException extends ClearingSystemRuntimeException {

    /**
	 * 
	 */
	private static final long serialVersionUID = 8956275899384546599L;

	public BalanceException() {
        super();
        super.setResponseCode(ResponseCode.BALANCE_OPERATE_FAIL);
    }

    public BalanceException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.BALANCE_OPERATE_FAIL);
    }
}
