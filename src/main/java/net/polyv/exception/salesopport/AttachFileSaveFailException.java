package net.polyv.exception.salesopport;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
 * 销售机会附件创建异常
 * <AUTHOR>
 * @since 2020/6/3
 */
public class AttachFileSaveFailException extends ClearingSystemRuntimeException {
    
    
    private static final long serialVersionUID = -5790587569212191700L;
    
    public AttachFileSaveFailException() {
        super();
        super.setResponseCode(ResponseCode.SALE_OPPORT_ATTACH_SAVE_FAIL);
    }
    
    public AttachFileSaveFailException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.SALE_OPPORT_ATTACH_SAVE_FAIL);
    }
}
