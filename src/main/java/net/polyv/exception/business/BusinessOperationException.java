package net.polyv.exception.business;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
 * 业务开通异常
 * <AUTHOR>
 * @since 2022/03/21
 */
public class BusinessOperationException extends ClearingSystemRuntimeException {

    /**
	 *
	 */
	private static final long serialVersionUID = 5311106263097496022L;

	public BusinessOperationException() {
        super();
        super.setResponseCode(ResponseCode.BUSINESS_OPERATION_ERROR);
    }

    public BusinessOperationException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.BUSINESS_OPERATION_ERROR);
    }
}
