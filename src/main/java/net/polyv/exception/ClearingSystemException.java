package net.polyv.exception;

import net.polyv.web.stereotype.ResponseCode;

/**
 * 结算系统异常
 * <AUTHOR>
 * @since 08/05/2020
 */
public class ClearingSystemException extends Exception{

    /**
	 * 
	 */
	private static final long serialVersionUID = -1743767863735006126L;
	private ResponseCode responseCode = ResponseCode.INTERNAL_SERVER_ERROR;

    public ClearingSystemException() {
        super();
    }

    public ClearingSystemException(String message) {
        super(message);
    }

    public ClearingSystemException(String message, Throwable cause) {
        super(message, cause);
    }

    public ClearingSystemException(Throwable cause) {
        super(cause);
    }

    protected ClearingSystemException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public ResponseCode getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(ResponseCode responseCode) {
        this.responseCode = responseCode;
    }

    @Override
    public String getMessage() {
        return responseCode.toString() + "," + super.getMessage();
    }
}
