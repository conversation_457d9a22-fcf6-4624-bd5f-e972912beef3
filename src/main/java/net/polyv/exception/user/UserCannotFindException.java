package net.polyv.exception.user;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

public class UserCannotFindException extends ClearingSystemRuntimeException {

    public UserCannotFindException() {
        super();
        super.setResponseCode(ResponseCode.LIVE_USER_ID_ILLEGAL);
    }

    public UserCannotFindException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.LIVE_USER_ID_ILLEGAL);
    }

    public UserCannotFindException(String message, ResponseCode code) {
        super(message);
        super.setResponseCode(code);
    }
}
