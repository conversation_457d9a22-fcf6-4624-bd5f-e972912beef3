package net.polyv.exception.config;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
 * 配置Key找不到异常
 * <AUTHOR>
 * @since 08/05/2020
 */
public class ConfigKeyNotFoundException extends ClearingSystemRuntimeException {

    /**
	 * 
	 */
	private static final long serialVersionUID = -1508388011432214973L;

	public ConfigKeyNotFoundException() {
        super();
        super.setResponseCode(ResponseCode.GLOBAL_CONFIG_KEY_NOT_FOUND);
    }
}
