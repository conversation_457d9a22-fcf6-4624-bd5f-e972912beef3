package net.polyv.exception.deposit.refund;

import net.polyv.exception.ClearingSystemException;

/**
 * 退款异常
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/22
 */
public class RefundException extends ClearingSystemException {

    public RefundException() {
        super();
    }

    public RefundException(String message) {
        super(message);
    }

    public RefundException(String message, Throwable cause) {
        super(message, cause);
    }

    public RefundException(Throwable cause) {
        super(cause);
    }

    protected RefundException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
