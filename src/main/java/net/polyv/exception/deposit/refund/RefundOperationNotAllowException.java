package net.polyv.exception.deposit.refund;

/**
 * 退款操作不允许
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/22
 */
public class RefundOperationNotAllowException extends RefundException {

    public RefundOperationNotAllowException() {
        super();
    }

    public RefundOperationNotAllowException(String message) {
        super(message);
    }

    public RefundOperationNotAllowException(String message, Throwable cause) {
        super(message, cause);
    }

    public RefundOperationNotAllowException(Throwable cause) {
        super(cause);
    }

    protected RefundOperationNotAllowException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
