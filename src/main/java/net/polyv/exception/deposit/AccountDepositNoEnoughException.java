package net.polyv.exception.deposit;

import net.polyv.web.stereotype.ResponseCode;

/**
 * 用户余额不足
 * <AUTHOR>
 * @since 2020/5/15
 */
public class AccountDepositNoEnoughException extends AccountDepositRunTimeException{

    /**
	 * 
	 */
	private static final long serialVersionUID = 8634910653623486356L;

	public AccountDepositNoEnoughException() {
        super();
        super.setResponseCode(ResponseCode.ACCOUNT_DEPOSIT_NOT_ENOUGH);
    }
}
