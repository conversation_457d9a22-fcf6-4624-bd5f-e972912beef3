package net.polyv.exception.deposit;

import net.polyv.web.stereotype.ResponseCode;

/**
 * 储值金额有误
 * <AUTHOR>
 * @since 09/05/2020
 */
public class AccountDepositAmountErrorRunTimeException extends AccountDepositRunTimeException{

    /**
	 * 
	 */
	private static final long serialVersionUID = -4871781478831515536L;

	public AccountDepositAmountErrorRunTimeException() {
        super();
        super.setResponseCode(ResponseCode.ACCOUNT_DEPOSIT_AMOUNT_ERROR);
    }

    public AccountDepositAmountErrorRunTimeException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.ACCOUNT_DEPOSIT_AMOUNT_ERROR);
    }
}
