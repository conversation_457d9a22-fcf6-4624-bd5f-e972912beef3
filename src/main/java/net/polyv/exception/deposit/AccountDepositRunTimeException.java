package net.polyv.exception.deposit;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
 * 账户储值异常
 * <AUTHOR>
 * @since 08/05/2020
 */
public class AccountDepositRunTimeException extends ClearingSystemRuntimeException {

    /**
	 * 
	 */
	private static final long serialVersionUID = 3484723593485942898L;

	public AccountDepositRunTimeException() {
        super();
        super.setResponseCode(ResponseCode.ACCOUNT_DEPOSIT_ERROR);
    }

    public AccountDepositRunTimeException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.ACCOUNT_DEPOSIT_ERROR);
    }
}
