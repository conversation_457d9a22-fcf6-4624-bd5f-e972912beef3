package net.polyv.exception;

import net.polyv.web.stereotype.ResponseCode;

/**
 * 结算系统运行时异常
 * <AUTHOR>
 * @since 08/05/2020
 */
public class ClearingSystemRuntimeException extends RuntimeException{

    /**
	 * 
	 */
	private static final long serialVersionUID = 7998490667804966837L;
	
	private ResponseCode responseCode = ResponseCode.INTERNAL_SERVER_ERROR;
	
	protected boolean needDingDingWarn = true;

    public ClearingSystemRuntimeException() {
        super();
    }

    public ClearingSystemRuntimeException(String message) {
        super(message);
    }

    public ClearingSystemRuntimeException(String message, Throwable cause) {
        super(message, cause);
    }

    public ClearingSystemRuntimeException(Throwable cause) {
        super(cause);
    }

    protected ClearingSystemRuntimeException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public ResponseCode getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(ResponseCode responseCode) {
        this.responseCode = responseCode;
    }
    
    public boolean isNeedDingDingWarn() {
        return needDingDingWarn;
    }
    
    public void setNeedDingDingWarn(boolean needDingDingWarn) {
        this.needDingDingWarn = needDingDingWarn;
    }
    
    @Override
    public String getMessage() {
        return responseCode.toString() + "," + super.getMessage();
    }
}
