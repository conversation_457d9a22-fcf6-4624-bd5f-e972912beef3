package net.polyv.exception.concurrence;

import net.polyv.web.stereotype.ResponseCode;

/**
 * 并发时段冲突
 * <AUTHOR>
 * @since 2020/5/15
 */
public class ConcurrenceSettingTimeIntervalException extends ConcurrenceSettingException {

    /**
	 * 
	 */
	private static final long serialVersionUID = -1476524255284885422L;

	public ConcurrenceSettingTimeIntervalException() {
        super();
        super.setResponseCode(ResponseCode.CONCURRENCE_SETTING_TIME_INTERVAL_CONFLICT);
        setNeedDingDingWarn(false);
    }

}
