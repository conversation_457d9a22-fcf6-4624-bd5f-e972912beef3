package net.polyv.exception.concurrence;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
 * 并发时段设置异常
 * <AUTHOR>
 * @since 2020/5/15
 */
public class ConcurrenceSettingException extends ClearingSystemRuntimeException {

    /**
	 * 
	 */
	private static final long serialVersionUID = 3585956978838660891L;

	public ConcurrenceSettingException() {
        super();
        super.setResponseCode(ResponseCode.CONCURRENCE_SETTING_ERROR);
    }

    public ConcurrenceSettingException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.CONCURRENCE_SETTING_ERROR);
    }
}
