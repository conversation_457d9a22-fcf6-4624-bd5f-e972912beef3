package net.polyv.exception.dao;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
 * <AUTHOR>
 * @since 2020/11/17
 */
public class DAOMethodInvoFailException extends ClearingSystemRuntimeException {
    
    public DAOMethodInvoFailException() {
        super();
        super.setResponseCode(ResponseCode.DAO_METHOD_INVOCATION_FAIL);
    }
    
    public DAOMethodInvoFailException(String message){
        super(message);
        super.setResponseCode(ResponseCode.DAO_METHOD_INVOCATION_FAIL);
    }
}
