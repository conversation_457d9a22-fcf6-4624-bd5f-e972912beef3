package net.polyv.exception.bill;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
*  计费项配置化异常
* <AUTHOR>
* @date 2022/7/25
*/
public class BillConfigException extends ClearingSystemRuntimeException {

    /**
	 *
	 */
	private static final long serialVersionUID = 5311106263097496022L;

	public BillConfigException() {
        super();
        super.setResponseCode(ResponseCode.BILLING_CONFIG_ERROR);
    }

    public BillConfigException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.BILLING_CONFIG_ERROR);
    }
}
