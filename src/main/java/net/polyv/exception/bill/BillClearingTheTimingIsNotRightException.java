package net.polyv.exception.bill;

import static net.polyv.web.stereotype.ResponseCode.BILL_CLEARING_TIMING_IS_NOT_RIGHT;

/**
 * 账单结算时机不对，当前无法结算该账单项
 * <AUTHOR>
 * @since 2020/5/20
 */
public class BillClearingTheTimingIsNotRightException extends BillClearingException{

    /**
	 * 
	 */
	private static final long serialVersionUID = 8506943435794449204L;

	public BillClearingTheTimingIsNotRightException() {
        super();
        super.setResponseCode(BILL_CLEARING_TIMING_IS_NOT_RIGHT);
    }
}
