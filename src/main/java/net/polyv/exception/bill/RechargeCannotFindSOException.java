package net.polyv.exception.bill;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
 * 偿还待支付，最后一笔充值找不到对应的销售机会
 * <AUTHOR>
 * @since 2021/2/23
 */
public class RechargeCannotFindSOException extends ClearingSystemRuntimeException {
    
    public RechargeCannotFindSOException() {
        super();
        super.setResponseCode(ResponseCode.PAY_FOR_UNPAID_RECHARGE_CANNOT_FIND_SO);
    }
    
    public RechargeCannotFindSOException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.PAY_FOR_UNPAID_RECHARGE_CANNOT_FIND_SO);
    }
}
