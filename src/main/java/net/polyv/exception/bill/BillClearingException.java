package net.polyv.exception.bill;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

/**
 * 账单结算异常
 * <AUTHOR>
 * @since 2020/5/20
 */
public class BillClearingException extends ClearingSystemRuntimeException {

    /**
	 * 
	 */
	private static final long serialVersionUID = 5311106263097496022L;

	public BillClearingException() {
        super();
        super.setResponseCode(ResponseCode.BILL_CLEARING_ERROR);
    }

    public BillClearingException(String message) {
        super(message);
        super.setResponseCode(ResponseCode.BILL_CLEARING_ERROR);
    }
}
