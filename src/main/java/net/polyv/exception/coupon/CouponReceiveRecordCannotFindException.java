package net.polyv.exception.coupon;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

public class CouponReceiveRecordCannotFindException extends ClearingSystemRuntimeException {

    public CouponReceiveRecordCannotFindException() {
        super();
        super.setResponseCode(ResponseCode.COUPON_RECEIVE_ID_ILLEGAL);
    }

    public CouponReceiveRecordCannotFindException(String message){
        super(message);
        super.setResponseCode(ResponseCode.COUPON_RECEIVE_ID_ILLEGAL);
    }

    public CouponReceiveRecordCannotFindException(String message, ResponseCode code) {
        super(message);
        super.setResponseCode(code);
    }
}
