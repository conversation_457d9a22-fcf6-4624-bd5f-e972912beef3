package net.polyv.exception.coupon;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

public class CouponInvalidException extends ClearingSystemRuntimeException {

    public CouponInvalidException() {
        super();
        super.setResponseCode(ResponseCode.COUPON_INVALID);
        setNeedDingDingWarn(false);
    }

    public CouponInvalidException(String message){
        super(message);
        super.setResponseCode(ResponseCode.COUPON_INVALID);
        setNeedDingDingWarn(false);
    }

    public CouponInvalidException(String message, ResponseCode code) {
        super(message);
        super.setResponseCode(code);
        setNeedDingDingWarn(false);
    }
}
