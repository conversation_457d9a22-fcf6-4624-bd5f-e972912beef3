package net.polyv.exception.coupon;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

public class CouponReceiveRecordDuplicatWriteOffException extends ClearingSystemRuntimeException {

    public CouponReceiveRecordDuplicatWriteOffException() {
        super();
        super.setResponseCode(ResponseCode.COUPON_RECEIVE_DUPLICATED_WRITE_OFF);
    }

    public CouponReceiveRecordDuplicatWriteOffException(String message){
        super(message);
        super.setResponseCode(ResponseCode.COUPON_RECEIVE_DUPLICATED_WRITE_OFF);
    }

    public CouponReceiveRecordDuplicatWriteOffException(String message, ResponseCode code) {
        super(message);
        super.setResponseCode(code);
    }
}
