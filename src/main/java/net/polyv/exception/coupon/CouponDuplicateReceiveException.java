package net.polyv.exception.coupon;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

public class CouponDuplicateReceiveException extends ClearingSystemRuntimeException {

    public CouponDuplicateReceiveException() {
        super();
        super.setResponseCode(ResponseCode.COUPON_DUPLICATED_RECEIVE);
        setNeedDingDingWarn(false);
    }

    public CouponDuplicateReceiveException(String message){
        super(message);
        super.setResponseCode(ResponseCode.COUPON_DUPLICATED_RECEIVE);
        setNeedDingDingWarn(false);
    }

    public CouponDuplicateReceiveException(String message, ResponseCode code) {
        super(message);
        super.setResponseCode(code);
        setNeedDingDingWarn(false);
    }
}
