package net.polyv.exception.coupon;

import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.web.stereotype.ResponseCode;

public class CouponCannotFindException extends ClearingSystemRuntimeException {

    public CouponCannotFindException() {
        super();
        super.setResponseCode(ResponseCode.COUPON_ID_ILLEGAL);
        setNeedDingDingWarn(false);
    }

    public CouponCannotFindException(String message){
        super(message);
        super.setResponseCode(ResponseCode.COUPON_ID_ILLEGAL);
        setNeedDingDingWarn(false);
    }

    public CouponCannotFindException(String message, ResponseCode code) {
        super(message);
        super.setResponseCode(code);
        setNeedDingDingWarn(false);
    }
}
