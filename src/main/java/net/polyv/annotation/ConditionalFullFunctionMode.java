package net.polyv.annotation;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;

import java.lang.annotation.*;

/**
 *
 * 控制本地跑服务的时候是否需要开启全部功能模式
 * spring.profiles.active = prod / polyv.pcs.full-function.enabled = true 则会开始全功能模式
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/5/8
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ConditionalOnExpression("'prod'.equals('${spring.profiles.active:dev}') or ${polyv.pcs.full-function.enabled:false}")
public @interface ConditionalFullFunctionMode {
}
