package net.polyv.annotation;

import net.polyv.constant.common.PrecisionConst;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 金额换算注解, 被注解的field必须为long型
 * <AUTHOR>
 * @since 2020/6/10
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AmountMatrixingHandle {
    // 换算系数
    int amountRatio() default PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION;
}
