package net.polyv.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 套餐模型注解
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PackageModel {
    //模型名称
    String  name() default  "" ;
    /**单位*/
    String  unit() default "";
    
    /**资源字段*/
    boolean resource() default false;
}
