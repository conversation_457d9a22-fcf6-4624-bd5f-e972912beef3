package net.polyv.annotation;

import net.polyv.constant.auditlog.AuditLogEvent;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @since: 2020/5/15
 * @description: 需要创建日志记录的自定义注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SystemAuditLog {

    // 事件类型
    AuditLogEvent event();

    // 是否需要返回生成日志操作记录后的operaId
    boolean needSetOperaId();

}
