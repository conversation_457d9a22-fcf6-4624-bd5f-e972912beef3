package net.polyv;

import net.polyv.modules.third.service.ChatApiService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.zuul.EnableZuulProxy;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@EnableJpaAuditing
@EnableAsync
@EnableDiscoveryClient
@EnableFeignClients({"net.polyv.**"})
@ComponentScan(
        basePackages = {"net.polyv.**"},
        excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = ChatApiService.class)}
)
@EnableZuulProxy
public class PcsApplication {

    public static void main(String[] args) {
        SpringApplication.run(PcsApplication.class, args);
    }

}
