package net.polyv.web.model.salesopportunities.result;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/6/23
 */
@Data
public class SalesOpportunitiesVO {

    // 销售机会记录自增id主键
    private Long id;

    // 销售机会id
    private String soId;

    // 合同id
    private String contractId;

    // 合同类型
    private String contractType;

    // 合同名称
    private String name;

    // 合同编号
    private String code;

    // 客户id
    private String customerId;

    // 公司
    private String company;

    // 邮箱
    private String email;

    // 合同状态
    private Integer status;

    // 到款日期
    private Date amountGainedDate;

    // 到款金额
    private Long amountGained;

    // 金额有效自然年数
    private Integer amountValidYears;

    // 合同总额
    private Long contractAmount;

    // 业务类型
    private Integer businessType;

    // 计费方式
    private String billingPlan;

    // 概要
    private String summary;

    // 业务开始日期
    private Date businessStartTime;

    // 业务结束日期
    private Date businessEndTime;

    // 合同开始时间
    private Date contractStartTime;

    // 合同结束时间
    private Date contractEndTime;

    // 责任销售的用户id
    private String saleUserId;

    // 责任销售的用户名
    private String saleUserName;

    // 发票类型
    private String invoiceType;

    // 收款账户
    private String acceptanceAccount;

    // 客户来源
    private String customerFrom;

    // 行业分布
    private String industryDistribution;

    // 分类
    private String type;

    // 套餐
    private String functionPackage;

    // 简介
    private String introduction;

    // 额外的属性（json格式）：授信额度，各个计费项的id、单价，赠送金额、年最低消费，附件路径
    private String ext;

    // 是否被删除
    private Integer isDel;

    // 钉钉审批实例id
    private String dingProcessId;

    // 是否为集团账号，0表示否，1表示是
    private Integer isGroupUser;
    
    // 合作主体
    private String cooperationSubject;

    /**
     * 创建用户id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
