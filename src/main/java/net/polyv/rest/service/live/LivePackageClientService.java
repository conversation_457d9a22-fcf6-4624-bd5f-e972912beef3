package net.polyv.rest.service.live;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Nullable;
import javax.annotation.Resource;


import net.polyv.constant.CustomerItemStateConst;
import net.polyv.constant.PcsFunctionConfigConst;
import net.polyv.constant.common.ZeroOneConst;
import net.polyv.constant.customer.LiveBillingType;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.item.OldPackItemCodeConst;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.*;
import net.polyv.dao.primary.custom.CustomerConfigDao;
import net.polyv.model.entity.primary.CustomerConfig;
import net.polyv.model.entity.primary.CustomerConfigConfig;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.model.entity.primary.LiveFlowPackageInfo;
import net.polyv.model.entity.primary.PcsFunctionConfig;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.modules.pcs.api.vo.SettlementConfigVO;
import net.polyv.rest.client.live.LivePackageClient;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.model.live.UserMsgVO;

import net.polyv.service.AccountDepositService;
import net.polyv.service.item.ItemService;
import net.polyv.service.settlement.SettlementConfigService;
import net.polyv.web.model.account.CustomerInfoGetInputVO;
import net.polyv.web.model.account.item.CustomerItemInfoGetInputVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;
import net.polyv.rest.util.ResponseUtil;
import net.polyv.rest.model.live.LivePackageMsgVO;
import net.polyv.service.CacheService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.group.CustomerWithGroup;

/**
 * 直播套餐相关
 * <AUTHOR>
 * @since 2020/05/29
 */


@Slf4j
@Service
public class LivePackageClientService {
	@Resource
	private CustomerConfigRepository customerConfigRepository;
	
	@Resource
	private CustomerConfigDao customerConfigDao;
	private final LivePackageClient livePackageClient;
	
	@Resource
	private LiveFlowPackageInfoRepository liveFlowPackageInfoRepository;
	
	@Resource
	
	private PcsFunctionConfigRepository pcsFunctionConfigRepository;
	
	@Resource
	private CacheService cacheService;

	@Autowired
	private ItemService itemService;

	@Autowired
	private SettlementConfigService settlementConfigService;

	@Autowired
	private AccountDepositService accountDepositService;

	@Autowired
	private AccountRechargeRecordResourcePointRepository accountRechargeRecordResourcePointRepository;

	@Autowired
	private AccountRechargeRecordRepository accountRechargeRecordRepository;

	@Autowired
	private LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository;
	
	@Autowired
	public LivePackageClientService(LivePackageClient livePackageClient) {
		super();
		this.livePackageClient = livePackageClient;
	}
	
	
	/**
	 * 1、普通账号直播并发和分钟数套餐信息是在直播维护，需要从直播获取
	 * 2、 直播流量套餐信息是在pcs维护，需要从pcs获取
	 * 获取顾客当前的直播套餐信息（普通账号）
	 * @param customerId 顾客id
	 * @return 顾客当前的直播套餐信息
	 */
	@Nullable
	public LivePackageMsgVO getLivePackageMsg(String customerId) {
		
		UserMsgVO user = UserClient.getUserByUnionId(customerId);
		if (user == null) {
			return null;
		}
		List<LiveFlowPackageInfo> liveFlowPackageInfo =
				liveFlowPackageInfoRepository.findByCustomerIdAndIsLiveFlowBilling(
				customerId, 1);
		if (CollectionUtils.isNotEmpty(liveFlowPackageInfo)) {
			LivePackageMsgVO livePackageMsgVO = new LivePackageMsgVO();
			livePackageMsgVO.setBillingPlan(OldPackItemCodeConst.live_flow.getBillingPlan());
			livePackageMsgVO.setPackageStart(
					DateFormatUtil.formatDateNormal(liveFlowPackageInfo.get(0).getStartDate()));
			livePackageMsgVO.setPackageEnd(DateFormatUtil.formatDateNormal(liveFlowPackageInfo.get(0).getEndDate()));
			return livePackageMsgVO;
		}
		ResponseEntity<WrappedResponse<?>> response = null;
		try {
			response = livePackageClient.getPackageMsg(user.getLiveUserId());
			return ResponseUtil.getDataFromResponse(response, LivePackageMsgVO.class);
		} catch (Exception e) {
			log.error("getPackageMsg失败,response：{}", response, e);
		}
		
		return null;
	}
	
	/**
	 * 直播资源结算时套餐信息
	 * @param customerId
	 * @return
	 */
	@Nullable
	public LivePackageMsgVO getLiveClearPackageMsg(String customerId, Date baseDate, String resourceCode) {
		
		UserMsgVO user = UserClient.getUserByUnionId(customerId);
		log.info("----------------------------- user {}",user);
		if (user == null) {
			return null;
		}
		
		//直播流量出账，如果当天打过直播流量套餐（直播流量计费），需要判断结算日期是否在上一个直播流量套餐有效期内
		if (ResourceCodeConst.live_flow.name().equals(resourceCode)) {
			log.info("----------------------------- resourceCode {}",resourceCode);
			//是否当天打过直播流量套餐
			boolean existCurrentDayFlowPackage =
					liveFlowPackageInfoRepository.existsByCustomerIdAndStartDateAndIsLiveFlowBilling(
					customerId, DateUtil.getCurrentDay(), ZeroOneConst.ONE.getValue());
			log.info("----------------------------- existCurrentDayFlowPackage {}",existCurrentDayFlowPackage);
			if (existCurrentDayFlowPackage) {
				//上一个有效期内的套餐
				LiveFlowPackageInfo lastFlowPackage =
						liveFlowPackageInfoRepository.findByCustomerIdAndIsLiveFlowBillingAndLastStartDateLessThanEqualAndLastEndDateGreaterThanEqual(
						customerId, ZeroOneConst.ONE.getValue(), baseDate, baseDate);
				log.info("----------------------------- lastFlowPackage {}",lastFlowPackage);
				//如果有，则直接返回上一个流量套餐信息
				if (Objects.nonNull(lastFlowPackage)) {
					LivePackageMsgVO livePackageMsgVO = new LivePackageMsgVO();
					livePackageMsgVO.setBillingPlan(OldPackItemCodeConst.live_flow.getBillingPlan());
					livePackageMsgVO.setPackageStart(
							DateFormatUtil.formatDateNormal(lastFlowPackage.getLastStartDate()));
					livePackageMsgVO.setPackageEnd(DateFormatUtil.formatDateNormal(lastFlowPackage.getLastEndDate()));
					return livePackageMsgVO;
				}
				
			}
		}
		
		List<LiveFlowPackageInfo> liveFlowPackageInfo =
				liveFlowPackageInfoRepository.findByCustomerIdAndIsLiveFlowBillingAndStartDateLessThanEqualAndEndDateGreaterThanEqual(
				customerId, 1, baseDate, baseDate);
		log.info("----------------------------- liveFlowPackageInfo {}",liveFlowPackageInfo);
		if (CollectionUtils.isNotEmpty(liveFlowPackageInfo)) {
			LivePackageMsgVO livePackageMsgVO = new LivePackageMsgVO();
			livePackageMsgVO.setBillingPlan(OldPackItemCodeConst.live_flow.getBillingPlan());
			livePackageMsgVO.setPackageStart(
					DateFormatUtil.formatDateNormal(liveFlowPackageInfo.get(0).getStartDate()));
			livePackageMsgVO.setPackageEnd(DateFormatUtil.formatDateNormal(liveFlowPackageInfo.get(0).getEndDate()));
			return livePackageMsgVO;
		}
		
		try {
			ResponseEntity<WrappedResponse<?>> response = livePackageClient.getPackageMsg(user.getLiveUserId());
			return ResponseUtil.getDataFromResponse(response, LivePackageMsgVO.class);
		} catch (Exception e) {
			log.error("getPackageMsg失败", e);
		}
		
		return null;
	}
	
	/**
	 * 当前是否是直播流量计费（套餐在生效中）
	 * @param customerId
	 * @param baseDate
	 * @return
	 */
	public boolean isLiveFlowBilling(String customerId, Date baseDate) {
		List<LiveFlowPackageInfo> liveFlowPackageInfoList =
				liveFlowPackageInfoRepository.findByCustomerIdAndIsLiveFlowBillingAndStartDateLessThanEqualAndEndDateGreaterThanEqual(
				customerId, 1, baseDate, baseDate);
		if (CollectionUtils.isNotEmpty(liveFlowPackageInfoList)) {
			LiveFlowPackageInfo liveFlowPackageInfo = liveFlowPackageInfoList.get(0);
			Date startDate = liveFlowPackageInfo.getStartDate();
			Date endDate = liveFlowPackageInfo.getEndDate();
			long startTime = Objects.nonNull(startDate) ? startDate.getTime() : 0L;
			long endTime = Objects.nonNull(endDate) ? endDate.getTime() : 0L;
			return usePrevPackageDate(startTime, endTime, baseDate);
		}
		return false;
	}
	
	/**
	 * 当前是否是直播分钟数计费（套餐在生效中）
	 * @param customerId
	 * @param baseDate
	 * @return
	 */
	public boolean isLiveDurationBilling(String customerId, Date baseDate) {
		UserMsgVO user = UserClient.getUserByUnionId(customerId);
		if (Objects.isNull(user)) {
			return false;
		}
		try {
			ResponseEntity<WrappedResponse<?>> response = livePackageClient.getPackageMsg(user.getLiveUserId());
			LivePackageMsgVO livePackageMsgVO = ResponseUtil.getDataFromResponse(response, LivePackageMsgVO.class);
			if (Objects.nonNull(livePackageMsgVO)) {
				log.info("isLiveDurationBilling livePackageMsgVO：{}", livePackageMsgVO);
				String startDate = livePackageMsgVO.getPackageStart();
				String endDate = livePackageMsgVO.getPackageEnd();
				long startTime =
						StringUtils.isNotBlank(startDate) ? DateFormatUtil.parseDateNormal(startDate).getTime() : 0L;
				long endTime = StringUtils.isNotBlank(endDate) ? DateFormatUtil.parseDateNormal(endDate).getTime() :
						0L;
				return LiveBillingType.Duration.getValue().equals(livePackageMsgVO.getBillingPlan()) &&
						usePrevPackageDate(startTime, endTime, baseDate);
				
			}
		} catch (Exception e) {
			log.error("getPackageMsg失败", e);
		}
		
		return false;
	}
	
	/**
	 * 保存上一次的直播套餐信息
	 * @param setting
	 * @param msgVO
	 */
	public void savePreLivePackageMsg(LiveCustomerConcurrenceSetting setting, LivePackageMsgVO msgVO) {
		CustomerConfig customerConfig = customerConfigDao.getOrCreateCustomerConfig(setting.getCustomerId());
		
		CustomerConfigConfig config = customerConfig.getConfig();
		log.info("修改用户的config，修改之前为:{}", JSON.toJSONString(config));
		
		if (config == null) {
			config = new CustomerConfigConfig();
		}
		
		config.setLivePrevBillingPlan(msgVO.getBillingPlan());
		Date startDate = DateFormatUtil.parseDateStr(msgVO.getPackageStart());
		if (startDate != null) {
			config.setLivePrevPackageStart(startDate.getTime());
		}
		Date endDate = DateFormatUtil.parseDateStr(msgVO.getPackageEnd());
		if (endDate != null) {
			config.setLivePrevPackageEnd(endDate.getTime());
		}
		if (OldPackItemCodeConst.duration.name().equals(msgVO.getBillingPlan())) {
			if (startDate != null) {
				config.setLivePrevDurationPackStart(startDate.getTime());
			}
			if (endDate != null) {
				config.setLivePrevDurationPackEnd(endDate.getTime());
			}
		}
		
		if (OldPackItemCodeConst.live_flow.name().equals(msgVO.getBillingPlan())) {
			if (startDate != null) {
				config.setLivePrevFlowPackStart(startDate.getTime());
			}
			if (endDate != null) {
				config.setLivePrevFlowPackEnd(endDate.getTime());
			}
		}
		
		customerConfig.setConfig(config);
		
		customerConfigRepository.save(customerConfig);
	}
	
	/**
	 * 并发结束后要打回什么类型的套餐（分钟数或流量）
	 * 取生效中的套餐，如果都生效，取开始时间最大的
	 * 如果开始时间一样，取功能开关配置的
	 * @param customerId
	 * @return
	 */
	public LivePackageMsgVO getConcurrenceEndToPackage(String customerId) {
		LivePackageMsgVO livePackageMsgVO = new LivePackageMsgVO();
		CustomerConfig customerConfig = customerConfigDao.getOrCreateCustomerConfig(customerId);
		CustomerConfigConfig config = customerConfig.getConfig();
		
		Long preDurationStart =
				Objects.isNull(config.getLivePrevDurationPackStart()) ? 0L : config.getLivePrevDurationPackStart();
		Long preDurationEnd =
				Objects.isNull(config.getLivePrevDurationPackEnd()) ? 0L : config.getLivePrevDurationPackEnd();
		Long preFlowStart = Objects.isNull(config.getLivePrevFlowPackStart()) ? 0L : config.getLivePrevFlowPackStart();
		Long preFlowEnd = Objects.isNull(config.getLivePrevFlowPackEnd()) ? 0L : config.getLivePrevFlowPackEnd();
		
		boolean isDurationEffect = usePrevPackageDate(preDurationStart, preDurationEnd);
		boolean isFlowEffect = usePrevPackageDate(preFlowStart, preFlowEnd);
		
		log.info("customerId is:{},preDurationStart:{}, preDurationEnd:{}, preFlowStart:{}, preFlowEnd:{}, " +
						"isDurationEffect:{}, " + "isFlowEffect:{}", customerId, preDurationStart, preDurationEnd,
				preFlowStart,
				preFlowEnd, isDurationEffect, isFlowEffect);
		
		String billingPlanCode = getBillingPlanCode(isDurationEffect, isFlowEffect, preDurationStart, preFlowStart,
				customerId);
		
		if (!isDurationEffect && !isFlowEffect) {
			livePackageMsgVO.setPackageStart(DateFormatUtil.formatDateNormal(new Date()));
			livePackageMsgVO.setPackageEnd(DateFormatUtil.formatDateNormal(new Date()));
		} else {
			setPackageStartAndEnd(livePackageMsgVO, billingPlanCode, preDurationStart, preDurationEnd, preFlowStart,
					preFlowEnd);
		}
		
		livePackageMsgVO.setBillingPlan(billingPlanCode);
		return livePackageMsgVO;
	}
	
	private String getBillingPlanCode(boolean isDurationEffect, boolean isFlowEffect, long preDurationStart,
			long preFlowStart, String customerId) {
		
		String billingPlanCode = PcsFunctionConfigConst.AMOUNT_BILLING_TYPE.getDefaultValue().toString();
		
		if (isDurationEffect && !isFlowEffect) {
			return OldPackItemCodeConst.duration.getBillingPlan();
		}
		if (!isDurationEffect && isFlowEffect) {
			return OldPackItemCodeConst.live_flow.getBillingPlan();
		}
		if (preDurationStart > preFlowStart) {
			return OldPackItemCodeConst.duration.getBillingPlan();
		}
		if (preDurationStart < preFlowStart) {
			return OldPackItemCodeConst.live_flow.getBillingPlan();
		}
		
		PcsFunctionConfig pcsFunctionConfig = pcsFunctionConfigRepository.findByCustomerIdAndCode(customerId,
				PcsFunctionConfigConst.AMOUNT_BILLING_TYPE.getConfigKey());
		if (Objects.nonNull(pcsFunctionConfig) && StringUtils.isNotBlank(pcsFunctionConfig.getValue())) {
			return pcsFunctionConfig.getValue();
		}
		
		return billingPlanCode;
	}
	
	private void setPackageStartAndEnd(LivePackageMsgVO livePackageMsgVO, String billingPlanCode,
			long preDurationStart,
			long preDurationEnd, long preFlowStart, long preFlowEnd) {
		if (OldPackItemCodeConst.duration.getBillingPlan().equals(billingPlanCode)) {
			livePackageMsgVO.setPackageStart(DateFormatUtil.formatDateNormal(new Date(preDurationStart)));
			livePackageMsgVO.setPackageEnd(DateFormatUtil.formatDateNormal(new Date(preDurationEnd)));
		}
		if (OldPackItemCodeConst.live_flow.getBillingPlan().equals(billingPlanCode)) {
			livePackageMsgVO.setPackageStart(DateFormatUtil.formatDateNormal(new Date(preFlowStart)));
			livePackageMsgVO.setPackageEnd(DateFormatUtil.formatDateNormal(new Date(preFlowEnd)));
		}
	}
	
	/**
	 * 注意：比如end = 2023-05-23,当前也是2023-05-23，也是生效中的
	 * @param start
	 * @param end
	 * @return
	 */
    
    private boolean usePrevPackageDate(Long start, Long end) {
        if (start == null || end == null) {
            return false;
        }
        if (start == 0 || end == 0) {
            return false;
        }
        
        return end == DateUtil.getCurrentDay().getTime() || new Date(end).after(DateUtil.getCurrentDay());
    }
    
    /**
	 * 比较套餐时间和结算基础日期，判断套餐是否在生效范围内
	 * 注意：比如end = 2023-05-23,当前也是2023-05-23，也是生效中的
	 * @param start:套餐开始时间戳
	 * @param end：套餐结束时间戳
	 * @param baseDate：基准日期
	 * @return
	 */
    
    private boolean usePrevPackageDate(Long start, Long end, Date baseDate) {
        if (start == null || end == null || baseDate == null) {
            return false;
        }
        if (start == 0 || end == 0) {
            return false;
        }
        
        return end == baseDate.getTime() || new Date(end).after(baseDate);
    }
    
    /**
	 * 获取直播分钟数或直播流量的结算用户信息
	 * @param processCustomerIdList：有消耗的用户列表
	 * @param concurrenceCustomerIds:有生效中并发的用户列表
	 * @param itemCode:计费项编码
	 * @return
	 */
	public List<CustomerWithGroup> getLiveClearingUser(List<CustomerWithGroup> processCustomerIdList,
			List<String> concurrenceCustomerIds, String itemCode, Date consumeDate) {
		String resourceCode;
		if (ItemCodeConst.china_pd.getCode().equals(itemCode) || ItemCodeConst.inter_pd.getCode().equals(itemCode) ||
				ItemCodeConst.isOverCodeRateItem(itemCode)) {
			resourceCode = ResourceCodeConst.duration.name();
		} else if (ItemCodeConst.live_flow.getCode().equals(itemCode) || ItemCodeConst.prtc_live_flow.getCode().equals(itemCode)) {
			resourceCode = ResourceCodeConst.live_flow.name();
		} else {
			resourceCode = "";
		}
		List<CustomerWithGroup> result;
        //先过滤掉有生效中并发的用户
        result = processCustomerIdList.stream().filter(id -> !concurrenceCustomerIds.contains(id.getCustomerId()))
				.collect(Collectors.toList());
		
		if (CollectionUtils.isNotEmpty(result)) {
			//遍历result，根据customerId获取计费方式，并筛选出计费方式等于itemCode的用户
			result = result.stream().filter(customerWithGroup -> {
				String liveBillingType = getLiveClearBillingType(customerWithGroup.getCustomerId(), consumeDate,
						resourceCode);
				log.info("customerId:{},计费方式:{}", customerWithGroup.getCustomerId(), liveBillingType);
				return resourceCode.equals(liveBillingType);
			}).collect(Collectors.toList());
		}
		log.info("itemCode:{},结算用户列表:{}", itemCode, result);
		return result;
	}
	
	//判断可用计费类型跟结算出账计费类型处理逻辑不一样
	public String getLiveBillingType(String customerId) {
		String cacheLiveBillingType = cacheService.getLiveBillingType(customerId);
		log.info("customerId == {},cacheLiveBillingType == {}", customerId, cacheLiveBillingType);
		if (StringUtils.isNotBlank(cacheLiveBillingType)) {
			return cacheLiveBillingType;
		}
		//金额模式默认计费方式
		PcsFunctionConfig pcsFunctionConfig = pcsFunctionConfigRepository.findByCustomerIdAndCode(customerId,
				PcsFunctionConfigConst.AMOUNT_BILLING_TYPE.getConfigKey());
		
		String billingType = (pcsFunctionConfig != null && StringUtils.isNotBlank(pcsFunctionConfig.getValue())) ?
				pcsFunctionConfig.getValue() : PcsFunctionConfigConst.AMOUNT_BILLING_TYPE.getDefaultValue().toString();
		LivePackageMsgVO livePackageMsgVO = this.getLivePackageMsg(customerId);
		
		if (Objects.nonNull(livePackageMsgVO)) {
			log.info("customerId == {},getLivePackageMsg == {}", customerId, livePackageMsgVO);
			//如果是并发无限制，是没有套餐生效时间的，直接返回计费类型
			if (OldPackItemCodeConst.unlimited.getBillingPlan().equals(livePackageMsgVO.getBillingPlan())) {
				billingType = OldPackItemCodeConst.unlimited.getBillingPlan();
			} else {
				String packageStart = livePackageMsgVO.getPackageStart();
				String packageEnd = livePackageMsgVO.getPackageEnd();
				long packageStartTime = 0L;
				long packageEndTime = 0L;
				if (StringUtils.isNotBlank(packageStart)) {
					packageStartTime = DateFormatUtil.dateStrToLong(packageStart);
				}
				if (StringUtils.isNotBlank(packageEnd)) {
					packageEndTime = DateFormatUtil.dateStrToLong(packageEnd);
				}
				//如果当前套餐生效，返回当前的计费方式
				if (usePrevPackageDate(packageStartTime, packageEndTime)) {
					billingType = livePackageMsgVO.getBillingPlan();
				}
			}
		}
		//当前计费方式放到缓存，60s有效期
		cacheService.setLiveBillingType(customerId, billingType);
		
		return billingType;
	}

	/**
	 * 获取客户使用过的所有主套餐计费类型列表
	 * @param customerId
	 * @return
	 */
	public List<String> getAllLiveBillingType(String customerId) {
		List<String> list = new ArrayList<>();
		list.add("直播分钟数");
		//判断是否有打过并发套餐
		LiveCustomerConcurrenceSetting liveCustomerConcurrenceSetting = liveCustomerConcurrenceSettingRepository.getLatestConcurrenceByCustomerId(customerId);
		if(Objects.nonNull(liveCustomerConcurrenceSetting)){
			list.add("直播并发");
		}
		//判断是否有打过直播流量套餐
		List<LiveFlowPackageInfo> liveFlowPackageInfoList = liveFlowPackageInfoRepository.findByCustomerId(customerId);
		if(CollectionUtils.isNotEmpty(liveFlowPackageInfoList)){
			list.add("直播流量");
			list.add("直播无延迟流量");
		}
		return list;
	}

	/**
	 * 判断客户的主套餐是否生效或可用
	 * @param customerId
	 * @return
	 */
	public boolean isCustomerLivePackageAavailable(String customerId,int livePackageRedundantExpiredDays){
		LivePackageMsgVO livePackageMsgVO = this.getLivePackageMsg(customerId);
		log.info("isCustomerLivePackageAavailable customerId == {},getLivePackageMsg == {}", customerId, livePackageMsgVO);
		if (Objects.nonNull(livePackageMsgVO)) {
			//如果是并发无限制，是没有套餐生效时间的，直接返回计费类型
			if (OldPackItemCodeConst.unlimited.getBillingPlan().equals(livePackageMsgVO.getBillingPlan())) {
				return true;
			} else {
				String packageEnd = livePackageMsgVO.getPackageEnd();
				long packageEndTime = 0L;
				if (StringUtils.isNotBlank(packageEnd)) {
					packageEndTime = DateFormatUtil.dateStrToLong(packageEnd);
					//增加冗余的判断，超过一定天数才认为是过期
					packageEndTime = packageEndTime + livePackageRedundantExpiredDays * 3600 * 24 * 1000;
				}
				//如果当前套餐生效，返回 true 生效
				if (comparePackageEndDate(packageEndTime)) {
					return true;
				}else{
					// 余额
					long balance = 0;
					// 授信额度
					long validCredit = 0;
					//判断是否有资源点或者金额
					SettlementConfigVO settlementConfigVO = settlementConfigService.querySettlementConfig(customerId);
					boolean resourcePointEnabled = settlementConfigVO != null && SwitchEnum.isY(settlementConfigVO.getResourcePointSettlement()) ;
					if(resourcePointEnabled){
						balance = accountRechargeRecordResourcePointRepository.sumUserValidBalanceAmount(customerId);
					}else{
						// 非资源点可用业务
						validCredit = accountDepositService.calcCustomerValidCredit(new CustomerInfoGetInputVO(customerId));
						balance = accountRechargeRecordRepository.sumUserValidBalanceAmount(customerId);
					}

					if(balance + validCredit > 0){
						return true;
					}

					String billingType = livePackageMsgVO.getBillingPlan();
					//分钟数 | 直播流量 | 并发调用获取可用量接口
					CustomerItemInfoGetInputVO inputVO = new CustomerItemInfoGetInputVO();
					inputVO.setCustomerId(customerId);
					inputVO.setResourceCode(billingType);
					return CustomerItemStateConst.State.NORMAL == itemService.getCustomerItemState(inputVO).getState();
				}
			}
		}
		return false;
	}

	/**
	 * 注意：比如end = 2023-05-23,当前也是2023-05-23，也是生效中的
	 * @param start
	 * @param end
	 * @return
	 */

	private boolean comparePackageEndDate(Long end) {
		if (end == null) {
			return false;
		}
		if (end == 0) {
			return false;
		}

		return end == DateUtil.getCurrentDay().getTime() || new Date(end).after(DateUtil.getCurrentDay());
	}
	
	//获取直播用户结算出账计费类型（和判断可用获取计费类型的逻辑要区分开来）
	public String getLiveClearBillingType(String customerId, Date consumeDate, String resourceCode) {
		//金额模式默认计费方式
		PcsFunctionConfig pcsFunctionConfig = pcsFunctionConfigRepository.findByCustomerIdAndCodeAndCreateTimeLessThan(
				customerId, PcsFunctionConfigConst.AMOUNT_BILLING_TYPE.getConfigKey(),
				DateUtil.getDateAfterDays(1, consumeDate));
		
		String billingType = (pcsFunctionConfig != null && StringUtils.isNotBlank(pcsFunctionConfig.getValue())) ?
				pcsFunctionConfig.getValue() : PcsFunctionConfigConst.AMOUNT_BILLING_TYPE.getDefaultValue().toString();
		
		LivePackageMsgVO livePackageMsgVO = this.getLiveClearPackageMsg(customerId, consumeDate, resourceCode);

		log.info("oooooooooooooo====================  livePackageMsgVO {}",livePackageMsgVO);
		
		if (Objects.nonNull(livePackageMsgVO)) {
			String packageStart = livePackageMsgVO.getPackageStart();
			String packageEnd = livePackageMsgVO.getPackageEnd();
			long packageStartTime = 0L;
			long packageEndTime = 0L;
			if (StringUtils.isNotBlank(packageStart)) {
				packageStartTime = DateFormatUtil.dateStrToLong(packageStart);
			}
			if (StringUtils.isNotBlank(packageEnd)) {
				packageEndTime = DateFormatUtil.dateStrToLong(packageEnd);
			}
			//如果当前套餐生效
			if (usePrevPackageDate(packageStartTime, packageEndTime, consumeDate)) {
				log.info("----------------------------- usePrevPackageDate ........");
				//如果是直播流量计费，则返回直播流量，否则返回分钟数计费
				// 这里为什么不用直播返回的计费类型？原因有如下两个
				// 1、会存在pcs是分钟数计费，直播是并发计费的情况，脏数据导致
				// 2、比如10.31打了并发套餐，然后出30号的分钟数账单，这时候直播返回的是并发套餐，但是实际上应该是分钟数套餐
				billingType =
						OldPackItemCodeConst.live_flow.getBillingPlan().equals(livePackageMsgVO.getBillingPlan()) ?
								OldPackItemCodeConst.live_flow.getBillingPlan() :
								OldPackItemCodeConst.duration.getBillingPlan();
			}
		}
		return billingType;
	}
}
