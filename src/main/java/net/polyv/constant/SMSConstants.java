package net.polyv.constant;

/**
 * @description:发送短信常量类
 * @author: Neo
 * @date: 2022-04-18
 */
public interface SMSConstants {
    String ALI_SMS_ACCESSID = "LTAIfaI1TwuCPpjW";
    String ALI_SMS_ACCESS_KEYSECRET = "Uqdbz6QwNNckCwTyZ03N7U2CZfKYGJ";
    String POLYV_SMS_EN_SIGN = "POLYV";
    String POLYV_SMS_SIGN = "保利威";
    String ALI_SMS_CODE_EN_TEMPLATEID = "SMS_199805180";

    String ALI_COMMON_SMS_CODE_TEMPLATEID = "SMS_206470333";


    /**
     * 超并发模板 code
     */
    String ALI_CONCURRENCY_SMS_CODE_TEMPLATE_ID = "SMS_239326224";

    /**
     * 用量不足
     */
    String ALI_QUANTITY_COMMON_SMS_CODE_TEMPLATE_ID = "SMS_239311523";
    /**
     * 套餐过期
     */
    String ALI_PACKAGE_SMS_CODE_TEMPLATE_ID = "SMS_239311217";
}
