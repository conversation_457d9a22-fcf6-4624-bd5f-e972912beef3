package net.polyv.constant.resource;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import lombok.Getter;

/**
 * 超码率表映射枚举类
 */
@Getter
public enum OverCodeRateTableEnum {
    
    temp_china_pd_over_rate_detail("temp_china_pd_over_rate_detail", "temp_china_pd_over_rate_sum"),
    
    temp_inter_pd_over_rate_detail("temp_inter_pd_over_rate_detail", "temp_inter_pd_over_rate_sum"),
    
    temp_prtc_china_pd_over_rate_detail("temp_prtc_china_pd_over_rate_detail", "temp_prtc_china_pd_over_rate_sum"),
    
    temp_prtc_inter_pd_over_rate_detail("temp_prtc_inter_pd_over_rate_detail", "temp_prtc_inter_pd_over_rate_sum"),
    
    
    ;
    
    OverCodeRateTableEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    private final String name;
    private final String code;
    
    public static OverCodeRateTableEnum getByCode(String code) {
        for (OverCodeRateTableEnum value : OverCodeRateTableEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    public static String getName(Object code) {
        for (OverCodeRateTableEnum aet : OverCodeRateTableEnum.values()) {
            if (aet.getCode().equals((Integer) code)) {
                return aet.getName();
            }
        }
        return null;
    }
    
    private static final Map<String, String> MAP = new HashMap<>();
    
    static {
        for (OverCodeRateTableEnum enableTypeEnum : OverCodeRateTableEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.name);
        }
    }
    
    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }
    
    public static Map<String, String> getMap() {
        return MAP;
    }
    
    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
