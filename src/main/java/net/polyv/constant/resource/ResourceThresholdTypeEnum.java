package net.polyv.constant.resource;

import com.alibaba.fastjson.JSON;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 *  金额充值类型枚举类
 */
@Getter
public enum ResourceThresholdTypeEnum {

    AMOUNT_NOT_ENOUGH(1,"金额不足");

    ResourceThresholdTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String name;
    private final Integer code;

    public static ResourceThresholdTypeEnum getByType(Integer code) {
        for (ResourceThresholdTypeEnum value : ResourceThresholdTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getText(Object code){
        for (ResourceThresholdTypeEnum aet : ResourceThresholdTypeEnum.values()) {
            if(aet.getCode().equals((Integer)code)){
                return aet.getName();
            }
        }
        return null;
    }

    private static final Map<Integer,String> MAP = new HashMap<>();

    static {
        for (ResourceThresholdTypeEnum enableTypeEnum : ResourceThresholdTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.name);
        }
    }

    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<Integer, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
