package net.polyv.constant;

import java.util.Objects;

import lombok.Getter;

/**
 * 结算系统常量
 * <AUTHOR>
 * @since 2020/12/06
 */
@Getter
public enum PcsEnumConst {
    /***/
    YES("是", 1), NO("否", 0),
    ;
    private final String label;
    private final int value;
    
    PcsEnumConst(String label, int value) {
        this.label = label;
        this.value = value;
    }
    
    public static boolean isYes(Integer value) {
        return Objects.nonNull(value) && value.intValue() == YES.value;
    }
}
