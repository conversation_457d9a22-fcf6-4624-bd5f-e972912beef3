package net.polyv.constant.finance;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum
ContractAssociationStatusEnum {

    WAIT_ASSOCIATION((short) 1, "待关联"),
    WAIT_AUDIT((short) 2, "待审核"),
    ASSOCIATED((short) 3, "已关联"),

    ;
    private Short code;
    private String desc;

    private static final Map<Short, String> MAP = new HashMap<>();

    static {
        for (ContractAssociationStatusEnum enableTypeEnum : ContractAssociationStatusEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(Short key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<Short, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
