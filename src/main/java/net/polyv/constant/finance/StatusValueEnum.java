package net.polyv.constant.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.polyv.constant.PcsConstant;

@Getter
@AllArgsConstructor
public enum StatusValueEnum {
    /**
     * 状态
     */
    YES((short) 1), NO((short) 0),
    ;
    private Short value;
    
    public static Short getValueByStatus(String status) {
        if (status.equals(PcsConstant.Y)) {
            return YES.getValue();
        }
        if (status.equals(PcsConstant.N)) {
            return NO.getValue();
        }
        return  -1;
    }
}
