package net.polyv.constant.finance;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

// 创建合同的来源
@Getter
@AllArgsConstructor
public enum ContractOriginEnum {

    ORIGIN_OPEN("origin_open", "正常开通"),
    ORIGIN_AMOUNT("origin_amount", "正常开通"),
    ORIGIN_PRE_OPEN("origin_pre_open", "预开通"),
    ORIGIN_MONTHLY_SETTLE("origin_monthly_settle", "月结"),
    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (ContractOriginEnum enableTypeEnum : ContractOriginEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String code) {
        return getMap().get(code) == null ? "" : getMap().get(code);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
