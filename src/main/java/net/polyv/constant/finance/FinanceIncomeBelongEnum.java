package net.polyv.constant.finance;


import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.List;

// 金额归属枚举
@Getter
@AllArgsConstructor
public enum FinanceIncomeBelongEnum {

    LIVE_BELONG("直播"),
    RESIDENT_BELONG("驻场服务"),
    HARDWARE_BELONG("硬件"),
    VOD_BELONG("点播"),
    INTERACTIVE_BELONG("互动学堂"),
    SEMINAR_BELONG("研讨会"),
    MR_BELONG("MR"),
    COOL_BELONG("酷播"),
    ONLINE_BELONG("网校云"),
    OPERATION_BELONG("运营服务"),
    MATERIAL("素材库")
    ;

    private String name;

    public static String getBelongByBillingItemCode(String billingItemCode) {
        switch (billingItemCode) {
            case "china_pd":
            case "inter_pd":
            case "inter_pd_diff":
            case "mic_pd":
            case "concur_daily":
            case "concur_monthly":
            case "concur_peak":
            case "inter_concur_diff":
            case "ppt_anim_page":
            case "ppt_anim_job":
            case "guide_pd":
            case "ppt_composite_duration":
            case "ppt_composite_job":
            case "manual-16":
            case "manual-22":
            case "manual-23":
            case "manual-24":
            case "manual-25":
            case "manual-26":
            case "manual-27":
            case "manual-28":
            case "manual-29":
            case "manual-30":
            case "manual-31":
            case "manual-32":
            case "prtc_china_pd":
            case "prtc_inter_pd":
            case "channel_number":
            case "manual-59":
            case "manual-62":
            case "manual-66":
            case "inter_pd_over_rate":
            case "china_pd_over_rate":
            case "prtc_china_pd_over_rate":
            case "prtc_inter_pd_over_rate":
            case "prtc_concur_daily":
            case "prtc_concur_monthly":
                return LIVE_BELONG.getName();
            case "manual-17":
                return RESIDENT_BELONG.getName();
            case "manual-19":
            case "manual-20":
            case "manual-21":
                return HARDWARE_BELONG.getName();
            case "manual-36":
            case "manual-37":
            case "traffic":
            case "video_space":
            case "manual-58":
                return VOD_BELONG.getName();
            case "small_class_duration":
            case "small_class_record_duration":
                return INTERACTIVE_BELONG.getName();
            case "seminar_duration":
            case "seminar_record_duration":
            case "seminar_quarter_package":
            case "manual-65":
                return SEMINAR_BELONG.getName();
            case "manual-67":
                return MR_BELONG.getName();
            case "expired_cleanup":
                return "-";
            default:
                return "";
        }
    }

    public static String getBelongByOperationCode(String operationCode) {
        if (StringUtils.isEmpty(operationCode)) {
            return "";
        }
        List<String> liveBelongList = Lists.newArrayList(
                ContractOperationCodeEnum.CREATE_GROUP_ACCOUNT_V2.getCode(),
                ContractOperationCodeEnum.LIVE_DURATION.getCode(),
                ContractOperationCodeEnum.LIVE_CONCURRENT_DAILY.getCode(),
                ContractOperationCodeEnum.LIVE_CONCURRENT_MONTHLY.getCode(),
                ContractOperationCodeEnum.LIVE_PRTC_CONCURRENT_DAILY.getCode(),
                ContractOperationCodeEnum.LIVE_PRTC_CONCURRENT_MONTHLY.getCode(),
                ContractOperationCodeEnum.LIVE_CONCURRENT_PEAK.getCode(),
                ContractOperationCodeEnum.LIVE_MIC_DURATION.getCode(),
                ContractOperationCodeEnum.LIVE_CHANNELS.getCode(),
                ContractOperationCodeEnum.LIVE_GUIDE_DURATION.getCode(),
                ContractOperationCodeEnum.LIVE_FUNCTION.getCode(),
                ContractOperationCodeEnum.CUSTOM_LIVE_CUSTOM.getCode(),
                ContractOperationCodeEnum.LIVE_CONCURRENT_V1.getCode(),
                ContractOperationCodeEnum.LIVE_MIC_DURATION_V1.getCode(),
                ContractOperationCodeEnum.LIVE_DURATION_V1.getCode()
        );
        List<String> vodBelongList = Lists.newArrayList(
                ContractOperationCodeEnum.VOD_MONTHLY_PACKAGE.getCode(),
                ContractOperationCodeEnum.VOD_YEARLY_PACKAGE.getCode(),
                ContractOperationCodeEnum.VOD_TEMP_FLOW.getCode(),
                ContractOperationCodeEnum.VOD_SPACE.getCode(),
                ContractOperationCodeEnum.VOD_PACK_FLOW.getCode(),
                ContractOperationCodeEnum.VOD_FUNCTION.getCode(),
                ContractOperationCodeEnum.OTHER_CDN_CDN.getCode(),
                ContractOperationCodeEnum.CUSTOM_VOD_CUSTOM.getCode(),
                ContractOperationCodeEnum.VOD_FLOW_V1.getCode(),
                ContractOperationCodeEnum.VOD_PACKAGE_V1.getCode()
        );
        List<String> coolBelongList = Lists.newArrayList(
                ContractOperationCodeEnum.COOL_MONTHLY_PACKAGE.getCode(),
                ContractOperationCodeEnum.COOL_YEARLY_PACKAGE.getCode(),
                ContractOperationCodeEnum.COOL_TEMP_FLOW.getCode(),
                ContractOperationCodeEnum.COOL_PACK_FLOW.getCode(),
                ContractOperationCodeEnum.COOL_SPACE.getCode()
        );
        List<String> hardBelongList = Lists.newArrayList(
                ContractOperationCodeEnum.HARD_CABIN_BODY.getCode(),
                ContractOperationCodeEnum.HARD_CABIN_INCREASE.getCode(),
                ContractOperationCodeEnum.HARD_OTHER_SALE.getCode(),
                ContractOperationCodeEnum.HARD_OTHER_RENT.getCode()
        );
        if (liveBelongList.contains(operationCode)) {
            return FinanceIncomeBelongEnum.LIVE_BELONG.getName();
        } else if (vodBelongList.contains(operationCode)) {
            return FinanceIncomeBelongEnum.VOD_BELONG.getName();
        } else if (coolBelongList.contains(operationCode)) {
            return FinanceIncomeBelongEnum.COOL_BELONG.getName();
        } else if (hardBelongList.contains(operationCode)) {
            return FinanceIncomeBelongEnum.HARDWARE_BELONG.getName();
        } else if (ContractOperationCodeEnum.ONLINE_PACKAGE.getCode().equals(operationCode)) {
            return FinanceIncomeBelongEnum.ONLINE_BELONG.getName();
        } else if (ContractOperationCodeEnum.RESIDENT_ALL.getCode().equals(operationCode) ||
                ContractOperationCodeEnum.RESIDENT_INCREASE_BUILD.getCode().equals(operationCode)) {
            return FinanceIncomeBelongEnum.RESIDENT_BELONG.getName();
        } else if (ContractOperationCodeEnum.OPERATION_ALL.getCode().equals(operationCode)) {
            return FinanceIncomeBelongEnum.OPERATION_BELONG.getName();
        } else if (ContractOperationCodeEnum.MATERIAL_SPACE.getCode().equals(operationCode) ||
                ContractOperationCodeEnum.MATERIAL_TRAFFIC.getCode().equals(operationCode)) {
            return FinanceIncomeBelongEnum.MATERIAL.getName();
        } else {
            return "";
        }
    }
}
