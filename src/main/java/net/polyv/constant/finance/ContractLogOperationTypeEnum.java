package net.polyv.constant.finance;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 合同操作日志操作类型枚举
 * @author: Neo
 * @date: 2022-09-20
 */
@Getter
@AllArgsConstructor
public enum ContractLogOperationTypeEnum {

    CREATE("CREATE","合同创建"),
    APPLY_ASSOCIATION("APPLY_ASSOCIATION","发起关联"),
    ASSOCIATION("ASSOCIATION","关联操作"),
    REFUND("REFUND","退费操作"),



    ;
    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (ContractLogOperationTypeEnum enableTypeEnum : ContractLogOperationTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
