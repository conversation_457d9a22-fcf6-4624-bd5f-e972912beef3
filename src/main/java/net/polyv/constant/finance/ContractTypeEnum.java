package net.polyv.constant.finance;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import lombok.AllArgsConstructor;
import lombok.Getter;

// 财务合同枚举类
@Getter
@AllArgsConstructor
public enum ContractTypeEnum {

    /**合同类型*/
    NONE("NONE", "无合同"),
    NORMAL("NORMAL", "普通合同"),
    STAGES("STAGES", "分期合同"),
    FRAME("FRAME", "框架合同"),
    SUPPLY_AGREEMENT("SUPPLY_AGREEMENT", "补充协议"),
    OTHER("OTHER", "其他"),
    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (ContractTypeEnum enableTypeEnum : ContractTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String code) {
        return getMap().get(code) == null ? "" : getMap().get(code);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
