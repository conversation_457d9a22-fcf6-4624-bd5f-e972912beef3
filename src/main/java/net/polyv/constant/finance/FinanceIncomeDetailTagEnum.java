package net.polyv.constant.finance;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @description: 财务收入明细标识
 * @author: Neo
 * @date: 2022-08-16
 */
@Getter
@AllArgsConstructor
public enum FinanceIncomeDetailTagEnum {


    ASSOCIATION_EXTRA_INCOME_DETAIL_TAG(-1990, "关联差额收入"),
    REFUND_EXTRA_INCOME_DETAIL_TAG(-1991, "退款差额收入"),
    EXPIRED_INCOME_DETAIL_TAG(-1992, "到期剩余收入"),
    TAIL_EXTRA_INCOME_DETAIL_TAG(-1993, "尾差收入"),
    PERIOD_EXPIRED_INCOME_DETAIL_TAG(-1994, "年流量套餐周期过期收入"),

    /**
     *  因为导出的时候使用明细表驱动其他表，明细表数据缺失会导致导出条数不对，故填补改特殊计费ID
     */
    MISSING_DEFAULT_INCOME_DETAIL_TAG(99999999,"填补缺失明细数据"),

    ;

    private Integer code;
    private String desc;

    private static final Map<Integer, String> MAP = new HashMap<>();

    static {
        for (FinanceIncomeDetailTagEnum enableTypeEnum : FinanceIncomeDetailTagEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(Integer code) {
        return getMap().get(code) == null ? "" : getMap().get(code);
    }

    public static Map<Integer, String> getMap() {
        return MAP;
    }

    public static Set<Integer> getTags() {
        return MAP.keySet();
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
