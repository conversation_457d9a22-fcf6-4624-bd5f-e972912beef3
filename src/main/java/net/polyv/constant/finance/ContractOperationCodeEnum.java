package net.polyv.constant.finance;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 财务合同业务类型枚举
 * @author: Neo
 * @date: 2022-08-15
 */
@Getter
@AllArgsConstructor
public enum ContractOperationCodeEnum {

    LIVE_DURATION("LIVE_DURATION", "添加直播分钟数"),
    LIVE_CONCURRENT_DAILY("LIVE_CONCURRENT_DAILY", "直播按天套餐"),
    LIVE_CONCURRENT_MONTHLY("LIVE_CONCURRENT_MONTHLY", "直播包月套餐"),
    LIVE_PRTC_CONCURRENT_DAILY("LIVE_PRTC_CONCURRENT_DAILY", "直播无延迟按天套餐"),
    LIVE_PRTC_CONCURRENT_MONTHLY("LIVE_PRTC_CONCURRENT_MONTHLY", "直播无延迟包月套餐"),
    LIVE_CONCURRENT_PEAK("LIVE_CONCURRENT_PEAK", "直播峰值并发"),
    LIVE_MIC_DURATION("LIVE_MIC_DURATION", "连麦分钟数"),
    LIVE_GUIDE_DURATION("LIVE_GUIDE_DURATION", "添加导播台分钟数"),
    LIVE_CHANNELS("LIVE_CHANNELS", "添加频道数"),
    VOD_MONTHLY_PACKAGE("VOD_MONTHLY_PACKAGE", "点播套餐月流量"),
    VOD_YEARLY_PACKAGE("VOD_YEARLY_PACKAGE", "点播套餐年流量"),
    VOD_TEMP_FLOW("VOD_TEMP_FLOW", "点播临时流量"),
    VOD_PACK_FLOW("VOD_PACK_FLOW", "点播流量包"),
    VOD_SPACE("VOD_SPACE", "添加点播空间"),
    COOL_MONTHLY_PACKAGE("COOL_MONTHLY_PACKAGE", "酷播云套餐月流量"),
    COOL_YEARLY_PACKAGE("COOL_YEARLY_PACKAGE", "酷播云套餐年流量"),
    COOL_TEMP_FLOW("COOL_TEMP_FLOW", "酷播云临时流量"),
    COOL_PACK_FLOW("COOL_PACK_FLOW", "酷播云流量包"),
    COOL_SPACE("COOL_SPACE", "添加酷播云空间"),
    PPT_PAGE_PAY("PPT_PAGE_PAY", "PPT按页数付费"),
    PPT_LINE_PAY("PPT_LINE_PAY", "PPT专享通道"),
    DEPOSIT_AMOUNT("DEPOSIT_AMOUNT", "充值金额"),
    OVER_AMOUNT_PAY("OVER_AMOUNT_PAY", "超额还款"),

    CREATE_GROUP_ACCOUNT_V2("CREATE_GROUP_ACCOUNT_V2", "功能开通-集团账号2.0-集团账号 2.0"),
    LIVE_FUNCTION("LIVE_FUNCTION", "直播-直播功能-直播功能"),
    VOD_FUNCTION("VOD_FUNCTION", "点播-点播功能-点播功能"),
    ONLINE_PACKAGE("ONLINE_PACKAGE", "网校云-套餐-套餐"),
    CUSTOM_LIVE_CUSTOM("CUSTOM_LIVE_CUSTOM", "定制服务-直播定制-定制服务"),
    CUSTOM_VOD_CUSTOM("CUSTOM_VOD_CUSTOM", "定制服务-点播定制-定制服务"),
    CUSTOM_PRIVATE_ALL("CUSTOM_PRIVATE_ALL", "定制服务-私有云-私有云"),
    RESIDENT_ALL("RESIDENT_ALL", "驻场服务-驻场服务-驻场服务"),
    RESIDENT_INCREASE_BUILD("RESIDENT_INCREASE_BUILD", "驻场服务-增值服务-直播间搭建"),
    OPERATION_ALL("OPERATION_ALL", "运营服务-运营服务-运营服务"),
    HARD_CABIN_BODY("HARD_CABIN_BODY", "硬件-直播舱-舱体"),
    HARD_CABIN_INCREASE("HARD_CABIN_INCREASE", "硬件-直播舱-增值服务"),
    HARD_OTHER_SALE("HARD_OTHER_SALE", "硬件-其它硬件-出售"), HARD_OTHER_RENT("HARD_OTHER_RENT", "硬件-其它硬件-租赁"),
    OTHER_CDN_CDN("OTHER_CDN_CDN", "其它-CDN-CDN"), OTHER_OTHER_OTHER("OTHER_OTHER_OTHER", "其它-其它-其它"),
    COOPERATIVE_RETURNING("COOPERATIVE_RETURNING", "合作伙伴服务-合作伙伴返佣"), LIVE_FLOW("LIVE_FLOW", "直播流量"),
    PRTC_LIVE_FLOW("PRTC_LIVE_FLOW", "无延迟直播流量"),
    
    // group_v1
    LIVE_DURATION_V1("LIVE_DURATION_V1", "集团1.0直播添加分钟数"),
    LIVE_CONCURRENT_V1("LIVE_CONCURRENT_V1", "集团1.0直播按天套餐"),
    LIVE_MIC_DURATION_V1("LIVE_MIC_DURATION_V1", "集团1.0连麦分钟数"), VOD_FLOW_V1("VOD_FLOW_V1", "集团1.0点播流量"),
    VOD_PACKAGE_V1("VOD_PACKAGE_V1", "集团1.0点播套餐"),
    
    MATERIAL_SPACE("materialspace", "素材库空间"),
    
    MATERIAL_TRAFFIC("materialtraffic", "素材库流量"),

    RESOURCE_POINT("RESOURCE_POINT" , "资源点")
    ;
    
    private String code;
    private String desc;
    
    private static final Map<String, String> MAP = new HashMap<>();
    
    static {
        for (ContractOperationCodeEnum enableTypeEnum : ContractOperationCodeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }

    /**
     * 一次性收入（含月结）
     */
    public static List<String> getOnceStatsOperationCodeList() {
        return Lists.newArrayList(
                ContractOperationCodeEnum.VOD_SPACE.getCode(),
                ContractOperationCodeEnum.CREATE_GROUP_ACCOUNT_V2.getCode(),
                ContractOperationCodeEnum.LIVE_FUNCTION.getCode(),
                ContractOperationCodeEnum.LIVE_CHANNELS.getCode(),
                ContractOperationCodeEnum.VOD_TEMP_FLOW.getCode(),
                ContractOperationCodeEnum.VOD_FUNCTION.getCode(),
                ContractOperationCodeEnum.COOL_SPACE.getCode(),
                ContractOperationCodeEnum.COOL_TEMP_FLOW.getCode(),
                ContractOperationCodeEnum.CUSTOM_LIVE_CUSTOM.getCode(),
                ContractOperationCodeEnum.CUSTOM_VOD_CUSTOM.getCode(),
                ContractOperationCodeEnum.CUSTOM_PRIVATE_ALL.getCode(),
                ContractOperationCodeEnum.RESIDENT_ALL.getCode(),
                ContractOperationCodeEnum.RESIDENT_INCREASE_BUILD.getCode(),
                ContractOperationCodeEnum.OPERATION_ALL.getCode(),
                ContractOperationCodeEnum.HARD_CABIN_BODY.getCode(),
                ContractOperationCodeEnum.HARD_CABIN_INCREASE.getCode(),
                ContractOperationCodeEnum.HARD_OTHER_SALE.getCode(),
                ContractOperationCodeEnum.HARD_OTHER_RENT.getCode(),
                ContractOperationCodeEnum.OTHER_CDN_CDN.getCode(),
                ContractOperationCodeEnum.OTHER_OTHER_OTHER.getCode()
        );
    }
}
