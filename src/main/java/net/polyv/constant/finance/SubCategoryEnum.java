package net.polyv.constant.finance;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

// 业务细分类型枚举类
@Getter
@AllArgsConstructor
public enum     SubCategoryEnum {

    // 如果为金额类型，则细分类型为对应计费项


    SC_CREATE_GROUP_ACCOUNT_V2("SC_CREATE_GROUP_ACCOUNT_V2", "集团账号 2.0"),
    SC_AMOUNT("SC_AMOUNT", "金额"),
    SC_LIVE_FUNCTION("SC_LIVE_FUNCTION", "直播功能"),
    SC_LIVE_DURATION("SC_LIVE_DURATION", "直播分钟数"),
    SC_LIVE_CONCURRENT_DAY("SC_LIVE_CONCURRENT_DAY", "并发包天"),
    SC_LIVE_CONCURRENT_MONTH("SC_LIVE_CONCURRENT_MONTH", "并发包月"),
    SC_LIVE_CONCURRENT_PEAK("SC_LIVE_CONCURRENT_PEAK", "年峰值并发"),
    SC_LIVE_MIC_DURATION("SC_LIVE_MIC_DURATION", "连麦分钟数"),
    SC_LIVE_GUIDE_DURATION("SC_LIVE_GUIDE_DURATION", "导播台分钟数"),
    SC_LIVE_CHANNEL("SC_LIVE_CHANNEL", "频道数"),
    SC_PACKAGE_MONTH("SC_PACKAGE_MONTH", "月流量"),
    SC_PACKAGE_YEAR("SC_PACKAGE_YEAR", "年流量"),
    SC_FLOW_TEMP("SC_FLOW_TEMP", "临时流量"),
    SC_FLOW_PACKAGE("SC_FLOW_PACKAGE", "流量包"),
    SC_SPACE("SC_SPACE", "空间"),
    SC_VOD_FUNCTION("SC_VOD_FUNCTION", "点播功能"),
    SC_ONLINE_PACKAGE("SC_ONLINE_PACKAGE", "套餐"),
    SC_CUSTOM("SC_LIVE_CUSTOM", "定制服务"),
    SC_PRIVATE("SC_PRIVATE", "私有云"),
    SC_RESIDENT("SC_RESIDENT", "驻场服务"),
    SC_STUDIO_BUILD("SC_STUDIO_BUILD", "直播间搭建"),
    SC_OPERATION("SC_OPERATION", "运营服务"),
    SC_BODY("SC_BODY", "舱体"),
    SC_INCREASE("SC_INCREASE", "增值服务"),
    SC_SALE("SC_SALE", "出售"),
    SC_RENT("SC_RENT", "租赁"),
    SC_CDN("SC_CDN", "CDN"),
    SC_EXPIRED_CLEAR("SC_EXPIRED_CLEAR", "过期清零"),
    SC_TAIL_EXTRA("SC_TAIL_EXTRA", "尾差收入"),
    SC_REFUND_EXTRA("SC_REFUND_EXTRA", "退费退收入"),
    SC_ASSOCIATION_EXTRA("SC_ASSOCIATION_EXTRA", "关联收入差额"),

    // 新增
    SC_TRANSCODING_PAGES("SC_TRANSCODING_PAGES", "转码页数"),
    SC_EXCLUSIVE_PASSAGE("SC_EXCLUSIVE_PASSAGE", "专享通道"),
    SC_REMAKE_VIDEO_DURATION("SC_REMAKE_VIDEO_DURATION", "重制视频时长"),
    SC_TRAIN_SERVICE("SC_TRAIN_SERVICE", "培训服务"), SC_ANNUAL_FEE("SC_ANNUAL_FEE", "年资费"),
    SC_EXCHANGE_FEE("SC_EXCHANGE_FEE", "转退费用"), SC_CLASS_DURATION("SC_CLASS_DURATION", "上课时长"),
    SC_MEETING_DURATION("SC_MEETING_DURATION", "参会时长"), SC_EXPIRE_SETTLE("SC_EXPIRE_SETTLE", "到期结算"),
    SC_CLOUD_RECORDING("SC_CLOUD_RECORDING", "云录制"), SC_FUNCTION_FEE("SC_FUNCTION_FEE", "功能费用"),
    SC_QUARTERLY_PACKAGE("SC_QUARTERLY_PACKAGE", "季度包"),
    SC_SEMINAR_CONCURRENT_MONTH("SC_SEMINAR_CONCURRENT_MONTH", "研讨会并发包月"),
    SC_COOPERATIVE_RETURNING("SC_COOPERATIVE_RETURNING", "合作伙伴返佣"),
    
    SC_LIVE_FLOW("SC_LIVE_FLOW", "直播流量"),

    SC_PRTC_LIVE_FLOW("SC_PRTC_LIVE_FLOW", "无延迟直播流量"),

    SC_LIVE_PRTC_CONCURRENT_DAY("SC_LIVE_PRTC_CONCURRENT_DAY", "无延迟并发包天"),
    SC_LIVE_PRTC_CONCURRENT_MONTH("SC_LIVE_PRTC_CONCURRENT_MONTH", "无延迟并发包月"),
    
    
    ;
    private String code;
    private String desc;
    
    private static final Map<String, String> MAP = new HashMap<>();
    
    static {
        for (SubCategoryEnum enableTypeEnum : SubCategoryEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String code) {
        return getMap().get(code) == null ? "" : getMap().get(code);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }

    // 根据合同OperationCode获取subCategory
    public static String getSubCategoryByOperationCode(String operationCode) {
        if (Lists.newArrayList(ContractOperationCodeEnum.DEPOSIT_AMOUNT.getCode(),
                ContractOperationCodeEnum.OVER_AMOUNT_PAY.getCode()).contains(operationCode)) {
            return SC_AMOUNT.getCode();
        } else if (ContractOperationCodeEnum.CREATE_GROUP_ACCOUNT_V2.getCode().equals(operationCode)) {
            return SC_CREATE_GROUP_ACCOUNT_V2.getCode();
        } else if (ContractOperationCodeEnum.LIVE_FUNCTION.getCode().equals(operationCode)) {
            return SC_LIVE_FUNCTION.getCode();
        } else if (ContractOperationCodeEnum.VOD_FUNCTION.getCode().equals(operationCode)) {
            return SC_VOD_FUNCTION.getCode();
        } else if (ContractOperationCodeEnum.LIVE_DURATION.getCode().equals(operationCode)) {
            return SC_LIVE_DURATION.getCode();
        } else if (ContractOperationCodeEnum.LIVE_CONCURRENT_DAILY.getCode().equals(operationCode)) {
            return SC_LIVE_CONCURRENT_DAY.getCode();
        }else if(ContractOperationCodeEnum.LIVE_PRTC_CONCURRENT_DAILY.getCode().equals(operationCode)){
            return SC_LIVE_PRTC_CONCURRENT_DAY.getCode();
        } else if (ContractOperationCodeEnum.LIVE_CONCURRENT_MONTHLY.getCode().equals(operationCode)) {
            return SC_LIVE_CONCURRENT_MONTH.getCode();
        }else if(ContractOperationCodeEnum.LIVE_PRTC_CONCURRENT_MONTHLY.getCode().equals(operationCode)){
            return SC_LIVE_PRTC_CONCURRENT_MONTH.getCode();
        } else if (ContractOperationCodeEnum.LIVE_CONCURRENT_PEAK.getCode().equals(operationCode)) {
            return SC_LIVE_CONCURRENT_PEAK.getCode();
        } else if (ContractOperationCodeEnum.LIVE_MIC_DURATION.getCode().equals(operationCode)) {
            return SC_LIVE_MIC_DURATION.getCode();
        } else if (ContractOperationCodeEnum.LIVE_GUIDE_DURATION.getCode().equals(operationCode)) {
            return SC_LIVE_GUIDE_DURATION.getCode();
        } else if (ContractOperationCodeEnum.LIVE_CHANNELS.getCode().equals(operationCode)) {
            return SC_LIVE_CHANNEL.getCode();
        } else if (Lists.newArrayList(
                ContractOperationCodeEnum.VOD_MONTHLY_PACKAGE.getCode(),
                ContractOperationCodeEnum.COOL_MONTHLY_PACKAGE.getCode()).contains(operationCode)) {
            return SC_PACKAGE_MONTH.getCode();
        } else if (Lists.newArrayList(
                ContractOperationCodeEnum.VOD_YEARLY_PACKAGE.getCode(),
                ContractOperationCodeEnum.COOL_YEARLY_PACKAGE.getCode()).contains(operationCode)) {
            return SC_PACKAGE_YEAR.getCode();
        } else if (Lists.newArrayList(
                ContractOperationCodeEnum.VOD_TEMP_FLOW.getCode(),
                ContractOperationCodeEnum.COOL_TEMP_FLOW.getCode()).contains(operationCode)) {
            return SC_FLOW_TEMP.getCode();
        } else if (Lists.newArrayList(
                ContractOperationCodeEnum.VOD_PACK_FLOW.getCode(),
                ContractOperationCodeEnum.COOL_PACK_FLOW.getCode()).contains(operationCode)) {
            return SC_FLOW_PACKAGE.getCode();
        } else if (Lists.newArrayList(
                ContractOperationCodeEnum.VOD_SPACE.getCode(),
                ContractOperationCodeEnum.COOL_SPACE.getCode()).contains(operationCode)) {
            return SC_SPACE.getCode();
        } else if (Lists.newArrayList(
                ContractOperationCodeEnum.CUSTOM_LIVE_CUSTOM.getCode(),
                ContractOperationCodeEnum.CUSTOM_VOD_CUSTOM.getCode()).contains(operationCode)) {
            return SC_CUSTOM.getCode();
        } else if (ContractOperationCodeEnum.CUSTOM_PRIVATE_ALL.getCode().equals(operationCode)) {
            return SC_PRIVATE.getCode();
        } else if (ContractOperationCodeEnum.RESIDENT_ALL.getCode().equals(operationCode)) {
            return SC_RESIDENT.getCode();
        } else if (ContractOperationCodeEnum.RESIDENT_INCREASE_BUILD.getCode().equals(operationCode)) {
            return SC_STUDIO_BUILD.getCode();
        } else if (ContractOperationCodeEnum.OPERATION_ALL.getCode().equals(operationCode)) {
            return SC_OPERATION.getCode();
        } else if (ContractOperationCodeEnum.HARD_CABIN_BODY.getCode().equals(operationCode)) {
            return SC_BODY.getCode();
        } else if (ContractOperationCodeEnum.HARD_CABIN_INCREASE.getCode().equals(operationCode)) {
            return SC_INCREASE.getCode();
        } else if (ContractOperationCodeEnum.HARD_OTHER_SALE.getCode().equals(operationCode)) {
            return SC_SALE.getCode();
        } else if (ContractOperationCodeEnum.HARD_OTHER_RENT.getCode().equals(operationCode)) {
            return SC_RENT.getCode();
        } else if (ContractOperationCodeEnum.OTHER_CDN_CDN.getCode().equals(operationCode)) {
            return SC_CDN.getCode();
        } else if (ContractOperationCodeEnum.ONLINE_PACKAGE.getCode().equals(operationCode)) {
            return SC_ONLINE_PACKAGE.getCode();
        } else if (ContractOperationCodeEnum.COOPERATIVE_RETURNING.getCode().equals(operationCode)) {
            return SC_COOPERATIVE_RETURNING.getCode();
        } else if (ContractOperationCodeEnum.LIVE_FLOW.getCode().equals(operationCode)) {
            return SC_LIVE_FLOW.getCode();
        } else if (ContractOperationCodeEnum.PRTC_LIVE_FLOW.getCode().equals(operationCode)) {
            return SC_PRTC_LIVE_FLOW.getCode();
        } else {
            return "";
        }
    }
}
