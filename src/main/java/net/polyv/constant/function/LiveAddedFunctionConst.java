package net.polyv.constant.function;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 直播增值功能
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/5/18
 */
public enum LiveAddedFunctionConst {

    /**
     * 内容转播
     */
    transmitEnabled("内容转播"),

    /**
     * 自定义页脚
     */
    footerSettingEnabled("自定义页脚"),

    /**
     * 图文直播
     */
    tuwenLiveEnabled("图文直播"),

    /**
     * 硬盘推流
     */
    diskPushEnabled("硬盘推流"),

    /**
     * 视频微门户
     */
    portalAccessEnabled("视频微门户"),

    /**
     * 边看边买
     */
    liveProductEnabled("边看边买(直播带货)"),

    /**
     * 渠道来源追踪
     */
    popularizationEnabled("渠道来源追踪（渠道推广）"),

    /**
     * 直播子账号
     */
    userChildrenEnabled("直播子账号"),

    /**
     * 双语直播间
     */
    cnAndEnLiveEnabled("双语直播间"),

    /**
     * API功能
     */
    showApiSetting("API功能"),

    /**
     * 聊天室翻译
     */
    chatTranslateEnabled("聊天室翻译"),

    /**
     * 直播品质分析
     */
    liveEvaluationEnabled("直播品质分析"),

    /**
     * 多码率观看
     */
    multirateEnabled("多码率观看"),

    /**
     * 保利威短信服务
     */
    polyvSmsEnabled("保利威短信服务"),

    /**
     * 美颜
     */
    beautyEnabled("美颜"),

    /**
     * 可创建子账号数量
     */
    childMaxNum("可创建子账号数量"),

    /**
     * 可创建子账号角色数量
     */
    childRoleMaxNum("可创建子账号角色数量"),

    /**
     * 可创建子账号组织数量
     */
    organizationMaxNum("可创建子账号组织数量"),
    ;

    private final String codeName;

    public String getCodeName() {
        return codeName;
    }

    LiveAddedFunctionConst(String codeName) {
        this.codeName = codeName;
    }

    private static final List<String> LIVE_ADDED_CODE_LIST = Arrays.stream(LiveAddedFunctionConst.values())
            .map(LiveAddedFunctionConst::name).collect(Collectors.toList());

    /**
     * 获取直播增值功能code list
     * @return 直播增值功能code list
     */
    public static List<String> getLiveAddedCodeList() {
        return LIVE_ADDED_CODE_LIST;
    }

    public static String getCodeNameByCode(String code) {

        for (LiveAddedFunctionConst functionConst : LiveAddedFunctionConst.values()) {
            if(functionConst.name().equals(code)) {
                return functionConst.getCodeName();
            }
        }

        return null;
    }
}
