package net.polyv.constant.cache;

/**
 * 缓存KEY
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/26
 */
public class CacheConst {

    private CacheConst() {

    }

    /**
     * 用户的可用分钟数缓存
     */
    public static final String CUSTOMER_AVAILABLE = "pcs:cache:customer-available";

    /**
     * 用户的可用分钟数缓存详情
     */
    public static final String CUSTOMER_AVAILABLE_DETAIL = "pcs:cache:customer-available-detail";

    // 同步纷享销客线索最大的时间戳
    // timestamp
    public static final String SYNC_CLUE_MAX_TIME_KEY = "pcs:cache:sync:clue:maxTime";

    // 手机号码对应的纷享销客线索和类型缓存key 类型：手机号
    public static final String NUMBER_MATCH_FXK_KEY = "pcs:cache:number:match:fxk:%s:%s";

    // 上传销售线索最大id缓存
    public static final String UPLOAD_RECORDING_MAX_ID = "pcs:cache:upload:recording:maxId";

    // 青鸟线索auth_code
    public static final String QING_NIAO_CLUE_AUTH_CODE_KEY = "pcs:cache:qing-niao:auth-code:key";

    // 青鸟线索token
    public static final String QING_NIAO_CLUE_TOKEN_KEY = "pcs:cache:qing-niao:token:key";

    // 青鸟线索fresh_token
    public static final String QING_NIAO_CLUE_FRESH_TOKEN_KEY = "pcs:cache:qing-niao:fresh-token:key";

    // 青鸟线索同步最大日期
    // yyyy-mm-dd
    public static final String QING_NIAO_CLUE_SYNC_MAX_DATE = "pcs:cache:qing-niao:clue-sync:max-date";

    // 腾讯线索同步最大日期
    // yyyy-mm-dd
    public static final String TENCENT_CLUE_SYNC_MAX_DATE = "pcs:cache:tencent:clue-sync:max-date";

    // 处理溯源结果最大时间
    // yyyy-mm-dd
    public static final String TRACE_CLUE_RESULT_MAX_DATE = "pcs:cache:trace:result:max-date";
    
    // 测试环境操作允许的用户
    public static final String TEST_ALLOW_USER = "pcs:cache:test:allow:user";
    
    /**
     * 字典数据
     */
    public static final String SYSTEM_DICT_LIST = "pcs:cache:system:dict:list:";
    
    //点播可用资源key
    public static final String VOD_AVAILABLE_RESOURCE_KEY = "VOD_AVAILABLE_RESOURCE_KEY:{0}:{1}";
    
    /**
     * 记录下一次清理用量记录表开始时间
     */
    public static final String CLEAN_ITEM_CONSUME_DAILY_START_DATE = "pcs:cache:clean:item-consume-daily:start-date";
    /**
     * 默认缓存时间
     */
    public static final int DEFAULT_CACHE_SECONDS = 3600;
    public static final int MIN_20_CACHE_SECONDS = 1200;
    
    
    public static String getCustomerAvailableCacheKey(String customerId, String resourceCode) {
        return CacheConst.CUSTOMER_AVAILABLE + "::" + customerId + ":" + resourceCode;
    }
    
    public static String getCustomerAvailableDetailCacheKey(String customerId, String resourceCode) {
        return CacheConst.CUSTOMER_AVAILABLE_DETAIL + "::" + customerId + ":" + resourceCode;
    }
    
    //随机返回0-59秒
    public static int getRandomSeconds() {
        return (int) (Math.random() * 60);
    }
}
