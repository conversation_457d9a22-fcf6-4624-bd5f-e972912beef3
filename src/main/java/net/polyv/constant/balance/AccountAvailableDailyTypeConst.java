package net.polyv.constant.balance;

/**
 * 账号余额快照类型
 * <AUTHOR>
 * @since 2020/6/28
 */
public enum AccountAvailableDailyTypeConst {
    
    /**
     * 充值
     */
    DEPOSIT(1),
    
    /**
     * 赠送金额
     */
    DONATE(2),
    
    /**
     * 授信额度
     */
    CREDIT(3),
    /**
     * 测试金额
     */
    TEST(4),
    ;
    
    private Integer value;
    
    AccountAvailableDailyTypeConst(Integer value) {
        this.value = value;
    }
    
    public Integer getValue() {
        return value;
    }
}
