package net.polyv.constant;

/**
 * 加锁key常量
 * <AUTHOR>
 * @since 2020/5/14
 */
public interface LockKeyConst {

    /**
     * 顾客余额操作
     * 余额=充值额度+赠送额度+授信额度
     */
    String CUSTOMER_BALANCE_OPERATE = "pcs:lock:balance:%s";
    
    /**
     * 结算各个阶段操作使用的锁
     * 格式：pcs:lock:clearing:{resourceCode}
     */
    String CLEARING_PROCESS = "pcs:lock:clearing:%s";
    
    /**
     * 定时任务开关用的锁
     * 格式： pcs:lock:schedule:switch:{class.method}
     */
    String SCHEDULE_SWITCH_KEY = "pcs:lock:schedule:switch:%s";
    /**
     * 手动触发同步数据
     * 格式： pcs:lock:sync:data:
     */
    String SYNC_DATA_KEY = "pcs:lock:sync:data";
    /**
     * 导出数据
     * 格式：pcs:lock:export:data: sign
     */
    String EXPORT_DATA_KEY = "pcs:lock:export:data:%s";
    
    
}
