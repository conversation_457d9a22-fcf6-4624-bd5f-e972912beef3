package net.polyv.constant.examinationDonate;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 直播包天计费模式
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022-06-14
 */
@Getter
@AllArgsConstructor
public enum LiveDailyBillingPlanEnum {

    TEST(1, "包天测试 - 出分钟数账单，正常扣分钟数"),
    DONATE(2, "包天计费 - 出并发账单，分钟数不扣")
    ;

    private Integer code;
    private String desc;

    private static final Map<Integer, String> MAP = new HashMap<>();

    static {
        for (LiveDailyBillingPlanEnum enableTypeEnum : LiveDailyBillingPlanEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<Integer, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
