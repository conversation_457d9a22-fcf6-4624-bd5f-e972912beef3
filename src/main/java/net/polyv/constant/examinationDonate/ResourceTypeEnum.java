package net.polyv.constant.examinationDonate;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 资源类型
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022-06-14
 */
@Getter
@AllArgsConstructor
public enum ResourceTypeEnum {

    DEPOSIT(0,"正式"),
    TEST(2, "测试"),
    DONATE(1, "赠送"),

    ;

    private Integer code;
    private String desc;

    private static final Map<Integer, String> MAP = new HashMap<>();

    static {
        for (ResourceTypeEnum enableTypeEnum : ResourceTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<Integer, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
