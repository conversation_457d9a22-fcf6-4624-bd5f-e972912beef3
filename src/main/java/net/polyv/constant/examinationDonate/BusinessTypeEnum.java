package net.polyv.constant.examinationDonate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import com.alibaba.fastjson.JSON;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 业务类型
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022-06-15
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
   /**业务类型*/
    VOD_PACKAGE(1, "点播套餐"),
    VOD_FLOW(2, "点播流量"),
    VOD_SPACE(3, "点播空间"),
    LIVE_DURATION(4, "直播分钟数"),
    LIVE_DAILY_TEST(5, "直播包天测试"),
    GUIDE_DURATION(6, "导播台分钟数"),
    MIC_DURATION(7, "连麦分钟数"),
    PRTC_AMOUNT(8, "无延迟体验金"),
    AMOUNT(9, "金额"),
    LIVE_PEAK_CONCURRENCE(10, "直播峰值并发"),
    ADD_CHANNELS(11, "频道数"),
    LIVE_MONTHLY_CONCURRENCE(13, "直播包月并发"),
    LIVE_DAILY(14, "直播包天"),
    LIVE_PRTC_DAILY_TEST(15, "直播无延迟包天测试"),
    LIVE_PRTC_DAILY(16, "直播无延迟包天"),
    LIVE_PRTC_MONTHLY(17, "直播无延迟包月并发"),
    RESOURCE_POINT(18 , "资源点"),
    LIVE_FLOW(19, "直播流量")

    ;
    
    private Integer code;
    private String desc;
    
    private static final Map<Integer, String> MAP = new HashMap<>();
    
    static {
        for (BusinessTypeEnum enableTypeEnum : BusinessTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<Integer, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
    
    public static  Optional<BusinessTypeEnum>  getCodeByEnumName(String enumName){
       return  Arrays.stream(values()).filter(v -> v.name().equals(enumName)).findFirst();
    }
    
    public static Optional<BusinessTypeEnum> getByCode(Integer code) {
        return Arrays.stream(values()).filter(v -> v.getCode().equals(code)).findFirst();
    }
}
