package net.polyv.constant.marketing;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 线索来源一枚举类
 * @author: Neo
 * @date: 2022-06-16
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ClueSourceTwoConstant {

    TWO_1("0", "市场获取-自然流量"),
    TWO_2("1", "市场获取-付费推广"),
    TWO_3("2", "市场获取-无法溯源"),
    TWO_4("3", "市场获取-400电话"),
    TWO_5("4", "市场获取-云市场"),
    TWO_6("5", "市场获取-私域活动"),
    TWO_7("6", "市场获取-内容营销"),
    TWO_8("7", "市场获取-线下活动"),
    TWO_9("8", "全员营销（内部推荐）"),
    TWO_10("9", "招投标网站"),
    TWO_11("10", "市场获取-其他"),
    TWO_12("11", "已入库"),
    TWO_13("12", "未入库"),
    TWO_14("13", "老客户介绍"),
    TWO_15("14", "展会"),
    TWO_16("15", "企业查询网站"),
    TWO_17("16", "协会"),
    TWO_18("18", "陌电或陌邮件"),
    TWO_19("19", "自有资源"),
    TWO_20("65Y0lqZ5t", "客户复购"),
    TWO_21("20", "市场获取-品牌流量"),

    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (ClueSourceTwoConstant enableTypeEnum : ClueSourceTwoConstant.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static String getByDesc(String desc) {
        return getMap().entrySet().stream()
                .filter(entry -> StringUtils.equals(entry.getValue(), desc))
                .map(Map.Entry::getKey).findFirst().orElse(null);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }

}
