package net.polyv.constant.marketing;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 线索来源一枚举类
 * @author: Neo
 * @date: 2022-06-16
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ClueSourceFourConstant {

    FOUR_1("0", "百度"),
    FOUR_2("1", "360"),
    FOUR_3("2", "谷歌"),
    FOUR_4("3", "搜狗"),
    FOUR_5("4", "直接访问"),
    FOUR_6("5", "知乎"),
    FOUR_7("6", "公众号矩阵"),
    FOUR_8("7", "百度-搜索"),
    FOUR_9("8", "百度-通用品专"),
    FOUR_10("9", "百度-品牌词品专"),
    FOUR_11("10", "百度-品牌词计划"),
    FOUR_12("11", "神马"),
    FOUR_13("12", "必应"),
    FOUR_14("13", "抖音信息流"),
    FOUR_15("14", "知乎信息流"),
    FOUR_16("15", "朋友圈广告"),
    FOUR_17("16", "头条信息流"),
    FOUR_18("17", "百度信息流"),
    FOUR_19("18", "微信"),
    FOUR_20("19", "微信公众号"),
    FOUR_21("20", "未知"),
    FOUR_22("21", "腾讯云"),
    FOUR_23("22", "阿里云"),
    FOUR_24("23", "华为云"),
    FOUR_25("24", "第三方公众号"),
    FOUR_26("25", "第三方社群"),
    FOUR_27("26", "保利威公众号"),
    FOUR_28("27", "保利威社群"),
    FOUR_29("28", "官网"),
    FOUR_30("29", "实体册"),
    FOUR_31("30", "多分类直播"),
    FOUR_32("31", "行业直播"),
    FOUR_33("32", "产品直播"),
    FOUR_34("33", "场景直播"),
    FOUR_35("34", "小红书"),
    FOUR_36("35", "品牌类-沙龙"),
    FOUR_37("36", "品牌类-发布会"),
    FOUR_38("37", "市场营销-线下展会"),
    FOUR_39("38", "企培类-线下展会"),
    FOUR_40("39", "教育类-线下展会"),
    FOUR_41("40", "网校云"),
    FOUR_42("41", "电销外呼"),
    FOUR_43("42", "中保协H5页"),
    FOUR_44("43", "36氪"),
    FOUR_45("44", "邮件咨询"),
    FOUR_46("45", "营销类-沙龙"),
    FOUR_47("46", "品牌类-行业峰会"),
    FOUR_48("47", "品牌类-线下展会"),
    FOUR_49("48", "人力资源-线下展会"),
    FOUR_50("49", "通用类-线下展会"),
    FOUR_51("50", "转推荐"),
    FOUR_52("51", "抖音"),
    FOUR_54("52", "视频号"),
    FOUR_53("53", "留言"),
    FOUR_55("Lmb7Od7Gb", "广点通"),
    FOUR_56("54", "360-品牌词计划"),
    FOUR_57("55", "搜狗-品牌词计划"),
    FOUR_58("56", "神马-品牌词计划"),
    FOUR_59("57", "百度知道合伙人"),
    FOUR_60("58", "知加"),
    FOUR_61("59", "百度文库"),
    FOUR_62("60", "百家号"),
    FOUR_63("61", "头条号"),
    FOUR_64("62", "哗哩哗哩"),
    FOUR_65("63", "csdn"),
    FOUR_66("64", "微博"),
    FOUR_67("65", "网易号"),
    FOUR_68("66", "搜狐号"),
    FOUR_69("67", "一点号"),
    FOUR_70("68", "人人都是产品经理"),
    FOUR_71("69", "简书"),
    FOUR_72("70", "腾讯视频"),
    FOUR_73("71", "快手"),
    FOUR_74("72", "好看视频"),
    FOUR_75("73", "视频号矩阵"),

    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (ClueSourceFourConstant enableTypeEnum : ClueSourceFourConstant.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static String getByDesc(String desc) {
        return getMap().entrySet().stream()
                .filter(entry -> StringUtils.equals(entry.getValue(), desc))
                .map(Map.Entry::getKey).findFirst().orElse(null);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }

}
