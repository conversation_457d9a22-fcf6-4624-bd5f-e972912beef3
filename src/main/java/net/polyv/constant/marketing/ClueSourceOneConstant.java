package net.polyv.constant.marketing;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 线索来源一枚举类
 * @author: Neo
 * @date: 2022-06-16
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ClueSourceOneConstant {

    ONE_1("0", "公司资源"),
    ONE_2("1", "生态渠道"),
    ONE_3("2", "自拓"),
    ONE_4("3", "市场合作"),

    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (ClueSourceOneConstant enableTypeEnum : ClueSourceOneConstant.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static String getByDesc(String desc) {
        return getMap().entrySet().stream()
                .filter(entry -> StringUtils.equals(entry.getValue(), desc))
                .map(Map.Entry::getKey).findFirst().orElse(null);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }

}
