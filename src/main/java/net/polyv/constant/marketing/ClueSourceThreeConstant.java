package net.polyv.constant.marketing;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 线索来源一枚举类
 * @author: Neo
 * @date: 2022-06-16
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ClueSourceThreeConstant {

    THREE_1("0", "官网注册"),
    THREE_2("1", "线上咨询"),
    THREE_3("2", "表单"),
    THREE_4("3", "无法溯源"),
    THREE_5("4", "400电话"),
    THREE_6("5", "云市场"),
    THREE_7("6", "私域活动"),
    THREE_8("7", "第三方投放咨询"),
    THREE_9("8", "资料领取"),
    THREE_10("9", "直播营销"),
    THREE_11("10", "自媒体"),
    THREE_12("11", "沙龙"),
    THREE_13("12", "峰会"),
    THREE_14("13", "线下展会"),
    THREE_15("14", "企业文化社群"),
    THREE_16("15", "SaaS点评网"),
    THREE_17("16", "CSDN"),
    THREE_18("17", "DCLOUD"),
    THREE_19("18", "杭州阔知网络科技有限公司"),
    THREE_20("19", "TO B CGO"),
    THREE_21("20", "优刻得科技股份有限公司"),
    THREE_22("21", "阿里云"),
    THREE_23("22", "爱点击（北京）数据科技有限公司"),
    THREE_24("23", "广州凡科互联网科技股份有限公司"),
    THREE_25("24", "乌不拉科技（上海）有限公司"),
    THREE_26("25", "深圳市蓝凌软件股份有限公司"),
    THREE_27("26", "培训杂志"),
    THREE_28("27", "企业文化老司机"),
    THREE_29("28", "赛驰网络"),
    THREE_30("29", "淘宝"),
    THREE_31("30", "未知"),
    THREE_32("31", "深圳市每日互连科技有限公司"),
    THREE_33("32", "北京致趣科技有限公司"),
    THREE_34("twEC861H6", "TO B PARK"),
    THREE_35("33", "外部咨询"),

    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (ClueSourceThreeConstant enableTypeEnum : ClueSourceThreeConstant.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static String getByDesc(String desc) {
        return getMap().entrySet().stream()
                .filter(entry -> StringUtils.equals(entry.getValue(), desc))
                .map(Map.Entry::getKey).findFirst().orElse(null);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }

}
