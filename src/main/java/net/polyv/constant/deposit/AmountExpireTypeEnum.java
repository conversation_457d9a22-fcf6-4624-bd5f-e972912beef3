package net.polyv.constant.deposit;

import com.alibaba.fastjson.JSON;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 *  充值金额过期类型枚举类
 */
@Getter
public enum AmountExpireTypeEnum {


    POSTPONE(1,"顺延"),

    INDEPENDENCE(2,"独立");

    AmountExpireTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String name;
    private final Integer code;

    public static AmountExpireTypeEnum getByType(Integer code) {
        for (AmountExpireTypeEnum value : AmountExpireTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getText(Object code){
        for (AmountExpireTypeEnum aet : AmountExpireTypeEnum.values()) {
            if(aet.getCode().equals((Integer)code)){
                return aet.getName();
            }
        }
        return null;
    }

    private static final Map<Integer,String> MAP = new HashMap<>();

    static {
        for (AmountExpireTypeEnum enableTypeEnum : AmountExpireTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.name);
        }
    }

    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<Integer, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
