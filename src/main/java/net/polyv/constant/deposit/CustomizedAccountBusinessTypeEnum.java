package net.polyv.constant.deposit;

import com.alibaba.fastjson.JSON;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 *  自定义账号（如华为云数字人）业务类型枚举
 */
@Getter
public enum CustomizedAccountBusinessTypeEnum {

    CONCURRENCE(1,"并发"),

    DURATION(2,"分钟数"),

    TEMP_SPACE(3,"增容空间"),

    FLOW_PACKAGE(4,"流量包"),

    MIC_DURATION(5,"连麦分钟数"),

    GUIDE_DURATION(6,"导播台分钟数")
    ;

    CustomizedAccountBusinessTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String name;
    private final Integer code;

    public static CustomizedAccountBusinessTypeEnum getByType(Integer code) {
        for (CustomizedAccountBusinessTypeEnum value : CustomizedAccountBusinessTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getText(Object code){
        for (CustomizedAccountBusinessTypeEnum aet : CustomizedAccountBusinessTypeEnum.values()) {
            if(aet.getCode().equals((Integer)code)){
                return aet.getName();
            }
        }
        return null;
    }

    private static final Map<Integer,String> MAP = new HashMap<>();

    static {
        for (CustomizedAccountBusinessTypeEnum enableTypeEnum : CustomizedAccountBusinessTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.name);
        }
    }

    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<Integer, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
