package net.polyv.constant.deposit;

import com.alibaba.fastjson.JSON;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 *  金额充值类型枚举类
 */
@Getter
public enum AmountDepositTypeEnum {

    TEST(2,"测试"),

    DONATE(1,"赠送"),

    DEPOSIT(0,"正式");

    AmountDepositTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String name;
    private final Integer code;

    public static AmountDepositTypeEnum getByType(Integer code) {
        for (AmountDepositTypeEnum value : AmountDepositTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getText(Object code){
        for (AmountDepositTypeEnum aet : AmountDepositTypeEnum.values()) {
            if(aet.getCode().equals((Integer)code)){
                return aet.getName();
            }
        }
        return null;
    }

    private static final Map<Integer,String> MAP = new HashMap<>();

    static {
        for (AmountDepositTypeEnum enableTypeEnum : AmountDepositTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.name);
        }
    }

    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<Integer, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
