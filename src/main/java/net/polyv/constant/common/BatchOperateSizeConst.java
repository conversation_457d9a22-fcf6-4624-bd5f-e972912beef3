package net.polyv.constant.common;

/**
 * 批量操作阈值
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/15
 */
public final class BatchOperateSizeConst {

    private BatchOperateSizeConst() { }

    /**
     * 同步crm销售机会的每次操作量
     */
    public static final int SYNC_CRM_SALES_OPPORTUNITIES_BATCH_SIZE = 100;

    /**
     * 同步crm客户的每次操作量
     */
    public static final int SYNC_CRM_CUSTOMER_BATCH_SIZE = 100;
}
