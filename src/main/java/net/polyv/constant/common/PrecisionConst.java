package net.polyv.constant.common;

import java.math.BigDecimal;

/**
 * 精度相关常量
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/16
 */
public final class PrecisionConst {

    private PrecisionConst() {
    }

    /**
     * 金额类默认扩大精度
     */
    public static final int DEFAULT_MONEY_EXPAND_PRECISION = 100000;
    
    /**
     * 默认保存精度
     */
    public static final int DEFAULT_PRECISION = 2;
    
    /**
     * 默认保存精度
     */
    public static final int DEFAULT_PRECISION_FOR_EXPORT = 5;
    /**
     * 默认单价比例单位
     */
    public static final int DEFAULT_RATIO_UNIT_HUNDRED = 100;
    
    /**
     * BigDecimal的进位模式
     */
    public static final int DEFAULT_BIG_DECIMAL_ROUNDING_MODE = BigDecimal.ROUND_HALF_UP;
    
    /**
     * 默认PRTC每分钟价格
     */
    public static final BigDecimal DEFAULT_PRTC_PRICE_PER_MINUTE = BigDecimal.valueOf(0.05);
}
