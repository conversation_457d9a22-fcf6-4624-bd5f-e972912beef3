package net.polyv.constant.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 下拉选项枚举类
 * @author: Neo
 * @date: 2021-11-09
 */
@AllArgsConstructor
public enum SelectionTypeEnum {

    /**
     * 商机-业务类别
     */
    BUSINESS_TYPE("business_type", "商机-业务类别"),

    /**
     * 商机计费方式类型
     */
    BUSINESS_BILLING_METHOD("business_billing_method", "商机-计费方式"),

    /**
     * 商机业务类别+计费方式联合返回
     */
    BUSINESS_TYPE_WITH_BILLING_METHOD("business_type_with_billing_method", "商机-业务类别+计费方式"),

    /**
     * 商机分类类型
     */
    BUSINESS_CLASSIFICATION("business_classification", "商机-分类类型"),

    /**
     * 商机分类类型
     */
    BUSINESS_CUSTOMER_SOURCE("business_customer_source", "商机-客户来源"),

    /**
     * 商机状态
     */
    BUSINESS_STATUS("business_status", "商机状态"),

    /**
     * 商机行业类型
     */
    BUSINESS_INDUSTRY_TYPE("business_industry_type", "商机行业类型"),

    /**
     * 商机-发票-发票类型
     */
    BUSINESS_INVOICE_TYPE("business_invoice_type", "商机-发票-发票类型"),

    /**
     * 商机-发票-开票形式
     */
    BUSINESS_INVOICE_FORM("business_invoice_form", "商机-发票-开票形式"),

    /**
     * 商机-发票-确认状态
     */
    BUSINESS_INVOICE_CONFIRM("business_invoice_confirm", "商机-发票-确认状态"),

    /**
     * 商机-发票-开票状态
     */
    BUSINESS_INVOICE_INVOICING("business_invoice_invoicing", "商机-发票-开票状态"),

    /**
     * 商机-金额规格-直播套餐
     */
    BUSINESS_AMOUNT_SPEC_LIVE_PACKAGE("business_amount_spec_vod_package", "商机-金额规格-直播套餐"),

    /**
     * 商机-金额规格-点播套餐
     */
    BUSINESS_AMOUNT_SPEC_VOD_PACKAGE("business_amount_spec_vod_package", "商机-金额规格-点播套餐"),


    /**
     * 商机-关联到款-收款账户
     */
    BUSINESS_PAYMENT_RECEIVE_ACCOUNT("business_payment_receive_account", "商机-关联到款-收款账户"),

    /**
     *  商机-金额规格-到期类型
     */
    BUSINESS_AMOUNT_SPECIFICATION_EXPIRE_TYPE("business_amount_specification_expire_type", "商机-金额规格-到期类型"),

    /**
     *  商机-金额规格-直播套餐
     */
    BUSINESS_AMOUNT_SPECIFICATION_LIVE_PACKAGE("business_amount_specification_live_package", "商机-金额规格-直播套餐"),

    /**
     *  商机-金额规格-点播套餐
     */
    BUSINESS_AMOUNT_SPECIFICATION_VOD_PACKAGE("business_amount_specification_vod_package", "商机-金额规格-点播套餐"),


    ;

    /**
     * name
     */
    @Getter
    private String name;

    /**
     * desc
     */
    @Getter
    private String desc;
}
