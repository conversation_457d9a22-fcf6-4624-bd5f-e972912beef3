package net.polyv.constant;

/**
 * 结算常量
 * <AUTHOR>
 * @date 2022/9/22 9:35
 */

public interface PcsConstant {
    /**
     * polyv字符
     */
    String POLYV = "polyv";
    
    String DEPOSIT_ZERO ="DEPOSIT_ZERO";
    String AMOUNT_REFUND ="AMOUNT_REFUND";
    /**
     * 状态符合
     */
    
    String Y = "Y";
    String N = "N";
    /**
     * 常用字符
     */
    String MONTHLY = "月结";
    String GROUP_1 = "000";
    String GROUP_2 = "0";
    String ZERO_AMOUNT = "0";
    String ZERO_AMOUNT_DOUBLE = "0.00";
    String ZERO_AMOUNT_FLOAT = "0.0";
    String NOT_CONTRACT = "-1";
    
    /**
     * 数值
     */
    Integer TWENTY_FOUR = 24;
    
    
    Integer FIVE = 5;
    /**
     * 响应字符
     */
    String SUCCESS = "success";
    
    /**
     * 时间格式
     */
    String DATE_MONTH = "yyyy-MM";
    /**
     * 字符串
     */
    String ZERO = "0";
}
