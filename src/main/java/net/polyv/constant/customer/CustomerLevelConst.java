package net.polyv.constant.customer;

/**
 * 客户等级枚举
 * <AUTHOR>
 */
public enum CustomerLevelConst {
    /**
     * 按分钟数计费。
     */
    One("0", "一级（SVIP客户）"),
    
    /**
     * 包天计费。
     */
    Two("1", "二级（VIP客户）"),
    
    /**
     * 包月计费。
     */
    Three("2", "三级（潜力大客户）"),
    
    /**
     * 并发峰值计费。
     */
    Four("3", "四级（潜力小客户）"),
    
    /**
     * 导播台分钟数计费。
     */
    Five("4", "五级（普通小客户）"),
    
    /**
     * 连麦分钟数计费。
     */
    ShengTai_Important("5", "生态伙伴（重要）"),
    
    ShengTai("6", "生态伙伴"),
    
    Other("other", "其他");
    /**
     * 值。
     */
    private String value;
    
    /**
     * 中文描述。
     */
    private String text;
    
    /**
     * @param status 值
     * @param desc 中文描述
     */
    private CustomerLevelConst(String status, String desc) {
        value = status;
        text = desc;
    }
    
    /**
     * @return 当前枚举对象的值。
     */
    public String getValue() {
        return value;
    }
    
    /**
     * @return 当前状态的中文描述。
     */
    public String getText() {
        return text;
    }
    
    /**
     * 根据用户状态的值获取枚举对象。
     * @param status 用户状态的值
     * @return 枚举对象
     */
    public static CustomerLevelConst getInstance(String status) {
        CustomerLevelConst[] allStatus = CustomerLevelConst.values();
        for (CustomerLevelConst ws : allStatus) {
            if (ws.getValue().equalsIgnoreCase(status)) {
                return ws;
            }
        }
        return null;
    }
}
