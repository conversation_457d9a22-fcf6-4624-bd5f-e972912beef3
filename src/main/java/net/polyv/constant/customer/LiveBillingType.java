package net.polyv.constant.customer;

/**
 * 直播计费类型
 * 
 * <AUTHOR>
 */
public enum LiveBillingType {
    /**
     * 按分钟数计费。
     */
    Duration("duration", "按分钟数计费"),
    
    /**
     * 包天计费。
     */
    Daily("daily", "包天计费"),

    /**
     * 包月计费。
     */
    Monthly("monthly", "包月计费"),

    /**
     * 并发峰值计费。
     */
    Peak("peak", "并发峰值计费"),

    /**
     * 导播台分钟数计费。
     */
    Guide("guide", "导播台分钟数计费"),

    /**
     * 连麦分钟数计费。
     */
    Mic_Minutes("minutes", "连麦分钟数计费"),

    /**
     * 连麦并发计费。
     */
    Mic_Concurrence("concurrence", "连麦并发计费"),

    /**
     * ppt按页数计费
     */
    Ppt_Page("page", "PPT按页数付费"),

    /**
     * ppt专享通道
     */
    Ppt_Line("line", "PPT专享通道"),

    /**
     * 无延迟分钟数
     */
    Prtc_Duration("prtc", "按无延迟分钟数计费"),

    /**
     * 无延迟包天
     */
    Prtc_Daily("prtc_daily", "无延迟包天计费"),

    /**
     * 无延迟包月
     */
    Prtc_Monthly("prtc_monthly", "无延迟包月计费"),

    ;
    /**
     * 值。
     */
    private String value;

    /**
     * 中文描述。
     */
    private String text;

    /**
     * @param status 值
     * @param desc 中文描述
     */
    private LiveBillingType(String status, String desc) {
        value = status;
        text = desc;
    }

    /**
     * @return 当前枚举对象的值。
     */
    public String getValue() {
        return value;
    }

    /**
     * @return 当前状态的中文描述。
     */
    public String getText() {
        return text;
    }

    /**
     * 根据用户状态的值获取枚举对象。
     * 
     * @param status 用户状态的值
     * @return 枚举对象
     */
    public static LiveBillingType getInstance(String status) {
        LiveBillingType[] allStatus = LiveBillingType.values();
        for (LiveBillingType ws : allStatus) {
            if (ws.getValue().equalsIgnoreCase(status)) {
                return ws;
            }
        }
        throw new IllegalArgumentException("status值非法，没有符合的枚举对象");
    }
}
