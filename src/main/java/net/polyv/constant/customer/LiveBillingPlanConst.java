package net.polyv.constant.customer;

/**
 * 直播计费方式
 * <AUTHOR>
 * @since 2020/5/20
 */
public enum LiveBillingPlanConst {

    /**
     * 分钟数计费
     */
    duration("按分钟数计费"),

    /**
     * 并发包天
     */
    daily("包天计费"),

    prtc_daily("无延迟包天计费"),

    /**
     * 并发包月
     */
    monthly("包月计费"),

    prtc_monthly("无延迟包月计费"),
    /**
     * 并发包年
     */
    yearly("包年计费"),

    /**
     * 并发峰值计费
     */
    peak("并发峰值计费"),
    /**
     * 并发时段
     */
    concurrence(""),
    /**
     * 无限制
     */
    unlimited("无限制使用"),
    
    live_flow("直播流量计费");
    
    private String label;
    
    LiveBillingPlanConst(String label) {
        this.label = label;
    }
    
    public String getLabel() {
        return label;
    }
}
