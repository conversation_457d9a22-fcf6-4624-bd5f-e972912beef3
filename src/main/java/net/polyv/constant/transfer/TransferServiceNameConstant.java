package net.polyv.constant.transfer;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据转移服务名称常量
 * <AUTHOR>
 * @date 2022/10/9 11:12
 */
@AllArgsConstructor
@Getter
public enum TransferServiceNameConstant {
    /**
     * 数据转移
     */
    REFUND_TRANSFER("退费数据转移任务", "RefundTransferDataServiceImpl"),
    CONTRACT_TRANSFER("财务合同数据同步任务", "ContractTransferDataServiceImpl"),
    INCOME_TRANSFER("历史收入数据同步任务", "IncomeTransferDataServiceImpl"),
    INCOME_DETAIL_TRANSFER("历史收入明细数据同步任务", "IncomeDetailTransferDataServiceImpl"),
    CONCURRENT_SETTING_TRANSFER("历史超并发数据同步任务", "ConcurrentSettingTransferDataServiceImpl"),
    COLLECT_MONEY_TRANSFER("回款信息同步任务", "CollectMoneyTransferDataServiceImpl"),
    UN_FOLLOW_CHANCE_TRANSFER("财务未关联合同的销售机会任务", "UnFollowChanceTransferDataServiceImpl"),
    VOD_TRAFFIC_SPACE_TRANSFER("点播空间-流量同步任务", "VodSpaceTrafficTransferServiceImpl"),
    VOD_TRAFFIC_SPACE_PRE_STATISTIC("点播空间-流量预统计任务", "VodSpaceTrafficPreStatisticServiceImpl"),
    ;
    private final String desc;
    private final String name;
    
    
}
