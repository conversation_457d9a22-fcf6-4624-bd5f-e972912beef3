package net.polyv.constant.group;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 主账号资源分配业务类型
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022-12-21
 */
@Getter
@AllArgsConstructor
public enum ResourceAssignBusinessTypeEnum {
    /**
     * 业务类型
     */
    CONCURRENCE(1, "并发"), DURATION(2, "分钟数"), SPACE(3, "空间"), TRAFFIC(4, "流量"), MIC_DURATION(5, "连麦分钟数"),
    GUIDE_DURATION(6, "导播台分钟数"), AMOUNT(7, "金额"), MATERIAL_SPACE(8, "素材库空间"), MATERIAL_TRAFFIC(9, "素材库流量")
    ,AI_PPTVIDEO_DIGITALHUMAN_ENABLED(10,"视频创作（含数字人）"),AI_PPTVIDEO_ENABLED(11,"视频创作（不含数字人）");
    
    private Integer code;
    private String desc;
    
    private static final Map<Integer, String> MAP = new HashMap<>();
    
    static {
        for (ResourceAssignBusinessTypeEnum enableTypeEnum : ResourceAssignBusinessTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }
    
    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }
    
    public static Map<Integer, String> getMap() {
        return MAP;
    }
    
    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
    
    public static Optional<ResourceAssignBusinessTypeEnum> getCodeByEnumName(String enumName) {
        return Arrays.stream(values()).filter(v -> v.name().equals(enumName)).findFirst();
    }
    
    public static ResourceAssignBusinessTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(values()).filter(v -> v.getCode().equals(code)).findFirst().get();
    }

    public static List<Integer> valuess(){
        return Stream.of(values()).map(ResourceAssignBusinessTypeEnum::getCode).collect(Collectors.toList()) ;
    }
}
