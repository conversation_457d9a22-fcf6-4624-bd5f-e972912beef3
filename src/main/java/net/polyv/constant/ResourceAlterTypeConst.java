package net.polyv.constant;

/**
 * 资源变动类型
 * <AUTHOR>
 * @since 2020/8/10
 */
public enum ResourceAlterTypeConst {
    discard_clean,
    expire_clean,
    deposit,
    consume,
    adjust_bill,
    manual_deposit,
    //点播同步 包含充值，消耗量
    vod_sync,
    ;
    public static ResourceAlterTypeConst getByName(String name){
        for (ResourceAlterTypeConst value : ResourceAlterTypeConst.values()) {
            if(value.name().equals(name)){
                return value;
            }
        }
        return null;
    }
}
