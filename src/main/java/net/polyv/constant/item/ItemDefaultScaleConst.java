package net.polyv.constant.item;

/**
 * 计费项默认的梯度（使用的计费项：并发包天、并发包月）
 * <AUTHOR>
 * @since 2020/5/29
 */
public enum ItemDefaultScaleConst {

    /**
     * 默认最小值
     */
    default_scale_min(0L),

    /**
     * 默认最大值：默认的最大值为一千万
     */
    default_scale_max(1073741824000000000L)

    ;

    ItemDefaultScaleConst(Long scale) {
        this.scale = scale;
    }

    private final Long scale;

    public Long getScale() {
        return scale;
    }
}
