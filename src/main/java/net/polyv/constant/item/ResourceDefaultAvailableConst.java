package net.polyv.constant.item;

/**
 * 资源默认可用量常量
 * <AUTHOR>
 * @since 2020/11/11
 */
public enum ResourceDefaultAvailableConst {
    
    PRTC_DURATION(ResourceCodeConst.prtc_duration, 2000L),
    GUIDE_DURATION(ResourceCodeConst.guide_duration, 120L),
    CDN_DURATION(ResourceCodeConst.duration, 180L),
    MIC_DURATION(ResourceCodeConst.mic_duration, 100L);
    
    private ResourceCodeConst resourceCode;
    private Long available;
    
    ResourceDefaultAvailableConst(ResourceCodeConst resourceCode, Long available) {
        this.resourceCode = resourceCode;
        this.available = available;
    }
    
    public ResourceCodeConst getResourceCode() {
        return resourceCode;
    }
    
    public Long getAvailable() {
        return available;
    }
    
    public static Long getAvailableByResourceCode(String resourceCode) {
        ResourceCodeConst resourceCodeConst = ResourceCodeConst.getByResourceCode(resourceCode);
        if (resourceCodeConst == null) {
            return 0L;
        }
    
        for (ResourceDefaultAvailableConst value : ResourceDefaultAvailableConst.values()) {
            if (resourceCodeConst.equals(value.getResourceCode())) {
                return value.getAvailable();
            }
        }
        return 0L;
    }
}
