package net.polyv.constant.item;

import net.polyv.constant.ConcurrenceModeConst;

/**
 * 旧套餐项目编码
 * <AUTHOR>
 * @since 2020/6/16
 */
public enum OldPackItemCodeConst {

    /**
     * 并发包天
     */
    daily("daily", ItemCodeConst.concur_daily, ConcurrenceModeConst.DAILY),

    /**
     * 无延迟包天
     */
    prtc_daily("prtc_daily", ItemCodeConst.prtc_concur_daily, ConcurrenceModeConst.PRTC_DAILY),

    /**
     * 并发包月
     */
    monthly("monthly", ItemCodeConst.concur_monthly, ConcurrenceModeConst.MONTHLY),

    /**
     * 无延迟包月
     */
    prtc_monthly("prtc_monthly", ItemCodeConst.prtc_concur_monthly, ConcurrenceModeConst.PRTC_MONTHLY),
    
    /**
     * 年峰值并发
     */
    peak("peak", ItemCodeConst.concur_peak, ConcurrenceModeConst.PEAK),
    
    /**
     * 分钟数套餐
     */
    duration("duration", null, null),
    
    /**
     * 流量套餐
     */
    live_flow("live_flow", null, null),
    
    /**
     * 无限制使用
     */
    unlimited("unlimited", null, null);;
    
    // 计费方式
    private String billingPlan;
    
    // 计费项枚举
    private ItemCodeConst itemCodeConst;
    
    // 并发模式
    private Integer mode;
    
    OldPackItemCodeConst(String billingPlan, ItemCodeConst itemCodeConst, Integer mode) {
        this.billingPlan = billingPlan;
        this.itemCodeConst = itemCodeConst;
        this.mode = mode;
    }
    
    public String getBillingPlan() {
        return billingPlan;
    }
    
    public ItemCodeConst getItemCodeConst() {
        return itemCodeConst;
    }
    
    public Integer getMode() {
        return mode;
    }
    
    public static OldPackItemCodeConst findByBillingPlan(String billingPlan){
        for(OldPackItemCodeConst codeConst: OldPackItemCodeConst.values()){
            if(codeConst.getBillingPlan().equals(billingPlan)){
                return codeConst;
            }
        }
        return null;
    }
}
