package net.polyv.constant.item;

/**
 * 项目梯度编码
 * <AUTHOR>
 * @since 2020/5/26
 */
public interface ItemScaleCodeConst {

    /**
     * 连麦分钟数梯度编码
     */
    enum MicPd {

        /**
         * 7 人及以下连麦
         */
        mic_pd_7,

        /**
         * 8 人及以上连麦
         */
        mic_pd_8
    }

    /**
     * 小班课梯度编码
     */
    enum SmallClass {

        /**
         * 小班课 1v1连麦 720p画质
         */
        smallcalss_1v1_720p,

        /**
         * 小班课 1v1连麦 1080p画质
         */
        smallcalss_1v1_1080p,

        /**
         * 小班课 1v16连麦 720p画质
         */
        smallcalss_1v16_720p
    }

    /**
     * 研讨会 梯度编码
     */
    enum Seminar {

        /**
         * 研讨会 1v16会议
         */
        seminar_1v6,

        /**
         * 研讨会 1v6会议
         */
        seminar_6,

        /**
         * 研讨会 1v47会议
         */
        seminar_1v16
    }

    /**
     * 小班课云录制梯度编码
     */
    enum SmallClassRecord {

        /**
         * 分辨率：1280*720档位
         */
        smallclass_record_1280_720,

        /**
         * 分辨率：1920*1080档位
         */
        smallclass_record_1920_1080,

        /**
         * 分辨率：大于1920*1080档位
         */
        smallclass_record_great_1920_1080
    }

    /**
     * 研讨会云录制梯度编码
     */
    enum SeminarRecord {

        /**
         * 分辨率：1280*720档位
         */
        seminar_record_1280_720,

        /**
         * 分辨率：1920*1080档位
         */
        seminar_record_1920_1080,

        /**
         * 分辨率：大于1920*1080档位
         */
        seminar_record_great_1920_1080
    }
}
