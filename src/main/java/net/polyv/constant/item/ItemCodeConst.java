package net.polyv.constant.item;

/**
 * 计费项编码
 * <AUTHOR>
 * @since 2020/5/15
 */
public enum ItemCodeConst {

    // 国内分钟数
    china_pd("china_pd"),

    // 海外分钟数
    inter_pd("inter_pd"),

    // 海外分钟数差额
    inter_pd_diff("inter_pd_diff"),

    // 连麦分钟数
    mic_pd("mic_pd"),

    // 并发包天
    concur_daily("concur_daily"),

    //无延迟并发包天
    prtc_concur_daily("prtc_concur_daily"),

    // 并发包月
    concur_monthly("concur_monthly"),

    //无延迟并发包月
    prtc_concur_monthly("prtc_concur_monthly"),

    // 峰值并发
    concur_peak("concur_peak"),

    // 海外并发差额
    inter_concur_diff("inter_concur_diff"),

    // 转码页数
    ppt_anim_page("ppt_anim_page"),

    // 专享通道
    ppt_anim_job("ppt_anim_job"),

    // 导播时长
    guide_pd("guide_pd"),

    // 重制课件时长
    ppt_composite_duration("ppt_composite_duration"),
    
    // 重制课件通道数
    ppt_composite_job("ppt_composite_job"),

    // 专享通道
    rebuild_job("rebuild_job"),

    // 转推量
    distribute_num("distribute_num"),

    // 截图数目
    id_porm_num("id_porm_num"),
    
    // 手工入账的计费项
    manual_item("manual_item"),
    
    // 无延迟国内分钟数
    prtc_china_pd("prtc_china_pd"),
    
    // 无延迟国外分钟数
    prtc_inter_pd("prtc_inter_pd"),

    //频道号增量费用
    channel_number("channel_number"),

    //小班课录制视频时长
    small_class_record_duration("small_class_record_duration"),
    //互动学堂录制视频时长
    seminar_record_duration("seminar_record_duration"),
    // 点播流量
    traffic("traffic"),

    // 点播视频存储空间
    video_space("video_space"),
    
    small_class_duration("small_class_duration"),
    
    seminar_duration("seminar_duration"),
    
    // 研讨会季度包
    SEMINAR_QUARTER_PACKAGE("seminar_quarter_package"),
    
    /**
     * 充值到期清理
     */
    EXPIRED_CLEANUP("expired_cleanup"), //音频审核分钟数
    audioModerationFunctionEnable("audioModerationFunctionEnable"),
    
    //云分发（按时长计费）
    distributeBillingPlanConcurrency("distributeBillingPlanConcurrency"), //云分发次数
    distributeBillingPlanDaily("distributeBillingPlanDaily"), //智能字幕分钟数
    SubtitleTask("SubtitleTask"), //动态PPT转码时长
    ppt_anim_new("ppt_anim_new"),
    live_flow("live_flow"), //直播流量
    prtc_live_flow("prtc_live_flow"), //直播无延迟流量
    ai_digital_human("aiPPTVideoDigitalHumanEnabled"), //ai数字人制课
    ai_smart_class("aiPPTVideoEnabled"), //ai普通制课
    
    ai_smart_clip("aiSmartClipEnabled"), //ai智能裁剪
    ai_assistant("aiAssistantEnabled"), //ai助手答疑
    
    china_pd_over_rate("china_pd_over_rate"), //国内分钟数超额费用
    inter_pd_over_rate("inter_pd_over_rate"), //海外分钟数超额费用
    
    prtc_china_pd_over_rate("prtc_china_pd_over_rate"), //无延迟国内分钟数超额费用
    
    prtc_inter_pd_over_rate("prtc_inter_pd_over_rate"),//无延迟国外分钟数超额费用
    
    /**
     * 素材库空间
     */
    material_space("materialspace"),
    /**
     * 素材库流量
     */
    material_traffic("materialtraffic"),

    /**
     * 资源点充值到期清理
     */
    EXPIRED_CLEANUP_RESOURCE_POINT("expired_cleanup_resource_point"),

    ;
    
    private final String code;
    
    ItemCodeConst(String code) {
        this.code = code;
    }
    
    public String getCode() {
        return code;
    }
    
    public static ItemCodeConst getByCode(String code) {
        for (ItemCodeConst value : ItemCodeConst.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 是否是超码率计费项
     * @param itemCode
     * @return
     */
    public static boolean isOverCodeRateItem(String itemCode) {
        if (ItemCodeConst.china_pd_over_rate.getCode().equals(itemCode) ||
                ItemCodeConst.inter_pd_over_rate.getCode().equals(itemCode) ||
                ItemCodeConst.prtc_china_pd_over_rate.getCode().equals(itemCode) ||
                ItemCodeConst.prtc_inter_pd_over_rate.getCode().equals(itemCode)) {
            return true;
        }
        
        return false;
    }
}
