package net.polyv.constant.item;

/**
 * dmp 连麦梯度等级
 * <AUTHOR>
 * @since 2020/6/21
 */
public enum MicItemScaleGradientConst {
    mic_pd_7(ItemScaleCodeConst.MicPd.mic_pd_7, 1),
    mic_pd_8(ItemScaleCodeConst.MicPd.mic_pd_8, 2),
    ;
    private ItemScaleCodeConst.MicPd micScale;
    private Integer concurrenceGradientLevel;
    
    MicItemScaleGradientConst(ItemScaleCodeConst.MicPd micScale, int concurrenceGradientLevel) {
        this.micScale = micScale;
        this.concurrenceGradientLevel = concurrenceGradientLevel;
    }
    
    public ItemScaleCodeConst.MicPd getMicScale() {
        return micScale;
    }
    
    public Integer getConcurrenceGradientLevel() {
        return concurrenceGradientLevel;
    }
    
    public static MicItemScaleGradientConst getByMicScaleCode(String micScaleCode) {
        for (MicItemScaleGradientConst micItemScaleGradientConst : MicItemScaleGradientConst.values()) {
            if (micScaleCode.equals(micItemScaleGradientConst.micScale.name())) {
                return micItemScaleGradientConst;
            }
        }
        
        return null;
    }
}
