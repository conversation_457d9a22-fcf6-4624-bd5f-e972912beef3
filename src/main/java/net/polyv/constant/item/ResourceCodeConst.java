package net.polyv.constant.item;

/**
 * 资源编码
 * <AUTHOR>
 * @since 2020/5/21
 */
public enum ResourceCodeConst {

    /**
     * 直播分钟数
     */
    duration,

    /**
     * 直播峰值并发数
     */
    concurrence,

    /**
     * 连麦分钟数
     */
    mic_duration,

    /**
     * 导播台分钟数
     */
    guide_duration,

    /**
     * ppt动效转码页数
     */
    ppt_anim_page,

    /**
     * ppt动效转码通道数
     */
    ppt_anim_job,
    
    /**
     * 课件重制
     */
    ppt_composite_duration,
    
    /**
     * 无延迟分钟数
     */
    prtc_duration,

    /**
     * 小班课云录制计费分钟数
     */
    small_class_record_duration,
    /**
     * 互动学堂云录制计费分钟数
     */
    seminar_record_duration,
    /**
     * 点播：点播存储流量
     */
    traffic,

    /**
     * 点播：点播存储空间
     */
    space,
    
    /**
     * 小班课 人分钟数
     */
    small_class_duration,
    
    /**
     * 研讨会 人分钟数
     */
    seminar_duration,
    
    /**
     * 直播流量
     */
    live_flow,

    /**
     * 无延迟直播流量
     */
    prtc_live_flow,

    /**
     * 无延迟并发
     */
    prtc_concurrence,
    /**
     * -1 表示无需用量结算
     */
    _1,
    ;
    
    public static ResourceCodeConst getByResourceCode(String resourceCode) {
        for (ResourceCodeConst value : ResourceCodeConst.values()) {
            if (value.name().equals(resourceCode)) {
                return value;
            }
        }
        return null;
    }
    
    public static boolean isVodResource(String resourceCode) {
        
        return traffic.name().equals(resourceCode) || space.name().equals(resourceCode);
    }
    
    /**
     * 是否是连麦资源
     * @param resourceCode
     * @return
     */
    public static boolean isMicResource(String resourceCode) {
        return mic_duration.name().equals(resourceCode) || small_class_duration.name().equals(resourceCode) ||
                seminar_duration.name().equals(resourceCode);
    }
    
}
