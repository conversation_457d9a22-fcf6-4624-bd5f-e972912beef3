package net.polyv.constant.auditlog;

/**
 * 操作日志类型 常量
 * <AUTHOR>
 * @since 07/05/2020
 */
public enum AuditLogEvent {

    /**
     * 充值
     */
    deposit("deposit"),

    /**
     * 开通功能
     */
    add_function("add-function"),
    
    /**
     * 更新授信额度
     */
    update_credit("update_credit"),
    
    /**
     * 更新单价
     */
    update_univalence("update_univalence"),

    /**
     * 打套餐
     */
    add_package("add-package"),

    /**
     * 提现
     */
    withdraw("withdraw"),

    /**
     * 领取代金券
     */
    discount("discount"),

    /**
     * 设置并发时段
     */
    add_concurrence_setting("add-concurrence"),

    /**
     * 取消并发时段
     */
    cancel_concurrence_setting("cancel-concurrence"),
    
    /**
     * 审批销售机会
     */
    approval_sale_opport("approval_so"),

    /**
     * 领取优惠券
     */
    receive_coupon("receive_coupon"),

    /**
     * 核销优惠券
     */
    write_off_coupon("write_off_coupon"),

    /**
     * 删除手工账单
     */
    del_manaul_bill("del_manual_bill"),

    /**
     * 金额退费
     */
    amount_refund("amount_refund"),

    /**
     * 资源点退费
     */
    resource_point_refund("resource_point_refund"),

    /**
     * 资源点充值
     */
    resource_point_deposit("resource_point_deposit"),

    ;

    private final String event;

    AuditLogEvent(String event) {
        this.event = event;
    }

    public String getEvent() {
        return event;
    }

    /**
     * 通过字符串获取对象，相当于改进版的valueOf
     * @param event 类型字符串
     * @return event对象
     */
    public AuditLogEvent parseByName(String event) {
        for (AuditLogEvent auditLogEvent : AuditLogEvent.values()) {
            if (auditLogEvent.event.equals(event)) {
                return auditLogEvent;
            }
        }

        return null;
    }
}
