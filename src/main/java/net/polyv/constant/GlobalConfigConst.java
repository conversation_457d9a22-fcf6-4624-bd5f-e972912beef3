package net.polyv.constant;

/**
 * 全局常量
 * <AUTHOR>
 * @since 07/05/2020
 */
public interface GlobalConfigConst {

    /**
     * 默认的最低消费金额
     */
    String MIN_CONSUMPTION = "min_consumption";

    /**
     * 默认的高级套餐充值门槛
     */
    String ADVANCED_DEPOSIT_AMOUNT = "advanced_deposit_amount";

    /**
     * 充值周期
     */
    String RECHARGE_PERIOD_DAYS = "recharge_period_days";

    String RESTRICT_NEW_USER_ID = "restrict:new:gray:userids"; // 使用新的限制逻辑的灰度用户

    String RESTRICT_PCS_RESULT = "restrict:pcs:result:%s:%s"; // 结算系统限制结果, 格式为 restrict:pcs:result:{userId}:{resourceCode}
    
    String PCS_RESOURCE_AVAILABLE = "user_clearing_resource_available_key:%s%s"; // 资源可用量缓存

    /**
     *  同步crm销售机会是否覆盖创建时间
     */
    String PCS_SYNC_CRM_COVER_CREATE_TIME = "sync_sales_opportunities_cover_create_time";

    String GROUP_AUTO_ID = "000"; // 集团账号的autoId
    
    // 创建销售机会的时候，默认设置客户的计费项系数
    Integer OVERWRITE_CHINA_PD_RATIO = 100;
    Integer OVERWRITE_INTER_PD_RATIO = 250;
    Integer OVERWRITE_MIC_7_PD_RATIO = 100;
    Integer OVERWRITE_MIC_8_PD_RATIO = 300;
    Integer OVERWRITE_PRTC_INTER_PD_RATIO = 100;
    
    String PROD_ENV = "prod";
    
    String LOCAL_ENV = "local";
    
    String RESOURCE_CUSTOMER_INIT = "resource:available:init:%s:%s"; // 客户资源初始用量，格式为resource:available:init
    // :{resourceCode}:{userId}
    
    String Y_FLAG = "y";
    
    /**
     * 集团账号的customerId
     */
    String GROUP_ACCOUNT_CUSTOMER_ID = "000";
    
    /**
     * 1G
     */
    long ONE_G = 1024 * 1024 * 1024;

    /**
     * ?
     */
    String QUESTION_SIGN = "?";
    
    
    String SALE_DEPARTMENT_BASE_AMOUNT_KEY = "SALE_DEPARTMENT_BASE_AMOUNT_KEY";
    
    String ESTABLISH_PACKAGE_BUSINESS_KEY = "ESTABLISH_PACKAGE_BUSINESS_KEY:%s";
    /**
     * 收入明细 上一个月的数据 contractId ： itemId
     */
    String INCOME_BEFORE_MONTH_KEY = "INCOME_BEFORE_MONTH_KEY:%s:%s";
    
    //合同回款状态处理队列名字
    String CONTRACT_PAYMENT_STATUS_QUEUE = "CONTRACT_PAYMENT_STATUS_QUEUE";
    
    //更新回款表合同id队列名字
    String UPDATE_PAYMENT_CONTRACT_ID_QUEUE = "UPDATE_PAYMENT_CONTRACT_ID_QUEUE";
    
    //集团分账号缓存key
    String GROUP_SUB_USER_KEY = "GROUP_SUB_USER_KEY:%s";
    
    //重算集团主账号剩余可分配资源队列名字
    String RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE = "RECALCULATE_GROUP_ASSIGNABLE_RESOURCE_QUEUE";
    
    
    //应收记录创建队列名字
    String FXIAOKE_RECEIVABLE_CREATE_QUEUE = "FXIAOKE_RECEIVABLE_CREATE_QUEUE";
    
    /**
     * 空间可用已经为0
     */
    String SPACE_AVAILABLE_IS_ZERO = "SPACE_AVAILABLE_IS_ZERO";
    /**
     * 流量可用已经为0
     */
    String FLOW_AVAILABLE_IS_ZERO = "FLOW_AVAILABLE_IS_ZERO";
    /**
     * 套餐流量可用
     */
    String FLOW_PACKAGE_AVAILABLE = "FLOW_PACKAGE_AVAILABLE";
    /**
     * 近七天消耗
     */
    String LAST_CONSUMED_DAY_7 = "LAST_CONSUMED_DAY_7:";
    
    String LIVE_BILLING_TYPE_KEY = "LIVE_FLOW_PACKAGE:%s";
    
    /**
     * 空间套餐可用
     */
    String SPACE_AVAILABLE_PACKAGE = "SPACE_AVAILABLE_PACKAGE";
    
    String VOD_SPACE_TRAFFIC_AVAILABLE = "VOD_SPACE_TRAFFIC_AVAILABLE:%s";
    
    
    String LIVE_CONCURRENCE_LIMIT_KEY = "LIVE_CONCURRENCE_LIMIT_KEY:%s";
    
    String IS_DONATE_AI_RESOURCE_KEY = "IS_DONATE_AI_RESOURCE:%s:%s";
    
    String IS_CUSTOMER_DEAL_KEY = "IS_CUSTOMER_DEAL:%s";
    
    String DONATE_AI_RESOURCE_USER_KEY = "DONATE_AI_RESOURCE_USER:%s:%s";
    
    
    /**
     * 套餐最早开通时间
     */
    String PACKAGE_EARLIEST_OPEN_TIME = "PACKAGE_EARLIEST_OPEN_TIME:%s:%s";
}
