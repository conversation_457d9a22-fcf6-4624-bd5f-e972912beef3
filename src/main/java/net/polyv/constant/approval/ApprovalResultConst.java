package net.polyv.constant.approval;

import net.polyv.constant.bill.ManualBillingStatusConst;
import net.polyv.modules.pcs.api.stereotype.SalesOpportunitiesStatusConst;

/**
 * 钉钉审批结果
 * <AUTHOR>
 * @since 2020/6/5
 */
public enum ApprovalResultConst {

    /**
     * 审批通过
     */
    AGREE("agree", SalesOpportunitiesStatusConst.pass, ManualBillingStatusConst.pass),

    /**
     * 审批拒绝
     */
    REFUSE("refuse", SalesOpportunitiesStatusConst.reject, ManualBillingStatusConst.reject)
    ;
    private String approvalResult;
    private SalesOpportunitiesStatusConst soStatus;
    private ManualBillingStatusConst manualBillingStatus;
    
    
    ApprovalResultConst(String approvalResult, SalesOpportunitiesStatusConst soStatus, ManualBillingStatusConst manualBillingStatus) {
        this.approvalResult = approvalResult;
        this.soStatus = soStatus;
        this.manualBillingStatus = manualBillingStatus;
    }
    
    public String getApprovalResult() {
        return approvalResult;
    }
    
    public SalesOpportunitiesStatusConst getSoStatus() {
        return soStatus;
    }
    
    public ManualBillingStatusConst getManualBillingStatus() {
        return manualBillingStatus;
    }
    
    /**
     * 通过审批结果获取销售机会状态值
     * @param approvalResult 类型字符串
     * @return 枚举对象
     */
    public static ApprovalResultConst getByResult(String approvalResult) {
        for (ApprovalResultConst approvalResultConst : ApprovalResultConst.values()) {
            if (approvalResult.equals(approvalResultConst.getApprovalResult())) {
                return approvalResultConst;
            }
        }
        
        return null;
    }
    
    @Override
    public String toString() {
        return "ApprovalResultConst{" + "approvalResult='" + approvalResult + '\'' + ", soStatus=" + soStatus +
                ", manualBillingStatus=" + manualBillingStatus + '}';
    }
}
