package net.polyv.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 结算功能开关配置枚举
 * <AUTHOR>
 * @since 2023/05/11
 */
@Getter
@AllArgsConstructor
public enum PcsFunctionConfigConst {
    
    AMOUNT_BILLING_TYPE("amountBillingType", "金额模式默认计费方式", "duration", SysTypeConst.Live),
    BASE_CODE_RATE("baseCodeRate", "基础码率", "1000", SysTypeConst.Live);
    private String configKey;
    
    private String desc;
    
    private Object defaultValue;
    
    
    private SysTypeConst production;
    
    
    public static PcsFunctionConfigConst getByConfigKey(String configKey) {
        for (PcsFunctionConfigConst instance : PcsFunctionConfigConst.values()) {
            if (instance.getConfigKey().equals(configKey)) {
                return instance;
            }
        }
        return null;
    }
}
