package net.polyv.constant.bill;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020/7/17
 */
@AllArgsConstructor
@Getter
public enum ManualBillIsChargingConst {

    YES(1, "是"),
    NO(0, "否"),
    ;
    
    private Integer value;
    private String text;
    
    public static String getText(Object value){
        for (ManualBillIsChargingConst manualBillIsChargingConst : ManualBillIsChargingConst.values()) {
            if(manualBillIsChargingConst.getValue().equals(value)){
                return manualBillIsChargingConst.getText();
            }
        }
        return null;
    }
}
