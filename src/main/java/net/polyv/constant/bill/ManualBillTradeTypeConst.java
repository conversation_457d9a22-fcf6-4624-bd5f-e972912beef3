package net.polyv.constant.bill;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.polyv.constant.billingdaily.CustomerBillingDailyTradeTypeConst;

/**
 * <AUTHOR>
 * @since 2020/7/17
 */
@AllArgsConstructor
@Getter
public enum ManualBillTradeTypeConst {

    AMOUNT(1, "金额结算", CustomerBillingDailyTradeTypeConst.amount_clearing),
    
    SUPPLEMENT(2, "补扣调账", CustomerBillingDailyTradeTypeConst.supplement_adjust_bill),

    RESOURCE_POINT(3 ,"资源点结算" , CustomerBillingDailyTradeTypeConst.resource_point_bill)

    ;
    
    private Integer value;
    private String text;
    private CustomerBillingDailyTradeTypeConst billingDailyTradeTypeConst;
    
    public static String getText(Object value){
        for (ManualBillTradeTypeConst manualBillTradeTypeConst : ManualBillTradeTypeConst.values()) {
            if(manualBillTradeTypeConst.getValue().equals(value)){
                return manualBillTradeTypeConst.getText();
            }
        }
        return null;
    }
    
    public static Integer getBillingDailyTrade(Integer tradeType){
        for (ManualBillTradeTypeConst manualBillTradeTypeConst : ManualBillTradeTypeConst.values()) {
            if(manualBillTradeTypeConst.getValue().equals(tradeType)){
                return manualBillTradeTypeConst.getBillingDailyTradeTypeConst().getTradeType();
            }
        }
        return null;
    }

    public boolean is(Integer tradeType){
        return this.getValue().equals(tradeType) ;
    }
}
