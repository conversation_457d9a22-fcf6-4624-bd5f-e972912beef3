package net.polyv.constant.bill;

/**
 * 账单状态，包括每日账单和手工账单
 * 状态有：1:未关联、5:已支付、6：待支付
 * <AUTHOR>
 * @since 2020/5/21
 */
public interface BillStatusConst {

    /**
     * 未关联, unpaid=0, contractId=null
     */
    int UNRELATED = 1;

    /**
     * 已支付, unpaid=0, contractId!=null
     */
    int PAID = 5;
    
    /**
     * 待支付, unpaid>0, contractId=null
     */
    int UNPAID = 6;

    /**
     * 根据账单状态获取状态对应的中文名
     * 特殊处理：未关联和待支付对外统一显示为待支付
     * @param status 账单状态
     * @return 状态对应的中文名
     */
    static String getStatusName(Integer status) {
        if (status == null) {
            return null;
        }
    
        switch (status) {
            case 1:
            case 6:
                return "待支付";
            case 5:
                return "已支付";
            default:
                return null;
        }
    }

    /**
     * 根据账单状态的中文名获取账单状态
     * @param statusName 账单状态中文名
     * @return -1表示找不到该状态，其他则返回对应的状态值
     */
    static int getStatus(String statusName) {
        if (statusName == null) {
            return -1;
        }

        switch (statusName) {
            case "未关联" :
                return 1;
            case "已支付":
                return 5;
            case "待支付":
                return 6;
            default:
                return -1;
        }
    }

}
