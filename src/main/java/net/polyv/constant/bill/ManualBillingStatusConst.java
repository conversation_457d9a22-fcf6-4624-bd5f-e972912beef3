package net.polyv.constant.bill;

/**
 * <AUTHOR>
 * @since 2020/7/15
 */
public enum ManualBillingStatusConst {

    /**
     * 待审核
     */
    wait_auditing(1, "待审核"),

    /**
     * 审批中
     */
    auditing(2, "审批中"),

    /**
     * 已驳回
     */
    reject(3, "已驳回"),

    /**
     * 已通过
     */
    pass(4, "待出账"),

    /**
     * 已关联
     */
    paid(5, "已出账");

    private int status;
    private String text;

    ManualBillingStatusConst(int status, String text) {
        this.status = status;
        this.text = text;
    }

    public int getStatus() {
        return status;
    }

    public String getText() {
        return text;
    }

    public static String getByStatus(Integer status) {
        for (ManualBillingStatusConst value : ManualBillingStatusConst.values()) {
            if (value.getStatus() == status) {
                return value.getText();
            }
        }
        return null;
    }
}
