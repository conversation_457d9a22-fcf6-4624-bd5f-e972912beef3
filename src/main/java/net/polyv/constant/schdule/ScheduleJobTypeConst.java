package net.polyv.constant.schdule;

import lombok.Getter;

/**
 * 定时任务类型
 * <AUTHOR>
 * @since 2020/5/29
 */
@Getter
public enum ScheduleJobTypeConst {
    /**
     * 充值
     */
    deposit(1),

    /**
     * 业务开通
     */
    business_open(2),

    /**
     * 定制化账号资源充值
     */
    customized_account_deposit(3),

    /**
     * 用户后台通过金额购买并发
     */
    concurrence_deposit_with_amount(4),

    /**
     * 资源点充值
     */
    resource_point_deposit(5),

    ;

    private final int type;

    ScheduleJobTypeConst(int type) {
        this.type = type;
    }

    /**
     * 根据type获取枚举类实体
     * @param type 类型
     * @return 枚举类实体
     */
    public static ScheduleJobTypeConst getByType(int type) {
        for (ScheduleJobTypeConst scheduleJobTypeConst : ScheduleJobTypeConst.values() ) {
            if (scheduleJobTypeConst.type == type) {
                return scheduleJobTypeConst;
            }
        }

        return null;
    }
}
