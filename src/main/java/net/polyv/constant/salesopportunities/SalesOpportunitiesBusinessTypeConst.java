package net.polyv.constant.salesopportunities;

import org.springframework.lang.Nullable;

/**
 * 销售机会记录表sales_opportunities的business_type字段：业务类型
 * <AUTHOR>
 * @since 2020/5/28
 */
public enum SalesOpportunitiesBusinessTypeConst {
    
    /**
     * 点播
     */
    vod(1, "点播"),

    /**
     * 直播
     */
    live_play(2, "直播"),

    /**
     * 金额
     */
    amount(3, "金额"),

    /**
     * 硬件
     */
    hardware(4, "硬件"),

    /**
     * 网校云
     */
    net_school_cloud(5, "网校云"),

    /**
     * 私有云
     */
    private_cloud(6, "私有云"),

    /**
     * 酷播云
     */
    cuplayer_cloud(7, "酷播云"),

    /**
     * 调整单价
     */
    adjust_bill(8, "调整单价"),

    /**
     * 调整授信额度
     */
    adjust_credit(9, "调整授信额度"),
    
    /**
     * cdn
     */
    cdn(10, "CDN"),
    
    /**
     * 直播+点播
     */
    live_vod(11, "直播+点播"),
    
    /**
     * 开通功能(需审批)
     */
    funct_access(12, "开通功能(需审批)"),
    
    /**
     * 开通功能(免审批)
     */
    funct_access_without_approval(13, "开通功能(免审批)"),

    /**
     * 驻场服务
     */
    on_site_service(14, "驻场服务"),
    channel_deduct_balance(15, "添加频道号(扣余额)"),

    /**
     * 运营服务
     */
    OPERATION_SERVICE(16, "运营服务"),
    CUSTOMIZED_SERVICE(17, "定制服务"),
    OTHER(18, "其他"),

    cooperative_returning(20,"合作伙伴服务"),

    resource_point(22,"资源点"),
    ;

    SalesOpportunitiesBusinessTypeConst(Integer businessType, String name) {
        this.businessType = businessType;
        this.name = name;
    }

    private final Integer businessType;

    private final String name;

    public Integer getBusinessType() {
        return businessType;
    }

    public String getName() {
        return name;
    }

    @Nullable
    public static SalesOpportunitiesBusinessTypeConst getByType(Integer type) {
        for (SalesOpportunitiesBusinessTypeConst value : SalesOpportunitiesBusinessTypeConst.values()) {
            if (value.getBusinessType().equals(type)) {
                return value;
            }
        }

        return null;
    }
    
    public static SalesOpportunitiesBusinessTypeConst getByName(String name){
        for (SalesOpportunitiesBusinessTypeConst value : SalesOpportunitiesBusinessTypeConst.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }

    public static String getText(Object value){
        for (SalesOpportunitiesBusinessTypeConst businessTypeConst : SalesOpportunitiesBusinessTypeConst.values()) {
            if(businessTypeConst.getBusinessType().equals(value)){
                return businessTypeConst.getName();
            }
        }
        return null;
    }
}
