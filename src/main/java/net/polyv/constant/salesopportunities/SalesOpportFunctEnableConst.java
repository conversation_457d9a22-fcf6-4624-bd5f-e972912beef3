package net.polyv.constant.salesopportunities;

import lombok.Getter;

/**
 * 销售机会的功能开通
 * <AUTHOR>
 * @since 2020/8/7
 */
@Getter
public enum SalesOpportFunctEnableConst {
    
    showApiSetting("showApiSetting", "API&SDK", true),
    linkMicLimit("linkMicLimit", "连麦人数", false),
    linkMicType("linkMicType", "连麦类型", false),
    chatTranslateEnabled("chatTranslateEnabled", "聊天室翻译", true),
    tuwenLiveEnabled("tuwenLiveEnabled", "图文直播", true),
    transmitEnabled("transmitEnabled", "转播", true),
    pptAnimationEnabled("pptAnimationEnabled", "PPT动效转码", true),
    footerSettingEnabled("footerSettingEnabled", "页脚设置", true),
    pureRtcEnabled("pureRtcEnabled", "无延迟直播", true),
    ;
    
    private String code;
    private String cnName;
    private boolean YN;
    
    SalesOpportFunctEnableConst(String code, String cnName, boolean YN){
        this.code = code;
        this.cnName = cnName;
        this.YN = YN;
    }
    
    public static String getCodeByCnName(String cnName){
        for (SalesOpportFunctEnableConst value : SalesOpportFunctEnableConst.values()) {
            if(value.getCnName().equals(cnName)){
                return value.getCode();
            }
        }
        return null;
    }
}
