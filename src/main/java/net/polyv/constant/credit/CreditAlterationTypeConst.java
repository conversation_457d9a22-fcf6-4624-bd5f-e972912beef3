package net.polyv.constant.credit;

/**
 * 授信额度变更类型（credit_alteration_record表的type字段）
 * <AUTHOR>
 * @since 12/05/2020
 */
public interface CreditAlterationTypeConst {

    /**
     * 扣款
     */
    int DEDUCTION = 1;

    /**
     * 充值
     */
    int RECHARGE = 2;

    /**
     * 冻结
     */
    int FREEZE = 3;

    /**
     * 补扣调账
     */
    int SUPPLEMENT_DEDUCT_FOR_ADJUST_BILL = 4;

    /**
     * 退费调账
     */
    int REFUND_FOR_ADJUST_BILL = 5;
    
    /**
     * 解冻
     */
    int UNFREEZE = 6;

}
