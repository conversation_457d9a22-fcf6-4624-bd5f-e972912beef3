package net.polyv.constant;

import lombok.Getter;

/**
 * 并发时段状态
 * <AUTHOR>
 * @since 2020/12/06
 */
@Getter
public enum ConcurrenceStateConst {
    ACTIVE("生效中", 1),
    UN_ACTIVE("待生效", 2),
    EXPIRED("已生效", 3),
    ;
    
    private final String label;
    private final int value;
    
    ConcurrenceStateConst(String label, int value) {
        this.label = label;
        this.value = value;
    }
}
