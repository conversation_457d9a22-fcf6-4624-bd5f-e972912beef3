package net.polyv.constant.monitor;

import net.polyv.constant.item.ItemCodeConst;

/**
 * 监控项目
 * <AUTHOR>
 * @since 2020/6/27
 */
public enum ItemConsumeMonitor {
    
    LIVE_FLOW(ItemCodeConst.live_flow),
    
    MIC_PD(ItemCodeConst.mic_pd),
    
    CHINA_PD(ItemCodeConst.china_pd),
    
    INTER_PD(ItemCodeConst.inter_pd),
    
    CONCURRENCE(ItemCodeConst.concur_daily),
    
    GUIDE_PD(ItemCodeConst.guide_pd),
    
    PRTC_CHINA_PD(ItemCodeConst.prtc_china_pd),
    
    PRTC_INTER_PD(ItemCodeConst.prtc_inter_pd),
    
    VIDEO_SPACE(ItemCodeConst.video_space),
    
    TRAFFIC(ItemCodeConst.traffic),

    PRTC_CONCURRENCE(ItemCodeConst.prtc_concur_daily),


//
//    SEMINAR_DURATION(ItemCodeConst.seminar_duration),
//
//    SMALL_CLASS_DURATION(ItemCodeConst.small_class_duration),
//
//    SEMINAR_RECORD_DURATION(ItemCodeConst.seminar_record_duration),
//
//    SMALL_CLASS_RECORD_DURATION(ItemCodeConst.small_class_record_duration),
//
//    audioModerationFunctionEnable(ItemCodeConst.audioModerationFunctionEnable),
//
//    distributeBillingPlanConcurrency(ItemCodeConst.distributeBillingPlanConcurrency),
//
//    distributeBillingPlanDaily(ItemCodeConst.distributeBillingPlanDaily),
//
//    SubtitleTask(ItemCodeConst.SubtitleTask),
//
//    ppt_anim_new(ItemCodeConst.ppt_anim_new),
//
//    PPT_COMPOSITE_DURATION(ItemCodeConst.ppt_composite_duration),
//
//    ai_digital_human(ItemCodeConst.ai_digital_human), //ai数字人制课
//    ai_smart_class(ItemCodeConst.ai_smart_class), //ai普通制课
//
//    ai_smart_clip(ItemCodeConst.ai_smart_clip), //ai智能裁剪
//    ai_assistant(ItemCodeConst.ai_assistant) //ai助手答疑
    
    
    ;
    
    
    private ItemCodeConst itemCodeConst;
    
    ItemConsumeMonitor(ItemCodeConst itemCodeConst) {
        this.itemCodeConst = itemCodeConst;
    }
    
    public ItemCodeConst getItemCodeConst() {
        return itemCodeConst;
    }
}
