package net.polyv.constant.billingdaily;

import org.springframework.lang.NonNull;

import lombok.Getter;

/**
 * 用户每日账单表customer_billing_daily的trade_type字段的交易类型
 * <AUTHOR>
 * @since 2020/5/19
 */
@Getter
public enum CustomerBillingDailyTradeTypeConst {
    /**
     * 用量结算
     */
    consumed_clearing(1, "用量结算"),

    /**
     * 金额结算
     */
    amount_clearing(2, "金额结算"),

    /**
     * 补扣调账
     */
    supplement_adjust_bill(3, "补扣调账"),

    /**
     * 退费调账
     */
    refund_adjust_bill(4, "退费调账"),

    /**
     * 资源点结算
     */
    resource_point_bill(5, "资源点结算"),
    
    /**
     * 未知扣费方式
     * 该值位于界面默认显示，保存时候请勿使用
     */
    unknown(-1, "-")
    ;

    CustomerBillingDailyTradeTypeConst(Integer tradeType, String name) {
        this.tradeType = tradeType;
        this.name = name;
    }

    private final Integer tradeType;
    private final String name;

    @NonNull
    public static CustomerBillingDailyTradeTypeConst getByType(Integer tradeType) {
        for (CustomerBillingDailyTradeTypeConst value : CustomerBillingDailyTradeTypeConst.values()) {
            if (value.getTradeType().equals(tradeType)) {
                return value;
            }
        }
        return unknown;
    }
}
