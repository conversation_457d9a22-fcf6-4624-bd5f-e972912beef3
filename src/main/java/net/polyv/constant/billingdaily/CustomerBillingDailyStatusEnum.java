package net.polyv.constant.billingdaily;

import lombok.Getter;

/**
 * 账单状态枚举类
 * <AUTHOR>
 * @since 2020/5/19
 */
@Getter
public enum CustomerBillingDailyStatusEnum {
    /**
     * 待审批
     */
    WAIT_AUDIT(1, "待审批"),

    /**
     * 审批中
     */
    PENDING(2, "审批中"),

    /**
     * 已驳回
     */
    REJECT(3, "已驳回"),

    /**
     * 待出账
     */
    WAIT_BILL(4, "待出账"),

    /**
     * 已出账
     */
    BILLED(5, "已出账")
    ;

    CustomerBillingDailyStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    private final String name;
    private final Integer status;

    public static CustomerBillingDailyStatusEnum getByType(Integer status) {
        for (CustomerBillingDailyStatusEnum value : CustomerBillingDailyStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }
}
