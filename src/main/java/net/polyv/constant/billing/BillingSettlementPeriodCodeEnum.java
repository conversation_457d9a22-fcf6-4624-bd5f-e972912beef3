package net.polyv.constant.billing;


import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 *  计费周期配置表Code常量
 */
@Getter
@AllArgsConstructor
public enum BillingSettlementPeriodCodeEnum {


    DAILY("daily","按天"),
    MONTHLY("monthly","按月"),
    QUARTERLY("quarterly","按季度"),
    CALENDAR_MONTH("calendar_month","按自然月"),
    SEMI_ANNUAL("semi_annual","按半年"),
    ANNUAL("annual","按年"),


    ;
    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (BillingSettlementPeriodCodeEnum enableTypeEnum : BillingSettlementPeriodCodeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String code) {
        return getMap().get(code) == null ? "" : getMap().get(code);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }

}
