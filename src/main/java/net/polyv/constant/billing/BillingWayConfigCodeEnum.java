package net.polyv.constant.billing;


import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 *  计费周期配置表Code常量
 */
@Getter
@AllArgsConstructor
public enum BillingWayConfigCodeEnum {
    
    
    BY_TIMES("by_times", "按次数"), BY_DURATION("by_duration", "按时长"), BY_USAGE("by_usage", "按用量"),
    
    BY_USAGE_DURATION("by_usage_duration", "按用量+限定使用时长计费"),
    
    ;
    private String code;
    private String desc;
    
    private static final Map<String, String> MAP = new HashMap<>();
    
    static {
        for (BillingWayConfigCodeEnum enableTypeEnum : BillingWayConfigCodeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String code) {
        return getMap().get(code) == null ? "" : getMap().get(code);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }

}
