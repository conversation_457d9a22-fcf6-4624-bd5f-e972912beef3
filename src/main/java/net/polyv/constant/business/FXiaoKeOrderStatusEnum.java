package net.polyv.constant.business;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 分享逍客订单状态枚举
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022-03-25
 */
@Getter
@AllArgsConstructor
public enum FXiaoKeOrderStatusEnum {

    HANDLE_ING("kUv26r3xI", "开通处理中"),
    ESTABLISHED("option1", "已开通")
    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (FXiaoKeOrderStatusEnum enableTypeEnum : FXiaoKeOrderStatusEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
