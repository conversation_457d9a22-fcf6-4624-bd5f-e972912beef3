package net.polyv.constant.business;


import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 商机套餐规格弹窗类型枚举类
 */
@Getter
@AllArgsConstructor
public enum BusinessBillingPlanCodeEnum {

    /**
     * 规格类型
     * vod_package:点播套餐
     * vod_space:点播添加空间
     * vod_flow:点播添加流量
     * live_duration:直播分钟数套餐
     * live_concurrent:直播并发套餐
     * live_channel:直播增加频道
     * live_mic_duration:直播连麦分钟数
     * live_guide_duration:直播导播台分钟数
     * create_group_account_v2:集团账号2.0创建集团账号主账号的订单规格
     * live_function:功能开通+直播功能
     * vod_function:功能开通+点播功能
     * group_v2_function:功能开通+集团账号 2.0
     * online_package: 网校云开通-包含：直播分钟数套餐+点播套餐
     */

    VOD_PACKAGE("vod_package","点播套餐"),
    VOD_FLOW("vod_flow","点播添加流量"),
    VOD_SPACE("vod_space","点播添加空间"),
    LIVE_DURATION("live_duration","直播分钟数套餐"),
    LIVE_CONCURRENT("live_concurrent","直播并发套餐"),
    LIVE_CHANNEL("live_channel","直播增加频道"),
    LIVE_MIC_DURATION("live_mic_duration","直播连麦分钟数"),
    LIVE_GUIDE_DURATION("live_guide_duration","直播导播台分钟数"),
    LIVE_FUNCTION("live_function","直播功能"),
    VOD_FUNCTION("vod_function","点播功能"),
    CREATE_GROUP_ACCOUNT_V2("create_group_account_v2","创建集团账号2.0主账号"),
    /**
     * 酷播云
     */
    COOL_PACKAGE("cool_package","酷播云套餐"),
    COOL_FLOW("cool_flow","酷播云添加流量"),
    COOL_SPACE("cool_space","酷播云添加空间"),

    /**
     * 金额
     */
    AMOUNT("amount","金额"),

    /**
     * 授信额度
     */
    CREDIT("credit","授信额度"),

    /**
     * 硬件
     */
    HARD_ALL("hard_all","硬件"),

    /**
     * 私有云
     */
    PRIVATE_ALL("private_all","私有云"),
    /**
     * 驻场服务
     */
    RESIDENT_ALL("resident_all", "驻场服务"),
    
    /**
     * 网校云
     */
    ONLINE_PACKAGE("online_package", "网校云"),
    
    /**
     * 运营服务
     */
    OPERATION_ALL("operation_all", "运营服务"),
    
    /**
     * 调整单价
     */
    UNIVALENCE("univalence", "调整单价"),
    
    /**
     * 定制服务类别下的产品名称
     */
    LIVE_CUSTOMIZATION("live_customization", "直播定制"), VOD_CUSTOMIZATION("vod_customization", "点播定制"),
    PRIVATE_CLOUD("private_cloud", "私有云"),
    /**
     * 驻场服务类型下
     */
    VALUE_ADDED_SERVICE("value_added_service", "增值服务"),
    /**
     * 硬件类别
     */
    LIVE_CABIN("live_cabin", "直播舱"), OTHER_HARDWARE("other_hardware", "其它硬件"),
    /**
     * 其他
     */
    CDN("cdn", "CDN"), COOPERATIVE_RETURNING("cooperative_returning", "合作伙伴返佣"),
    LIVE_FLOW("live_flow", "直播流量"),

    LIVE_PRTC_CONCURRENT("live_prtc_concurrent","直播无延迟并发套餐"),

    RESOURCE_POINT("resource_point","资源点")
    
    ;
    
    private final String code;
    private final String desc;
    
    
    public static BusinessBillingPlanCodeEnum matchAny(String match) {
        if (StringUtils.isNotEmpty(match)) {
            for (BusinessBillingPlanCodeEnum bpps : BusinessBillingPlanCodeEnum.values()) {
                if (bpps.getCode().equalsIgnoreCase(match.trim())) {
                    return bpps;
                }
            }
        }
        return null;
    }

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (BusinessBillingPlanCodeEnum enableTypeEnum : BusinessBillingPlanCodeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }

    public boolean is(String billingPlanCode){
        return this.getCode().equals(billingPlanCode) ;
    }
}
