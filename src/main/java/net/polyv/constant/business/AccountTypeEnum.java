package net.polyv.constant.business;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 账号类型
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022-03-09
 */
@Getter
@AllArgsConstructor
public enum AccountTypeEnum {

    NORMAL("normal", "普通账号"),
    GROUP1("group_v1", "集团账号1.0主账号"),
    GROUP2("group_v2", "集团账号2.0主账号"),
    GROUP1_SUB("group_v1_sub", "集团账号1.0分账号"),
    GROUP2_SUB("group_v2_sub", "集团账号2.0分账号")
    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (AccountTypeEnum enableTypeEnum : AccountTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static AccountTypeEnum getIdentity(String key) {
        for (AccountTypeEnum enableTypeEnum : AccountTypeEnum.values()) {
            if (StringUtils.isNotEmpty(key) && enableTypeEnum.getCode().equals(key)) {
                return enableTypeEnum;
            }
        }
        return null;
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
