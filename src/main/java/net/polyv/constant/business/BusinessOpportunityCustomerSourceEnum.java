package net.polyv.constant.business;


import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 商机客户来源枚举
 */
@Getter
@AllArgsConstructor
public enum BusinessOpportunityCustomerSourceEnum {

    /**
     * 待审核
     */
    WAIT_AUDITING("company", "公司提供"),

    /**
     * 审批中
     */
    AUDITING("initiative", "主动开发"),

    /**
     *已驳回
     */
    REJECTED("renew_company", "续费-公司提供"),

    /**
     * 已通过
     */
    PASSED("renew_initiative", "续费-主动开发"),


    ;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (BusinessOpportunityCustomerSourceEnum enableTypeEnum : BusinessOpportunityCustomerSourceEnum.values()) {
            MAP.put(enableTypeEnum.value, enableTypeEnum.desc);
        }
    }

    /**
     * value
     */
    private String value;

    /**
     * desc
     */
    private String desc;


    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
