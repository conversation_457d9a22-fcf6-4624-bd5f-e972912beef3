package net.polyv.constant.business;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum OldOperationNameEnum {


    /**
     * 点播套餐
     */
    VODPACKAGE("VODPACKAGE", "点播套餐"),

    /**
     * 添加点播空间
     */
    VODSPACE("VODSPACE", "添加点播空间"),

    /**
     * 添加点播流量
     */
    VODFLOW("VODFLOW", "添加点播流量"),

    /**
     * 点播功能定制
     */
    VODCUSTOM("VODCUSTOM", "点播功能定制"),

    /**
     * 直播添加分钟数
     */
    LIVEDURATION("LIVEDURATION", "添加直播分钟数"),

    /**
     * 直播按天套餐
     */
    LIVEDAILY("LIVEDAILY", "直播按天套餐"),

    /**
     * 直播包月套餐
     */
    LIVEMONTHLY("LIVEMONTHLY", "直播包月套餐"),

    /**
     * 直播播功能定制
     */
    LIVECUSTOM("LIVECUSTOM", "直播功能定制"),

    /**
     * 硬件购买
     */
    HARDWARE("HARDWARE", "硬件购买"),

    /**
     * 硬件租赁
     */
    HARDWARERENT("HARDWARERENT", "硬件租赁"),

    /**
     *  硬件技术服务
     */
    HARDWARE_TECHNICAL_SERVICES("HARDWARETECHNICALSERVICES", "硬件技术服务"),

    /**
     * 直播月结
     */
    LIVEMONTHLYSTAGE("LIVEMONTHLYSTAGE", "直播月结"),

    /**
     * 点播月结
     */
    VODMONTHLYSTAGE("VODMONTHLYSTAGE", "点播月结"),

    /**
     * 集团账号点播月结
     */
    GROUPVODMONTHLYSTAGE("GROUPVODMONTHLYSTAGE", "集团账号点播月结"),

    /**
     * 集团账号直播月结
     */
    GROUPLIVEMONTHLYSTAGE("GROUPLIVEMONTHLYSTAGE", "集团账号直播月结"),

    /**
     * 酷播月结
     */
    CUPLAYERMONTHLYSTAGE("CUPLAYERMONTHLYSTAGE", "酷播云月结"),

    /**
     * 网校云月结
     */
    SCHOOLMONTHLYSTAGE("SCHOOLMONTHLYSTAGE", "网校云月结"),

    /**
     * CDN月结
     */
    CDNMONTHLYSTAGE("CDNMONTHLYSTAGE", "CDN月结"),

    /**
     * 直播添加频道号
     */
    LIVECHANNELS("LIVECHANNELS", "添加频道数"),

    /**
     * 添加导播台分钟数
     */
    LIVEGUIDEDURATION("LIVEGUIDEDURATION", "添加导播台分钟数"),

    /**
     * 直播峰值并发套餐
     */
    LIVEPEAK("LIVEPEAK", "直播峰值并发"),

    /**
     * 添加集团点播流量
     */
    VODGROUPFLOW("VODGROUPFLOW", "添加集团点播流量"),

    /**
     * 添加集团点播空间
     */
    VODGROUPSPACE("VODGROUPSPACE", "添加集团点播空间"),

    /**
     * 添加集团点播套餐
     */
    VODGROUPPACKAGE("VODGROUPPACKAGE", "添加集团点播套餐"),

    /**
     * 集团点播账号延期
     */
    VODGROUPDELAY("VODGROUPDELAY", "集团点播账号延期"),

    /**
     * 添加集团直播分钟数
     */
    LIVEGROUPDURATION("LIVEGROUPDURATION", "添加集团直播分钟数"),

    /**
     * 添加集团直播分钟数
     */
    LIVEGROUPMICDURATION("LIVEGROUPMICDURATION", "添加集团连麦分钟数"),

    /**
     * 添加集团直播并发
     */
    LIVEGROUPCONCURRENCY("LIVEGROUPCONCURRENCY", "添加集团直播并发"),

    /**
     * 添加集团直播延期
     */
    LIVEGROUPDELAY("LIVEGROUPDELAY", "添加集团直播延期"),

    /**
     * 添加集团频道数
     */
    LIVEGROUPCHANNELS("LIVEGROUPCHANNELS", "添加集团频道数"),

    /**
     * 酷播云套餐
     */
    CUPLAYERPACKAGE("CUPLAYERPACKAGE", "酷播云套餐"),

    /**
     * 酷播云功能定制
     */
    CUPLAYERCUSTOM("CUPLAYERCUSTOM", "酷播云功能定制"),

    /**
     * 网校云套餐
     */
    SCHOOLPACKAGE("SCHOOLPACKAGE", "网校云套餐"),

    /**
     * 网校云功能定制
     */
    SCHOOLCUSTOM("SCHOOLCUSTOM", "网校云功能定制"),

    /**
     * 连麦分钟数
     */
    LIVEMICDURATION("LIVEMICDURATION", "连麦分钟数"),

    /**
     * 充值金额
     */
    DEPOSITAMOUNT("DEPOSITAMOUNT", "充值金额"),

    /**
     * 超额还款
     */
    OVERAMOUNTPAY("OVERAMOUNTPAY", "超额还款"),

    /**
     * ppt按页数付费
     */
    PPT_PAGE_PAY("PPTPAGEPAY", "PPT按页数付费"),

    /**
     * ppt专享通道付费
     */
    PPT_LINE_PAY("PPTLINEPAY", "PPT专享通道"),

    /**
     * PRTC添加分钟数
     */
    PRTCDURATION("PRTCDURATION", "添加无延迟分钟数"),

    /**
     * 驻场服务
     */
    ONSITESERVICE("ONSITESERVICE", "驻场服务"),

    /**
     * 驻场服务月结
     */
    ONSITEMONTHLYSTAGE("ONSITEMONTHLYSTAGE", "驻场服务月结"),

    /**
     * 集团账号驻场服务月结
     */
    GROUPONSITEMONTHLYSTAGE("GROUPONSITEMONTHLYSTAGE", "集团账号驻场服务月结"),


    /**
     *  运营服务月结
     */
    OPERATION_SERVICE_MONTHLY_STAGE("OPERATION_SERVICE_MONTHLY_STAGE", "运营服务月结"),

    /**
     *  集团账号运营服务月结
     */
    GROUP_OPERATION_SERVICE_MONTHLY_STAGE("GROUP_OPERATION_SERVICE_MONTHLY_STAGE", "集团账号运营服务月结"),

    /**
     *  私有云月结
     */
    PRIVATE_CLOUD_MONTHLY_STAGE("PRIVATE_CLOUD_MONTHLY_STAGE", "私有云月结"),

    ;

    /**
     * 类型的值。
     */
    private String value;

    /**
     * 类型的中文描述。
     */
    private String text;


    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (OldOperationNameEnum enableTypeEnum : OldOperationNameEnum.values()) {
            MAP.put(enableTypeEnum.value, enableTypeEnum.text);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static OldOperationNameEnum matchAny(String match) {
        if (StringUtils.isNotEmpty(match)) {
            for (OldOperationNameEnum oon : OldOperationNameEnum.values()) {
                if (oon.getText().equals(match.trim())) {
                    return oon;
                }
            }
        }
        return null;
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
