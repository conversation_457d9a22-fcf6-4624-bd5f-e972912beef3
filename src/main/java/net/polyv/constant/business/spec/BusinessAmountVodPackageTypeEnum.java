package net.polyv.constant.business.spec;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 商机-金额规格-点播套餐
 * @author: Neo
 * @date: 2021-11-15
 */
@AllArgsConstructor
@Getter
public enum BusinessAmountVodPackageTypeEnum {

    NONE("0", "不设置"),
    BASIS("8", "基础版"),
    EDUCATION("114", "教育版"),
    BIG_CUSTOMER("98", "大客户版"),


    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (BusinessAmountVodPackageTypeEnum enableTypeEnum : BusinessAmountVodPackageTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
