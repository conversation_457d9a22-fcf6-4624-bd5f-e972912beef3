package net.polyv.constant.business.spec;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


/**
 * @description: 商机套餐规格-直播无延迟并发类型
 */
@Getter
@AllArgsConstructor
public enum BusinessPackagePrtcConcurrentTypeNum {
    PACKAGE_SPEC_CONCURRENT_PRTC_DAILY("prtc_daily", "并发包天"),
    PACKAGE_SPEC_CONCURRENT_PRTC_MONTHLY("prtc_monthly", "并发包月")


    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (BusinessPackagePrtcConcurrentTypeNum enableTypeEnum : BusinessPackagePrtcConcurrentTypeNum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
