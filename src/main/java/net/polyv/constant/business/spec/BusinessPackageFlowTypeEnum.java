package net.polyv.constant.business.spec;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 商机套餐规格-点播流量类型枚举
 * @author: Neo
 * @date: 2021-11-15
 */
@Getter
@AllArgsConstructor
public enum BusinessPackageFlowTypeEnum {

    PACKAGE_SPEC_FLOW_TEMP("package_spec_flow_temp", "临时流量"),
    PACKAGE_SPEC_FLOW_PACK("package_spec_flow_pack", "流量包")


    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (BusinessPackageFlowTypeEnum enableTypeEnum : BusinessPackageFlowTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
