package net.polyv.constant.business.spec;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 商机套餐规格-直播并发类型
 * @author: Neo
 * @date: 2021-11-15
 */
@Getter
@AllArgsConstructor
public enum BusinessPackageConcurrentTypeEnum {

    PACKAGE_SPEC_CONCURRENT_DAILY("daily", "并发包天"),
    PACKAGE_SPEC_CONCURRENT_MONTHLY("monthly", "并发包月"),
    PACKAGE_SPEC_CONCURRENT_PEEK("peak", "峰值并发")


    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (BusinessPackageConcurrentTypeEnum enableTypeEnum : BusinessPackageConcurrentTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
