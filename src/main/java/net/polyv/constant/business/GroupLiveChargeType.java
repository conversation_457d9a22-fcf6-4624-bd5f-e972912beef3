package net.polyv.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 集团账号直播计费类型
 */
@Getter
@AllArgsConstructor
public enum GroupLiveChargeType {

	MINUTES("minutes" ,"分钟数") ,
	PARALLEL_CONCURRENT("parallelConcurrent" ,"并行并发"),
	TOTAL_CONCURRENT("totalConcurrent" ,"累计并发"),

	;

	private final String value ;

	private final String desc ;
	
	public static GroupLiveChargeType get(String value) {
		for (GroupLiveChargeType chargeType : values()) {
			if (chargeType.getValue().equals(value)) {
				return chargeType;
			}
		}
		return null;
	}

}
