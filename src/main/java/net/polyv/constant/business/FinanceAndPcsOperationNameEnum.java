package net.polyv.constant.business;

import lombok.AllArgsConstructor;

/**
 * 结算和财务业务名称
 */
@AllArgsConstructor
public enum FinanceAndPcsOperationNameEnum {

    /**
     * 点播套餐
     */
    VODPACKAGE("VODPACKAGE","vod_package", "点播套餐"),

    /**
     * 添加点播空间
     */
    VODSPACE("VODSPACE","vod_space", "添加点播空间"),

    /**
     * 添加点播流量
     */
    VODFLOW("VODFLOW","vod_flow", "添加点播流量"),

    /**
     * 点播功能定制
     */
    VODCUSTOM("VODCUSTOM","vod_function","点播功能定制"),

    /**
     * 直播添加分钟数
     */
    LIVEDURATION("LIVEDURATION","live_duration", "添加直播分钟数"),

    /**
     * 直播按天套餐
     */
    LIVEDAILY("LIVEDAILY","live_concurrent_daily","直播按天套餐"),

    /**
     * 直播无延迟包天套餐
     */
    LIVEPRTCDAILY("LIVEPRTCDAILY","live_prtc_concurrent_prtc_daily","直播无延迟按天套餐"),

    /**
     * 直播包月套餐
     */
    LIVEMONTHLY("LIVEMONTHLY","live_concurrent_monthly","直播包月套餐"),

    /**
     * 直播无延迟包月套餐
     */
    LIVEPRTCMONTHLY("LIVEPRTCMONTHLY","live_prtc_concurrent_prtc_monthly","直播无延迟包月套餐"),

    /**
     * 直播播功能定制
     */
    LIVECUSTOM("LIVECUSTOM","live_function", "直播功能定制"),

    /**
     * 硬件购买
     */
    HARDWARE("HARDWARE","hard_all", "硬件购买"),

    /**
     * 直播添加频道号
     */
    LIVECHANNELS("LIVECHANNELS","live_channel", "添加频道数"),

    /**
     * 添加导播台分钟数
     */
    LIVEGUIDEDURATION("LIVEGUIDEDURATION","live_guide_duration", "添加导播台分钟数"),

    /**
     * 直播峰值并发套餐
     */
    LIVEPEAK("LIVEPEAK", "live_concurrent_peak","直播峰值并发"),



    /**
     * 酷播云套餐
     */
    CUPLAYERPACKAGE("CUPLAYERPACKAGE","cool_package","酷播云套餐"),

    /**
     * 酷播云流量
     */
    CUPLAYERFLOW("VODFLOW","cool_flow","酷播云流量"),

    /**
     * 酷播云空间
     */
    CUPLAYERSPACE("VODSPACE","cool_space","酷播云空间"),



    /**
     * 网校云套餐
     */
    SCHOOLPACKAGE("SCHOOLPACKAGE", "online_package","网校云套餐"),



    /**
     * 连麦分钟数
     */
    LIVEMICDURATION("LIVEMICDURATION","live_mic_duration", "连麦分钟数"),

    /**
     * 充值金额
     */
    DEPOSITAMOUNT("DEPOSITAMOUNT","amount", "充值金额"),



    /**
     * 驻场服务
     */
    ONSITESERVICE("ONSITESERVICE", "resident_all", "驻场服务"),
    
    
    /**
     * 运营服务月结
     */
    OPERATION_SERVICE_MONTHLY_STAGE("OPERATION_SERVICE_MONTHLY_STAGE", "operation_all", "运营服务月结"),
    
    
    /**
     * 私有云月结
     */
    PRIVATE_CLOUD_MONTHLY_STAGE("PRIVATE_CLOUD_MONTHLY_STAGE", "private_all", "私有云月结"),
    
    /**
     * 直播流量套餐
     */
    LIVE_FLOW("LIVE_FLOW", "live_flow", "直播流量套餐"),

    /**
     * 资源点
     */
    RESOURCE_POINT("RESOURCE_POINT", "resource_point", "资源点"),
    ;
    
    /**
     * 财务对应业务操作code。
     */
    private String financeCode;
    
    
    /**
     * 结算对应业务操作code。
     */
    private String pcsCode;
    /**
     * 类型的中文描述。
     */
    private String desc;

    public String getFinanceCode() {
        return financeCode;
    }

    public void setFinanceCode(String financeCode) {
        this.financeCode = financeCode;
    }

    public String getPcsCode() {
        return pcsCode;
    }

    public void setPcsCode(String pcsCode) {
        this.pcsCode = pcsCode;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 通过结算编码获取描述
     * @param pcsCode 值
     * @return 业务项值
     */
    public static String getDescByPcsCode(String pcsCode) {
        FinanceAndPcsOperationNameEnum[] allEnums = FinanceAndPcsOperationNameEnum.values();
        for (FinanceAndPcsOperationNameEnum ws : allEnums) {
            if (ws.getPcsCode().equalsIgnoreCase(pcsCode)) {
                return ws.getDesc();
            }
        }
        return "";
    }
    /**
     * 通过结算编码获取财务编码
     * @param pcsCode 值
     * @return 业务项值
     */
    public static String getFinanceCodeByPcsCode(String pcsCode) {
        FinanceAndPcsOperationNameEnum[] allEnums = FinanceAndPcsOperationNameEnum.values();
        for (FinanceAndPcsOperationNameEnum ws : allEnums) {
            if (ws.getPcsCode().equalsIgnoreCase(pcsCode)) {
                return ws.getFinanceCode();
            }
        }
        return "";
    }
}
