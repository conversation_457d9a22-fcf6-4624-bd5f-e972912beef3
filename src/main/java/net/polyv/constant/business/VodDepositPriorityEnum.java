package net.polyv.constant.business;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 点播充值类型执行顺序优先级枚举
 * 点播套餐(酷博云套餐)  > 点播空间流量(酷博云空间和流量)
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022-05-07
 */
@Getter
@AllArgsConstructor
public enum VodDepositPriorityEnum {

    VOD_PACKAGE("vod_package", 1),
    COOL_PACKAGE("cool_package", 1),
    VOD_SPACE("vod_space",2),
    COOL_SPACE("cool_space",2),
    VOD_FLOW("vod_flow",2),
    COOL_FLOW("cool_flow",2)
    ;

    private String code;
    private Integer priority;

    private static final Map<String, Integer> MAP = new HashMap<>();

    static {
        for (VodDepositPriorityEnum enableTypeEnum : VodDepositPriorityEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.priority);
        }
    }

    public static Integer getPriority(String key) {
        return getMap().get(key) == null ? 0 : getMap().get(key);
    }
    public static Map<String, Integer> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
