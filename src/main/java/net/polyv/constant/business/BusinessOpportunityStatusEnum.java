package net.polyv.constant.business;


import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 商机状态枚举
 */
@Getter
@AllArgsConstructor
public enum BusinessOpportunityStatusEnum {

    /**
     * 待审核
     */
    WAIT_AUDITING(1, "待审核"),

    /**
     * 审批中
     */
    AUDITING(2, "审批中"),

    /**
     *已驳回
     */
    REJECTED(3, "已驳回"),

    /**
     * 已通过
     */
    PASSED(4, "已通过"),

    /**
     * 已关联
     */
    ASSOCIATE(5, "已关联"),

    /**
     *  退费
     */
    REFUND(5, "退费"),


    ;

    private static final Map<Integer, String> MAP = new HashMap<>();

    static {
        for (BusinessOpportunityStatusEnum enableTypeEnum : BusinessOpportunityStatusEnum.values()) {
            MAP.put(enableTypeEnum.value, enableTypeEnum.desc);
        }
    }

    /**
     * value
     */
    private Integer value;

    /**
     * desc
     */
    private String desc;


    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<Integer, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
