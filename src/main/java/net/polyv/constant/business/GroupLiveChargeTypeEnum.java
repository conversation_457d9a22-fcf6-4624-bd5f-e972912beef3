package net.polyv.constant.business;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 集团账号直播计费方式
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022-03-25
 */
@Getter
@AllArgsConstructor
public enum GroupLiveChargeTypeEnum {

    MINUTES("minutes", "分钟数计费"),
    PARALLEL_CONCURRENT("parallelConcurrent", "并行并发"),
    TOTAL_CONCURRENT("totalConcurrent", "累计并发")
    ;

    private String code;
    private String desc;

    private static final Map<String, String> MAP = new HashMap<>();

    static {
        for (GroupLiveChargeTypeEnum enableTypeEnum : GroupLiveChargeTypeEnum.values()) {
            MAP.put(enableTypeEnum.code, enableTypeEnum.desc);
        }
    }

    public static String getValue(String key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }

    public static Map<String, String> getMap() {
        return MAP;
    }

    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
