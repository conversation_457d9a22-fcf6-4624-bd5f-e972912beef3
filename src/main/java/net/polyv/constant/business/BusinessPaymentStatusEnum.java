package net.polyv.constant.business;


import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 回款状态枚举
 */
@Getter
@AllArgsConstructor
public enum BusinessPaymentStatusEnum {
    
    /**
     * 已回款
     */
    HAS_PAYMENT(1, "已回款"),
    
    /**
     * 已失效
     */
    HAS_EXPIRED(2, "已失效"),
    
    /**
     * 已删除
     */
    HAS_DELETED(3, "已删除");
    
    private static final Map<Integer, String> MAP = new HashMap<>();
    
    static {
        for (BusinessPaymentStatusEnum enableTypeEnum : BusinessPaymentStatusEnum.values()) {
            MAP.put(enableTypeEnum.value, enableTypeEnum.desc);
        }
    }
    
    /**
     * value
     */
    private Integer value;
    
    /**
     * desc
     */
    private String desc;
    
    
    public static String getValue(Integer key) {
        return getMap().get(key) == null ? "" : getMap().get(key);
    }
    
    public static Map<Integer, String> getMap() {
        return MAP;
    }
    
    public static String getJson() {
        return JSON.toJSONString(getMap());
    }
}
