package net.polyv.constant;

/**
 * 系统常量
 * <AUTHOR>
 * @since 2021/4/14
 */
public enum SysTypeConst {
    Live("直播"), Vod("点播"), SmallClass("互动课堂"), Meeting("研讨会"), GroupV2("集团账号2.0"),
    
    Material("素材库");
    
    private final String label;
    
    SysTypeConst(String label) {
        this.label = label;
    }
    
    public String getLabel() {
        return label;
    }
    
    
    public static  SysTypeConst  findByLabel(String label){
        for (SysTypeConst typeConst  :values()){
            if (label.equals(typeConst.getLabel())){
                return typeConst;
            }
        }
        return  null;
    }
}
