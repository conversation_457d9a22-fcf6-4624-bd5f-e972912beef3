package net.polyv.dao.primary;

import java.util.List;

import net.polyv.model.entity.primary.CustomizedAccountDepositDetail;
import org.springframework.data.jpa.repository.Query;

import net.polyv.dao.BatchRepository;
import net.polyv.model.entity.primary.CustomerBase;

/**
 * 客户基础信息
 * <AUTHOR>
 * @date 2022/10/8 10:57
 */

public interface CustomerBaseRepository extends BatchRepository<CustomerBase, String> {
    
    
    @Query(nativeQuery = true, value = "select union_id from customer_base where union_id>:customerId order by " +
            "union_id limit :limit")
    List<String> findUnionIdByLimit(String customerId, Integer limit);

    /**
     * 根据直播 ID 列表查询对应的记录
     */
    @Query(nativeQuery = true, value = " select * from customer_base where  live_user_id in (:liveUserList)")
    List<CustomerBase> getCustomerIdByLiveUserId(List<String> liveUserList);

    /**
     根据点播 ID 列表查询对应的记录
     */
    @Query(nativeQuery = true, value = " select * from customer_base where  vod_user_id in (:vodUserList)")
    List<CustomerBase> getCustomerIdByVodUserId(List<String> vodUserList);

    CustomerBase findFirstByUnionId(String customerId);

    CustomerBase findFirstByLiveUserId(String liveUserId);
}
