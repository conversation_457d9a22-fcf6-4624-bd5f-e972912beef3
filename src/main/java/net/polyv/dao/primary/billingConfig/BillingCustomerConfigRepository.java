package net.polyv.dao.primary.billingConfig;

import net.polyv.model.entity.primary.billingconfig.BillingCustomerConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 计费项客户配置
 * <AUTHOR>
 * @date 2022/7/20 15:08
 */

public interface BillingCustomerConfigRepository extends JpaRepository<BillingCustomerConfig, Integer>, JpaSpecificationExecutor<BillingCustomerConfig> {
    
    /**
     * 根据计费项时间获取匹配的结算客户
     * contract_id is null :测试、赠送开通数据
     * @param dateStr 时间格式 yyyy-MM-dd
     * @param isClearByDay 是否按天结算 否
     * @param itemCode 计费项
     * @return 客户配置化计费项列表
     * <AUTHOR>
     * @date 2022/7/21
     */
    @Query(nativeQuery = true, value = " select  * from billing_customer_config where billing_time =:dateStr" +
            " and is_clear_by_day =:isClearByDay  and is_deleted =0 and contract_id is not null " +
            "  and if( :itemCode is not null and  :itemCode != '', item_code = :itemCode  , 1=1)")
    List<BillingCustomerConfig> findByBillingTimeAndIsClearByDay(@Param("dateStr") String dateStr,
            @Param("isClearByDay") Integer isClearByDay, @Param("itemCode") String itemCode);
    
    /**
     * 查询已经失效的客户配置计费项
     * @param isDeleted 是否删除  0否 1是
     * @param expireTime 失效时间
     * @return 客户配置化计费项列表
     * @Param billingWayId 计费方式id
     */
    List<BillingCustomerConfig> findByIsDeletedAndExpireTimeLessThanAndBillingWayId(Integer isDeleted, Date expireTime,
            Integer billingWayId);
    
    /**
     * 获取客户信息
     * @param unionId 客户标识
     * @param itemCode 计费项编码
     * @return 多笔开通记录
     * <AUTHOR>
     * @date 2022/7/26
     */
    @Query(nativeQuery = true, value = "SELECT *  FROM billing_customer_config  WHERE  union_id=:unionId AND " +
            "item_code=:itemCode AND is_deleted=0 order by expire_time asc limit 1")
    BillingCustomerConfig findByUnionIdAndItemCode(String unionId, String itemCode);
    
    /**
     * 逻辑删除客户配置化信息
     * @param customerId 客户id
     * @param itemCodes 计费项编码
     * @return {@link int}
     * <AUTHOR>
     * @date 2022/7/26
     */
    @Modifying
    @Query(nativeQuery = true, value =
            "UPDATE  billing_customer_config SET is_deleted =1  WHERE union_id=:customerId and is_deleted = 0 " +
                    "and  item_code in  (:itemCodes) ")
    int logicDeleteByCustomerAndItemCodes(String customerId, List<String> itemCodes);
    
    List<BillingCustomerConfig> findByUnionIdAndItemCodeIn(String customerId, List<String> itemCodes);
    
    
    /**
     * 根据客户id和启用状态获取客户配置化计费项的信息（排除按用量+使用时长计费项，逻辑不一样，另外处理）
     * @param customerId 客户id
     * @param isDeleted 是否删除
     * @param expire 有效期
     * @return {@link List<BillingCustomerConfig>}
     * <AUTHOR>
     * @date 2022/8/2
     */
    @Query(nativeQuery = true, value = "select *from billing_customer_config where  is_deleted = :isDeleted " +
            " and union_id =:customerId and expire_time> :expire and billing_way_id != :usageAndTimeBillingWayId")
    List<BillingCustomerConfig> findCustomerEnableConfig(@Param("customerId") String customerId,
            @Param("expire") Date expire, @Param("isDeleted") Integer isDeleted, @Param("usageAndTimeBillingWayId") Integer usageAndTimeBillingWayId);
    
    
    /**
     * 根据计费方式id获取用户生肖猪的记录
     * @param unionId
     * @param billingWayId
     * @param isDeleted
     * @param expireTime
     * @return
     */
    List<BillingCustomerConfig> findByUnionIdAndBillingWayIdAndIsDeletedAndExpireTimeGreaterThanEqual(String unionId,
            Integer billingWayId, int isDeleted, Date expireTime);
    
    /// List<BillingCustomerConfig> findByUnionIdAndIsDeleted(String customerId, int value);
    
    /**
     * 根据资源类型获取客户计费项配置
     * @param resourceType 资源
     * @return 配置
     * <AUTHOR>
     * @date 2022/11/7
     */

    List<BillingCustomerConfig> findByResourceType(Integer resourceType);
    
    /**
     * 根据合同id和资源类型获取客户计费项配置
     * @param resourceType 资源
     * @param contractId 合同id
     * @return 配置
     * <AUTHOR>
     * @date 2022/11/7
     */
    BillingCustomerConfig findByContractIdAndResourceType(String contractId, Integer resourceType);
    
    /**
     * 合同id获取 客户配置化信息
     * @param contractId 合同id
     * @param unionId 用户id
     * @return {@link BillingCustomerConfig}
     * <AUTHOR>
     * @date 2022/11/17
     */
    BillingCustomerConfig findByUnionIdAndContractId(String unionId, String contractId);
    
    /**
     * 获取客户过期未清零配置
     * *
     * @param unionId 客户id
     * @param itemCode 计费项
     * @param date 查询时间
     * @return {@link List<BillingCustomerConfig>}
     * <AUTHOR>
     * @date 2022/11/17
     */
    @Query(nativeQuery = true, value = "SELECT * FROM billing_customer_config  WHERE union_id = :unionId " +
            " AND item_code = :itemCode AND is_expire_reset = 1  AND is_reset = 0 AND available_value>0" +
            " AND expire_time <= :date")
    List<BillingCustomerConfig> findCustomerExpireConfig(@Param("unionId") String unionId,
            @Param("itemCode") String itemCode, @Param("date") String date);
    
    /**
     * 获取客户生效中的计费项资源
     * @param unionId 客户id
     * @param itemCode 计费项
     * @param date 查询时间
     * @return 资源列表
     */
    @Query(nativeQuery = true, value = "select * from billing_customer_config " +
            "where union_id =:unionId and item_code = :itemCode and expire_time> :date ")
    List<BillingCustomerConfig> findCustomerUnExpireAvailable(@Param("unionId") String unionId,
            @Param("itemCode") String itemCode, @Param("date") Date date);
    
    
    /**
     * 查询是否存在指定结算日期的计费项
     */
    @Query(nativeQuery = true, value = "select count(1) from billing_customer_config where union_id =:unionId " +
            "and item_code = :itemCode and is_deleted =0 and billing_time = :specifyDate")
    int existsTodayBillingTimeData(@Param(value = "unionId") String unionId, @Param(value = "itemCode") String itemCode,
            @Param(value = "specifyDate") String specifyDate);
    
    @Query(nativeQuery = true, value = " select  * from billing_customer_config where item_code = :itemCode" +
            "  and contract_id is not null ")
    List<BillingCustomerConfig> findByItemCode(@Param("itemCode") String itemCode);

    @Query(nativeQuery = true, value = " select  * from billing_customer_config where item_code = :itemCode" +
            "  and contract_id is not null and is_reset =0 and is_expire_reset =1 and available_value >0 and expire_time <= :date")
    List<BillingCustomerConfig> findNeedResetByItemCode(@Param("itemCode") String itemCode ,@Param("date") Date date);

    /**
     * 获取客户即将失效的资源
     */
    @Query(nativeQuery = true, value =
            "SELECT * FROM billing_customer_config " +
                    "WHERE item_code =:itemCode and expire_time >= :nowTime AND expire_time <= :expireTime AND is_deleted =0 ", countQuery =
            "SELECT count(*) FROM billing_customer_config WHERE item_code =:itemCode and expire_time >= :nowTime " +
                    "AND expire_time <= :expireTime AND is_deleted =0 ")
    Page<BillingCustomerConfig> customerResourceWillExpireByPage(@Param("itemCode") String itemCode,@Param("nowTime") String nowTime,
                                                           @Param("expireTime") String expireTime, Pageable pageable);


    /**
     * 获取该资源创建时间范围内的数据
     * @param itemCode 计费项编码
     * @param startTime
     * @param endTime
     */
    @Query(nativeQuery = true, value = " SELECT  union_id AS customerId,count(*) AS num" +
            "        FROM     billing_customer_config        WHERE  item_code = :itemCode " +
            "        AND create_time >= :startTime  AND create_time <= :endTime GROUP BY union_id")
    List<Map<String, Object>> getCustomerRechargeNum(@Param("itemCode") String itemCode,
                                                     @Param("startTime") String startTime, @Param("endTime") String endTime);
    
    /**
     * 获取用户该资源编码启动中的数据
     * @param customerId 客户id
     * @param resourceCode 资源编码
     * @param isDel 是否删除
     * @return
     */
    List<BillingCustomerConfig> findByUnionIdAndResourceCodeAndIsDeleted(String customerId, String resourceCode,
            int isDel);
    
    
    /**
     * 获取用户该资源编码启动中的数据
     * @param customerId 客户id
     * @param itemCode 计费项编码
     * @param isDel 是否删除
     * @return
     */
    List<BillingCustomerConfig> findByUnionIdAndItemCodeAndIsDeletedAndExpireTimeGreaterThanEqual(String customerId,
            String itemCode, int isDel, Date expireTime);

    /**
     * 获取用户该资源编码启动中的数据
     * @param itemCode 计费项编码
     * @param isDel 是否删除
     * @return
     */
    List<BillingCustomerConfig> findByItemCodeAndIsDeletedAndExpireTimeGreaterThanEqual(String itemCode, int isDel, Date expireTime);

    /**
     * 获取用户该资编码已过期的数据
     * @param customerId 客户id
     * @param itemCode 计费项编码
     * @param isDel 是否删除
     * @return
     */
    List<BillingCustomerConfig> findByUnionIdAndItemCodeAndIsDeletedAndExpireTimeLessThan(String customerId,
                                                                                                  String itemCode,
                                                                                                  int isDel,
                                                                                                  Date expireTime);

    /**
     * 获取客户有效的资源
     */
    @Query(nativeQuery = true, value =
            "SELECT * FROM billing_customer_config " +
                    "WHERE item_code =:itemCode and expire_time >= :nowTime AND is_deleted =0 ", countQuery =
            "SELECT count(*) FROM billing_customer_config WHERE item_code =:itemCode and expire_time >= :nowTime " +
                    " AND is_deleted =0 ")
    Page<BillingCustomerConfig> customerResourceNotExpireByPage(@Param("itemCode") String itemCode,@Param("nowTime") String nowTime, Pageable pageable);

    /**
     * 获取客户生效中的计费项资源
     * @param unionId 客户id
     * @param itemCode 计费项
     * @param date 查询时间
     * @return 资源列表
     */
    @Query(nativeQuery = true, value = "select * from billing_customer_config " +
            "where union_id =:unionId and item_code = :itemCode and expire_time> :date and is_deleted =0 and available_value > 0 ")
    List<BillingCustomerConfig> findCustomerUnExpireAvailabled(@Param("unionId") String unionId,
                                                              @Param("itemCode") String itemCode, @Param("date") Date date);

    /**
     * 获取客户指定计费所有的信息
     * @param unionId 客户id
     * @param itemCode 计费项
     * @return 资源列表
     */
    @Query(nativeQuery = true, value = "select * from billing_customer_config " +
            "where union_id =:unionId and item_code = :itemCode ")
    List<BillingCustomerConfig> findCustomerAndItemCode(@Param("unionId") String unionId, @Param("itemCode") String itemCode);

    /**
     * 获取用户该资源未过期的列表
     * @param customerId 客户id
     * @param itemCode 计费项编码
     */
    @Query(nativeQuery = true, value = "select * from billing_customer_config " +
            "where union_id =:unionId and item_code = :itemCode and expire_time> :expireTime and resource_type = :resourceType and is_deleted =0 order by billing_time asc")
    List<BillingCustomerConfig> findByNotExpiredList(String unionId, String itemCode, Date expireTime,int resourceType);

    /**
     * 获取客户生效中的计费项资源
     * @param unionId 客户id
     * @param itemCode 计费项
     * @param date 查询时间
     * @return 资源列表
     */
    @Query(nativeQuery = true, value = "select * from billing_customer_config " +
            "where union_id =:unionId and item_code = :itemCode and expire_time> :date and is_deleted =0 and resource_type = 0 ")
    List<BillingCustomerConfig> findCustomerUnExpirePackage(@Param("unionId") String unionId,
                                                               @Param("itemCode") String itemCode, @Param("date") Date date);
}
