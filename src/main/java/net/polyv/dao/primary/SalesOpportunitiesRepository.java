package net.polyv.dao.primary;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.web.model.salesopportunities.input.SalesOpportCustomerInfoUpdateInputVO;
import net.polyv.web.model.stat.StatQueryRequest;

/**
 * @Author: chenshaofeng
 * @Date: 2020/5/8
 * @Description: 销售机会记录表
 */
public interface SalesOpportunitiesRepository extends JpaRepository<SalesOpportunities, Long> {

    /**
     * 根据id与saleUserId查询销售机会记录
     *
     * @param id         id
     * @param saleUserId 责任销售的用户id
     * @return 销售机会记录
     */
    Optional<SalesOpportunities> findByIdAndSaleUserId(Long id, String saleUserId);
    
    /**
     * 按条件查询销售机会记录
     * @return 匹配条件的、最新的一条记录
     */
    @Query(nativeQuery = true, value = "select so.* from sales_opportunities so " +
            "join finance_contract fc on so.so_id = fc.so_id " +
            "where so.customer_id  = :customerId and so.status = :status " +
            "and so.billing_plan_code = :billingPlanCode and so.business_start_time = :businessStartTime " +
            "and so.business_end_time = :businessEndTime " +
            "and fc.contract_way = :contractWay " +
            "order by fc.create_time desc limit 1;")
    Optional<SalesOpportunities> findOne(@Param("customerId") String customerId,
            @Param("status") Integer status, @Param("billingPlanCode") String billingPlanCode,
            @Param("businessStartTime") Date businessStartTime, @Param("businessEndTime") Date businessEndTime,
            @Param("contractWay") String contractWay);
    
    /**
     * 按条件查询销售机会记录
     * @return 匹配条件的、最新的一条记录
     */
    @Query(nativeQuery = true, value = "select so.* from sales_opportunities so " +
            "join finance_contract fc on so.so_id = fc.so_id " +
            "where so.customer_id  = :customerId and so.status = :status " +
            "and so.cooperation_subject is not null and fc.contract_way = :contractWay " +
            "order by fc.create_time desc limit 1;")
    Optional<SalesOpportunities> findOne(@Param("customerId") String customerId, @Param("status") Integer status,
            @Param("contractWay") String contractWay);

    /**
     * 查询所有的销售机会记录
     *
     * @param spec 查询条件
     * @return 销售机会记录
     */
    List<SalesOpportunities> findAll(Specification<SalesOpportunities> spec);

    /**
     * 分页按条件查询销售机会记录
     *
     * @param spec     查询条件
     * @param pageable 分页条件
     * @return 分页数据
     */
    Page<SalesOpportunities> findAll(Specification<SalesOpportunities> spec, Pageable pageable);

    /**
     * 按条件更新不为null的属性（除了contractStartTime、contractEndTime、amountGainedDate需要用其他字段来判断是否需要更新）
     *
     * @param salesOpportunities     要更新的销售机会记录数据
     * @param updateAmountGainedDate 更新到款日期
     * @param updateContractTime     更新合同时间
     * @return 更新行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities so SET " +
            "so.summary              = CASE WHEN :#{#s.summary}                 IS NULL THEN so.summary                 ELSE :#{#s.summary}                 END ," +
            "so.contractType         = CASE WHEN :#{#s.contractType}            IS NULL THEN so.contractType            ELSE :#{#s.contractType}            END , " +
            "so.name                 = CASE WHEN :#{#s.name}                    IS NULL THEN so.name                    ELSE :#{#s.name}                    END , " +
            "so.code                 = CASE WHEN :#{#s.code}                    IS NULL THEN so.code                    ELSE :#{#s.code}                    END , " +
            "so.company              = CASE WHEN :#{#s.company}                 IS NULL THEN so.company                 ELSE :#{#s.company}                 END ," +
            "so.email                = CASE WHEN :#{#s.email}                   IS NULL THEN so.email                   ELSE :#{#s.email}                   END ," +
            "so.businessType         = CASE WHEN :#{#s.businessType}            IS NULL THEN so.businessType            ELSE :#{#s.businessType}            END ," +
            "so.billingPlan          = CASE WHEN :#{#s.billingPlan}             IS NULL THEN so.billingPlan             ELSE :#{#s.billingPlan}             END ," +
            "so.amountGained         = CASE WHEN :#{#s.amountGained}            IS NULL THEN so.amountGained            ELSE :#{#s.amountGained}            END ," +
            "so.contractAmount       = CASE WHEN :#{#s.contractAmount}          IS NULL THEN so.contractAmount          ELSE :#{#s.contractAmount}          END ," +
            "so.invoiceType          = CASE WHEN :#{#s.invoiceType}             IS NULL THEN so.invoiceType             ELSE :#{#s.invoiceType}             END ," +
            "so.acceptanceAccount    = CASE WHEN :#{#s.acceptanceAccount}       IS NULL THEN so.acceptanceAccount       ELSE :#{#s.acceptanceAccount}       END ," +
            "so.customerFrom         = CASE WHEN :#{#s.customerFrom}            IS NULL THEN so.customerFrom            ELSE :#{#s.customerFrom}            END ," +
            "so.industryDistribution = CASE WHEN :#{#s.industryDistribution}    IS NULL THEN so.industryDistribution    ELSE :#{#s.industryDistribution}    END ," +
            "so.type                 = CASE WHEN :#{#s.type}                    IS NULL THEN so.type                    ELSE :#{#s.type}                    END ," +
            "so.functionPackage      = CASE WHEN :#{#s.functionPackage}         IS NULL THEN so.functionPackage         ELSE :#{#s.functionPackage}         END ," +
            "so.introduction         = CASE WHEN :#{#s.introduction}            IS NULL THEN so.introduction            ELSE :#{#s.introduction}            END ," +
            "so.ext                  = CASE WHEN :#{#s.ext}                     IS NULL THEN so.ext                     ELSE :#{#s.ext}                     END ," +
            "so.status               = CASE WHEN :#{#s.status}                  IS NULL THEN so.status                  ELSE :#{#s.status}                  END ," +
            "so.amountValidYears     = CASE WHEN :#{#s.amountValidYears}        IS NULL THEN so.amountValidYears        ELSE :#{#s.amountValidYears}        END ," +
            "so.customerId           = CASE WHEN :#{#s.customerId}              IS NULL THEN so.customerId              ELSE :#{#s.customerId}              END ," +
            "so.amountExpireDate     = CASE WHEN :#{#s.amountExpireDate}        IS NULL THEN so.amountExpireDate        ELSE :#{#s.amountExpireDate}        END ," +
            "so.amountExpireType     = CASE WHEN :#{#s.amountExpireType}        IS NULL THEN so.amountExpireType        ELSE :#{#s.amountExpireType}        END ," +
            "so.contractStartTime    = CASE WHEN :updateContractTime            = false THEN so.contractStartTime       ELSE :#{#s.contractStartTime}       END ," +
            "so.contractEndTime      = CASE WHEN :updateContractTime            = false THEN so.contractEndTime         ELSE :#{#s.contractEndTime}         END ," +
            "so.amountGainedDate     = CASE WHEN :updateAmountGainedDate        = false THEN so.amountGainedDate        ELSE :#{#s.amountGainedDate}        END ," +
            "so.isGroupUser          = CASE WHEN :#{#s.isGroupUser}             IS NULL THEN so.isGroupUser             ELSE :#{#s.isGroupUser}             END ," +
            "so.updateTime           = ?#{new java.util.Date()} " +
            "WHERE so.id = :#{#s.id}")
    int updateById(@Param("s") SalesOpportunities salesOpportunities, @Param("updateContractTime") Boolean updateContractTime, @Param("updateAmountGainedDate") Boolean updateAmountGainedDate);


    /**
     * 按条件更新不为null的属性（除了contractStartTime、contractEndTime、amountGainedDate需要用其他字段来判断是否需要更新）
     *
     * @param salesOpportunities     要更新的销售机会记录数据
     * @param updateAmountGainedDate 更新到款日期
     * @param updateContractTime     更新合同时间
     * @return 更新行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities so SET " +
            "so.summary              = CASE WHEN :#{#s.summary}                 IS NULL THEN so.summary                 ELSE :#{#s.summary}                 END ," +
            "so.contractType         = CASE WHEN :#{#s.contractType}            IS NULL THEN so.contractType            ELSE :#{#s.contractType}            END , " +
            "so.name                 = CASE WHEN :#{#s.name}                    IS NULL THEN so.name                    ELSE :#{#s.name}                    END , " +
            "so.code                 = CASE WHEN :#{#s.code}                    IS NULL THEN so.code                    ELSE :#{#s.code}                    END , " +
            "so.company              = CASE WHEN :#{#s.company}                 IS NULL THEN so.company                 ELSE :#{#s.company}                 END ," +
            "so.email                = CASE WHEN :#{#s.email}                   IS NULL THEN so.email                   ELSE :#{#s.email}                   END ," +
            "so.businessType         = CASE WHEN :#{#s.businessType}            IS NULL THEN so.businessType            ELSE :#{#s.businessType}            END ," +
            "so.billingPlan          = CASE WHEN :#{#s.billingPlan}             IS NULL THEN so.billingPlan             ELSE :#{#s.billingPlan}             END ," +
            "so.amountGained         = CASE WHEN :#{#s.amountGained}            IS NULL THEN so.amountGained            ELSE :#{#s.amountGained}            END ," +
            "so.contractAmount       = CASE WHEN :#{#s.contractAmount}          IS NULL THEN so.contractAmount          ELSE :#{#s.contractAmount}          END ," +
            "so.invoiceType          = CASE WHEN :#{#s.invoiceType}             IS NULL THEN so.invoiceType             ELSE :#{#s.invoiceType}             END ," +
            "so.acceptanceAccount    = CASE WHEN :#{#s.acceptanceAccount}       IS NULL THEN so.acceptanceAccount       ELSE :#{#s.acceptanceAccount}       END ," +
            "so.customerFrom         = CASE WHEN :#{#s.customerFrom}            IS NULL THEN so.customerFrom            ELSE :#{#s.customerFrom}            END ," +
            "so.industryDistribution = CASE WHEN :#{#s.industryDistribution}    IS NULL THEN so.industryDistribution    ELSE :#{#s.industryDistribution}    END ," +
            "so.type                 = CASE WHEN :#{#s.type}                    IS NULL THEN so.type                    ELSE :#{#s.type}                    END ," +
            "so.functionPackage      = CASE WHEN :#{#s.functionPackage}         IS NULL THEN so.functionPackage         ELSE :#{#s.functionPackage}         END ," +
            "so.introduction         = CASE WHEN :#{#s.introduction}            IS NULL THEN so.introduction            ELSE :#{#s.introduction}            END ," +
            "so.ext                  = CASE WHEN :#{#s.ext}                     IS NULL THEN so.ext                     ELSE :#{#s.ext}                     END ," +
            "so.status               = CASE WHEN :#{#s.status}                  IS NULL THEN so.status                  ELSE :#{#s.status}                  END ," +
            "so.amountValidYears     = CASE WHEN :#{#s.amountValidYears}        IS NULL THEN null                       ELSE :#{#s.amountValidYears}        END ," +
            "so.customerId           = CASE WHEN :#{#s.customerId}              IS NULL THEN so.customerId              ELSE :#{#s.customerId}              END ," +
            "so.amountExpireDate     = CASE WHEN :#{#s.amountExpireDate}        IS NULL THEN null                       ELSE :#{#s.amountExpireDate}        END ," +
            "so.amountExpireType     = CASE WHEN :#{#s.amountExpireType}        IS NULL THEN so.amountExpireType        ELSE :#{#s.amountExpireType}        END ," +
            "so.contractStartTime    = CASE WHEN :updateContractTime            = false THEN so.contractStartTime       ELSE :#{#s.contractStartTime}       END ," +
            "so.contractEndTime      = CASE WHEN :updateContractTime            = false THEN so.contractEndTime         ELSE :#{#s.contractEndTime}         END ," +
            "so.amountGainedDate     = CASE WHEN :updateAmountGainedDate        = false THEN so.amountGainedDate        ELSE :#{#s.amountGainedDate}        END ," +
            "so.isGroupUser          = CASE WHEN :#{#s.isGroupUser}             IS NULL THEN so.isGroupUser             ELSE :#{#s.isGroupUser}             END ," +
            "so.updateTime           = ?#{new java.util.Date()} " +
            "WHERE so.id = :#{#s.id}")
    int updateByIdForExpireDate(@Param("s") SalesOpportunities salesOpportunities, @Param("updateContractTime") Boolean updateContractTime, @Param("updateAmountGainedDate") Boolean updateAmountGainedDate);

    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities so SET " +
            "so.company              = CASE WHEN :#{#input.company}                 IS NULL THEN so.company                 ELSE :#{#input.company}                 END ," +
            "so.saleUserId           = CASE WHEN :#{#input.saleUserId}              IS NULL THEN so.saleUserId              ELSE :#{#input.saleUserId}              END ," +
            "so.saleUserName         = CASE WHEN :#{#input.saleUserName}            IS NULL THEN so.saleUserName            ELSE :#{#input.saleUserName}            END ," +
            "so.updateTime           = ?#{new java.util.Date()} " +
            "WHERE so.customerId = :#{#input.customerId}")
    int updateCustomerInfo(@Param("input") SalesOpportCustomerInfoUpdateInputVO input);

    /**
     * 更新销售机会记录的状态与合同id
     *
     * @param id         销售机会记录自增id
     * @param status     要更新销售机会记录的状态
     * @param contractId 合同id
     * @return 更新条数
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities SET " +
            "status = CASE WHEN :status IS NULL THEN status ELSE :status END, " +
            "contractId = CASE WHEN :contractId IS NULL THEN contractId ELSE :contractId END " +
            "WHERE id =:id ")
    int updateStatusAndContractIdById(@Param("id") Long id, @Param("status") Integer status, @Param("contractId") String contractId);

    /**
     * 根据id更新业务时间（业务开始时间与业务结束时间）
     *
     * @param id                销售机会记录自增id
     * @param businessStartTime 业务开始时间
     * @param businessEndTime   业务结束时间
     * @return 更新条数
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities SET " +
            "businessStartTime  = CASE WHEN :businessStartTime  IS NULL THEN businessStartTime  ELSE :businessStartTime END, " +
            "businessEndTime    = CASE WHEN :businessEndTime    IS NULL THEN businessEndTime    ELSE :businessEndTime   END " +
            "WHERE id = :id")
    int updateBusinessTimeById(@Param("id") Long id, @Param("businessStartTime") Date businessStartTime, @Param("businessEndTime") Date businessEndTime);

    /**
     * 根据销售机会自增id设置销售机会是否被删除
     *
     * @param id    销售机会自增id
     * @param isDel 要设置的删除状态
     * @return 更新条数
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities SET isDel = :isDel WHERE id = :id ")
    int updateIsDelById(@Param("id") Long id, @Param("isDel") Integer isDel);

    /**
     * 根据销售机会自增id与客户id查找销售机会记录
     *
     * @param id         销售机会自增id
     * @param customerId 客户id
     * @return 销售机会记录
     */
    Optional<SalesOpportunities> findByIdAndCustomerId(Long id, String customerId);

    /**
     * 根据销售机会自增id更新销售机会状态
     *
     * @param id     销售机会自增id
     * @param status 要更新的销售机会状态
     * @return 更新条数
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities SET status = :status WHERE id = :id ")
    int updateStatusById(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新钉钉审批流程id
     *
     * @param id            销售机会id
     * @param dingProcessId 钉钉审批流程id
     * @return 更新数量
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities SET ding_process_id = :dingProcessId WHERE id = :id ")
    int updateDingProcessIdById(@Param("id") Long id, @Param("dingProcessId") String dingProcessId);

    /**
     * 更新钉钉审批状态
     *
     * @param dingProcessId 钉钉流程实例id
     * @param status        审批状态
     * @return 更新行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities SET status = :status WHERE ding_process_id = :dingProcessId ")
    int updateStatusByDingProcessId(@Param("dingProcessId") String dingProcessId, @Param("status") Integer status);

    /**
     * 更新销售机会id
     *
     * @param soId crm销售机会id
     * @param id   销售机会id
     * @return 更新行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying(clearAutomatically = true)
    @Query("UPDATE SalesOpportunities SET soId = :soId WHERE id = :id ")
    int updateSoIdById(@Param("soId") String soId, @Param("id") Long id);

    /**
     * 通过crm的销售机会id获取销售机会列表
     *
     * @param soIds 销售机会id
     * @return 销售机会列表
     */
    List<SalesOpportunities> findBySoIdIn(List<String> soIds);

    /**
     * 根据钉钉审批id查找销售机会
     *
     * @param dingProcessId
     * @return
     */
    Optional<SalesOpportunities> findByDingProcessId(String dingProcessId);

    /**
     * 根据创建日期范围和业务类型查询销售机会列表
     *
     * @param businessTypeList 业务类型列表
     * @param start            开始时间
     * @param end              结束时间
     * @return
     */
    @Query("select l from SalesOpportunities l where businessType in :businessTypeList and isDel=0 and status=4 and createTime between :start and :end")
    List<SalesOpportunities> findYesterdayOldPackageSoList(List<Integer> businessTypeList, Date start, Date end);

    List<SalesOpportunities> findByBusinessTypeInAndStatusAndIsDel(List<Integer> businessTypeList, Integer status,
                                                                   Integer isDel);

    List<SalesOpportunities> findByContractId(String contractId);

    /**
     * TODO 删掉
     * <p>
     * <p>
     * 更新开通功能列表
     * ext字段的addFunctionList
     *
     * @param id
     * @param addFunctionList
     * @return
     * @see #updateExtFunctionList(long, String)
     */
    @Deprecated
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(nativeQuery = true,
            value = "update sales_opportunities set ext=json_set(ext, '$.addFunctionList', json_array(:addFunctionList), '$.linkMicLimit', :linkMicLimit) where id=:id")
    int updateExtAddFunctionList(@Param("id") long id, @Param("addFunctionList") List<String> addFunctionList,
                                 @Param("linkMicLimit") Integer linkMicLimit);

    /**
     * 更新开通功能列表
     * ext字段的addFunctionList
     *
     * @param id           销售机会id
     * @param functionList 开通功能列表
     * @return 修改的次数
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(nativeQuery = true,
            value = "UPDATE sales_opportunities SET ext=json_set(ext, '$.functionList', CONVERT(:functionList, JSON)) WHERE id=:id")
    int updateExtFunctionList(@Param("id") long id, @Param("functionList") String functionList);

    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(nativeQuery = true,
            value = "UPDATE sales_opportunities SET ext=json_set(ext, '$.snapshotFunctionList', :snapshotFunctionList) WHERE id=:id")
    int updateExtSnapshotFunctionList(@Param("id") long id, @Param("snapshotFunctionList") String snapshotFunctionList);


    /**
     * 根据用户id，销售机会id，到款金额大于amountGained 查询记录数
     */
    @Query("select count(l) from SalesOpportunities l where l.customerId=:customerId and l.status=:status and l.amountGained>:amountGained")
    Integer countByCustomerIdAndStatusAndAmountGainedGreaterThan(String customerId, Integer status, Long amountGained);

    SalesOpportunities findByContractIdAndBusinessType(String contractId, Integer businessType);

    SalesOpportunities findBySoId(String soId);

    /**
     * 更新ext字段的billingItemList
     *
     * @param id                销售机会id
     * @param billingItemDOList 计费项列表
     * @return 修改的次数
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(nativeQuery = true,
            value = "UPDATE sales_opportunities SET ext=json_set(ext, '$.billingItemDOList', CONVERT(:billingItemDOList, JSON)) WHERE id=:id")
    int updateExtBillingItemList(@Param("id") long id, @Param("billingItemDOList") String billingItemDOList);


    /**
     * 临时用 同步crm数据更新销售机会创建日期
     *
     * @param createTime 创建时间
     * @param soId       销售机会id
     * @return 更新条数
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query("UPDATE SalesOpportunities SET createTime = :createTime WHERE soId = :soId")
    int updateCreateTimeBySoId(@Param("createTime") Date createTime, @Param("soId") String soId);

    List<SalesOpportunities> findByCustomerIdOrderByCreateTimeDesc(String customerId);

    boolean existsByCustomerIdAndAmountGainedGreaterThanAndStatusIn(String customerId, Long depositAmount,List<Integer> status);

    List<SalesOpportunities> findByCustomerIdAndBillingPlanCode(String customerId,String billingPlanCode);

    
    boolean existsByCustomerIdAndAmountGainedGreaterThan(String customerId, Long depositAmount);

    /**
     * 获取待关联订单
     * 历史销售机会没有billingPlanCode，相同businessType就可以关联
     * unionID 相同
     * 业务类别、产品名称相同
     * 订单 status = 4
     */
    @Query(value = "select so from SalesOpportunities so where so.customerId = :unionId and so.businessType = :businessType and so.status = 4 " +
            "and (so.billingPlanCode = :billingPlanCode or length(so.soId) in (8,20)) ")
    List<SalesOpportunities> getToBeAssociatedList(@Param(value = "unionId") String unionId,
                                                   @Param(value = "businessType") Integer businessType,
                                                   @Param(value = "billingPlanCode") String billingPlanCode);

    /**
     * 获取待配置或待提交订单
     * 历史销售机会没有billingPlanCode，相同businessType就可以关联
     * soId 订单号
     * 业务类别、产品名称相同
     * 订单 status = 1（待配置）或者2（待提交）
     */
    @Query(value = "select so from SalesOpportunities so where so.soId = :soId and so.businessType = :businessType and ( so.status = 1 or so.status = 2) " +
            "and so.billingPlanCode = :billingPlanCode ")
    List<SalesOpportunities> getToBeAssociatedListBySoId(@Param(value = "soId") String soId,
                                                   @Param(value = "businessType") Integer businessType,
                                                   @Param(value = "billingPlanCode") String billingPlanCode);

    /**
     * 获取待配置或待提交订单
     * soId 订单号
     * 业务类别、产品名称相同
     * 订单 status = 1（待配置）或者2（待提交）
     */
    @Query(value = "select so from SalesOpportunities so where so.soId = :soId and ( so.status = 1 or so.status = 2) ")
    List<SalesOpportunities> getToBeAssociatedListByOnlySoId(@Param(value = "soId") String soId);


    /**
     * 查询合同与销售机会关联状态
     *
     * @param inputVO  分页条件
     * @param pageable 分页
     * @return 分页数据
     * <AUTHOR>
     * @date 2022/8/23
     */
    @Query(nativeQuery = true ,value =
                    "  SELECT so_id AS soId, contract_id  AS contractId FROM  sales_opportunities" +
                    "  WHERE is_del =0 "+
                    " AND if(:#{#inputVO.soId} is not null and :#{#inputVO.soId}  != '',so_id =:#{#inputVO.soId} ,1=1)" +
                    " AND if(:#{#inputVO.contractId} is not null and :#{#inputVO.contractId} != '',contract_id =:#{#inputVO.contractId},1=1)" +
                            " AND if(:#{#inputVO.startTime} is not null and :#{#inputVO.notIncrementalDataSync} ,create_time >=:#{#inputVO.startTime},1=1)" +
                            " AND if(:#{#inputVO.endTime} is not null and  :#{#inputVO.notIncrementalDataSync} ,create_time <=:#{#inputVO.endTime},1=1) " +
                            " AND if(:#{#inputVO.startTime} is not null and :#{#inputVO.incrementalDataSync}  ,update_time >=:#{#inputVO.startTime},1=1)" +
                    " AND if(:#{#inputVO.endTime} is not null and :#{#inputVO.incrementalDataSync} ,update_time <=:#{#inputVO.endTime},1=1) " +
                    "  UNION "+
                    "  SELECT so_id  AS soId, contract_id  AS contractId FROM  finance_contract " +
                    " WHERE status =1 " +
                    " AND if(:#{#inputVO.soId} is not null and :#{#inputVO.soId}  != '',so_id =:#{#inputVO.soId} ,1=1)" +
                    " AND if(:#{#inputVO.contractId} is not null and :#{#inputVO.contractId} != '',contract_id =:#{#inputVO.contractId},1=1)" +
                            " AND if(:#{#inputVO.startTime} is not null and :#{#inputVO.notIncrementalDataSync} ,create_time >=:#{#inputVO.startTime},1=1)" +
                            " AND if(:#{#inputVO.endTime} is not null and  :#{#inputVO.notIncrementalDataSync} ,create_time <=:#{#inputVO.endTime},1=1) " +
                            " AND if(:#{#inputVO.startTime} is not null and :#{#inputVO.incrementalDataSync}  ,update_time >=:#{#inputVO.startTime},1=1)" +
                    " AND if(:#{#inputVO.endTime} is not null and :#{#inputVO.incrementalDataSync} ,update_time <=:#{#inputVO.endTime},1=1) "
            ,countQuery =
            "  select count(0)  from (" +
                    "SELECT so_id AS soId, contract_id  AS contractId FROM  sales_opportunities" +
                    "  WHERE is_del =0 "+
                    " AND if(:#{#inputVO.soId} is not null and :#{#inputVO.soId}  != '',so_id =:#{#inputVO.soId} ,1=1)" +
                    " AND if(:#{#inputVO.contractId} is not null and :#{#inputVO.contractId} != '',contract_id =:#{#inputVO.contractId},1=1)" +
                    " AND if(:#{#inputVO.startTime} is not null and :#{#inputVO.notIncrementalDataSync} ,create_time >=:#{#inputVO.startTime},1=1)" +
                    " AND if(:#{#inputVO.endTime} is not null and  :#{#inputVO.notIncrementalDataSync} ,create_time <=:#{#inputVO.endTime},1=1) " +
                    " AND if(:#{#inputVO.startTime} is not null and :#{#inputVO.incrementalDataSync}  ,update_time >=:#{#inputVO.startTime},1=1)" +
                    " AND if(:#{#inputVO.endTime} is not null and :#{#inputVO.incrementalDataSync} ,update_time <=:#{#inputVO.endTime},1=1) " +
                    " UNION "+
                    " SELECT so_id  AS soId, contract_id  AS contractId FROM  finance_contract " +
                    " WHERE status =1 " +
                    " AND if(:#{#inputVO.soId} is not null and :#{#inputVO.soId}  != '',so_id =:#{#inputVO.soId} ,1=1)" +
                    " AND if(:#{#inputVO.contractId} is not null and :#{#inputVO.contractId} != '',contract_id =:#{#inputVO.contractId},1=1)" +
                    " AND if(:#{#inputVO.startTime} is not null and :#{#inputVO.notIncrementalDataSync} ,create_time >=:#{#inputVO.startTime},1=1)" +
                    " AND if(:#{#inputVO.endTime} is not null and  :#{#inputVO.notIncrementalDataSync} ,create_time <=:#{#inputVO.endTime},1=1) " +
                    " AND if(:#{#inputVO.startTime} is not null and  :#{#inputVO.incrementalDataSync}  ,update_time >=:#{#inputVO.startTime},1=1)" +
                    " AND if(:#{#inputVO.endTime} is not null and :#{#inputVO.incrementalDataSync} ,update_time <=:#{#inputVO.endTime},1=1) " +
                    ") A"
    )
    Page<Map<String,Object>> listStatData(@Param("inputVO") StatQueryRequest inputVO, Pageable pageable);


    /**
     * 根据销售机会自增id更新销售机会状态
     *
     * @param soIds     销售机会自增id
     * @param status 要更新的销售机会状态
     * @return 更新条数
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("UPDATE SalesOpportunities SET status = :status WHERE status = 2 and soId in (:soIds)")
    int updateStatusTo4BySoIds(@Param("soIds")  List<String> soIds, @Param("status") Integer status);



    /**
     * 更新开通功能列表
     * ext字段的addFunctionList
     * @param id 销售机会id
     * @param otherBillItemList 开通功能列表
     * @param accountType 账号类型
     * @return 修改的次数
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(nativeQuery = true, value = "update sales_opportunities set ext=json_set(ext, '$.otherBillItemList', " +
            "CONVERT(:otherBillItemList, JSON), '$.accountType', :accountType) where so_id=:soId")
    int updateExtOtherBillItemList(@Param("soId") String soId, @Param("otherBillItemList") String otherBillItemList,
            @Param("accountType") String accountType);


    /**
     * 获取客户是否为正式客户 批量获取
     * @param customerIds 客户id列表
     * @return 客户id-充值金额
     */
    @Query(nativeQuery = true, value =
            "select  customer_id as customerId,   COALESCE( sum(amount_gained), 0) as num from sales_opportunities " +
                    "where customer_id in (:customerIds)  group by  customer_id ")
    List<Map<String, Object>> getCustomerIsFormalUser(@Param("customerIds") List<String> customerIds);
    
    
    /**
     * 获取客户最后一笔充值记录的 ext数据
     * @param customerIds  客户id
     * @param status    订单状态
     * @param billPlanCode  计费编码
     * @return
     */
    @Query(nativeQuery = true, value =
            "SELECT B.customer_id  as customerId,CAST(B.ext->'$.donate' AS SIGNED)  as donate ,B.amount_gained as " +
                    "recharge,B.update_time as time" +
                    " FROM " + "    ( SELECT customer_id union_id,  max( update_time ) time" +
                    "      FROM sales_opportunities" + "      WHERE customer_id in(:customerIds) " +
                    "      AND `status` = :status AND billing_plan_code = :billPlanCode GROUP BY customer_id ) A " +
                    " LEFT JOIN sales_opportunities B ON A.union_id = B.customer_id AND A.time = B.update_time " +
                    " WHERE  B.customer_id in(:customerIds) AND B.`status`= :status AND B.billing_plan_code = " +
                    ":billPlanCode ")
    List<Map<String, Object>> getLastRechargeOrderExt(@Param("customerIds") List<String> customerIds,
            @Param("status") Integer status, @Param("billPlanCode") String billPlanCode);

    /**
     * 获取客户最后一笔充值记录id
     * @param customerIds  客户id
     * @param status    订单状态
     * @param billPlanCode  计费编码
     * @return
     */
    @Query(nativeQuery = true, value =
            "SELECT B.*" +
            " FROM " + "    ( SELECT customer_id,  max( update_time ) time" +
            "      FROM sales_opportunities" + "      WHERE customer_id in(:customerIds) " +
            "      AND `status` = :status AND billing_plan_code = :billPlanCode GROUP BY customer_id ) A " +
            " LEFT JOIN sales_opportunities B ON A.customer_id = B.customer_id AND A.time = B.update_time " +
            " WHERE  B.customer_id in(:customerIds) AND B.`status`= :status AND B.billing_plan_code = " +
            ":billPlanCode ")
    List<SalesOpportunities> getLastRechargeOrder(@Param("customerIds") List<String> customerIds, @Param("status") Integer status, @Param("billPlanCode") String billPlanCode);
    
    /**
     * 根据合同Id修改订到到期时间
     * @param expireDate
     * @param contractId
     * @return
     */
    @Modifying
    @Query("UPDATE SalesOpportunities SET amountExpireDate = :expireDate,updateTime = now() WHERE contractId = " +
            ":contractId")
    int updateExpireDateByContractId(@Param("contractId") String contractId, @Param("expireDate") Date expireDate);
}
