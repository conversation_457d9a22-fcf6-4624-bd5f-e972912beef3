package net.polyv.schedule;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.config.PcsConfig;
import net.polyv.service.GrayTestService;
import net.polyv.service.customer.CustomerSpaceCalculationService;


/**
 * created by liujiantao 09/23/2021
 * 点播流量超量使用处理定时任务
 */
@Component
@Slf4j
public class CalculationSpaceGrayUserScheduleRunner extends AbstractScheduleRunner {
    @Resource
    private CustomerSpaceCalculationService customerSpaceCalculationService;
    @Resource
    private GrayTestService grayTestService;
    
    
    //5点获取用户数据，处理点播空间为0的进入缓存
    // 要调整这个时间的话, 留意看下缓存过期是不是要一起调整, 这里大概5点写入, 调度器大概8点多的时候会依赖这个缓存的数据做空间用量推送
    @Scheduled(cron = "0 0 5 * * ?")
    public void execute() {
        log.info("start to execute CalculationSpaceGrayUserScheduleRunner....");
        customerSpaceCalculationService.calculationSpaceIsZeroUser(null);
        log.info("end execute CalculationSpaceGrayUserScheduleRunner....");
    }
}
