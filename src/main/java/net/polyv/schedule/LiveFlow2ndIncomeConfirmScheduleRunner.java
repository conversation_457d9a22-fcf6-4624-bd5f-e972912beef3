package net.polyv.schedule;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.schedule.clearing.AbstractClearingScheduleRunner;
import net.polyv.service.finance.FinanceIncomeService;
import net.polyv.util.DingWarnRobot;

/**
 * @description: 直播流量每个月2号确认上一个月收入（由于出账是第二天下午四点，财务是早上六点确认收入，需要单独处理直播流量的）
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023-05-20
 */
@Slf4j
@Component
public class LiveFlow2ndIncomeConfirmScheduleRunner extends AbstractClearingScheduleRunner {
    
    @Autowired
    private FinanceIncomeService incomeService;
    
    @Autowired
    private DingWarnRobot dingWarnRobot;
    
    /**
     * 每个月2号早上7:10执行
     */
    @Scheduled(cron = "0 10 7 2 * ?")
    public void execute() {
        try {
            log.info("start to execute LiveFlow2ndIncomeConfirmScheduleRunner ....");
            incomeService.liveFlowStatistics(null);
        } catch (Exception e) {
            log.error(String.format("LiveFlow2ndIncomeConfirmScheduleRunner exception...%s", e.getMessage()), e);
            dingWarnRobot.sendWarnMsg("2号确认直播流量上个月收入异常告警", e.getMessage());
        } finally {
            log.info("end of execute LiveFlow2ndIncomeConfirmScheduleRunner....");
        }
    }
}
