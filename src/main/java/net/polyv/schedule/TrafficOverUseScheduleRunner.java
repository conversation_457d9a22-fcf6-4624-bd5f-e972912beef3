package net.polyv.schedule;

import lombok.extern.slf4j.Slf4j;
import net.polyv.annotation.ScheduleSwitchControl;
import net.polyv.service.resource.CustomerOverUseRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/** created by <PERSON><PERSON><PERSON><PERSON><PERSON> 09/23/2021
 * 点播流量超量使用处理定时任务
 */
@Component
@Slf4j
public class TrafficOverUseScheduleRunner {
    @Autowired
    private CustomerOverUseRecordService customerOverUseRecordService;


    //半小时处理一次所有超用量的账单
    //目前点播流量超用的情况基本没有，暂时屏蔽掉超量处理
    //@Scheduled(cron = "0 0/30 * * * ?")
    public void execute(){
        log.info("start to execute trafficOverUseScheduleRunner....");
        customerOverUseRecordService.handleTrafficOverUse();
        log.info("end execute trafficOverUseScheduleRunner....");
    }
}
