package net.polyv.schedule;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.customer.CustomerLastConsumedService;
import net.polyv.util.DingWarnRobot;


/**
 * created by l<PERSON>jiantao 09/23/2021
 * 点播流量超量使用处理定时任务
 */
@Component
@Slf4j
public class CalculationLastConsumedScheduleRunner extends AbstractScheduleRunner {
    
    @Resource
    private CustomerLastConsumedService customerLastConsumedService;
    
    @Resource
    private ThreadPoolExecutor threadPoolExecutor;
    @Resource
    private DingWarnRobot dingWarnRobot;
    
    //提前计算近七天消耗量
    @Scheduled(cron = "0 0 6 * * ?")
    public void execute() {
        log.info("start to execute CalculationLastConsumedScheduleRunner....");
        customerLastConsumedService.preCalculateUserConsumeByLast7Day();
        log.info("end execute CalculationLastConsumedScheduleRunner....");
    }
}
