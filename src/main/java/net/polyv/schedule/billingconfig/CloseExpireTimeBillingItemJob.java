package net.polyv.schedule.billingconfig;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.schedule.clearing.AbstractClearingScheduleRunner;
import net.polyv.service.impl.billingConfig.BillingItemCloseService;
import net.polyv.util.DateUtil;

/**
 * 执行关闭到达到期时间的计费项
 * <AUTHOR>
 * @date 2022/7/22 11:03
 */
@Slf4j
@Component
public class CloseExpireTimeBillingItemJob extends AbstractClearingScheduleRunner {
    
    @Resource
    private BillingItemCloseService billingItemCloseService;
    
    @Override
    @Scheduled(cron = "0 0 0 * * ? ")
    public void execute() {
        //获取失效时间小于今日的 表示已经过期
        Date currentDay = DateUtil.getCurrentDay();
        billingItemCloseService.closeFunction(currentDay);
    }
    
}
