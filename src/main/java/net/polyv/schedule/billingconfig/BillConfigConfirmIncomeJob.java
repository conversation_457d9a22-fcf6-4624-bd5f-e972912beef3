package net.polyv.schedule.billingconfig;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.schedule.clearing.AbstractClearingScheduleRunner;
import net.polyv.service.impl.billingConfig.BillingConfigConfirmIncomeService;

/**
 * 计费项配置化确认收入
 * <AUTHOR>
 * @date 2022/11/7 10:57
 */
@Component
@Slf4j
public class BillConfigConfirmIncomeJob extends AbstractClearingScheduleRunner {
    
    @Resource
    private BillingConfigConfirmIncomeService billingConfigConfirmIncomeService;
    
    @Override
    @Scheduled(cron = "0 0 7 * * ? ")
    public void execute() {
        this.billingConfigConfirmIncomeService.confirmIncome("", new Date());
    }
}
