package net.polyv.schedule;

import lombok.extern.slf4j.Slf4j;
import net.polyv.annotation.ConditionalFullFunctionMode;
import net.polyv.annotation.ScheduleSwitchControl;
import net.polyv.model.data.customer.CrmUserSearchDO;
import net.polyv.rest.client.crm.ConcatCrmClient;
import net.polyv.rest.model.crm.CrmContactVO;
import net.polyv.rest.model.crm.input.CrmContactSearchInputVO;
import net.polyv.rest.model.vod.QiKeBaoContactVO;
import net.polyv.rest.util.CrmExtendInfoExtractUtil;
import net.polyv.service.account.CustomerService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户信息同步任务（已废弃）
 * <AUTHOR>
 * @since 2021年4月15日
 */
@Slf4j
public class CrmCustomerSyncSchedule {

    private final CustomerService customerService;

    @Autowired
    public CrmCustomerSyncSchedule(CustomerService customerService) {
        log.info(String.format("start job %s ....", getClass().getName()));
        this.customerService = customerService;
    }

    // 凌晨 5 点全量同步一次客户信息，同步公海客户
    @Scheduled(cron = "0 0 5 * * ?")
    @ScheduleSwitchControl
    public void syncAll() {

        CrmUserSearchDO searchDO = new CrmUserSearchDO();
        searchDO.setVisible(2);
        customerService.addSyncTaskToQueue(searchDO);
    }

    // 每 10 分钟同步前一小时的客户信息
    @Scheduled(cron = "0 0/10 * * * ?")
    @ScheduleSwitchControl
    public void syncNearly() {

        // 同步今日创建的联系人
        syncCreateContactToday();

        // 同步客户信息
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusHours(1);

        CrmUserSearchDO searchDO = new CrmUserSearchDO();
        searchDO.setVisible(-1);
        searchDO.setStartUpdateTime(start);
        searchDO.setEndUpdateTime(end);
        customerService.addSyncTaskToQueue(searchDO);
    }

    private void syncCreateContactToday() {
        Date current = new Date();

        // 当日创建的联系人同步到点播库中
        CrmContactSearchInputVO contactSearchInputVO = CrmContactSearchInputVO.builder()
                .startDate(current)
                .endDate(current)
                .build();
        List<CrmContactVO> contactVOList = new ArrayList<>(ConcatCrmClient.listConcat(contactSearchInputVO));
        List<QiKeBaoContactVO> qiKeBaoContactVOList= contactVOList.stream().map(this::convert2QiKeBaoContactVO).collect(
                Collectors.toList());
        customerService.updateContactFromVodDb(qiKeBaoContactVOList);
    }


    private QiKeBaoContactVO convert2QiKeBaoContactVO(CrmContactVO input){
        QiKeBaoContactVO output = new QiKeBaoContactVO();
        BeanUtils.copyProperties(input, output);
        output.setEmail(CrmExtendInfoExtractUtil.getValueByExtendInfoField(input.getExtendedInfos(), "邮箱"));
        output.setMobile(CrmExtendInfoExtractUtil.getValueByExtendInfoField(input.getExtendedInfos(), "手机"));
        return output;
    }
}
