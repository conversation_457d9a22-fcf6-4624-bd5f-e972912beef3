package net.polyv.schedule;

import cn.hutool.core.date.DateUtil;
import net.polyv.constant.schdule.ScheduleJobTypeConst;
import net.polyv.constant.schdule.ScheduleStatusConst;
import net.polyv.dao.primary.CustomerEffectiveScheduleJobRepository;
import net.polyv.model.entity.primary.CustomerEffectiveScheduleJob;
import net.polyv.service.schedule.ScheduleJobExecutor;
import net.polyv.service.schedule.ScheduleJobExecutorSimpleFactory;
import lombok.extern.slf4j.Slf4j;
import net.polyv.util.DingWarnRobot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 用户定时生效执行入口
 * <AUTHOR>
 * @since 09/05/2020
 */
@Slf4j
@Component
public class CustomerEffectiveScheduleRunner extends AbstractScheduleRunner {

    /**
     * 每次处理的记录条数
     */
    private static final int DEFAULT_RECORD_COUNT_PROCESS_PER_TIME = 200;

    private final CustomerEffectiveScheduleJobRepository customerEffectiveScheduleJobRepository;
    private final ScheduleJobExecutorSimpleFactory scheduleJobExecutorSimpleFactory;

    @Resource
    private DingWarnRobot dingWarnRobot;

    @Autowired
    public CustomerEffectiveScheduleRunner(CustomerEffectiveScheduleJobRepository customerEffectiveScheduleJobRepository,
                                           ScheduleJobExecutorSimpleFactory scheduleJobExecutorSimpleFactory) {
        this.customerEffectiveScheduleJobRepository = customerEffectiveScheduleJobRepository;
        this.scheduleJobExecutorSimpleFactory = scheduleJobExecutorSimpleFactory;
    }

    @Scheduled(cron = "0 1 0 * * ? ")
    public void execute() {

        List<CustomerEffectiveScheduleJob> jobList;

        int count = 0;
        for (; ; count++) {

            // 程序保护
            if (count >= MAX_LOOP_TIMES) {
                log.warn("{} 定时器循环次数触发最大循环次数限制:{}，将终止本次执行", this.getClass().getName(), MAX_LOOP_TIMES);
                break ;
            }

            // 获取待执行任务
            jobList = customerEffectiveScheduleJobRepository.findByStatusAndEffectiveTimeOrderByPriorityAsc(ScheduleStatusConst.WAIT,
                    DateUtil.beginOfDay(DateUtil.date()), PageRequest.of(count, DEFAULT_RECORD_COUNT_PROCESS_PER_TIME));
            if (CollectionUtils.isEmpty(jobList)) {
                break ;
            }

            // 执行任务
            jobList.forEach(job -> {
                ScheduleJobExecutor executor =
                        scheduleJobExecutorSimpleFactory.get(ScheduleJobTypeConst.getByType(job.getType()));

                log.info("执行定时生效job，类型：{}，任务id：{}", ScheduleJobTypeConst.getByType(job.getType()), job.getId());

                int status = ScheduleStatusConst.EXECUTED;
                try {
                    boolean success = executor.process(job);
                    if (!success) {
                        log.error("执行任务id: {}, 任务执行失败", job.getId());
                        dingWarnRobot.sendWarnMsg("【业务定时开通失败】","执行任务id = " +job.getId());
                        status = ScheduleStatusConst.ERROR;
                    }
                } catch (Exception e) {
                    log.error("任务执行失败, 任务id:"+job.getId(), e);
                    dingWarnRobot.sendWarnMsg("【业务定时开通失败】","执行任务id = " +job.getId());
                    status = ScheduleStatusConst.ERROR;
                }

                // 实时保存，确保不会因为后面的报错导致回滚
                job.setStatus(status);
                job.setUpdateTime(new Date());
                customerEffectiveScheduleJobRepository.save(job);
            });
        }

        log.info("用户定时任务生效执行完毕，本次执行记录数: {}", count);

    }

}
