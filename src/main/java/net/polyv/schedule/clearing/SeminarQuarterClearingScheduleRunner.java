package net.polyv.schedule.clearing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.impl.clearing.SeminarQuarterClearingService;
import net.polyv.util.LockUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description: 研讨会季度包出账定时任务
 * @author: Neo
 * @date: 2022-05-16
 */
@Slf4j
@Component
public class SeminarQuarterClearingScheduleRunner extends AbstractClearingScheduleRunner {

    /**
     * 研讨会季度包结算服务
     */
    @Autowired
    private SeminarQuarterClearingService seminarQuarterClearingService;


    @Autowired
    private LockUtil lockUtil;

    /**
     * 每天1点执行出账单逻辑
     */
    @Scheduled(cron = "0 0 1 * * ? ")
    public void execute() {
        try {
            log.info("start to execute seminar quarter package clearing....");
            seminarQuarterClearingService.clearSeminarQuarterPackage(null);
        } catch (Exception e) {
            log.error(String.format("seminar quarter package clearing exception...%s", e.getMessage()), e);
        } finally {
            log.info("end of execute seminar quarter package clearing....");
        }
    }
}
