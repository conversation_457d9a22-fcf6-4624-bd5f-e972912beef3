package net.polyv.schedule.clearing;

import net.polyv.service.clearing.ItemClearingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 定时触发结算
 * <AUTHOR>
 * @since 2020/6/21
 */
@Slf4j
@Component
public class ItemClearingScheduleRunner extends AbstractClearingScheduleRunner {
    
    private final ItemClearingService itemClearingService;
    private final ConsumeDailySyncAndItemClearingSyncThreadPool consumeDailySyncAndItemClearingSyncThreadPool;

    @Autowired
    public ItemClearingScheduleRunner(ItemClearingService itemClearingService,
                                      ConsumeDailySyncAndItemClearingSyncThreadPool consumeDailySyncAndItemClearingSyncThreadPool) {
        this.itemClearingService = itemClearingService;
        this.consumeDailySyncAndItemClearingSyncThreadPool = consumeDailySyncAndItemClearingSyncThreadPool;
    }

    //@Scheduled(cron = "0 10,40 * * * ? ")
    public void execute() {

       // consumeDailySyncAndItemClearingSyncThreadPool.execute(itemClearingService::clearing);
    }

}
