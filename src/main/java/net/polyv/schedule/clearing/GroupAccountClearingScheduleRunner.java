package net.polyv.schedule.clearing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.business.GroupLiveChargeType;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.GroupAccountConfigRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.GroupAccountConfig;
import net.polyv.service.AccountDepositService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;
import net.polyv.web.model.resource.CleanResourceVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 集团账号主账号清数据
 */
@Slf4j
@Component
public class GroupAccountClearingScheduleRunner extends AbstractClearingScheduleRunner{

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CustomerResourceService customerResourceService;
    @Autowired
    private GroupAccountConfigRepository groupAccountConfigRepository;


    @Scheduled(cron = "0 0 2 * * ? ")
    public void execute() {
        logger.info("过期的集团账号可用资源清理，starttime {}", System.currentTimeMillis());
        Date now = DateUtil.getDateStart(new Date());
        Date pre7Days = DateUtil.getXDay(now, -7);
        List<GroupAccountConfig> list = groupAccountConfigRepository.findAll();
        List<String> groupIdList = new ArrayList<>();
        for (GroupAccountConfig obj : list) {
            //判断是否为直播分钟数计费，并且过期时间在7天内的
            Long endDateLong = obj.getLiveEnd();
            if(GroupLiveChargeType.MINUTES.getValue().equals(obj.getGroupLiveBillingPlan())){
                if(null != endDateLong && now.getTime() > endDateLong && endDateLong > pre7Days.getTime()){
                    groupIdList.add(obj.getGroupId());
                }
            }
        }
        logger.info("过期的集团账号可用分钟数及导播台资源清理, customerIds={}, resourceCode={} and {}", groupIdList.toString(), ResourceCodeConst.guide_duration.toString(),ResourceCodeConst.duration.toString());
        groupIdList.forEach(customerId -> {
            CleanResourceVO guideCleanResourceVO = CleanResourceVO.builder().resourceCode(ResourceCodeConst.guide_duration.toString())
                    .customerId(customerId).build();
            customerResourceService.cleanItemResource(guideCleanResourceVO);
            CleanResourceVO durationCleanResourceVO = CleanResourceVO.builder().resourceCode(ResourceCodeConst.duration.toString())
                    .customerId(customerId).build();
            customerResourceService.cleanItemResource(durationCleanResourceVO);
        });
    }
}
