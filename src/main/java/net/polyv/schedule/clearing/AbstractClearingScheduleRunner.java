package net.polyv.schedule.clearing;

import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import net.polyv.schedule.ScheduleRunner;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.LockKeyConst;
import net.polyv.util.LockUtil;

/**
 * <AUTHOR>
 * @since 2020/6/21
 */
@Slf4j
public abstract class AbstractClearingScheduleRunner implements ScheduleRunner{
    
    @Autowired
    private LockUtil lockUtil;
    
    protected boolean lock(String resourceCode){
        String lockKey = String.format(LockKeyConst.CLEARING_PROCESS, resourceCode);
        //加锁10分钟
        return lockUtil.lock(lockKey, 10, TimeUnit.MINUTES);
    }
    
    protected void unlock(String resourceCode){
        String lockKey = String.format(LockKeyConst.CLEARING_PROCESS, resourceCode);
        lockUtil.releaseLock(lockKey);
    }
}
