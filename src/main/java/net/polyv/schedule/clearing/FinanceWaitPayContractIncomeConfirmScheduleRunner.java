package net.polyv.schedule.clearing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.finance.FinanceIncomeService;
import net.polyv.util.DingWarnRobot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description: 财务待支付合同收入确认定时器
 * @author: Neo
 * @date: 2022-10-18
 */
@Slf4j
@Component
public class FinanceWaitPayContractIncomeConfirmScheduleRunner extends AbstractClearingScheduleRunner {

    @Autowired
    private FinanceIncomeService incomeService;

    @Autowired
    private DingWarnRobot dingWarnRobot;

    /**
     * 每月第一天(6点半)执行上个月特殊的合同数据
     */
    @Scheduled(cron = "0 30 6 1,2 * ?")
    public void execute() {
        try {
            log.info("start to execute income statisticsSpecial....");
            incomeService.statisticsSpecialStage(null);
        } catch (Exception e) {
            log.error(String.format("income statisticsSpecial exception...%s", e.getMessage()), e);
            dingWarnRobot.sendWarnMsg("财务特殊合同确认收入异常告警", e.getMessage());
        } finally {
            log.info("end of execute income statisticsSpecial....");
        }
    }
}
