package net.polyv.schedule.clearing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.finance.FinanceIncomeService;
import net.polyv.util.DingWarnRobot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description: 财务业务合同收入确认定时器
 * @author: Neo
 * @date: 2022-10-18
 */
@Slf4j
@Component
public class FinanceContractIncomeConfirmScheduleRunner extends AbstractClearingScheduleRunner {

    @Autowired
    private FinanceIncomeService incomeService;

    @Autowired
    private DingWarnRobot dingWarnRobot;

    /**
     * 每天6点执行出账单逻辑
     */
    @Scheduled(cron = "0 40 6 * * ?")
    public void execute() {
        try {
            log.info("start to execute income statistics....");
            incomeService.statistics(null);
        } catch (Exception e) {
            log.error(String.format("income statistics exception...%s", e.getMessage()), e);
            dingWarnRobot.sendWarnMsg("财务合同每日确认收入异常告警", e.getMessage());
        } finally {
            log.info("end of execute income statistics....");
        }
    }
}
