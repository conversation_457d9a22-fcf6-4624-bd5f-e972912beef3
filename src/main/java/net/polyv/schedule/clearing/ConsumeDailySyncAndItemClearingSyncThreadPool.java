package net.polyv.schedule.clearing;

import org.springframework.stereotype.Service;

import java.util.concurrent.*;

/**
 * 同步日用量和结算进度同步线程池
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/19
 */
@Service
public class ConsumeDailySyncAndItemClearingSyncThreadPool implements Executor {

    private static final ExecutorService SYNC_EXECUTOR;
    static {
        SYNC_EXECUTOR = new ThreadPoolExecutor(0, 1,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>());
    }

    public static ExecutorService getSyncExecutor() {
        return SYNC_EXECUTOR;
    }

    @Override
    public void execute(Runnable runnable) {

        SYNC_EXECUTOR.execute(runnable);
    }
}
