package net.polyv.schedule.clearing;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.resource.ResourceAlterationRecordRepository;
import net.polyv.model.entity.primary.resource.ResourceAlterationRecord;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateUtil;
import net.polyv.web.model.resource.RecalculateResourceAvailDailyVO;

/**
 * TODO 移除定时复算的操作，充值变动不再生成快照
 * http://wiki.igeeker.org/pages/viewpage.action?pageId=251035972
 * 可用资源的复算，这里可以覆盖到昨日有充值，但是无消耗的客户
 * 昨日的资源可用量 = 前天的资源可用量+昨日变动
 * <AUTHOR>
 * @since 2020/8/10
 */
//@Slf4j
//@Component
public class ItemAvailableDailyRecalcuScheduleRunner extends AbstractClearingScheduleRunner{
    
    @Autowired
    private ResourceAlterationRecordRepository resourceAlterationRecordRepository;
    @Autowired
    private CustomerResourceService customerResourceService;
    
//    @Scheduled(cron = "30 0 0 * * ? ")
    public void execute() {
        Date yesterday = DateUtil.getYesterday();
    
        List<ResourceAlterationRecord> list = resourceAlterationRecordRepository.findAlterationRecordByStatAt(yesterday);
        for (ResourceAlterationRecord resourceAlterationRecord : list) {
            // 复算资源剩余量
            Date statAt = yesterday;
            if(ResourceCodeConst.mic_duration.name().equals(resourceAlterationRecord.getResourceCode())){
                // 连麦类型的需要再往前减去1天
                statAt = DateUtil.getDateAfterDays(-1, yesterday);
            }
            RecalculateResourceAvailDailyVO inputVO = RecalculateResourceAvailDailyVO.builder()
                    .customerId(resourceAlterationRecord.getCustomerId())
                    .resourceCode(resourceAlterationRecord.getResourceCode())
                    .statAt(statAt)
                    .build();
            customerResourceService.recalculateAvailDaily(inputVO);
        }
    }
}
