package net.polyv.schedule.clearing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.service.clearing.ItemClearingService;
import net.polyv.service.consume.ItemConsumeDailySyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 定时将dmp的每日用量同步到结算系统的item_consume_daily表中，并做结算
 * <AUTHOR>
 * @since 2022/01/11
 */
@Slf4j
@Component
public class ItemConsumeDailySyncAndClearingScheduleRunner extends AbstractClearingScheduleRunner {

    /**
     * 旧的重制课件出账方式是否启用
     */
    @Value("ppt-composite-duration.old.consume.enabled:N")
    private String pptCompositeDurationOldConsumeEnabled ;

    private final ItemClearingService itemClearingService;
    private final ItemConsumeDailySyncService itemConsumeDailySyncService;

    @Autowired
    public ItemConsumeDailySyncAndClearingScheduleRunner(ItemConsumeDailySyncService itemConsumeDailySyncService,
                                                         ItemClearingService itemClearingService) {
        this.itemConsumeDailySyncService = itemConsumeDailySyncService;
        this.itemClearingService = itemClearingService;
    }


    /**
     * 每30分钟根据条件执行计费项的用量同步和结算
     */
    @Scheduled(cron = "0 0 */2 * * ? ")
    public void execute() {
        //用量同步
        log.info("start to execute item consume daily sync....");
        itemConsumeDailySyncService.sync();
        log.info("end to execute item consume daily sync....");
        //结算
        log.info("start to execute item clearing....");
        List<ResourceCodeConst> resourceCodeConsts = new ArrayList<>() ;

        if(SwitchEnum.isY(pptCompositeDurationOldConsumeEnabled)){
            resourceCodeConsts.add(ResourceCodeConst.ppt_composite_duration) ;
        }

        resourceCodeConsts.add(ResourceCodeConst.small_class_record_duration) ;
        resourceCodeConsts.add(ResourceCodeConst.seminar_record_duration) ;

        itemClearingService.clearingWithResourceCodes(resourceCodeConsts, SwitchEnum.N.getCode());
        log.info("end to execute item clearing....");
    }
    
}
