package net.polyv.schedule.clearing;

import java.util.*;

import javax.annotation.Resource;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.dao.primary.BillingCompleteStatusRecordRepository;
import net.polyv.model.entity.primary.BillingCompleteStatusRecord;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.constant.monitor.ItemClearingMonitor;
import net.polyv.constant.monitor.ItemConsumeMonitor;
import net.polyv.dao.primary.BillingItemRepository;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.service.SystemEnvService;
import net.polyv.service.bill.BillingDataService;
import net.polyv.service.bill.BillingService;
import net.polyv.service.item.ItemConsumeDailyService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;

/**
 * 结算状态检查，发钉钉告警
 * 钉钉告警需要在8点做
 * 1.检查昨日全部资源的消耗记录，无记录需要告警
 * 2.检查昨日各个计费项的账单记录，无记录需要告警
 * <AUTHOR>
 * @since 2020/6/23
 */
@Slf4j
@Component
public class ClearingDingWarmScheduleRunner extends AbstractClearingScheduleRunner{
    
    @Autowired
    private ItemConsumeDailyService itemConsumeDailyService;
    
    @Autowired
    private BillingItemRepository billingItemRepository;
    
    @Autowired
    private BillingDataService billingDataService;
    
    @Autowired
    private DingWarnRobot dingWarnRobot;
    
    @Autowired
    private BillingService billingService;
    
    @Autowired
    private BillingCompleteStatusRecordRepository billingCompleteStatusRecordRepository;
    
    @Resource
    private SystemEnvService systemEnvService;
    
    @Scheduled(cron = "0 0 11,17 * * ? ")
    public void execute() {
        //非线上或预发布环境，不进行告警，测试环境大数据不会同步消耗到pcs，自然找不到消耗记录和账单记录，不需要告警
        if (!systemEnvService.isProdEnv()) {
            return;
        }
        log.info("【clearing monitor】start to check consume and billing data.");
        
        Date yesterday = DateUtil.getYesterday();
        Date before2Days = DateUtil.getDateAfterDays(-2);
        Date currentDay = DateUtil.getCurrentDay();
        Date checkDate;
        boolean billComplete = true; //每天账单是否完整，财务取账单得时候，如果不完整，不做统计并给出告警
        Set<Integer> itemIds = Sets.newHashSet(); //计费项id列表
        List<String> warningMessags = new ArrayList<>();
        for (ItemConsumeMonitor value : ItemConsumeMonitor.values()) {
            if (value.getItemCodeConst().equals(ItemCodeConst.mic_pd) ||
                    value.getItemCodeConst().equals(ItemCodeConst.seminar_duration) ||
                    value.getItemCodeConst().equals(ItemCodeConst.small_class_duration)) {
                checkDate = before2Days;
            } else {
                checkDate = yesterday;
        
            }
            List<BillingItem> items = billingItemRepository.findByCode(value.getItemCodeConst().getCode());
            for (BillingItem item : items) {
                if (!itemConsumeDailyService.isDataExistWithGroupAccount(checkDate, item.getId(),
                        SwitchEnum.N.getCode())) {
                    //钉钉告警
                    warningMessags.add(String.format(
                            "【%s】计费项: itemCode: %s, scaleCode: %s 普通用户没有用量记录, 需要人工确认是否无客户使用该资源.",
                            DateFormatUtil.formatDateNormal(checkDate), item.getCode(), item.getScaleCode()));
                }
                if (!itemConsumeDailyService.isDataExistWithGroupAccount(checkDate, item.getId(),
                        SwitchEnum.Y.getCode())) {
                    //钉钉告警
                    warningMessags.add(String.format(
                            "【%s】计费项: itemCode: %s, scaleCode: %s 主账号没有用量记录, 需要人工确认是否无客户使用该资源.",
                            DateFormatUtil.formatDateNormal(checkDate), item.getCode(), item.getScaleCode()));
                }
            }
        }
    
        if (!CollectionUtils.isEmpty(warningMessags)) {
            dingWarnRobot.sendMsgToAtPeople("用量数据检查告警", warningMessags, null);
        }
    
        Date checkBillStartDate, checkBillEndDate;
        warningMessags = new ArrayList<>();

        for (ItemClearingMonitor value : ItemClearingMonitor.values()) {
            if (value.getItemCodeConst().equals(ItemCodeConst.mic_pd) ||
                    value.getItemCodeConst().equals(ItemCodeConst.seminar_duration) ||
                    value.getItemCodeConst().equals(ItemCodeConst.small_class_duration)) {
                checkBillStartDate = checkBillEndDate = before2Days;
            } else {
                checkBillStartDate = checkBillEndDate = yesterday;
            }
            //年峰值并发，不是当月1号不需要检查
            if (value.getItemCodeConst().equals(ItemCodeConst.concur_peak) &&
                    DateUtils.toCalendar(new Date()).get(Calendar.DATE) != 1) {
                continue;
            }
            
            // 并发包月和年峰值并发的使用日期比较特殊，需要单独设置
            if(value.getItemCodeConst().equals(ItemCodeConst.concur_monthly) || value.getItemCodeConst().equals(ItemCodeConst.prtc_concur_monthly)){
//                checkBillStartDate = billingService.getMonthlyConcurClearingStartDate(checkBillEndDate, checkBillEndDate);
                //TODO 包月的数据先不做检测，需要额外做一些判断处理
                continue;
            }else if(value.getItemCodeConst().equals(ItemCodeConst.concur_peak)){
                checkBillStartDate = DateUtil.getBeginOfMonth(checkBillEndDate);
            }
            
            List<BillingItem> items = billingItemRepository.findByCode(value.getItemCodeConst().getCode());
            for (BillingItem item : items) {
//                if (!billingDataService.existsBillingData(currentDay, checkBillStartDate, checkBillEndDate, item.getCode(), item.getScaleCode())) {
//                    itemIds.add(item.getId());
//                    // 钉钉告警
//                    warningMessags.add(String.format("【%s~%s】计费项: itemCode: %s, scaleCode: %s 没有账单数据, 需要人工确认是否无用量或者未到出账条件.",
//                            DateFormatUtil.formatDateNormal(checkBillStartDate),
//                            DateFormatUtil.formatDateNormal(checkBillEndDate), item.getCode(), item.getScaleCode()));
//                }

                // 普通账号/分帐号
                if (!billingDataService.existsBillingData(currentDay,
                        checkBillStartDate,
                        checkBillEndDate,
                        item.getCode(),
                        item.getScaleCode() ,
                        Arrays.asList(AccountTypeEnum.NORMAL.getCode() , AccountTypeEnum.GROUP1_SUB.getCode() , AccountTypeEnum.GROUP2_SUB.getCode()))) {
                    itemIds.add(item.getId());
                    // 钉钉告警
                    warningMessags.add(String.format("【%s~%s】计费项: itemCode: %s, scaleCode: %s 普通账号/分帐号没有账单数据, 需要人工确认是否无用量或者未到出账条件.",
                            DateFormatUtil.formatDateNormal(checkBillStartDate),
                            DateFormatUtil.formatDateNormal(checkBillEndDate), item.getCode(), item.getScaleCode()));
                }

                // 主账号
                if (!billingDataService.existsBillingData(currentDay,
                        checkBillStartDate,
                        checkBillEndDate,
                        item.getCode(),
                        item.getScaleCode(),
                        Arrays.asList(AccountTypeEnum.GROUP1.getCode() ,AccountTypeEnum.GROUP2.getCode()))) {
                    itemIds.add(item.getId());
                    // 钉钉告警
                    warningMessags.add(String.format("【%s~%s】计费项: itemCode: %s, scaleCode: %s 集团主账号没有账单数据, 需要人工确认是否无用量或者未到出账条件.",
                            DateFormatUtil.formatDateNormal(checkBillStartDate),
                            DateFormatUtil.formatDateNormal(checkBillEndDate), item.getCode(), item.getScaleCode()));
                }
            }
        }
    
        if (!CollectionUtils.isEmpty(warningMessags)) {
            billComplete = false;
            dingWarnRobot.sendMsgToAtPeople("账单数据检查告警", warningMessags, null);
        }
        BillingCompleteStatusRecord billingCompleteStatusRecord = BillingCompleteStatusRecord.builder()
                .billDate(DateUtil.getYesterday()).itemIds(StringUtils.join(itemIds.toArray(),","))
                .status(billComplete ? 1 : 0)
                .build();
        billingCompleteStatusRecordRepository.save(billingCompleteStatusRecord);
        log.info("【clearing monitor】end to check consume and billing data.");
    }
    
}
