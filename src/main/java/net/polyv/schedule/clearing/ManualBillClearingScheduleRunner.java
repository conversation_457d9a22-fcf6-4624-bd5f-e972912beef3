package net.polyv.schedule.clearing;

import java.util.Date;
import java.util.List;

import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.model.entity.primary.BillingItem;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.modules.pcs.api.vo.SettlementConfigVO;
import net.polyv.service.item.UnivalenceItemService;
import net.polyv.service.settlement.SettlementConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.bill.ManualBillingStatusConst;
import net.polyv.dao.primary.CustomerManualBillingRecordRepository;
import net.polyv.model.entity.primary.CustomerManualBillingRecord;
import net.polyv.service.bill.BillingService;
import net.polyv.util.DateUtil;
import net.polyv.web.model.bill.BillClearingInputVO;

import javax.annotation.Resource;

/**
 * 每日0点10分执行手工入账的结算
 * <AUTHOR>
 * @since 2020/7/20
 */
@Slf4j
@Component
public class ManualBillClearingScheduleRunner extends AbstractClearingScheduleRunner {
    
    @Autowired
    private CustomerManualBillingRecordRepository recordRepository;
    @Autowired
    private BillingService billingService;

    @Resource
    private SettlementConfigService settlementConfigService ;


    @Resource
    private UnivalenceItemService univalenceItemService ;
    
    @Scheduled(cron = "0 10 0 * * ?")
    public void execute() {
        Date currentDay = DateUtil.getCurrentDay();
        Integer status = ManualBillingStatusConst.pass.getStatus();
        List<CustomerManualBillingRecord> list = recordRepository.findByBillDateAndStatus(currentDay, status);
        list.forEach(record -> {
            BillingItem billingItem = billingService.getById(record.getItemId());
            if (billingItem == null) {
                log.error("根据计费项id查不到全局计费项设置, id={}", record.getItemId());
                return;
            }
            BillClearingInputVO vo = new BillClearingInputVO(billingItem.getCode(),record.getConsumeStartDate(), record.getConsumeEndDate(),
                    record);
            vo.setCustomerIdArrStr(record.getCustomerId());
            String isGroupAccount = AccountTypeEnum.GROUP2.getCode().equals(record.getAccountType())
                    ? SwitchEnum.Y.getCode() : SwitchEnum.N.getCode();
            vo.setIsGroupAccount(isGroupAccount);

            //增加是否资源点的配置输出
            SettlementConfigVO settlementConfigVO = settlementConfigService.querySettlementConfig(record.getCustomerId()) ;
            vo.setResourcePointEnabled(settlementConfigVO != null && SwitchEnum.isY(settlementConfigVO.getResourcePointSettlement()));
            vo.setResourcePointBusiness(univalenceItemService.isResourcePointSettlementResourceCode(billingItem.getResourceCode()));

            billingService.clearing(vo);
        });
    }
}
