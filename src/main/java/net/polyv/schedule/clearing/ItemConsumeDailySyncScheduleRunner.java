package net.polyv.schedule.clearing;

import net.polyv.service.consume.ItemConsumeDailySyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 定时将dmp的每日用量同步到结算系统的item_consume_daily表中
 * TODO 一期由于时间紧迫，暂时得需要将dmp的数据同步到结算系统处理
 * TODO 二期需要结算系统直接从dmp拿每日消耗资源的数据
 * <AUTHOR>
 * @since 2020/6/18
 */
@Slf4j
@Component
public class ItemConsumeDailySyncScheduleRunner extends AbstractClearingScheduleRunner {

    private final ItemConsumeDailySyncService itemConsumeDailySyncService;
    private final ConsumeDailySyncAndItemClearingSyncThreadPool consumeDailySyncAndItemClearingSyncThreadPool;

    @Autowired
    public ItemConsumeDailySyncScheduleRunner(ItemConsumeDailySyncService itemConsumeDailySyncService,
                                              ConsumeDailySyncAndItemClearingSyncThreadPool consumeDailySyncAndItemClearingSyncThreadPool) {
        this.itemConsumeDailySyncService = itemConsumeDailySyncService;
        this.consumeDailySyncAndItemClearingSyncThreadPool = consumeDailySyncAndItemClearingSyncThreadPool;
    }


    /**
     * 每30分钟根据条件执行计费项的用量同步
     */
    //@Scheduled(cron = "0 0/30 * * * ? ")
    public void execute() {

        //consumeDailySyncAndItemClearingSyncThreadPool.execute(itemConsumeDailySyncService::sync);
    }
    
}
