package net.polyv.schedule.clearing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.exception.ClearingSystemException;
import net.polyv.service.AccountDepositService;
import net.polyv.util.DingWarnRobot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 资源点过期调度任务
 * @author: chenjunyu
 * @date: 2021-09-23
 */
@Slf4j
@Component
public class ResourcePointExpiredCleanupSchedule extends AbstractClearingScheduleRunner {


    /**
     * 账户充值服务
     */
    @Autowired
    private AccountDepositService accountDepositService;

    @Autowired
    private DingWarnRobot dingWarnRobot;
    /**
     * 资源点过期清理
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void execute() {
        List<String> warningMessage = new ArrayList<>();
        try {
            accountDepositService.executeExpiredCleanupResourcePoint(null);
        } catch (ClearingSystemException e) {
            warningMessage.add(e.getMessage());
            dingWarnRobot.sendWarnMsg("【资源点过期清零】", warningMessage);
        }
    }
}
