package net.polyv.schedule.clearing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.exception.ClearingSystemException;
import net.polyv.service.AccountDepositService;
import net.polyv.util.DingWarnRobot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 账户充值金额过期调度任务
 * @author: Neo
 * @date: 2021-09-23
 */
@Slf4j
@Component
public class AccountDepositExpiredCleanupSchedule extends AbstractClearingScheduleRunner {


    /**
     * 账户充值服务
     */
    @Autowired
    private AccountDepositService accountDepositService;

    @Autowired
    private DingWarnRobot dingWarnRobot;


    @Scheduled(cron = "0 0 4 * * ?")
    public void execute() {
        List<String> warningMessage = new ArrayList<>();
        try {
            accountDepositService.executeExpiredCleanup(null);
        } catch (ClearingSystemException e) {
            warningMessage.add(e.getMessage());
            dingWarnRobot.sendWarnMsg("【充值过期清零】", warningMessage);
        }
    }

}
