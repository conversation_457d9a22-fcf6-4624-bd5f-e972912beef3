package net.polyv.schedule.clearing;


import lombok.extern.slf4j.Slf4j;
import net.polyv.service.resource.CustomerResourceService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 定制化客户资源包 + 增容空间过期清理
 */
@Slf4j
@Component
public class CustomizedAccountExpireCleanScheduleRunner extends AbstractClearingScheduleRunner{

 @Resource
 private CustomerResourceService customerResourceService;


    @Scheduled(cron = "0 20 0 * * ? ")
    public void execute() {
        log.info("定制化客户资源过期清理，startTime : {}", System.currentTimeMillis());
        customerResourceService.cleanCustomizedExpireResource(null);
        log.info("定制化客户资源过期清理，endTime : {}", System.currentTimeMillis());
    }
}