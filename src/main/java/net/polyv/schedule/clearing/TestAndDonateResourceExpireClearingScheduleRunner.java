package net.polyv.schedule.clearing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.ResourceAlterTypeConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.custom.ResourceAlterationRecordDao;
import net.polyv.dao.primary.resource.ResourceAlterationRecordRepository;
import net.polyv.model.entity.primary.resource.ResourceAlterationRecord;
import net.polyv.service.CacheService;
import net.polyv.service.account.CustomerService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 测试和赠送资源过期清理（排除掉直播分钟数和导播台分钟数）
 * 普通账号：由直播系统进行触发清0
 * 集团主账号2.0：GroupAccountClearingScheduleRunner处理
 */
@Slf4j
@Component
public class TestAndDonateResourceExpireClearingScheduleRunner extends AbstractClearingScheduleRunner{



    @Autowired
    ResourceAlterationRecordRepository resourceAlterationRecordRepository;

    @Autowired
    private ResourceAlterationRecordDao resourceAlterationRecordDao;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private CustomerService customerService;


    //@Scheduled(cron = "0 50 0 * * ? ")
    public void execute() {
        log.info("测试赠送过期可用资源清理，startTime {}", System.currentTimeMillis());

        String expireDate = DateFormatUtil.formatDateNormal(DateUtil.getDateAfterDays(-1)); //到期时间是前一天
        Date now = DateUtil.getCurrentDay();
        List<ResourceAlterationRecord> expireResourceRecords
                = resourceAlterationRecordRepository.getExpireResourceRecord(expireDate);
        if(CollectionUtils.isNotEmpty(expireResourceRecords)){
            for(ResourceAlterationRecord record : expireResourceRecords){
                log.info("TestAndDonateResourceExpireClearingScheduleRunner clear resource == {}",record);
                resourceAlterationRecordDao.saveResourceAlteration(now, record.getCustomerId(), record.getResourceCode(),
                        ResourceAlterTypeConst.expire_clean, -(record.getAlteration()));
                //普通用户需要清理缓存
                if(AccountTypeEnum.NORMAL.getCode().equals(customerService.getAccountType(record.getCustomerId()))){
                    //清理直播和pcs缓存
                    cacheService.cleanLiveRestrictPcsResult(record.getCustomerId());
                    // 清理点播用户缓存
                    if (ResourceCodeConst.isVodResource(record.getResourceCode())) {
                        cacheService.cleanVodUserCache(record.getCustomerId());
                    }
                }
            }
        }

    }
}
