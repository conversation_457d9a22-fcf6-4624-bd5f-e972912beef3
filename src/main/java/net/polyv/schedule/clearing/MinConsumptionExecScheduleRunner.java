package net.polyv.schedule.clearing;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.service.bill.BillingDataService;
import net.polyv.service.customer.MinConsumptionService;
import net.polyv.util.DateUtil;
import net.polyv.web.model.account.CustomerInfoGetInputVO;

/**
 * 定时触发最低消费结算
 * 超过8点还无结算最低消费，需要告警
 * TODO 还需要等需求确定下来再定
 * <AUTHOR>
 * @since 2020/6/22
 */
@Slf4j
//@Component
public class MinConsumptionExecScheduleRunner extends AbstractClearingScheduleRunner {
    
    @Autowired
    private MinConsumptionService minConsumptionService;
    
    @Autowired
    private BillingDataService billingDataService;
    
    public void execute(){
        // 判断所有的计费项的结算已经做完，遍历所有的计费项，假如8点之后，还有部分计费项还没做完，需要发出
        // 钉钉告警，并且有手动触发最低消费结算的url，手工确认程序无问题后，再去手工触发最低消费结算
        Date yesterday = DateUtil.getYesterday();
        Date before2Days = DateUtil.getDateAfterDays(-2);
        
        // 检查结算状态的依据：1.账单表有数据， 2.结算状态
        if(!checkClearingStatus(yesterday, ItemCodeConst.china_pd)){
            return;
        }
    
        if(!checkClearingStatus(yesterday, ItemCodeConst.inter_pd)){
            return;
        }
    
        if(!checkClearingStatus(yesterday, ItemCodeConst.concur_daily)){
            return;
        }

        if(!checkClearingStatus(yesterday, ItemCodeConst.prtc_concur_daily)){
            return;
        }
    
        if(!checkClearingStatus(before2Days, ItemCodeConst.mic_pd)){
            return;
        }
    
        if(!checkClearingStatus(yesterday, ItemCodeConst.guide_pd)){
            return;
        }
        
        // 遍历有充值记录的用户，触发最低消费结算
        CustomerInfoGetInputVO inputVO = new CustomerInfoGetInputVO();
        minConsumptionService.calcMinConsumption(inputVO);
    }
    
    private boolean checkClearingStatus(Date date, ItemCodeConst codeConst){
        if(!billingDataService.existsBillingData(date, date, date, codeConst.name(), null)){
            log.warn("{} not finish clearing. exit...", codeConst);
            return false;
        }
        return true;
    }
}
