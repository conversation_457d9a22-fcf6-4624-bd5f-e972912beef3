package net.polyv.schedule;

import lombok.extern.slf4j.Slf4j;
import net.polyv.annotation.ConditionalFullFunctionMode;
import net.polyv.annotation.ScheduleSwitchControl;
import net.polyv.model.data.salesopportunities.CrmSalesOpportunitiesSearchDO;
import net.polyv.service.crm.CrmSaleOpportService;
import net.polyv.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 定时同步crm近一年的销售机会到结算系统
 * <AUTHOR>
 * @since 2020/6/23
 */
@ConditionalFullFunctionMode
@Slf4j
@Component
public class CrmSalesOpportSyncScheduleRunner{

    private final CrmSaleOpportService crmSaleOpportService;

    @Autowired
    public CrmSalesOpportSyncScheduleRunner(CrmSaleOpportService crmSaleOpportService) {
        log.info(String.format("start job %s ....", getClass().getName()));
        this.crmSaleOpportService = crmSaleOpportService;
    }

    /**
     * 每天凌晨1点同步所有的数据
     */
    // @Scheduled(cron = "0 0 1 * * ?")
    // @ScheduleSwitchControl
    public void syncAllData() {

        log.info("start synchronize all crm sale opportunities to database");
        CrmSalesOpportunitiesSearchDO searchDO = new CrmSalesOpportunitiesSearchDO();
        searchDO.setStatus(2);
        // crmSaleOpportService.addSyncTaskToQueue(searchDO);
        log.info("end synchronize all crm sale opportunities to database");
    }

    //  10 分钟同步当天的销售机会（企客宝只能按照日期，这里同步当天）
    // @Scheduled(cron = "0 0/10 * * * ?")
    @ScheduleSwitchControl
    public void syncDataNearly() {

        Date current = new Date();
        CrmSalesOpportunitiesSearchDO searchDO = new CrmSalesOpportunitiesSearchDO();
        searchDO.setStatus(2);
        searchDO.setStartCreateDate(DateUtil.date2LocalDate(current));
        searchDO.setEndCreateDate(DateUtil.date2LocalDate(current));

        log.info("start synchronize nearly one hour's crm sale opportunities to database");
        // crmSaleOpportService.addSyncTaskToQueue(searchDO);
        log.info("end synchronize nearly one hour's crm sale opportunities to database");
    }

}
