package net.polyv.schedule.marketing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.SystemEnvService;
import net.polyv.service.marketing.MarketingClueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: Neo
 * @date: 2022-06-09
 */
@Slf4j
@Component
public class MarketingClueTencentScheduleRunner {

    @Autowired
    private MarketingClueService marketingClueService;

    @Autowired
    private SystemEnvService systemEnvService;

    /**
     * 每天4点同步腾讯线索至纷享销客
     */
    @Scheduled(cron = "0 0 4 * * ? ")
    public void execute() {
        try {
            if (!systemEnvService.isProdEnv()) {
                return;
            }
            log.info("start to syncTencentClue2Fxk....");
            marketingClueService.syncTencentClue2Fxk();
        } catch (Exception e) {
            log.error(String.format("syncTencentClue2Fxk exception...%s", e.getMessage()), e);
        } finally {
            log.info("end of syncTencentClue2Fxk....");
        }
    }
}
