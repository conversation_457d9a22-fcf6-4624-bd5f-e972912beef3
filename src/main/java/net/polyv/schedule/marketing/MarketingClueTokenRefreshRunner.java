package net.polyv.schedule.marketing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.SystemEnvService;
import net.polyv.service.marketing.MarketingClueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description: 刷新市场营销线索第三方token
 * @author: Neo
 * @date: 2022-06-23
 */
@Slf4j
@Component
public class MarketingClueTokenRefreshRunner {

    @Autowired
    private MarketingClueService marketingClueService;

    @Autowired
    private SystemEnvService systemEnvService;

    /**
     * 每天1点和13点执行刷新token
     */
    @Scheduled(cron = "0 0 1,13 * * ? ")
    public void execute() {
        try {
            if (!systemEnvService.isProdEnv()) {
                return;
            }
            log.info("start to refreshQingniaoTokenSchedule....");
            marketingClueService.refreshQingniaoTokenSchedule();
        } catch (Exception e) {
            log.error(String.format("refreshQingniaoTokenSchedule exception...%s", e.getMessage()), e);
        } finally {
            log.info("end of refreshQingniaoTokenSchedule....");
        }
    }
}
