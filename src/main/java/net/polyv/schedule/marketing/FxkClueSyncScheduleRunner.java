package net.polyv.schedule.marketing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.SystemEnvService;
import net.polyv.service.market.FXiaoKeClueSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description: 同步线索定时任务
 * @author: Neo
 * @date: 2022-06-02
 */
@Slf4j
@Component
public class FxkClueSyncScheduleRunner {


    @Autowired
    private FXiaoKeClueSyncService fXiaoKeClueSyncService;

    @Autowired
    private SystemEnvService systemEnvService;

    /**
     * 每天4点执行同步
     * 同步纷享销客线索至数据库，提供给大数据分析溯源
     */
    @Scheduled(cron = "0 0 4 * * ? ")
    public void execute() {
        try {
            // 测试环境&线上环境开启
            if (!systemEnvService.isProdEnv() || !systemEnvService.isTestEnv()) {
                return;
            }
            log.info("start to sync clue....");
            fXiaoKeClueSyncService.syncSpecifyPoolClue();
        } catch (Exception e) {
            log.error(String.format("sync clue exception...%s", e.getMessage()), e);
        } finally {
            log.info("end of sync clue....");
        }
    }
}
