package net.polyv.schedule.marketing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.SystemEnvService;
import net.polyv.service.market.FXiaoKeClueSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description: 同步线索定时任务
 * @author: Neo
 * @date: 2022-06-02
 */
@Slf4j
@Component
public class FxkClueMatchAndUpdateScheduleRunner {


    @Autowired
    private FXiaoKeClueSyncService fXiaoKeClueSyncService;

    @Autowired
    private SystemEnvService systemEnvService;

    /**
     * 从0点开始，每3小时执行一次
     * 将大数据溯源后的结果同步更新到纷享销客
     */
    @Scheduled(cron = "0 0 1,14 * * ? ")
    public void execute() {
        try {
            if (!systemEnvService.isProdEnv()) {
                return;
            }
            log.info("start to match clue and update....");
            fXiaoKeClueSyncService.matchClueAndUpdate();
        } catch (Exception e) {
            log.error(String.format("match clue and update exception...%s", e.getMessage()), e);
        } finally {
            log.info("end of match clue and update....");
        }
    }
}
