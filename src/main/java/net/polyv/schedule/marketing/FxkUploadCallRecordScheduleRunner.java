package net.polyv.schedule.marketing;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.SystemEnvService;
import net.polyv.service.market.CallRecordingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description: 通话记录上传纷享销客定时任务
 * @author: Neo
 * @date: 2022-06-02
 */
@Slf4j
@Component
public class FxkUploadCallRecordScheduleRunner {


    @Autowired
    private CallRecordingService callRecordingService;

    @Autowired
    private SystemEnvService systemEnvService;

    /**
     * 每天8点30执行同步
     * 将通话记录同步至纷享销客销售记录
     */
    @Scheduled(cron = "0 0 22 * * ? ")
    public void execute() {
        try {
            if (!systemEnvService.isProdEnv()) {
                return;
            }
            log.info("start to execute upload2Crm....");
            callRecordingService.upload2Crm();
        } catch (Exception e) {
            log.error(String.format("execute upload2Crm exception...%s", e.getMessage()), e);
        } finally {
            log.info("end of execute upload2Crm....");
        }
    }
}
