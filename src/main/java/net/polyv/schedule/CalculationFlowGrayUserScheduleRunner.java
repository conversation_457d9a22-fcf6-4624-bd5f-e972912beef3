package net.polyv.schedule;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.pcs.api.req.crm.GetUserLast7DayConsumedRequest;
import net.polyv.modules.pcs.api.vo.crm.GetUserLast7DayConsumedResponse;
import net.polyv.service.GrayTestService;
import net.polyv.service.customer.CustomerFlowCalculationService;
import net.polyv.service.customer.CustomerLastConsumedService;


/**
 * created by l<PERSON><PERSON>ant<PERSON> 09/23/2021
 * 点播流量超量使用处理定时任务
 */
@Component
@Slf4j
public class CalculationFlowGrayUserScheduleRunner extends AbstractScheduleRunner {
    @Resource
    private CustomerFlowCalculationService customerFlowCalculationService;
    @Resource
    private CustomerLastConsumedService customerLastConsumedService;
    @Resource
    private GrayTestService grayTestService;
    
    //七点半获取灰度用户数据，处理点播空间为0的进入缓存
    @Scheduled(cron = "0 30 7 * * ?")
    public void execute() {
        log.info("start to execute CalculationFlowGrayUserScheduleRunner....");
        customerFlowCalculationService.preCalculationFlowGrayUser();
        log.info("end execute CalculationFlowGrayUserScheduleRunner....");
    }
}
