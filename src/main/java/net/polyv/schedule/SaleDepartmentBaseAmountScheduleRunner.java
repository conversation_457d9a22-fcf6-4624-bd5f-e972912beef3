package net.polyv.schedule;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.dao.primary.examinationDonate.SaleDepartmentBaseAmountRepository;
import net.polyv.model.entity.primary.examinationDonate.SaleDepartmentBaseAmount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 同步数据库中销售部门基础额度数据到redis
 * @author: liujiantao
 * @date: 2022-06-13
 */
@Slf4j
@Component
public class SaleDepartmentBaseAmountScheduleRunner {



    @Autowired
    private SaleDepartmentBaseAmountRepository saleDepartmentBaseAmountRepository;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    /**
     * 每隔1个小时同步一次
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void execute() {
        log.info("start to sync baseAmount....");
        try {
            //先删除原来hash数据
            redisTemplate.delete(GlobalConfigConst.SALE_DEPARTMENT_BASE_AMOUNT_KEY);
            //批量同步mysql数据到redis
            Pageable pageable = PageRequest.of(0, 1000);
            Page<SaleDepartmentBaseAmount> saleDepartmentBaseAmountPage = saleDepartmentBaseAmountRepository.findAll(pageable);
            while(!saleDepartmentBaseAmountPage.isEmpty()){
                syncDbToRedis(saleDepartmentBaseAmountPage.getContent());
                pageable = PageRequest.of(pageable.getPageNumber() + 1, pageable.getPageSize());
                saleDepartmentBaseAmountPage = saleDepartmentBaseAmountRepository.findAll(pageable);

            }
        } catch (Exception e) {
            log.error(String.format("sync baseAmount exception...%s", e.getMessage()), e);
        } finally {
            log.info("end to sync baseAmount....");
        }
    }

    public void syncDbToRedis(List<SaleDepartmentBaseAmount> list){
        if(!CollectionUtils.isEmpty(list)){
            HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
            Map<String,SaleDepartmentBaseAmount> map = list.stream().collect(Collectors.toMap(
                    SaleDepartmentBaseAmount::getSaleUserId, Function.identity(), (key1, key2) -> key2));
            opsForHash.putAll(GlobalConfigConst.SALE_DEPARTMENT_BASE_AMOUNT_KEY,map);
        }
    }
}
