package net.polyv.schedule.stat;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import net.polyv.schedule.AbstractScheduleRunner;
import net.polyv.service.sync.StatDataSyncService;
import net.polyv.util.DateUtil;
import net.polyv.web.model.stat.StatQueryRequest;

/**
 * 汇总合同统计数据
 * <AUTHOR>
 * @date 2022/8/23 16:52
 */
@Component
public class SummaryContractScheduleRunner extends AbstractScheduleRunner {
    @Resource
    private StatDataSyncService statContractSyncService;
    
    @Override
    @Scheduled(cron = "0 0 7 * * ?")
    public void execute() {
        Date dateBeforeDays = DateUtil.getYesterday();
        StatQueryRequest statQueryRequest = new StatQueryRequest();
        statQueryRequest.setStatTime(dateBeforeDays);
        statQueryRequest.setStartTime(DateUtil.getDateStart(dateBeforeDays));
        statQueryRequest.setEndTime(DateUtil.getDateEnd(dateBeforeDays));
        statQueryRequest.setPageSize(200);
        statContractSyncService.incrementalDataSync(statQueryRequest);
    }
}
