package net.polyv.schedule;


import java.util.*;

import javax.annotation.Resource;

import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.GroupLiveChargeType;
import net.polyv.dao.primary.GroupAccountConfigRepository;
import net.polyv.dao.primary.LiveFlowPackageInfoRepository;
import net.polyv.model.entity.primary.GroupAccountConfig;
import net.polyv.rest.client.live.UserPackageClient;
import net.polyv.service.account.CustomerService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.ConcurrenceModeConst;
import net.polyv.constant.OperateResultConst;
import net.polyv.constant.PackageTypeConst;
import net.polyv.constant.customer.LiveBillingPlanConst;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.dao.primary.AvailableCustomerResourceDailyRepository;
import net.polyv.dao.primary.CustomerConfigRepository;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.dao.primary.custom.CustomerConfigDao;
import net.polyv.dao.primary.custom.ResourceAlterationRecordDao;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.rest.client.live.LivePackageClient;
import net.polyv.rest.model.live.LivePackageMsgVO;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.rest.service.live.LivePackageClientService;
import net.polyv.service.CacheService;
import net.polyv.service.SystemEnvService;
import net.polyv.service.bill.BillingService;
import net.polyv.service.concurrence.ConcurrenceSettingService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.SpecialSignUtils;
import net.polyv.web.model.account.GetCustomerStateInputVO;
import net.polyv.web.model.bill.BillClearingInputVO;
import net.polyv.web.model.common.CommonOperateResultVO;
import net.polyv.web.model.oldpackage.LivePackageState;
import net.polyv.web.model.user.CustomerStateVO;

/**
 * 并发时段启停定时任务
 * <p>
 * 有时间这个类要优化一下写法
 * <AUTHOR>
 * @since 2020/05/29
 */
@Slf4j
@Component
public class ConcurrenceSettingStartStopScheduleRunner extends AbstractScheduleRunner {
    
    @Value("${polyv.sign.secretKey}")
    private String signSecretKey;
    
    
    private final LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository;
    private final CustomerConfigDao customerConfigDao;
    private final LivePackageClientService livePackageClientService;
    private final LivePackageClient livePackageClient;
    private final CustomerConfigRepository customerConfigRepository;
    private final AvailableCustomerResourceDailyRepository availableCustomerResourceDailyRepository;
    @Autowired
    private BillingService billingService;
    @Autowired
    private DingWarnRobot dingWarnRobot;
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private SystemEnvService systemEnvService;
    
    @Autowired
    private CustomerService customerService;
    
    
    @Autowired
    private GroupAccountConfigRepository groupAccountConfigRepository;
    
    
    @Resource
    private ConcurrenceSettingService concurrenceSettingService;
    
    @Autowired
    public ConcurrenceSettingStartStopScheduleRunner(
            LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository,
            CustomerConfigDao customerConfigDao, LivePackageClientService livePackageClientService,
            LivePackageClient livePackageClient, CustomerConfigRepository customerConfigRepository,
            AvailableCustomerResourceDailyRepository availableCustomerResourceDailyRepository) {
        super();
        this.liveCustomerConcurrenceSettingRepository = liveCustomerConcurrenceSettingRepository;
        this.customerConfigDao = customerConfigDao;
        this.livePackageClientService = livePackageClientService;
        this.livePackageClient = livePackageClient;
        this.customerConfigRepository = customerConfigRepository;
        this.availableCustomerResourceDailyRepository = availableCustomerResourceDailyRepository;
    }
    
    @Scheduled(cron = "0 35 0 * * ? ")
    public void execute() {
        
        // 处理开始的并发时段(新版的业务开通如果是未来开通,会有单独的定时任务去处理,这里需要屏蔽掉并发的开通)
        //this.processStartBatch();
        
        // 处理结束的并发时段(需要区分普通账号和集团主账号2.0)
        this.processEndBatch();
    }
    
    /**
     * 处理并发时段开始
     * @return 处理开始的并发时段数量
     */
    private long processStartBatch() {
        
        // 处理开始的数量
        long processCount = 0L;
        // 循环计数器
        int count = 0;
        
        // 创建日期成本较大，将new Date抽取出来
        Date current = new Date();
        Date dateBegin = DateUtil.getBeginOfDay(current);
        Date dateEnd = DateUtil.getEndOfDay(current);
        
        while (true) {
            // 死循环保护
            if (count >= MAX_LOOP_TIMES) {
                log.warn("process start exceed max loop times, stop job now.");
                return processCount;
            }
            
            // 获取需要处理的数据
            Page<LiveCustomerConcurrenceSetting> settingList =
                    liveCustomerConcurrenceSettingRepository.findByStartTimeBetweenAndIsOldPackage(
                    dateBegin, dateEnd, PageRequest.of(count++, DEFAULT_PROCESS_PAGE_SIZE), 0);
            if (settingList.getNumberOfElements() == 0) {
                return processCount;
            }
            
            // 执行处理
            settingList.getContent().forEach(item -> {
                if(!needAffectProduct(item.getCustomerId())){
                    return;
                }
                // 防止一个顾客执行错误，导致所有顾客都不可执行
                try {
                    //this.processStart(item);
                } catch (Exception e) {
                    log.error("【并发套餐过期操作】开始并发时段执行异常,顾客id=" + item.getCustomerId(), e);
                }
            });
        }
    
    }
    
    private boolean needAffectProduct(String customerId) {
        return systemEnvService.isProdEnv() || (systemEnvService.isTestAllowUser(customerId));
    }
    
    /**
     * 处理并发时段结束
     * @return 处理结束的并发时段数量
     */
    private void processEndBatch() {
        
        Date expireDay = DateUtil.getYesterday();
        Date currentDay = DateUtil.getCurrentDay();
        
        // 获取需要处理的数据
        List<LiveCustomerConcurrenceSetting> settingList = liveCustomerConcurrenceSettingRepository.findByExpireDate(
                expireDay, currentDay);
        if (settingList.isEmpty()) {
            return;
        }
        log.info("需要处理的并发过期数据 == {}",JSON.toJSONString(settingList));
        // 执行处理
        settingList.forEach(item -> {
            // 防止一个顾客执行错误，导致所有顾客都不可执行
            try {
                // 由于此时用量数据不一定已经汇总出来，所以账单的结算交给结算定时器来操作
//                this.processClearing(item);
                if (!isNeedHandleConcurrenceEnd(item)) {
                    dingWarnRobot.sendWarnMsg("【并发套餐过期，由于当前计费方式为分钟数，无须打回分钟数套餐】",
                            String.format("客户id为：%s", item.getCustomerId()));
                } else {
                    log.info("过期的并发套餐 == {}", item);
                    this.processEnd(item);
                }
            } catch (Exception e) {
                log.error("【并发套餐过期操作】结束并发时段执行异常,顾客id=" + item.getCustomerId(), e);
            }
        });
    }
    
    private void processStart(LiveCustomerConcurrenceSetting setting) {
        log.info("【并发时段生效】 LiveCustomerConcurrenceSetting={}", setting);
        LivePackageMsgVO msgVO = livePackageClientService.getLivePackageMsg(setting.getCustomerId());
        if (msgVO != null) {
            // 保存上一次的直播套餐信息
            livePackageClientService.savePreLivePackageMsg(setting, msgVO);
        }

        this.addLivePackage(setting);
        // 清理使用限制的缓存
        cacheService.cleanLiveRestrictPcsResult(setting.getCustomerId());
        // 清理直播用户缓存
        cacheService.cleanLiveUserCache(setting.getCustomerId());
    }

    private void processEnd(LiveCustomerConcurrenceSetting setting) {
        concurrenceSettingService.endConcurrence(setting);
    }
    
    /**
     * 是否需要处理并发结束打回其他套餐的逻辑
     * 当前存在生效中的分钟数或者流量套餐，不需要打回其他套餐
     * @param setting
     * @return
     */
    private boolean isNeedHandleConcurrenceEnd(LiveCustomerConcurrenceSetting setting) {
        //集团2.0主账号（不支持打直播流量套餐，所以只判断分钟数）
        if (AccountTypeEnum.GROUP2.getCode().equals(setting.getAccountType())) {
            GroupAccountConfig groupAccountConfig = groupAccountConfigRepository.findByGroupId(setting.getCustomerId())
                    .orElse(null);
            if (Objects.nonNull(groupAccountConfig)) {
                return GroupLiveChargeType.MINUTES.getValue().equals(groupAccountConfig.getGroupLiveBillingPlan());
            }
            return false;
        }
        //普通账号
        else {
            Date baseDate = DateUtil.getCurrentDay();
            boolean isLiveFlowBilling = livePackageClientService.isLiveFlowBilling(setting.getCustomerId(), baseDate);
            //当前生效中是分钟数或直播流量套餐，不需要处理
            if (isLiveFlowBilling ||
                    livePackageClientService.isLiveDurationBilling(setting.getCustomerId(), baseDate)) {
                return false;
            }
            return true;
        }
    }
    
    
    /**
     * 修改用户为直播套餐
     * @param setting 生效的并发时段
     */
    private void addLivePackage(LiveCustomerConcurrenceSetting setting) {
        
        String customerId = setting.getCustomerId();
        long timestamp = System.currentTimeMillis();
        String type = "";
        if(ConcurrenceModeConst.DAILY == setting.getMode()){
            type = PackageTypeConst.daily.name();
        }
        if(ConcurrenceModeConst.PRTC_DAILY == setting.getMode()){
            type = PackageTypeConst.prtc_daily.name();
        }
        if(ConcurrenceModeConst.MONTHLY == setting.getMode()){
            type = PackageTypeConst.monthly.name();
        }
        if(ConcurrenceModeConst.PRTC_MONTHLY == setting.getMode()){
            type = PackageTypeConst.prtc_monthly.name();
        }
        long duration = 0L;
        long concurrences = setting.getConcurrence();
        long financeConcurrences = setting.getConcurrence();
        String startDate = DateFormatUtil.formatDateNormal(setting.getStartTime());
        String endDate = DateFormatUtil.formatDateNormal(setting.getEndTime());
        long packageId = 0L;
        long isUnionId = 1L;
        int needCallClearing = 0;
        int isKeepPackageId = 1;
        
        Map<String, String> map = new HashMap<>(11);
        map.put("customerId", customerId);
        map.put("timestamp", String.valueOf(timestamp));
        map.put("type", type);
        map.put("duration", String.valueOf(duration));
        map.put("concurrences", String.valueOf(concurrences));
        map.put("financeConcurrences", String.valueOf(financeConcurrences));
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("packageId", String.valueOf(packageId));
        map.put("isUnionId", String.valueOf(isUnionId));
        map.put("syncClearing", String.valueOf(needCallClearing));
        String sign = SpecialSignUtils.getAddPackageSign(timestamp);
        
        livePackageClient.addPackage(customerId, timestamp, sign, type, duration, concurrences, financeConcurrences,
                startDate, endDate, packageId, isUnionId, needCallClearing, isKeepPackageId);
        
    }
    
    
    /**
     * 结束的时候结算出账
     * @param activeSetting
     */
    private void processClearing(LiveCustomerConcurrenceSetting activeSetting) {
        BillClearingInputVO billClearingInputVO = new BillClearingInputVO();
        billClearingInputVO.setCustomerIdArrStr(activeSetting.getCustomerId());
        billClearingInputVO.setConsumeEndDate(activeSetting.getEndTime());
        billClearingInputVO.setConsumeStartDate(activeSetting.getEndTime());
        
        Assert.notNull(activeSetting.getMode(), "并发模式不能为空");
        String itemCode = "";
        if(ConcurrenceModeConst.MONTHLY == activeSetting.getMode()){
            itemCode = ItemCodeConst.concur_monthly.getCode();
        }else if(ConcurrenceModeConst.PRTC_MONTHLY == activeSetting.getMode()){
            itemCode = ItemCodeConst.prtc_concur_monthly.getCode();
        }else if(ConcurrenceModeConst.DAILY == activeSetting.getMode()){
            itemCode = ItemCodeConst.concur_daily.getCode();
        }else if(ConcurrenceModeConst.PRTC_DAILY == activeSetting.getMode()){
            itemCode = ItemCodeConst.prtc_concur_daily.getCode();
        }else {
            itemCode = ItemCodeConst.concur_daily.getCode();
        }
        
        CommonOperateResultVO resultVO = billingService.clearing(billClearingInputVO);

        if (resultVO.getResult() == null || OperateResultConst.SUCCESS != resultVO.getResult()) {
            log.error("顾客切换旧套餐触发结算失败, 返回参数={}", JSON.toJSONString(resultVO));
        }
    }
    
    
}
