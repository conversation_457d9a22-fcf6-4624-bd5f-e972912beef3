package net.polyv.schedule;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.common.PrecisionConst;
import net.polyv.constant.resource.ResourceThresholdTypeEnum;
import net.polyv.dao.primary.ResourceThresholdNotifyRepository;
import net.polyv.model.entity.primary.ResourceThresholdNotify;
import net.polyv.schedule.clearing.AbstractClearingScheduleRunner;
import net.polyv.service.account.CustomerService;
import net.polyv.util.DateUtil;
import net.polyv.util.JsonMapper;
import net.polyv.web.model.account.AccountAmountLessThanResultVO;
import net.polyv.web.model.account.AmountNotEnoughVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 资源阈值记录统计定时任务
 * <AUTHOR>
 * @since 2022/08/18
 */
@Slf4j
@Component
public class ResourceThresholdNotifyScheduleRunner extends AbstractClearingScheduleRunner {

    @Value("${polyv.threshold.notify.amount-not-enough:10000}")
    private String amountThresholds;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private ResourceThresholdNotifyRepository resourceThresholdNotifyRepository;

    @Scheduled(cron = "0 0 7 * * ?")
    public void execute() {
        log.info("ResourceThresholdNotifyScheduleRunner start....");
        List<ResourceThresholdNotify> result = Lists.newArrayList();
        //金额不足提醒记录生成
        for(String amountThreshold : amountThresholds.split(",")){
            List<AccountAmountLessThanResultVO> amountLessThanResultList = customerService.listAmountLessThan(Long.parseLong(amountThresholds) * PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION);
            amountLessThanResultList.forEach(amount ->{
                ResourceThresholdNotify resourceThresholdNotify = new ResourceThresholdNotify();
                BeanUtils.copyProperties(amount,resourceThresholdNotify);
                resourceThresholdNotify.setThreshold(new BigDecimal(amountThreshold));
                resourceThresholdNotify.setStatAt(DateUtil.getCurrentDay());
                resourceThresholdNotify.setType(ResourceThresholdTypeEnum.AMOUNT_NOT_ENOUGH.getCode());
                resourceThresholdNotify.setCreateTime(new Date());
                AmountNotEnoughVO amountNotEnoughVO = new AmountNotEnoughVO();
                BeanUtils.copyProperties(amount,amountNotEnoughVO);
                resourceThresholdNotify.setExt(Objects.nonNull(amountNotEnoughVO) ? JsonMapper.jsonToString(amountNotEnoughVO) : "{}");
                result.add(resourceThresholdNotify);
            });
        }
        if(CollectionUtils.isNotEmpty(result)){
            resourceThresholdNotifyRepository.saveAll(result);
        }
        log.info("ResourceThresholdNotifyScheduleRunner end....");
    }


}
