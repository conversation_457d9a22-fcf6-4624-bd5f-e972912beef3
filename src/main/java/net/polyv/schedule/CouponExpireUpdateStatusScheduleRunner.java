package net.polyv.schedule;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.coupon.CouponReceiveRecordStatusConst;
import net.polyv.dao.primary.CouponReceiveRecordRepository;
import net.polyv.model.entity.primary.CouponReceiveRecord;
import net.polyv.service.coupon.CouponReceiveRecordService;
import net.polyv.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 定期将过期优惠券的领取记录修改状态
 */
@Slf4j
@Component
public class CouponExpireUpdateStatusScheduleRunner extends AbstractScheduleRunner {

    @Autowired
    private CouponReceiveRecordService couponReceiveRecordService;
    @Autowired
    private CouponReceiveRecordRepository couponReceiveRecordRepository;

    @Scheduled(cron = "0 1 0 * * ? ")
    @Override
    public void execute(){
        log.info("start to update expire coupon receive records");
        Date currentDay = DateUtil.getCurrentDay();
        List<CouponReceiveRecord> list = couponReceiveRecordService.listExpireRecords(currentDay);
        for (CouponReceiveRecord receiveRecord : list) {
            log.info("update expire coupon receive record status, receiveRecordId={}", receiveRecord.getId());
            receiveRecord.setStatus(CouponReceiveRecordStatusConst.expire.getStatus());
            receiveRecord.setUpdateTime(new Date());
        }
        couponReceiveRecordRepository.saveAll(list);
        log.info("end to update expire coupon receive records");
    }
}
