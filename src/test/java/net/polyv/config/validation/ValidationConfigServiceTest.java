package net.polyv.config.validation;

import org.junit.Assert;
import org.junit.Test;

/**
 * mvc参数、业务校验规则单测
 * <AUTHOR>
 * @since 07/05/2020
 */
public class ValidationConfigServiceTest {

    private final ValidationConfigService validationConfigService = new ValidationConfigService();

    @Test
    public void loadConfigNormal() {
        ValidationConfig config = validationConfigService.getConfig("/test");
        Assert.assertEquals("解析参数校验规则失败", 1, config.getParamValidationRules().size());
        Assert.assertEquals("解析业务校验规则失败", 0, config.getBusinessValidationRules().size());
    }
}