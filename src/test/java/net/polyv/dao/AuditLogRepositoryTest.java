package net.polyv.dao;

import java.util.List;
import java.util.UUID;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import net.polyv.PcsApplication;
import net.polyv.dao.primary.AuditLogRepository;
import net.polyv.model.data.GlobalUserInfoDO;
import net.polyv.model.entity.primary.AuditLog;
import net.polyv.util.UserInfoThreadLocal;
import org.springframework.transaction.annotation.Transactional;

/**
 * jpa配置环境单测
 * <AUTHOR>
 * @since 07/05/2020
 */
@Ignore("环境整合jpa单测，无须跑")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PcsApplication.class)
public class AuditLogRepositoryTest {

    @Autowired
    private AuditLogRepository auditLogRepository;

    @Transactional(rollbackFor = Exception.class)
    @Test
    public void insert() {
        GlobalUserInfoDO userInfoDO = new GlobalUserInfoDO();
        userInfoDO.setUserId("123");
        userInfoDO.setUserIp("127.0.0.1");
        UserInfoThreadLocal.setGlobalUserInfoDO(userInfoDO);

        String customerId = UUID.randomUUID().toString().replace("-", "").substring(0, 10);

        AuditLog log = new AuditLog();
        log.setContractId("123");
        log.setCreateUserIp(UserInfoThreadLocal.getUserIp());
        log.setDetail("{}");
        log.setEvent("event");
        log.setStatus(1);
        log.setCustomerId(customerId);
        auditLogRepository.save(log);

        List<AuditLog> list = auditLogRepository.findByCustomerId(customerId);
        Assert.assertEquals("jpa插入失败","127.0.0.1", list.get(0).getCreateUserIp());
    }
}