package net.polyv.dao;

import net.polyv.PcsApplication;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.model.entity.primary.SalesOpportunities;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * @Author: chenshaofeng
 * @Date: 2020/5/8 22:46
 * @Description:
 */
@Ignore
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PcsApplication.class)
public class SalesOpportunitiesRepositoryTest {

    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;

    @Test
    public void insertTest(){
        String soId = this.createUUID();
        this.saveSalesOpportunitiesWithSoId(soId);

        List<SalesOpportunities> salesOpportunitiesList = salesOpportunitiesRepository.findAll();

        assertTrue("insert fail",salesOpportunitiesList.size()  > 0);
        assertEquals("insert fail", soId ,salesOpportunitiesList.get(salesOpportunitiesList.size()-1).getSoId());

    }

    @Test
    public void updateTest(){
        List<SalesOpportunities> salesOpportunitiesList = salesOpportunitiesRepository.findAll();
        if (CollectionUtils.isEmpty(salesOpportunitiesList)){
            String soId = this.createUUID();
            this.saveSalesOpportunitiesWithSoId(soId);
        }

        SalesOpportunities opportunities = salesOpportunitiesList.get(salesOpportunitiesList.size() - 1);
        String name = this.createUUID();
        opportunities.setName(name);
        salesOpportunitiesRepository.save(opportunities);

        List<SalesOpportunities> resultList = salesOpportunitiesRepository.findAll();
        assertEquals("update fail", name ,resultList.get(resultList.size()-1).getName());
    }

    private void saveSalesOpportunitiesWithSoId(String soId) {
        SalesOpportunities opportunities = new SalesOpportunities();
//        opportunities.setSoId(id);
        opportunities.setSoId(soId);
        opportunities.setContractId("1");
        opportunities.setContractType("1");
        opportunities.setName("name");
        opportunities.setCode("code");
        opportunities.setCustomerId("userId");
        opportunities.setCompany("company");
        opportunities.setEmail("email");
        opportunities.setStatus(1);
        opportunities.setAmountGainedDate(new Date());
        opportunities.setAmountGained(1L);
        opportunities.setContractAmount(2L);
        opportunities.setBusinessType(1);
        opportunities.setBillingPlan("1");
        opportunities.setSummary("1");
        opportunities.setBusinessStartTime(new Date());
        opportunities.setBusinessEndTime(new Date());
        opportunities.setSaleUserId("salesUserId");
        opportunities.setInvoiceType("invoiceType");
        opportunities.setAcceptanceAccount("Acceptance");
        opportunities.setCustomerFrom("CustomerFrom");
        opportunities.setIndustryDistribution("CustomerFrom");
        opportunities.setType("CustomerFrom");
        opportunities.setFunctionPackage("CustomerFrom");
        opportunities.setIntroduction("CustomerFrom");
        opportunities.setExt("{}");
//        opportunities.setCreateTime(new Date());
//        opportunities.setUpdateTime(new Date());
        opportunities.setContractType("1");

        salesOpportunitiesRepository.save(opportunities);
    }

    private String createUUID(){
        return UUID.randomUUID().toString().replace("-", "").substring(0, 10);
    }

}