package net.polyv.dao.primary;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StopWatch;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.SyncTaskStatusEnum;
import net.polyv.model.entity.primary.SyncFinanceDataRecordTask;

/**
 * 转移数据测试
 * <AUTHOR>
 * @date 2022/10/8 17:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
@ActiveProfiles("local")
public class SyncFinanceDataRecordTaskRepositoryTest extends TestCase {
    
    @Resource
    private SyncFinanceDataRecordTaskRepository syncFinanceDataRecordTaskRepository;
    
    @Test
    public void testBatch() {
        List<SyncFinanceDataRecordTask> list = new ArrayList<>();
//        list.add(new SyncFinanceDataRecordTask("sss","{}", SyncTaskStatusEnum.FAIL.getStatus(), new Date()));
//        list.add(new SyncFinanceDataRecordTask("sdfsdf","{}", SyncTaskStatusEnum.FAIL.getStatus(), new Date()));
//        list.add(new SyncFinanceDataRecordTask("sdfasdfsa","{}", SyncTaskStatusEnum.FAIL.getStatus(), new Date()));
//        list.add(new SyncFinanceDataRecordTask("dfasdfasd","{}", SyncTaskStatusEnum.FAIL.getStatus(), new Date()));
//        list.add(new SyncFinanceDataRecordTask("sfasdfasdfa","{}", SyncTaskStatusEnum.FAIL.getStatus(), new Date()));
        for (int i = 0; i < 5000; i++) {
            list.add(new SyncFinanceDataRecordTask("sfasdfasdfa" + i, "{}", SyncTaskStatusEnum.FAIL.getStatus(),
                    new Date()));
        }
        StopWatch st = new StopWatch("保存测试");
        st.start();
        long timeMillis = System.currentTimeMillis();
        syncFinanceDataRecordTaskRepository.saveByBatch(list);
        st.stop();
        log.info("消耗耗时：{}s", (System.currentTimeMillis()-timeMillis)/1000);
    }
}