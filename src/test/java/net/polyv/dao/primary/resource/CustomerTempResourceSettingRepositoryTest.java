package net.polyv.dao.primary.resource;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import net.polyv.PcsApplication;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = PcsApplication.class)
public class CustomerTempResourceSettingRepositoryTest extends TestCase {
    @Resource
    private CustomerTempResourceSettingRepository customerTempResourceSettingRepository;
    @Test
    public void testFindWithinEffectiveTempSetting() {
    
      /*  List<CustomerTempResourceSetting> withinEffectiveTempSetting =
                customerTempResourceSettingRepository.findActiveTempSettingV2(Lists.newArrayList("bcb6ef082c"),"space", "2023-06-27");
        */
        
      /*  List<CustomerTempResourceSetting> withinEffectiveTempSettings =
                customerTempResourceSettingRepository.findActiveTempSetting(Lists.newArrayList("bcb6ef082c"),"space",
                        DateUtil.getCurrentDay());
        //  System.out.println(withinEffectiveTempSetting);
        System.out.println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        System.out.println(withinEffectiveTempSettings);*/
    
    }
}