package net.polyv.service.transfer;

import java.math.BigDecimal;
import java.util.IntSummaryStatistics;
import java.util.Iterator;
import java.util.List;
import java.util.LongSummaryStatistics;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import net.polyv.constant.finance.FinanceTaxRateConst;
import net.polyv.modules.pcs.api.vo.GroupMicDurationResultVO;

/**
 * 转移测试
 * <AUTHOR>
 * @date 2022/10/28 16:10
 */
public class TransferTest {
    
    public static void main(String[] args) {
        System.out.println(groupSumTest());
    }
    
    private static String amount() {
        BigDecimal contractAmount = new BigDecimal("1000.00");
        BigDecimal money = new BigDecimal("943.40");
        //退费数据没有乘税的如果退一半 就是 退费金额 *1.06*10w 全额:合同金额减去退费金额小于1分钱 的取合同金额
        BigDecimal multiply = money.multiply(FinanceTaxRateConst.NORMAL.getTaxRate());
        BigDecimal subtract = contractAmount.subtract(multiply);
        BigDecimal decimal = new BigDecimal("0.01");
        if (subtract.equals(decimal)) {
            return contractAmount.toString();
        }
        return subtract.compareTo(decimal) <= 0 ? contractAmount.toString() : multiply.toString();
    }
    
    private static String groupSumTest() {
        List<GroupMicDurationResultVO> resultVOList = Lists.newArrayList();
        GroupMicDurationResultVO v1 = new GroupMicDurationResultVO();
        v1.setStatAt("2022-03-01");
        v1.setMicLevel(4);
        v1.setDuration(10);
        resultVOList.add(v1);
        
        GroupMicDurationResultVO v2 = new GroupMicDurationResultVO();
        v2.setStatAt("2022-03-01");
        v2.setMicLevel(4);
        v2.setDuration(20);
        resultVOList.add(v2);
        
        GroupMicDurationResultVO v3 = new GroupMicDurationResultVO();
        v3.setStatAt("2022-03-01");
        v3.setMicLevel(5);
        v3.setDuration(30);
        resultVOList.add(v3);
        
        GroupMicDurationResultVO v4 = new GroupMicDurationResultVO();
        v4.setStatAt("2022-03-02");
        v4.setMicLevel(4);
        v4.setDuration(40);
        resultVOList.add(v4);
        
        GroupMicDurationResultVO v5 = new GroupMicDurationResultVO();
        v5.setStatAt("2022-03-02");
        v5.setMicLevel(5);
        v5.setDuration(50);
        resultVOList.add(v5);
        Map<String, Map<Integer, IntSummaryStatistics>> aggMap = resultVOList.stream()
                .collect(Collectors.groupingBy(GroupMicDurationResultVO::getStatAt,
                        Collectors.groupingBy(GroupMicDurationResultVO::getMicLevel,
                                Collectors.summarizingInt(GroupMicDurationResultVO::getDuration))));
        System.out.println(aggMap);
        Set<Map.Entry<String, Map<Integer, IntSummaryStatistics>>> entries = aggMap.entrySet();
        Iterator<Map.Entry<String, Map<Integer, IntSummaryStatistics>>> iterator = entries.iterator();
        List<GroupMicDurationResultVO> resultVOS = Lists.newArrayList();
        while (iterator.hasNext()) {
            Map.Entry<String, Map<Integer, IntSummaryStatistics>> next = iterator.next();
            String statAt = next.getKey();
            Map<Integer, IntSummaryStatistics> map = next.getValue();
            for (Map.Entry<Integer, IntSummaryStatistics> summary : map.entrySet()) {
                GroupMicDurationResultVO vo = new GroupMicDurationResultVO();
                vo.setStatAt(statAt);
                vo.setMicLevel(summary.getKey());
                vo.setDuration((int) summary.getValue().getSum());
                resultVOS.add(vo);
            }
        }
        System.out.println(resultVOS);
        return "";
    }
}
