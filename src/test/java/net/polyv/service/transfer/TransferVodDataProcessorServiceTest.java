package net.polyv.service.transfer;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.service.impl.resource.sync.SyncVodResourceService;
import net.polyv.service.resource.ResourceChangeHistoryOperationService;
import net.polyv.service.resource.ResourceChangeHistoryService;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class TransferVodDataProcessorServiceTest extends TestCase {
    @Resource
    private TransferVodDataProcessorService transferVodDataProcessorService;
    @Resource
    private SyncVodResourceService syncVodResourceService;
    @Resource
    private ResourceChangeHistoryService resourceChangeHistoryService;
    @Resource
    private ResourceChangeHistoryOperationService resourceChangeHistoryOperationService;
    @Test
    public void testDoTransferData() {
         //        transferVodDataProcessorService.doTransferData("002e3ace9a,004c4a6bf6,0074c4741f,0075f7a4d7,
        //        008a98cdb9,00927d2c7a,009e741f66,00aa15b2d2,00ac291530");
        //   transferVodDataProcessorService.doTransferData("230054f157,23008050c2,2301243ffd,2301295ef2,23016bc97c,
        //   23017a8079,2301f661d4,23020517b9,23020ab059,2302b91115,2302e3e15b,2303006bc3,230318c7b6,23037553e9,
        //   230397dd94,2303d354ec,2303dc7785,2304321738,230453f43c,23046fb13f,2304959b8b,2304dcae0a,2304e3d58f,
        //   2304f5867e,23053d4c09,2305a430b8,2305cb690b,2306131751,2306a8c697,2306b0be3d,2307578ce0,23076503fd,
        //   230789c524,230957b277,230960739f,2309811f44,2309fbdc28,230a7897f9,230a8eb0ad,230aadf8ae,230afbfbcd,
        //   230b39832b,230c5f61dd,230c9a5fb9,230d05ed00,230d184a33,230d8f3d4d,230d9fdf85,230da24121,230db26a2d,
        //   230de194c1,230e377a09,230e3efb0e,230e54d0f6,230e7942b6,230e8f4fae,230ec0d43f,230eee1896,230f480e72,
        //   230f82303e,230fae1141,230fb30714,230fba26e7,230fffc932,2310a8b5b9,2310f3e563,231134de87,23114bdb80,
        //   2311cba97b,2311d9e5e0,2311ee270b,23127f86d0,2312b8eba4,23133833fb,23136a3d9d,2313b834bc,2313e573f6,
        //   23145c4026,2315b391ef,231620532d,2316b3fd3f,2316f52882,231722ca7b,231756fc44,23178c08bd,23183cd279,
        //   231866fe0e,231901ac50,23191dc1eb,2319201617,2319379246,23194f1cf2,2319618376,2319c178ac,2319e4295b,
        //   231a7a852b,231aa607de,231ac3eae0,231acdb3cc,231ae3950f,231b28726c,231b78c8da,231ba193cb,231bb47bef,
        //   231c11f9a6,231ca9dd58,231cfd7490,231d312633,231d422526,231d92bb7c,231e47af72,231e587e69,231edaf54c,
        //   231f06beee,231fd594dd,231ff916eb,2320514556,23213d26b9,23214d49c6,23215d5384,232184f641,2321fc9ab9,
        //   232255032b,232255093f,2322a015f9,2322b302bf,2322cdf85f,232319352d,23238d3145,2323bf4798,232408a247,
        //   23241565a6,2324319cb0,23243f988f,2324411a73,2324813ecf,2324acd022,2324e0480e,2325412150,23254b2461,
        //   23254b313f,232551dd90,2325c1afae,2325d3c938,2325edbbab,2325ee1f59,23266ef046,23268f7ce2,2326d611f3,
        //   2326d91480,2326d9dde3,2326e94ba5,2326f713dc,23276e53e3,2327755694,2327baeeec,23285b8fbf,2329180647,
        //   2329491245,232954ab20,2329993d27,232a86a4f5,232b09533b,232b94a679,232ba6d199,232ba79d6a,232bee918c,
        //   232bf5ed2e,232c2732b4,232c57bf00,232c912a8d,232cb0effd,232cdf6a65,232d998b2e,232da2c3c6,232de72072,
        //   232e1372bd,232e7f0c98,232e97fe50,232f296a0c,232fbcc461,232ff5bc50,23302bd196,233033f927,23303a667d,
        //   23303fd852,2330978e05,2330c7b017,2330e7bad9,233187dc27,2332578786,23325a760e,2332628b18,2332a6ebd0,
        //   2332e9e2c2,233336766c,23337964f2,2333abd392,23340b0dc0");
        try {
            transferVodDataProcessorService.doTransferData("499e2943b7");
            TimeUnit.MINUTES.sleep(1000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
    
}