package net.polyv.service.transfer;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.dao.primary.finance.FinanceIncomeRepository;
import net.polyv.model.dto.TransferDataDTO;
import net.polyv.util.DingWarnRobot;
import net.polyv.web.model.stat.QueryStatDataRequest;

/**
 * 转移数据测试
 * <AUTHOR>
 * @date 2022/9/20 16:52
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class TransferDataManagerTest extends TestCase {
    @Resource
    private TransferDataManager transferDataManager;
    @Resource
    private DingWarnRobot dingWarnRobot;
    
    @Resource
    private FinanceIncomeRepository financeIncomeRepository;
    
    @Test
    public void test() {
       /* StatSummaryIncomeRequest statSummaryIncomeRequest = new StatSummaryIncomeRequest();
        statSummaryIncomeRequest.setIncomeMonthEnd("2022-10");
        statSummaryIncomeRequest.setIncomeMonthStart("2022-09");
        statSummaryIncomeRequest.setSoIdList("12,123,12312");
        // 根据创建时间倒序排序
        Sort sort = Sort.by(
                new Sort.Order(Sort.Direction.DESC, FieldUtil.noPrefix(FinanceIncomeDetailEntity::getUpdateTime)));*/
        Pageable pageable = PageRequest.of(0, 100);
        QueryStatDataRequest queryStatDataRequest = new QueryStatDataRequest();
        queryStatDataRequest.setIncomeMonthEnd("2022-10");
        queryStatDataRequest.setIncomeMonthStart("2022-10");
/*        queryStatDataRequest.setBusinessStartTime("2022-01-01");
        queryStatDataRequest.setBusinessEndTime("2022-10-01");
        queryStatDataRequest.setOrderCreateTimeStart("2022-01-01");
        queryStatDataRequest.setOrderCreateTimeEnd("2022-10-01");
       // queryStatDataRequest.setSoIdList(Lists.newArrayList("fsafsdfasdfasdfasdfa"));
        queryStatDataRequest.setSoIdList(Lists.newArrayList("630eede1d11b060001881e82"));
        queryStatDataRequest.setCompanyList(Lists.newArrayList("保利威内测"));
        queryStatDataRequest.setUnionIdList(Lists.newArrayList("54390a07db"));
        queryStatDataRequest.setEmailList(Lists.newArrayList("<EMAIL>"));*/
//        queryStatDataRequest.setContractIdList(Lists.newArrayList("c7108dc4d1"));
//        queryStatDataRequest.setRelationStatusList(Lists.newArrayList(0,1));
//     //   queryStatDataRequest.setProductList(Lists.newArrayList());
//        queryStatDataRequest.setBusinessTypeList(Lists.newArrayList(2,3,4));
        Page<Map<String, Object>> maps = financeIncomeRepository.listStatData(queryStatDataRequest, pageable);
        System.out.println(maps);
    }
    
    @Test
    public void testRefundInvoker() {
        TransferDataDTO transferDataDTO = new TransferDataDTO();
        
        transferDataDTO.setTaskId("RefundTransferDataServiceImpl");
        transferDataManager.invoker(transferDataDTO);
    }
    
    @Test
    public void testCollectedInvoker() {
        TransferDataDTO transferDataDTO = new TransferDataDTO();
      /*  transferDataDTO.setStartTime(DateFormatUtil.parseDate("2012-01-01", DateFormatUtil.FORMAT_DATE_NORMAL));
        transferDataDTO.setEndTime(DateFormatUtil.parseDate("2022-10-19", DateFormatUtil.FORMAT_DATE_NORMAL));*/
        transferDataDTO.setTaskId("CollectMoneyTransferDataServiceImpl");
        transferDataManager.invoker(transferDataDTO);
//        ArrayList<String> objects = new ArrayList<>();
//        objects.add("测试");
//        dingWarnRobot.sendMsgToAtPeople("测试@到人",objects,"18374768874");
    
    }
    
    @Test
    public void testUnFollowChanceInvoker() {
        TransferDataDTO transferDataDTO = new TransferDataDTO();/*
        transferDataDTO.setStartTime(DateFormatUtil.parseDate("2012-01-01", DateFormatUtil.FORMAT_DATE_NORMAL));
        transferDataDTO.setEndTime(DateFormatUtil.parseDate("2022-10-01", DateFormatUtil.FORMAT_DATE_NORMAL));*/
        transferDataDTO.setTaskId("UnFollowChanceTransferDataServiceImpl");
        transferDataManager.invoker(transferDataDTO);
//        ArrayList<String> objects = new ArrayList<>();
//        objects.add("测试");
//        dingWarnRobot.sendMsgToAtPeople("测试@到人",objects,"18374768874");
    
    }
    @Test
    public void testContractTransferInvoker() throws InterruptedException {
        TransferDataDTO transferDataDTO = new TransferDataDTO();
//        transferDataDTO.setStartTime(DateFormatUtil.parseDate("2022-07-25", DateFormatUtil.FORMAT_DATE_NORMAL));
//        transferDataDTO.setEndTime(DateFormatUtil.parseDate("2022-07-30", DateFormatUtil.FORMAT_DATE_NORMAL));
//
        transferDataDTO.setTaskId("ContractTransferDataServiceImpl");
        transferDataManager.invoker(transferDataDTO);
    
    
        // TimeUnit.MINUTES.sleep(60L);
//        ArrayList<String> objects = new ArrayList<>();
//        objects.add("测试");
//        dingWarnRobot.sendMsgToAtPeople("测试@到人",objects,"18374768874");
    
    }
    
    @Test
    public void testIncomeDetailTransferDataInvoker() throws InterruptedException {
        TransferDataDTO transferDataDTO = new TransferDataDTO();
     /*   transferDataDTO.setStartTime(DateFormatUtil.parseDate("2022-07-27 00:00:00", DateFormatUtil
     .FORMAT_DATETIME_NORMAL));
        transferDataDTO.setEndTime(DateFormatUtil.parseDate("2022-07-30 23:59:59", DateFormatUtil
        .FORMAT_DATETIME_NORMAL));
*/
        transferDataDTO.setTaskId("IncomeDetailTransferDataServiceImpl");
        transferDataManager.invoker(transferDataDTO);
    }
    
    @Test
    public void testIncomeTransferDataInvoker() throws InterruptedException {
        TransferDataDTO transferDataDTO = new TransferDataDTO();
     /*   transferDataDTO.setStartTime(DateFormatUtil.parseDate("2022-07-27 00:00:00", DateFormatUtil
     .FORMAT_DATETIME_NORMAL));
        transferDataDTO.setEndTime(DateFormatUtil.parseDate("2022-07-30 23:59:59", DateFormatUtil
        .FORMAT_DATETIME_NORMAL));
*/
        transferDataDTO.setTaskId("IncomeTransferDataServiceImpl");
        transferDataManager.invoker(transferDataDTO);
    }
    
    @Test
    public void testAll() throws InterruptedException {
        transferDataManager.transferAll();
        TimeUnit.MINUTES.sleep(60L);
    }
    
    @Test
    public void testAgain() {
        transferDataManager.againTryInvoker(1L);
    }
    @Test
    public void testVodInvoker() {
        TransferDataDTO transferDataDTO = new TransferDataDTO();
        
        transferDataDTO.setTaskId("VodSpaceTrafficTransferServiceImpl");
        transferDataManager.invoker(transferDataDTO);
    }
    
    
    
    
}