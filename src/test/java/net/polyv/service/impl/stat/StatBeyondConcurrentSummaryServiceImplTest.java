package net.polyv.service.impl.stat;

import java.time.LocalDate;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.stat.StatBeyondConcurrentRequest;
import net.polyv.modules.pcs.api.vo.stat.StatBeyondConcurrentVO;
import net.polyv.service.stat.StatBeyondConcurrentSummaryService;

/**
 * 并发测试
 * <AUTHOR>
 * @date 2022/8/30 18:07
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class StatBeyondConcurrentSummaryServiceImplTest extends TestCase {
    @Resource
    private StatBeyondConcurrentSummaryService statBeyondConcurrentSummaryService;
    
    @Test
    public void testBeyondConcurrent() {
        StatBeyondConcurrentRequest request = new StatBeyondConcurrentRequest();
        request.setConcurrentStartTime(LocalDate.now());
        request.setConcurrentEndTime(LocalDate.now());
        //    request.setCompanyList("广州,易方");
        //  request.setEmailList("<EMAIL>,<EMAIL>");
        //    request.setUnionIdList("1fasdfa,2sersfa");
        //    request.setContractIdList("1sadfasd,2sadfshfhsd");
        request.setConcurrentTypeList("1,2");
        request.setIsLimitConcurrent(null);
        Pager<StatBeyondConcurrentVO> statBeyondConcurrentVOPager =
                this.statBeyondConcurrentSummaryService.beyondConcurrent(
                request);
        
        
    }
}