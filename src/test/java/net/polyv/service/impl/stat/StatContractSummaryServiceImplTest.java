package net.polyv.service.impl.stat;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.pcs.api.req.stat.StatBusinessSummaryRequest;
import net.polyv.modules.pcs.api.vo.stat.StatBusinessSummaryVO;

/**
 * 合同汇总服务实现测试
 * <AUTHOR>
 * @date 2022/8/30 18:06
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class StatContractSummaryServiceImplTest extends TestCase {
    @Resource
    private  StatContractSummaryServiceImpl statContractSummaryService;
    @Test
    public void testContractSummary() {
    }
    
    @Test
    public void testIncomeSummary() {
    }
    
    @Test
    public void testBusinessSummary() {
        StatBusinessSummaryRequest statBusinessSummaryRequest = new StatBusinessSummaryRequest();
        statBusinessSummaryRequest.setContractIdList("ZqMVbE7d6M");
        Pager<StatBusinessSummaryVO> statBusinessSummaryVOPager = this.statContractSummaryService.businessSummary(
             statBusinessSummaryRequest);
    
        System.out.println(JsonUtil.beanToString(statBusinessSummaryVOPager));
    }
}