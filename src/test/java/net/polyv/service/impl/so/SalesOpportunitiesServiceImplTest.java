
package net.polyv.service.impl.so;

import com.alibaba.excel.util.DateUtils;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.SysTypeConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.salesopportunities.SalesOpportunitiesBusinessTypeConst;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.model.data.GlobalUserInfoDO;
import net.polyv.model.data.salesopportunities.CrmSalesOpportunitiesSearchDO;
import net.polyv.model.data.salesopportunities.UserFunctionDO;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.rest.client.dingding.SalesOpportApprovalDingDingClient;
import net.polyv.rest.client.live.UserClient;
import net.polyv.rest.client.vod.user.UnionUserClient;
import net.polyv.rest.model.live.UserFunctionVO;
import net.polyv.rest.model.vod.user.CustomerSearchDTO;
import net.polyv.rest.model.vod.user.UserDetail;
import net.polyv.rest.service.dingding.DingUserService;
import net.polyv.service.crm.CrmSaleOpportService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.item.UnivalenceItemService;
import net.polyv.service.so.SalesOpportEstablishBusinessService;
import net.polyv.service.so.SalesOpportunitiesService;
import net.polyv.util.DateUtil;
import net.polyv.util.JsonMapper;
import net.polyv.util.UserInfoThreadLocal;
import net.polyv.web.model.account.GetCustomerStateInputVO;
import net.polyv.web.model.salesopportunities.input.*;
import net.polyv.web.model.salesopportunities.result.SalesOpportunitiesAddFunctionListResultVO;
import net.polyv.web.model.salesopportunities.result.SalesOpportunitiesGetResultVO;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static net.polyv.constant.salesopportunities.SalesOpportunitiesBusinessTypeConst.adjust_bill;
import static net.polyv.constant.salesopportunities.SalesOpportunitiesBusinessTypeConst.adjust_credit;
import static net.polyv.constant.salesopportunities.SalesOpportunitiesBusinessTypeConst.amount;
import static org.junit.Assert.fail;

/**
 * 销售机会 代码逻辑单元测试
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/12
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class SalesOpportunitiesServiceImplTest {
    @Autowired
    private SalesOpportunitiesService salesOpportunitiesService;
    @Autowired
    private SalesOpportEstablishBusinessService salesOpportEstablishBusinessService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private UnionUserClient unionUserClient;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private CrmSaleOpportService crmSaleOpportService;
    
    @Autowired
    private SalesOpportApprovalDingDingClient salesOpportApprovalDingDingClient;
    
    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    
    @Autowired
    private DingUserService dingUserService;
    
    @Autowired
    private UnivalenceItemService univalenceItemService;
    
    @Test
    public void testUpdateSalesOpportunities_groupUserNotAllowUseNewBusiness() {
        
        List<SalesOpportunitiesBusinessTypeConst> newBusinessTypeList = Arrays.asList(adjust_credit, amount,
                adjust_bill);
        SalesOpportunitiesUpdateInputVO inputVO = new SalesOpportunitiesUpdateInputVO();
        
        for (SalesOpportunitiesBusinessTypeConst type : newBusinessTypeList) {
            inputVO.setBusinessType(type.getBusinessType());
            inputVO.setIsGroupUser(1);
            try {
                salesOpportunitiesService.updateSalesOpportunities(inputVO);
            } catch (ClearingSystemRuntimeException e) {
                if (e.getMessage().contains("不允许")) {
                    continue;
                }
            }
            fail("未抛出异常");
        }
    }

    /**
     * 获取频道号测试
     */
    @Test
    public void getChannelCountsTest() {
        int count = salesOpportunitiesService.getTotalChannelsByEmail("<EMAIL>");
        System.out.println("channel counts is:" + count);
    }

    /**
     * 添加频道号测试
     */
    @Test
    public void addChannelTest() {
        SalesOpportunitiesUpdateExtChannelInputVO vo = new SalesOpportunitiesUpdateExtChannelInputVO();
        vo.setChannel(7);
        vo.setTotalPrice(new BigDecimal(13));
        vo.setId(22311L);
        salesOpportunitiesService.channelAdd(vo);
    }

    /**
     * 销售机会详情测试
     */
    @Test
    public void saleOpportunitiesDetailTest() {
        SalesOpportunitiesGetInputVO inputVO = new SalesOpportunitiesGetInputVO();
        String functionListQuery =
                "[{\"code\":\"API\",\"sys\":\"Vod\"},{\"code\":\"viewerIdStatsEnabled\",\"sys\":\"Live\"},{\"code\":\"showSdStatsEnabled\",\"sys\":\"Live\"},{\"code\":\"doubleTeacherEnabled\",\"sys\":\"Live\"},{\"code\":\"weixinAccountFunctionEnabled\",\"sys\":\"Live\"},{\"code\":\"pureRtcEnabled\",\"sys\":\"Live\"},{\"code\":\"topclassMaxRate\",\"sys\":\"Live\"},{\"code\":\"pptMaxRate\",\"sys\":\"Live\"},{\"code\":\"aloneMaxRate\",\"sys\":\"Live\"},{\"code\":\"scenes\",\"sys\":\"Live\"},{\"code\":\"transmitEnabled\",\"sys\":\"Live\"},{\"code\":\"footerSettingEnabled\",\"sys\":\"Live\"},{\"code\":\"tuwenLiveEnabled\",\"sys\":\"Live\"},{\"code\":\"diskPushEnabled\",\"sys\":\"Live\"},{\"code\":\"portalAccessEnabled\",\"sys\":\"Live\"},{\"code\":\"liveProductEnabled\",\"sys\":\"Live\"},{\"code\":\"popularizationEnabled\",\"sys\":\"Live\"},{\"code\":\"userChildrenEnabled\",\"sys\":\"Live\"},{\"code\":\"childMaxNum\",\"sys\":\"Live\"},{\"code\":\"childRoleMaxNum\",\"sys\":\"Live\"},{\"code\":\"organizationMaxNum\",\"sys\":\"Live\"},{\"code\":\"cnAndEnLiveEnabled\",\"sys\":\"Live\"},{\"code\":\"showApiSetting\",\"sys\":\"Live\"},{\"code\":\"chatTranslateEnabled\",\"sys\":\"Live\"},{\"code\":\"liveEvaluationEnabled\",\"sys\":\"Live\"},{\"code\":\"multirateEnabled\",\"sys\":\"Live\"},{\"code\":\"polyvSmsEnabled\",\"sys\":\"Live\"},{\"code\":\"beautyEnabled\",\"sys\":\"Live\"},{\"code\":\"maxGuestAccountSize\",\"sys\":\"Live\"},{\"code\":\"multiplexingEnabled\",\"sys\":\"Live\"},{\"code\":\"colinMicType\",\"sys\":\"Live\"},{\"code\":\"linkMicLimit\",\"sys\":\"Live\"},{\"code\":\"linkMicType\",\"sys\":\"Live\"},{\"code\":\"rtcMaxResolution\",\"sys\":\"Live\"},{\"code\":\"rtcType\",\"sys\":\"Live\"},{\"code\":\"checkinEnabled\",\"sys\":\"Live\"},{\"code\":\"lotteryEnabled\",\"sys\":\"Live\"},{\"code\":\"questionnaireEnabled\",\"sys\":\"Live\"},{\"code\":\"answerEnabled\",\"sys\":\"Live\"},{\"code\":\"raceAnswerEnabled\",\"sys\":\"Live\"},{\"code\":\"qaMenuEnabled\",\"sys\":\"Live\"},{\"code\":\"timerEnabled\",\"sys\":\"Live\"},{\"code\":\"redpackRainSkin\",\"sys\":\"Live\"},{\"code\":\"enrollLotteryEnabled\",\"sys\":\"Live\"},{\"code\":\"redPackRainEnabled\",\"sys\":\"Live\"},{\"code\":\"pptRecordEnabled\",\"sys\":\"Live\"},{\"code\":\"convertEncodeEnabled\",\"sys\":\"Live\"},{\"code\":\"recordMp4Enabled\",\"sys\":\"Live\"},{\"code\":\"dockerRecordEnabled\",\"sys\":\"Live\"},{\"code\":\"hd1080pRecordEnabled\",\"sys\":\"Live\"},{\"code\":\"pptAnimationEnabled\",\"sys\":\"Live\"},{\"code\":\"smallClassMenuEnabled\",\"sys\":\"SmallClass\"},{\"code\":\"scenes\",\"sys\":\"Meeting\"}]";
        inputVO.setId(37001L);
        inputVO.setFunctionListQuery(functionListQuery);
        SalesOpportunitiesGetResultVO salesOpportunities = salesOpportunitiesService.getSalesOpportunities(inputVO);
        Assert.assertNotNull(salesOpportunities);
        System.out.println("totalChannelCounts is :" + salesOpportunities.getTotalChannelCounts());
    }

    /**
     * 添加销售机会测试
     */
    @Test
    public void addSaleOpportunitiesTest() {
        GlobalUserInfoDO userInfoDO = new GlobalUserInfoDO();
        userInfoDO.setUserId("1111");
        userInfoDO.setUserIp("127.0.0.1");
        UserInfoThreadLocal.setGlobalUserInfoDO(userInfoDO);
        SalesOpportunitiesSaveInputVO inputVO = new SalesOpportunitiesSaveInputVO();
        inputVO.setCustomerId("d8c6faded1");
        inputVO.setSummary("test");
        inputVO.setContractType("无合同");
        inputVO.setSaleUserId("TVhQQA==");
        inputVO.setSaleUserName("黄裕丰");
        inputVO.setCompany("广州易方信息科技股份优先公司");
        inputVO.setEmail("<EMAIL>");
        inputVO.setBusinessType(15);
        inputVO.setIntroduction("1111");
        inputVO.setIsGroupUser(0);
        salesOpportunitiesService.saveSalesOpportunities(inputVO);
    }

    //销售机会开通测试
    @Test
    public void establishTest() {
        GlobalUserInfoDO userInfoDO = new GlobalUserInfoDO();
        userInfoDO.setUserId("1111");
        userInfoDO.setUserIp("127.0.0.1");
        UserInfoThreadLocal.setGlobalUserInfoDO(userInfoDO);
        SalesOpportEstablishInputVO inputVO = new SalesOpportEstablishInputVO();
        inputVO.setSalesOpportunitiesId(28295L);
        inputVO.setCustomerId("baf4ad57f6");
        inputVO.setContractId("abcd1234");
        salesOpportEstablishBusinessService.establishBusiness(inputVO);
    }
    //旧业务销售机会开通测试(财务系统操作开通)
    @Test
    public void oldBusinessEstablishTest() {
        GlobalUserInfoDO userInfoDO = new GlobalUserInfoDO();
        userInfoDO.setUserId("1111");
        userInfoDO.setUserIp("127.0.0.1");
        UserInfoThreadLocal.setGlobalUserInfoDO(userInfoDO);
        OldSalesOpportEstablishInputVO inputVO = new OldSalesOpportEstablishInputVO();
        inputVO.setSoId("z9ThnjTrx3A63ialN2cF");
        inputVO.setContractId("QT5CwNSqqS");
        salesOpportEstablishBusinessService.establishOldBusiness(inputVO);
    }
    /**
     * 账号概览功能开关
     */
    @Test
    public void accountFunctionList() {
        String liveUserId = "d82ee148ba";
        String s = "[{\"code\":\"API\",\"sys\":\"Vod\"},{\"code\":\"viewerIdStatsEnabled\",\"sys\":\"Live\"},{\"code\":\"showSdStatsEnabled\",\"sys\":\"Live\"},{\"code\":\"doubleTeacherEnabled\",\"sys\":\"Live\"},{\"code\":\"weixinAccountFunctionEnabled\",\"sys\":\"Live\"},{\"code\":\"pureRtcEnabled\",\"sys\":\"Live\"},{\"code\":\"topclassMaxRate\",\"sys\":\"Live\"},{\"code\":\"pptMaxRate\",\"sys\":\"Live\"},{\"code\":\"aloneMaxRate\",\"sys\":\"Live\"},{\"code\":\"scenes\",\"sys\":\"Live\"},{\"code\":\"transmitEnabled\",\"sys\":\"Live\"},{\"code\":\"footerSettingEnabled\",\"sys\":\"Live\"},{\"code\":\"tuwenLiveEnabled\",\"sys\":\"Live\"},{\"code\":\"diskPushEnabled\",\"sys\":\"Live\"},{\"code\":\"portalAccessEnabled\",\"sys\":\"Live\"},{\"code\":\"liveProductEnabled\",\"sys\":\"Live\"},{\"code\":\"popularizationEnabled\",\"sys\":\"Live\"},{\"code\":\"userChildrenEnabled\",\"sys\":\"Live\"},{\"code\":\"childMaxNum\",\"sys\":\"Live\"},{\"code\":\"childRoleMaxNum\",\"sys\":\"Live\"},{\"code\":\"organizationMaxNum\",\"sys\":\"Live\"},{\"code\":\"cnAndEnLiveEnabled\",\"sys\":\"Live\"},{\"code\":\"showApiSetting\",\"sys\":\"Live\"},{\"code\":\"chatTranslateEnabled\",\"sys\":\"Live\"},{\"code\":\"liveEvaluationEnabled\",\"sys\":\"Live\"},{\"code\":\"multirateEnabled\",\"sys\":\"Live\"},{\"code\":\"polyvSmsEnabled\",\"sys\":\"Live\"},{\"code\":\"beautyEnabled\",\"sys\":\"Live\"},{\"code\":\"maxGuestAccountSize\",\"sys\":\"Live\"},{\"code\":\"multiplexingEnabled\",\"sys\":\"Live\"},{\"code\":\"colinMicType\",\"sys\":\"Live\"},{\"code\":\"linkMicLimit\",\"sys\":\"Live\"},{\"code\":\"linkMicType\",\"sys\":\"Live\"},{\"code\":\"rtcMaxResolution\",\"sys\":\"Live\"},{\"code\":\"rtcType\",\"sys\":\"Live\"},{\"code\":\"checkinEnabled\",\"sys\":\"Live\"},{\"code\":\"lotteryEnabled\",\"sys\":\"Live\"},{\"code\":\"questionnaireEnabled\",\"sys\":\"Live\"},{\"code\":\"answerEnabled\",\"sys\":\"Live\"},{\"code\":\"raceAnswerEnabled\",\"sys\":\"Live\"},{\"code\":\"qaMenuEnabled\",\"sys\":\"Live\"},{\"code\":\"timerEnabled\",\"sys\":\"Live\"},{\"code\":\"redpackRainSkin\",\"sys\":\"Live\"},{\"code\":\"enrollLotteryEnabled\",\"sys\":\"Live\"},{\"code\":\"redPackRainEnabled\",\"sys\":\"Live\"},{\"code\":\"pptRecordEnabled\",\"sys\":\"Live\"},{\"code\":\"convertEncodeEnabled\",\"sys\":\"Live\"},{\"code\":\"recordMp4Enabled\",\"sys\":\"Live\"},{\"code\":\"dockerRecordEnabled\",\"sys\":\"Live\"},{\"code\":\"hd1080pRecordEnabled\",\"sys\":\"Live\"},{\"code\":\"pptAnimationEnabled\",\"sys\":\"Live\"},{\"code\":\"smallClassMenuEnabled\",\"sys\":\"SmallClass\"},{\"code\":\"scenes\",\"sys\":\"Meeting\"}]";
        List<UserFunctionDO> functionVOList = JsonMapper.stringToComplicatedObject(s, List.class, UserFunctionDO.class);
        List<UserFunctionVO> list = customerService.getUserFunctionList(liveUserId, functionVOList);
        Assert.assertNotNull(list);
        System.out.println(JsonMapper.jsonToString(list));
    }

    /**
     * 销售机会列表功能开关
     */
    @Test
    public void salesOpportunitiesFunctionList() {
        SalesOpportunitiesGetInputVO vo = new SalesOpportunitiesGetInputVO();
        String functionListQuery = "[{\"code\":\"VideoQAndA\",\"sys\":\"Vod\"},{\"code\":\"InfoCollector\",\"sys\":\"Vod\"},{\"code\":\"PPTPlayer\",\"sys\":\"Vod\"},{\"code\":\"API\",\"sys\":\"Vod\"},{\"code\":\"SubAccount\",\"sys\":\"Vod\"},{\"code\":\"VideoAd\",\"sys\":\"Vod\"},{\"code\":\"VideoSlice\",\"sys\":\"Vod\"},{\"code\":\"AudioPlay\",\"sys\":\"Vod\"},{\"code\":\"PublishBeforeAudit\",\"sys\":\"Vod\"},{\"code\":\"MultiTerminal\",\"sys\":\"Vod\"},{\"code\":\"PlayPassword\",\"sys\":\"Vod\"},{\"code\":\"VideoEncryption\",\"sys\":\"Vod\"},{\"code\":\"PlayDomain\",\"sys\":\"Vod\"},{\"code\":\"VoiceprintMarquee\",\"sys\":\"Vod\"},{\"code\":\"PrivacyTraceAnalyzeStats\",\"sys\":\"Vod\"},{\"code\":\"Playlist\",\"sys\":\"Vod\"},{\"code\":\"QRCode\",\"sys\":\"Vod\"},{\"code\":\"VideoBarrage\",\"sys\":\"Vod\"},{\"code\":\"VideoCata\",\"sys\":\"Vod\"},{\"code\":\"Subtitles\",\"sys\":\"Vod\"},{\"code\":\"smallClassMenuEnabled\",\"sys\":\"SmallClass\"},{\"code\":\"recordResolution\",\"sys\":\"SmallClass\"},{\"code\":\"RightClickMenu\",\"sys\":\"Vod\"},{\"code\":\"SponsoredLinks\",\"sys\":\"Vod\"},{\"code\":\"MultiLanguagePlayer\",\"sys\":\"Vod\"},{\"code\":\"scenes\",\"sys\":\"Meeting\"},{\"code\":\"transmitEnabled\",\"sys\":\"Live\"},{\"code\":\"footerSettingEnabled\",\"sys\":\"Live\"},{\"code\":\"diskPushEnabled\",\"sys\":\"Live\"},{\"code\":\"portalAccessEnabled\",\"sys\":\"Live\"},{\"code\":\"liveProductEnabled\",\"sys\":\"Live\"},{\"code\":\"userChildrenEnabled\",\"sys\":\"Live\"},{\"code\":\"childMaxNum\",\"sys\":\"Live\"},{\"code\":\"childRoleMaxNum\",\"sys\":\"Live\"},{\"code\":\"organizationMaxNum\",\"sys\":\"Live\"},{\"code\":\"showApiSetting\",\"sys\":\"Live\"},{\"code\":\"chatTranslateEnabled\",\"sys\":\"Live\"},{\"code\":\"liveEvaluationEnabled\",\"sys\":\"Live\"},{\"code\":\"multirateEnabled\",\"sys\":\"Live\"},{\"code\":\"polyvSmsEnabled\",\"sys\":\"Live\"},{\"code\":\"beautyEnabled\",\"sys\":\"Live\"},{\"code\":\"pptAnimationEnabled\",\"sys\":\"Live\"},{\"code\":\"interactiveGameEnabled\",\"sys\":\"Live\"},{\"code\":\"distributeModifyEnabled\",\"sys\":\"Live\"},{\"code\":\"aiVirtualBgEnabled\",\"sys\":\"Live\"},{\"code\":\"maskPptEnabled\",\"sys\":\"Live\"},{\"code\":\"cktEnabled\",\"sys\":\"Live\"},{\"code\":\"microActivityEnabled\",\"sys\":\"Live\"},{\"code\":\"microActivitySettingEntryEnabled\",\"sys\":\"Live\"},{\"code\":\"distributeConcurrentLimit\",\"sys\":\"Live\"},{\"code\":\"tuwenLiveEnabled\",\"sys\":\"Live\"},{\"code\":\"popularizationEnabled\",\"sys\":\"Live\"},{\"code\":\"cnAndEnLiveEnabled\",\"sys\":\"Live\"},{\"code\":\"showSdStatsEnabled\",\"sys\":\"Live\"},{\"code\":\"doubleTeacherEnabled\",\"sys\":\"Live\"},{\"code\":\"weixinAccountFunctionEnabled\",\"sys\":\"Live\"},{\"code\":\"pureRtcEnabled\",\"sys\":\"Live\"},{\"code\":\"aloneMaxRate\",\"sys\":\"Live\"},{\"code\":\"scenes\",\"sys\":\"Live\"},{\"code\":\"userLiveBgType\",\"sys\":\"Live\"},{\"code\":\"newGuideEnabled\",\"sys\":\"Live\"},{\"code\":\"userAssistantType\",\"sys\":\"Live\"},{\"code\":\"rtcAudioSubEnabled\",\"sys\":\"Live\"},{\"code\":\"staticPptEncodeEnabled\",\"sys\":\"Live\"},{\"code\":\"pullStreamEnabled\",\"sys\":\"Live\"},{\"code\":\"channelBasicUpdateCallbackEnabled\",\"sys\":\"Live\"},{\"code\":\"watchCustomWeixinAuthEnabled\",\"sys\":\"Live\"},{\"code\":\"pptMaxRate\",\"sys\":\"Live\"},{\"code\":\"userIdentityVerificationEnabled\",\"sys\":\"Live\"},{\"code\":\"pptRecordEnabled\",\"sys\":\"Live\"},{\"code\":\"convertEncodeEnabled\",\"sys\":\"Live\"},{\"code\":\"recordMp4Enabled\",\"sys\":\"Live\"},{\"code\":\"dockerRecordEnabled\",\"sys\":\"Live\"},{\"code\":\"hd1080pRecordEnabled\",\"sys\":\"Live\"},{\"code\":\"maxGuestAccountSize\",\"sys\":\"Live\"},{\"code\":\"multiplexingEnabled\",\"sys\":\"Live\"},{\"code\":\"colinMicType\",\"sys\":\"Live\"},{\"code\":\"linkMicLimit\",\"sys\":\"Live\"},{\"code\":\"linkMicType\",\"sys\":\"Live\"},{\"code\":\"rtcMaxResolution\",\"sys\":\"Live\"},{\"code\":\"rtcType\",\"sys\":\"Live\"},{\"code\":\"checkinEnabled\",\"sys\":\"Live\"},{\"code\":\"lotteryEnabled\",\"sys\":\"Live\"},{\"code\":\"questionnaireEnabled\",\"sys\":\"Live\"},{\"code\":\"answerEnabled\",\"sys\":\"Live\"},{\"code\":\"raceAnswerEnabled\",\"sys\":\"Live\"},{\"code\":\"qaMenuEnabled\",\"sys\":\"Live\"},{\"code\":\"timerEnabled\",\"sys\":\"Live\"},{\"code\":\"redpackRainSkin\",\"sys\":\"Live\"},{\"code\":\"enrollLotteryEnabled\",\"sys\":\"Live\"},{\"code\":\"redPackRainEnabled\",\"sys\":\"Live\"},{\"code\":\"startSideRedpackEnabled\",\"sys\":\"Live\"},{\"code\":\"startSideWordRedpackEnabled\",\"sys\":\"Live\"},{\"code\":\"redpackRainSkinCustomEnabled\",\"sys\":\"Live\"},{\"code\":\"startSideRedpackSkinEnabled\",\"sys\":\"Live\"},{\"code\":\"odmEnabled\",\"sys\":\"GroupV2\"},{\"code\":\"resourceLimit\",\"sys\":\"GroupV2\"}]";
        vo.setId(29155L);
        vo.setFunctionListQuery(functionListQuery);
        SalesOpportunitiesAddFunctionListResultVO functionList = salesOpportunitiesService.functionList(vo);
        Assert.assertNotNull(functionList);
        System.out.println("functionList====" + JsonMapper.jsonToString(functionList));
    }

    @Test
    public void groupFunctionListTest(){
        String groupId = "123aaa";
        SysTypeConst sys = SysTypeConst.GroupV2;
        List<String> codes = Lists.newArrayList("GroupV2_pptRecordEnabled", "GroupV2_odmEnabled",
                "GroupV2_distributeModifyEnabled", "GroupV2_1321321");
        List<UserFunctionVO> list = customerService.getGroupV2FunctionList(groupId, codes, sys);
        System.out.println("functionList====" + JsonMapper.jsonToString(list));
    }

    /**
     * 销售机会功能开关更新
     */
    @Test
    public void setSalesOpportunitiesFunctionUpdate() {
        SalesOpportunitiesUpdateExtAddFunctionListInputVO vo = new SalesOpportunitiesUpdateExtAddFunctionListInputVO();
        String functionListQuery = "[" +
                "{\"code\":\"linkMicLimit\",\"codeName\":\"连麦人数\",\"value\":\"8\",\"valueName\":\"8\",\"sys\":\"Live\"}" +
                ",{\"code\":\"API\",\"codeName\":\"API功能\",\"value\":\"N\",\"valueName\":\"\",\"sys\":\"Vod\",\"extraValue\":\"123\"}" +
                "]";
        vo.setId(22427L);
        vo.setFunctionList(functionListQuery);
        int count = salesOpportunitiesService.updateFunctionList(vo);
    }

    @Test
    public void getSingleUserByParamTest() {
        UserClient.getUserByUnionId("d82ee148ba");
    }

    @Test
    public void test() {
        List<String> userIds = Lists.newArrayList();
        userIds.add("ff59d3d588");
        Map<String, String> liveUnionIdMap = customerService.getLiveUnionIdMap(userIds);
        CustomerSearchDTO searchDTO = new CustomerSearchDTO();
        searchDTO.setUnionIds("d82ee148ba");
        List<UserDetail> detailList = unionUserClient.listCustomer(searchDTO).getData();
        System.out.println(detailList != null ? JsonMapper.jsonToString(detailList) : null);
    }

    @Test
    public void testRedisSet() {
        ValueOperations<String, String> redisVo = this.redisTemplate.opsForValue();
        // redisVo.set(GlobalConfigConst.PCS_SYNC_CRM_COVER_CREATE_TIME,"123");
        String s = redisVo.get(GlobalConfigConst.PCS_SYNC_CRM_COVER_CREATE_TIME);
        System.out.println(s);
    }

    @Test
    public void testSyncFromCrm() {
        CrmSalesOpportunitiesSearchDO searchDO = new CrmSalesOpportunitiesSearchDO();
        searchDO.setStatus(2);
        try {
            searchDO.setStartCreateDate(DateUtil.date2LocalDate(DateUtils.parseDate("2021-09-07 00:00:00")));
            searchDO.setEndCreateDate(DateUtil.date2LocalDate(DateUtils.parseDate("2021-09-07 23:59:59")));
        } catch (ParseException e) {
        }
        this.crmSaleOpportService.runCrmSaleOpportunitiesSyncTask(searchDO);
    }

    @Test
    public void dingdingApprove(){
        SalesOpportunities so = salesOpportunitiesRepository.findById(22891L)
                .orElseThrow(() -> new ClearingSystemRuntimeException("获取销售机会失败"));
    
        String dingUserId = "123";
        salesOpportApprovalDingDingClient.createApprovalInstance(so, dingUserId);
    }
    
    @Test
    public void testUpdateBillingItemJson() {
        SalesOpportunities salesOpportunities = salesOpportunitiesRepository.findBySoId("627a11a4df31b3000189ee11");
        String billingItemJson = "[{\"itemId\":33,\"baseItemId\":null,\"code\":\"prtc_china_pd\"," +
                "\"name\":\"无延迟国内观看时长\",\"scaleCode\":null,\"univalence\":\"6000\",\"univalenceUnit\":\"元/分钟\"," +
                "\"itemConsumedUnit\":\"分钟\",\"ratio\":100,\"scaleLte\":null,\"scaleGte\":null,\"production\":\"直播\"," +
                "\"category\":\"直播分钟数\",\"option\":\"直播分钟数\",\"_X_ROW_KEY\":\"row_35\",\"newUnivalence\":\"0.06\"}]";
        salesOpportunitiesService.transferBillingItemJsonForExtObjectDO(salesOpportunities.getExtObjectDO(),
                billingItemJson, salesOpportunities.getCustomerId());
    }
    
    @Test
    public void priceGetTest() {
        GetCustomerStateInputVO inputVO = new GetCustomerStateInputVO();
        inputVO.setAccountType(AccountTypeEnum.GROUP2.getCode());
        inputVO.setGroupId("0a564a07fb");
        univalenceItemService.getUserBillingItems(inputVO);
    }
    
}