package net.polyv.service.impl.so;

import org.apache.commons.lang.StringUtils;

import junit.framework.TestCase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.polyv.modules.common.constant.Constant;

/**
 * 测试
 * <AUTHOR>
 * @date 2022/10/11 11:59
 */

public class BusinessOrderServiceImplTest  {
    public static void main(String[] args) {
        String  livePackage="";
        String  vodPackage="";
        if (StringUtils.isNotEmpty(getPackageName())){
            String[] split = getPackageName().split(Constant.COMMA);
            if (split.length == 2){
                livePackage = split[0];
                vodPackage = split[1];
            }
        }
        System.out.println(livePackage + vodPackage);
    }
    
    private static String getPackageName() {
        return "专享套餐（5万分钟数）,个人版";
    }
}