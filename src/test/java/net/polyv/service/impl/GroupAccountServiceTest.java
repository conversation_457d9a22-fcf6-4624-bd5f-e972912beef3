package net.polyv.service.impl;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.service.GroupAccountEstablishPackageService;
import net.polyv.service.so.BusinessOperationService;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class GroupAccountServiceTest {
    
    @Resource
    private BusinessOperationService businessOperationService;
    @Autowired
    private GroupAccountEstablishPackageService groupAccountEstablishPackageService;
    
    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    
    @Test
    public void testEnabledFunction() {
        SalesOpportunities salesOpportunities = salesOpportunitiesRepository.findBySoId("polyv1661757919847");
        groupAccountEstablishPackageService.enabledFunctionAccess(salesOpportunities);
    }
    
    @Test
    public void testSetGroupResourceAssign() {
        String groupId = "0a564a07fb";
        Boolean resourceLimit = true;
        businessOperationService.setGroupResourceAssignLimit(groupId, resourceLimit);
    }
}
