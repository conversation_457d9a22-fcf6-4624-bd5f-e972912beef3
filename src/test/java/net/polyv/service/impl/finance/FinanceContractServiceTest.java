package net.polyv.service.impl.finance;

import net.polyv.common.CommonResult;
import net.polyv.constant.finance.ContractOperationCodeEnum;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.business.BusinessPackageSpecificationInfo;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.finance.FinanceContractEntity;
import net.polyv.modules.pcs.api.vo.finance.FinanceContractInfoVO;
import net.polyv.rest.model.finance.AddContractResultVO;
import net.polyv.rest.model.finance.PreOpenAndMonthlyContractSpecResultVO;
import net.polyv.service.FinanceIncomeConfirmService;
import net.polyv.service.business.BusinessPackageSpecificationService;
import net.polyv.service.finance.FinanceContractOperationLogService;
import net.polyv.service.finance.FinanceContractService;
import net.polyv.service.finance.FinanceIncomeService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.DingWarnRobot;
import net.polyv.util.RandomUtils;
import net.polyv.web.model.finance.input.AddAmountContractDTO;
import net.polyv.web.model.finance.input.AddContractOperationLogDTO;
import net.polyv.web.model.finance.input.AddOpenContractDTO;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: Neo
 * @date: 2022-08-18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles(value = "dev")
public class FinanceContractServiceTest {

    @Autowired
    private FinanceContractService financeContractService;

    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;

    @Autowired
    private FinanceIncomeService financeIncomeService;

    @Autowired
    private FinanceContractOperationLogService logService;

    @Autowired
    private BusinessPackageSpecificationService businessPackageSpecificationService;

    @Resource
    private DingWarnRobot dingWarnRobot;

    @Autowired
    private FinanceIncomeConfirmService financeIncomeConfirmService;

    @Test
    public void testAddOpenContract() {
        String orderNo = "polyv1661935257515";
        AddOpenContractDTO aoc = new AddOpenContractDTO();
        SalesOpportunities so = salesOpportunitiesRepository.findBySoId(orderNo);
        aoc.setSoId(orderNo);
        aoc.setContractId("contract_id");
        aoc.setPayBackStatus(1);
        this.financeContractService.addOpenBusinessContract(aoc);
    }

    @Test
    public void testConfirmIncome() {
        try {
            this.financeIncomeService.statistics(DateFormatUtil.parseDateNormal("2023-08-15"));
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testLiveFlowConfirmIncome() {
        this.financeIncomeService.confirmLiveFlowIncomeAfterSpecifyDate(DateFormatUtil.parseDateNormal("2023-05-20"));
    }

    @Test
    public void testConfirmIncome3() {
        /*try {
            Date date = DateFormatUtil.parseDateNormal("2022-08-15");
            this.financeIncomeService.statisticsMicDuration(date);
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }*/
        dingWarnRobot.sendMsgToAtPeople("同步待支付账单收入", com.google.common.collect.Lists.newArrayList("prprprprpr"), "+86-***********");
    }

    @Test
    public void testConfirmIncome4() {

        Date date = DateFormatUtil.parseDateNormal("2022-11-15");
        this.financeIncomeService.statisticsSpecialStage(date);

    }

    @Test
    public void testSpecifyDate() {
        Date specifyDate = DateFormatUtil.parseDateNormal("2022-08-01");
        // this.financeContractService.calcYearlyPackagePeriod(DateFormatUtil.parseDateNormal("2022-08-25"), DateFormatUtil.parseDateNormal("2025-08-25"), 12);
        // Date date = DateFormatUtil.parseDateNormal("2022-08-25");
        // String s = DateFormatUtil.formatDateNormal(DateUtils.addDays(DateUtils.addMonths(date, 12),-1));
        Date currentDate = DateUtil.getFirstDayOfPreviousMonth(specifyDate);
        String currentMonth = DateFormatUtil.formatDateMonth(currentDate);

        // 当月的第二天和下一个月的第一天（出账日期是隔天出账）
        // Date currentFirstDate = DateUtil.getSecondDayOfMonth(currentDate);
        // Date currentEndDate = DateUtil.getDateEnd(DateUtil.getFirstDayOfXMonth(currentDate, 1));
        // Date currentEndDate2 = DateUtil.getEndOfDay(DateUtil.getFirstDayOfXMonth(currentDate, 1));
        // System.out.println(currentEndDate);
        // System.out.println(currentEndDate2);
        String month = "2022-06";
        String s = DateFormatUtil.formatDateMonth(DateUtil.getAddMonthDate(DateFormatUtil.parseDateMonthNormal(month), -1));
        Date date = DateFormatUtil.parseDateMonthNormal(month);
        List<Date> dlist = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            DateUtil.getAddMonthDate(currentDate, i);
        }
        System.out.println(date);
        System.out.println(s);
    }

    @Test
    public void testGetPreOpenOrMonthlyContractSpec() {
        try {
            CommonResult<List<PreOpenAndMonthlyContractSpecResultVO>> preOpenOrMonthlyContractSpecResult =
                    this.financeContractService.getPreOpenOrMonthlyContractSpec(Lists.newArrayList("E42fGIKZWQ", "JSfPww9OwM", "VNVjZjJEY7", "ECntczu2rf", "l3ITM9zoGr"));
            List<PreOpenAndMonthlyContractSpecResultVO> data = preOpenOrMonthlyContractSpecResult.getData();
            System.out.println(data);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testAddLog() {
        this.logService.addContractLogAsync(AddContractOperationLogDTO.builder()
                .operatorId("123").operationType("321").build());
        try {
            Thread.sleep(5000); // 异步，要睡一下
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testQueryPackage() {
        BusinessPackageSpecificationInfo bpsi = new BusinessPackageSpecificationInfo();

        // this.businessPackageSpecificationService.assemblePackageOptionsInfo(bpsi, BusinessBillingPlanCodeEnum.VOD_PACKAGE.getCode());
        // this.businessPackageSpecificationService.assemblePackageOptionsInfo(bpsi, BusinessBillingPlanCodeEnum.LIVE_DURATION.getCode());
        // this.businessPackageSpecificationService.assemblePackageOptionsInfo(bpsi, BusinessBillingPlanCodeEnum.COOL_PACKAGE.getCode());
        System.out.println(bpsi.getSelectOptionList());
    }

    @Test
    public void testContract() {
        CommonResult<FinanceContractInfoVO> contractInfo = this.financeContractService.getContractInfo("2qyRvDJn8z");
        System.out.println(contractInfo);
    }

    @Test
    public void testAddAmountCotnract() {
        AddAmountContractDTO addAc = AddAmountContractDTO.builder()
                .soId("6353f09b4d766b000138ab88")
                .unionId("8733ee6df2")
                .operationCode(ContractOperationCodeEnum.DEPOSIT_AMOUNT.getCode())
                .contractAmount(0L)
                .payOverAmount(0L)
                .donateAmount(0L)
                .payBackStatus(1)
                .contractId("")
                .build();
        CommonResult<AddContractResultVO> addAmountContractResult = this.financeContractService.addAmountContract(addAc);
    }

    @Test
    public void testAddContract() {
        FinanceContractEntity contractEntity = new FinanceContractEntity();
        contractEntity.setContractId(RandomUtils.getRandStr(10));
        this.financeContractService.saveContract(contractEntity);
    }

    @Test
    public void testGenerateMissingDetail() {
        this.financeIncomeConfirmService.generateMissingIncomeDetail(DateFormatUtil.parseDateNormal("2023-05-15"));
    }
}
