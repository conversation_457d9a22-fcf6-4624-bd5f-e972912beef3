package net.polyv.service.impl.finance;

import net.polyv.service.finance.FinanceContractReceivableService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description:
 * @author: Neo
 * @date: 2023-02-27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles(value = "dev")
public class FinanceContractReceivableTest {

    @Autowired
    private FinanceContractReceivableService contractReceivableService;

    @Test
    public void testGenerateReceivableContractCreated() {

    }


    @Test
    public void testGenerateReceivableScheduling() {
        this.contractReceivableService.generateReceivableScheduling("2023-04", null);
        // this.contractReceivableService.generateReceivableScheduling("2023-03", Lists.newArrayList("osGEIp3gFg"));
    }
    
    
}
