package net.polyv.service.impl.sync;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import net.polyv.common.CommonResult;
import net.polyv.service.sync.StatDataSyncService;

/**
 * 合同同步数据
 * <AUTHOR>
 * @date 2022/9/14 15:21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class StatContractSyncServiceImplTest extends TestCase {
    @Resource
    private StatDataSyncService statContractSyncService;
    
    @Test
    public void initDataSync() {
        CommonResult result = this.statContractSyncService.initDataSync(null);
    }
}