package net.polyv.service.impl;


import cn.hutool.core.date.DateUtil;
import net.polyv.model.data.GlobalUserInfoDO;
import net.polyv.model.data.bill.BillClearingReduceStateDO;

import net.polyv.service.bill.manual.ManualBillService;
import net.polyv.util.UserInfoThreadLocal;
import net.polyv.web.model.bill.manual.ManualBillAddInputVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import java.math.BigDecimal;
import java.util.Map;



/**
 * 手工账单相关接口测试
 * <AUTHOR>
 * @since 08/07/2021
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ManualBillRecordServiceImplTest {

    @Autowired
    private  ManualBillService manualBillService;

    // 手工账单记录添加
    @SuppressWarnings("unchecked")
    @Test
    public void add() {
        ManualBillAddInputVO manualBillAddInputVO = new ManualBillAddInputVO();
        manualBillAddInputVO.setCompany("test");
        manualBillAddInputVO.setCustomerId("cca90d24f7");
        manualBillAddInputVO.setItemId(20);
        manualBillAddInputVO.setItemProduction("点播");
        manualBillAddInputVO.setItemCategory("视频存储");
        manualBillAddInputVO.setItemName("存储空间");
        manualBillAddInputVO.setTradeType(1);
        manualBillAddInputVO.setUnivalence(12000L);
        manualBillAddInputVO.setUnivalenceUnit("元/G");
        manualBillAddInputVO.setItemConsumed(BigDecimal.valueOf(111.2255));
        manualBillAddInputVO.setItemConsumedUnit("G");
        manualBillAddInputVO.setCost(111L);
        manualBillAddInputVO.setIsCharging(1);
        manualBillAddInputVO.setConsumeStartDate("2021-07-08");
        manualBillAddInputVO.setConsumeEndDate("2021-07-09");
        manualBillAddInputVO.setBillDate("2021-07-10");
        manualBillAddInputVO.setItemConsumedUnit("用量单位");
        String threadId = String.valueOf(Thread.currentThread().getId());
        GlobalUserInfoDO userInfoDO = new GlobalUserInfoDO();
        userInfoDO.setUserId(threadId);
        userInfoDO.setUserIp(threadId);
        UserInfoThreadLocal.setGlobalUserInfoDO(userInfoDO);
        manualBillService.add(manualBillAddInputVO);

    }

    @Test
    public void testMap(){
        BillClearingReduceStateDO stateDO = new BillClearingReduceStateDO();
        Map<String, Long> contractAndReduceItemAmountMap = stateDO.getContractAndReduceItemAmountMap();
        long transferItemConsumed = 1111; //单位字节
        contractAndReduceItemAmountMap.put("-1",transferItemConsumed); //手工账单合同id为-1
        Assert.assertEquals("设置失败",true,
                (stateDO.getContractAndReduceItemAmountMap().get("-1") == 1111));
    }


}