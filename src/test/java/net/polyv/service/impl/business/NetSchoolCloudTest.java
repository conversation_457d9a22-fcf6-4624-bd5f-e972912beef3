package net.polyv.service.impl.business;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;
import net.polyv.model.data.business.BusinessOrderAddSpecDTO;
import net.polyv.util.JsonMapper;
import net.polyv.web.controller.business.BusinessOperationController;
import net.polyv.web.controller.business.BusinessOrderController;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.salesopportunities.input.BusinessOperationInputVO;

/**
 * 网校云测试
 * <AUTHOR>
 * @date 2022/8/16 11:46
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class NetSchoolCloudTest {
    @Resource
    private BusinessOperationController businessOperationController;
    @Resource
    private BusinessOrderController businessOrderController;

    public BusinessOrderAddSpecDTO createNetSchool() {
        String msg = "{\"soId\":\"628497749e27fa000138e250\",\"accountType\":\"group_v1\",\"email\":\"test0001@polyv" +
                ".com\",\"validPeriodStartTime\":\"2022-08-15\",\"validPeriodEndTime\":\"2022-08-30\"," +
                "\"isAlreadyActivated\":null,\"vodSpace\":\"200\",\"vodFlow\":\"1024\",\"vodPackageType\":\"109\"," +
                "\"vodPackageIsAnnualFlow\":0,\"packageIsCustomize\":null,\"vodPackageFlowMonthsOfYear\":null," +
                "\"vodFlowType\":null,\"liveChannelQuantity\":\"1\",\"livePackageType\":\"2\"," +
                "\"livePackageMinutesQuantity\":\"1000000\",\"livePackageMinutesQuantityGift\":\"1\"," +
                "\"liveConcurrentType\":null,\"liveConcurrentQuantity\":null,\"liveConcurrentIsLimit\":null," +
                "\"liveConcurrentDuration\":null,\"liveConcurrentDurationUnit\":\"天\"," +
                "\"liveConcurrentBillingType\":null,\"liveMicMinutesQuantity\":null," +
                "\"liveDirectorMinutesQuantity\":null,\"packageName\":\"企业版（100万分钟）\",\"packageDesc\":\"最高可赠送20万分钟\"," +
                "\"billingItemJson\":\"[]\",\"functionList\":\"[]\",\"company\":\"保利威\",\"unionId\":\"8703412bb4\"," +
                "\"remark\":\"\"}\n";
        
        return JsonMapper.stringToBean(msg, BusinessOrderAddSpecDTO.class);
    }
    
    @Test
    public void addNetSchool() {
        BusinessOrderAddSpecDTO dto = createNetSchool();
        
        ResponseEntity<WrappedResponse<Object>> addResult = this.businessOrderController.addPackageSpecInfo(dto);
        log.info("保存业务规格result:{})", addResult);
        
    }
    
    @Test
    public void openNetSchool() {
        BusinessOperationInputVO vo = new BusinessOperationInputVO();
        vo.setOrderNo("628497749e27fa000138e250");
        vo.setPaybackStatus(1);
        vo.setLoginUserIp("***************");
        vo.setLoginUserId("123123");
        vo.setOpenUrl("http://**************:8004/#/open?orderNo=628497749e27fa000138e250");
        ResponseEntity<WrappedResponse<Object>> entity = this.businessOperationController.businessOperation(vo);
        log.info("业务开通result:{}", entity);
    }
    
}
