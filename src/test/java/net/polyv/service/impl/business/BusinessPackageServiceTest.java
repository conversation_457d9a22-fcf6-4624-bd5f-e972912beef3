package net.polyv.service.impl.business;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.modules.pcs.api.vo.packages.PackageBaseVO;

/**
 *
 * <AUTHOR>
 * @date 2022/12/26 14:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class BusinessPackageServiceTest extends TestCase {
    @Resource
    private BusinessPackageService businessPackageService;
    @Test
    public void testWaitOpenPackageList() {
        List<PackageBaseVO> packageBaseVOS = businessPackageService.waitOpenPackageList("f736b4617e");
    
        System.out.println(JsonUtil.beanToString(packageBaseVOS).orElse(""));
    }
}