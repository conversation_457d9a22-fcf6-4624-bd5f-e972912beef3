package net.polyv.service.impl.business;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.modules.pcs.api.vo.dict.PreOpenLayoutCodeVO;
import net.polyv.service.business.BusinessOpenDictService;

/**
 * 业务开通字典测试
 * <AUTHOR>
 * @date 2022/9/1 15:14
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class BusinessOpenDictServiceImplTest extends TestCase {
    @Resource
    private BusinessOpenDictService businessOpenDictService;
    
    @Test
    public void testGetLayoutCodeByDict() {
        String accountType="group_v1";
        String businessType="1";
        String businessOpenType="origin_pre_open";
        String productType="vod_package";
        PreOpenLayoutCodeVO layoutCodeByDict = this.businessOpenDictService.getLayoutCodeByDict(accountType,
                businessType, businessOpenType, productType);
        System.out.println(JsonUtil.beanToString(layoutCodeByDict).orElse(""));
    }
}