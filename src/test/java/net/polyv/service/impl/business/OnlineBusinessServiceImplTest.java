package net.polyv.service.impl.business;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.dao.primary.business.OnlineBusinessOpenLogRepository;
import net.polyv.exception.ClearingSystemException;
import net.polyv.modules.common.exception.BizException;
import net.polyv.modules.common.stereotype.BizErrorCodeEnum;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenRequest;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenResource;
import net.polyv.modules.pcs.api.stereotype.OnlineBusinessOpenResourceCodeEnum;
import net.polyv.modules.third.api.client.payment.PaymentApiClient;
import net.polyv.modules.third.req.payment.PcsNotifyReq;
import net.polyv.modules.third.vo.payment.PaymentResponseVO;
import net.polyv.service.handler.OnlineBusinessOpenHandler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * OnlineBusinessServiceImpl单元测试
 * 测试open方法的所有逻辑分支
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class OnlineBusinessServiceImplTest {

    private OnlineBusinessServiceImpl onlineBusinessService;

    @Mock
    private OnlineBusinessOpenLogRepository onlineBusinessOpenLogRepository;

    @Mock
    private PaymentApiClient paymentApiClient;

    @Mock
    private OnlineBusinessOpenHandler mockHandler1;

    @Mock
    private OnlineBusinessOpenHandler mockHandler2;

    private OnlineBusinessOpenRequest testRequest;
    private OnlineBusinessOpenResource testResource;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        
        // 设置MDC上下文
        Map<String, String> mdcMap = new HashMap<>();
        mdcMap.put("traceId", "test-trace-id");
        MDC.setContextMap(mdcMap);
        
        // 初始化测试数据
        testResource = new OnlineBusinessOpenResource();
        testResource.setResourceCode(OnlineBusinessOpenResourceCodeEnum.LIVE_PACKAGE);
        testResource.setExtInfo("{\"packageId\":123}");
        
        testRequest = new OnlineBusinessOpenRequest();
        testRequest.setUnionId("test-union-id");
        testRequest.setOrderNo("test-order-123");
        testRequest.setAsync(false);
        testRequest.setOpenResource(testResource);
        
        // 手动创建服务实例，传入mock对象
        List<OnlineBusinessOpenHandler> handlers = Arrays.asList(mockHandler1, mockHandler2);
        onlineBusinessService = new OnlineBusinessServiceImpl(
            onlineBusinessOpenLogRepository,
            handlers,
            paymentApiClient
        );
    }

    /**
     * 测试同步执行成功场景
     */
    @Test
    public void testOpenSyncSuccess() throws ClearingSystemException {
        // 准备数据
        testRequest.setAsync(false);
        
        // Mock handler行为
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doReturn(CommonResult.ok("开通成功")).when(mockHandler1).open(testRequest);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertTrue("应该返回成功", result.isSuccess());
        
        // 验证调用
        verify(mockHandler1, times(1)).canOpen(testRequest);
        verify(mockHandler1, times(1)).open(testRequest);
        verify(mockHandler2, never()).canOpen(any());
        verify(paymentApiClient, never()).pcsNotify(any());
    }

    /**
     * 测试同步执行Handler返回失败场景
     */
    @Test
    public void testOpenSyncHandlerFailure() throws ClearingSystemException {
        // 准备数据
        testRequest.setAsync(false);
        
        // Mock handler行为 - 返回失败
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doReturn(CommonResult.build(null, "开通失败", 400)).when(mockHandler1).open(testRequest);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertFalse("应该返回失败", result.isSuccess());
        assertTrue("错误码应该匹配", Objects.equals(400, result.getError().getCode()));
        assertEquals("错误信息应该匹配", "开通失败", result.getError().getDesc());
        
        // 验证调用
        verify(mockHandler1, times(1)).canOpen(testRequest);
        verify(mockHandler1, times(1)).open(testRequest);
    }

    /**
     * 测试同步执行BizException异常场景
     */
    @Test
    public void testOpenSyncBizException() throws ClearingSystemException {
        // 准备数据
        testRequest.setAsync(false);
        
        // Mock handler行为 - 抛出BizException
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doThrow(new BizException(500, "业务异常")).when(mockHandler1).open(testRequest);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertFalse("应该返回失败", result.isSuccess());
        assertTrue("错误码应该匹配", Objects.equals(500, result.getError().getCode()));
        assertEquals("错误信息应该匹配", "业务异常", result.getError().getDesc());
        
        // 验证调用
        verify(mockHandler1, times(1)).canOpen(testRequest);
        verify(mockHandler1, times(1)).open(testRequest);
    }

    /**
     * 测试同步执行通用Exception异常场景
     */
    @Test
    public void testOpenSyncGeneralException() throws ClearingSystemException {
        // 准备数据
        testRequest.setAsync(false);
        
        // Mock handler行为 - 抛出通用异常
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doThrow(new RuntimeException("系统异常")).when(mockHandler1).open(testRequest);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertFalse("应该返回失败", result.isSuccess());
        assertTrue("错误码应该是系统异常", Objects.equals(BizErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), result.getError().getCode()));
        assertEquals("错误信息应该匹配", BizErrorCodeEnum.SYSTEM_EXCEPTION.getDesc(), result.getError().getDesc());
        
        // 验证调用
        verify(mockHandler1, times(1)).canOpen(testRequest);
        verify(mockHandler1, times(1)).open(testRequest);
    }

    /**
     * 测试同步执行ClearingSystemException异常场景
     */
    @Test
    public void testOpenSyncClearingSystemException() throws ClearingSystemException {
        // 准备数据
        testRequest.setAsync(false);
        
        // Mock handler行为 - 抛出ClearingSystemException
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doThrow(new ClearingSystemException("清算系统异常")).when(mockHandler1).open(testRequest);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertFalse("应该返回失败", result.isSuccess());
        assertTrue("错误码应该是系统异常", Objects.equals(BizErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), result.getError().getCode()));
        assertEquals("错误信息应该匹配", BizErrorCodeEnum.SYSTEM_EXCEPTION.getDesc(), result.getError().getDesc());
        
        // 验证调用
        verify(mockHandler1, times(1)).canOpen(testRequest);
        verify(mockHandler1, times(1)).open(testRequest);
    }

    /**
     * 测试找不到合适Handler的场景
     */
    @Test
    public void testOpenNoSuitableHandler() {
        // 准备数据
        testRequest.setAsync(false);
        
        // Mock handler行为 - 都不支持
        when(mockHandler1.canOpen(testRequest)).thenReturn(false);
        when(mockHandler2.canOpen(testRequest)).thenReturn(false);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertFalse("应该返回失败", result.isSuccess());
        assertTrue("错误码应该匹配", Objects.equals(400, result.getError().getCode()));
        assertEquals("错误信息应该匹配", "当前资源不支持开通", result.getError().getDesc());
        
        // 验证调用
        verify(mockHandler1, times(1)).canOpen(testRequest);
        verify(mockHandler2, times(1)).canOpen(testRequest);
        try {
            verify(mockHandler1, never()).open(any());
            verify(mockHandler2, never()).open(any());
        } catch (ClearingSystemException e) {
            // 这里不会到达，因为我们验证的是never()调用
        }
    }

    /**
     * 测试handlers列表为空的场景
     */
    @Test
    public void testOpenEmptyHandlersList() throws Exception {
        // 准备数据 - 创建空handlers列表的服务实例
        onlineBusinessService = new OnlineBusinessServiceImpl(
            onlineBusinessOpenLogRepository,
            Collections.emptyList(),
            paymentApiClient
        );
        
        testRequest.setAsync(false);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertFalse("应该返回失败", result.isSuccess());
        assertTrue("错误码应该匹配", Objects.equals(400, result.getError().getCode()));
        assertEquals("错误信息应该匹配", "当前资源不支持开通", result.getError().getDesc());
    }

    /**
     * 测试handlers列表为null的场景
     */
    @Test
    public void testOpenNullHandlersList() throws Exception {
        // 准备数据 - 创建null handlers列表的服务实例
        onlineBusinessService = new OnlineBusinessServiceImpl(
            onlineBusinessOpenLogRepository,
            null,
            paymentApiClient
        );
        
        testRequest.setAsync(false);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertFalse("应该返回失败", result.isSuccess());
        assertTrue("错误码应该匹配", Objects.equals(400, result.getError().getCode()));
        assertEquals("错误信息应该匹配", "当前资源不支持开通", result.getError().getDesc());
    }

    /**
     * 测试异步执行成功场景
     */
    @Test
    public void testOpenAsyncSuccess() throws InterruptedException, ClearingSystemException {
        // 准备数据
        testRequest.setAsync(true);
        testRequest.setOrderNo("test-order-async");
        
        // Mock handler行为
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doReturn(CommonResult.ok("异步开通成功")).when(mockHandler1).open(testRequest);
        
        // Mock payment API
        PaymentResponseVO<Void> mockPaymentResponse = new PaymentResponseVO<>();
        when(paymentApiClient.pcsNotify(any(PcsNotifyReq.class))).thenReturn(mockPaymentResponse);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证同步返回结果
        assertNotNull("返回结果不能为空", result);
        assertTrue("异步调用应该立即返回成功", result.isSuccess());
        
        // 等待异步任务完成
        Thread.sleep(2000);
        
        // 验证调用
        verify(mockHandler1, times(1)).canOpen(testRequest);
        verify(mockHandler1, times(1)).open(testRequest);
        
        // 验证回调调用
        ArgumentCaptor<PcsNotifyReq> notifyReqCaptor = ArgumentCaptor.forClass(PcsNotifyReq.class);
        verify(paymentApiClient, timeout(5000).times(1)).pcsNotify(notifyReqCaptor.capture());
        
        PcsNotifyReq capturedReq = notifyReqCaptor.getValue();
        assertEquals("订单号应该匹配", "test-order-async", capturedReq.getOrder_id());
        assertTrue("回调结果应该是成功", capturedReq.getResponse().isSuccess());
    }

    /**
     * 测试异步执行失败场景
     */
    @Test
    public void testOpenAsyncFailure() throws InterruptedException, ClearingSystemException {
        // 准备数据
        testRequest.setAsync(true);
        testRequest.setOrderNo("test-order-async-fail");
        
        // Mock handler行为 - 返回失败
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doReturn(CommonResult.build(null, "异步开通失败", 400)).when(mockHandler1).open(testRequest);
        
        // Mock payment API
        PaymentResponseVO<Void> mockPaymentResponse = new PaymentResponseVO<>();
        when(paymentApiClient.pcsNotify(any(PcsNotifyReq.class))).thenReturn(mockPaymentResponse);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证同步返回结果
        assertNotNull("返回结果不能为空", result);
        assertTrue("异步调用应该立即返回成功", result.isSuccess());
        
        // 等待异步任务完成
        Thread.sleep(2000);
        
        // 验证回调调用
        ArgumentCaptor<PcsNotifyReq> notifyReqCaptor = ArgumentCaptor.forClass(PcsNotifyReq.class);
        verify(paymentApiClient, timeout(5000).times(1)).pcsNotify(notifyReqCaptor.capture());
        
        PcsNotifyReq capturedReq = notifyReqCaptor.getValue();
        assertEquals("订单号应该匹配", "test-order-async-fail", capturedReq.getOrder_id());
        assertFalse("回调结果应该是失败", capturedReq.getResponse().isSuccess());
    }

    /**
     * 测试异步执行抛出异常场景
     */
    @Test
    public void testOpenAsyncException() throws InterruptedException, ClearingSystemException {
        // 准备数据
        testRequest.setAsync(true);
        testRequest.setOrderNo("test-order-async-exception");
        
        // Mock handler行为 - 抛出异常
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doThrow(new RuntimeException("异步执行异常")).when(mockHandler1).open(testRequest);
        
        // Mock payment API
        PaymentResponseVO<Void> mockPaymentResponse = new PaymentResponseVO<>();
        when(paymentApiClient.pcsNotify(any(PcsNotifyReq.class))).thenReturn(mockPaymentResponse);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证同步返回结果
        assertNotNull("返回结果不能为空", result);
        assertTrue("异步调用应该立即返回成功", result.isSuccess());
        
        // 等待异步任务完成
        Thread.sleep(2000);
        
        // 验证回调调用
        ArgumentCaptor<PcsNotifyReq> notifyReqCaptor = ArgumentCaptor.forClass(PcsNotifyReq.class);
        verify(paymentApiClient, timeout(5000).times(1)).pcsNotify(notifyReqCaptor.capture());
        
        PcsNotifyReq capturedReq = notifyReqCaptor.getValue();
        assertEquals("订单号应该匹配", "test-order-async-exception", capturedReq.getOrder_id());
        assertFalse("回调结果应该是失败", capturedReq.getResponse().isSuccess());
        assertTrue("错误码应该是系统异常", Objects.equals(BizErrorCodeEnum.SYSTEM_EXCEPTION.getCode(), 
                capturedReq.getResponse().getError().getCode()));
    }

    /**
     * 测试异步执行但orderNo为空的场景（不进行回调）
     */
    @Test
    public void testOpenAsyncWithoutOrderNo() throws InterruptedException, ClearingSystemException {
        // 准备数据
        testRequest.setAsync(true);
        testRequest.setOrderNo(null); // 没有订单号
        
        // Mock handler行为
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doReturn(CommonResult.ok("异步开通成功")).when(mockHandler1).open(testRequest);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证同步返回结果
        assertNotNull("返回结果不能为空", result);
        assertTrue("异步调用应该立即返回成功", result.isSuccess());
        
        // 等待异步任务完成
        Thread.sleep(2000);
        
        // 验证不会调用回调
        verify(paymentApiClient, never()).pcsNotify(any());
    }

    /**
     * 测试异步执行但orderNo为空字符串的场景（不进行回调）
     */
    @Test
    public void testOpenAsyncWithEmptyOrderNo() throws InterruptedException, ClearingSystemException {
        // 准备数据
        testRequest.setAsync(true);
        testRequest.setOrderNo(""); // 空字符串订单号
        
        // Mock handler行为
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doReturn(CommonResult.ok("异步开通成功")).when(mockHandler1).open(testRequest);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证同步返回结果
        assertNotNull("返回结果不能为空", result);
        assertTrue("异步调用应该立即返回成功", result.isSuccess());
        
        // 等待异步任务完成
        Thread.sleep(2000);
        
        // 验证不会调用回调
        verify(paymentApiClient, never()).pcsNotify(any());
    }

    /**
     * 测试第二个Handler被选中的场景
     */
    @Test
    public void testOpenSecondHandlerSelected() throws ClearingSystemException {
        // 准备数据
        testRequest.setAsync(false);
        
        // Mock handler行为 - 第一个不支持，第二个支持
        when(mockHandler1.canOpen(testRequest)).thenReturn(false);
        when(mockHandler2.canOpen(testRequest)).thenReturn(true);
        doReturn(CommonResult.ok("第二个Handler开通成功")).when(mockHandler2).open(testRequest);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertTrue("应该返回成功", result.isSuccess());
        
        // 验证调用
        verify(mockHandler1, times(1)).canOpen(testRequest);
        verify(mockHandler2, times(1)).canOpen(testRequest);
        verify(mockHandler1, never()).open(any());
        verify(mockHandler2, times(1)).open(testRequest);
    }

    /**
     * 测试MDC上下文传递场景
     */
    @Test
    public void testOpenMdcContextPropagation() throws ClearingSystemException {
        // 准备数据
        testRequest.setAsync(false);
        
        // 设置特定的MDC上下文
        MDC.put("customKey", "customValue");
        
        // Mock handler行为
        when(mockHandler1.canOpen(testRequest)).thenReturn(true);
        doAnswer(invocation -> {
            // 验证MDC上下文是否正确传递
            String mdcValue = MDC.get("customKey");
            assertEquals("MDC上下文应该正确传递", "customValue", mdcValue);
            return CommonResult.ok("MDC测试成功");
        }).when(mockHandler1).open(testRequest);
        
        // 执行测试
        ResponseVO<Void> result = onlineBusinessService.open(testRequest);
        
        // 验证结果
        assertNotNull("返回结果不能为空", result);
        assertTrue("应该返回成功", result.isSuccess());
        
        // 清理MDC
        MDC.remove("customKey");
    }

} 