package net.polyv.service.impl.concurrence;

import java.util.Objects;

import javax.annotation.Resource;

import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.GroupLiveChargeType;
import net.polyv.constant.customer.LiveBillingPlanConst;
import net.polyv.dao.primary.GroupAccountConfigRepository;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.model.entity.primary.GroupAccountConfig;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.rest.client.live.UserPackageClient;
import net.polyv.rest.model.live.UserMsgVO;
import net.polyv.service.account.CustomerService;
import net.polyv.service.concurrence.ConcurrenceSettingService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DingWarnRobot;
import net.polyv.web.model.account.GetCustomerStateInputVO;
import net.polyv.web.model.oldpackage.LivePackageState;
import net.polyv.web.model.resource.CustomerMaxConcurrenceInputVO;
import net.polyv.web.model.resource.CustomerMaxConcurrenceVO;
import net.polyv.web.model.user.CustomerStateVO;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description: 并发设置服务测试类
 * @author: Neo
 * @date: 2021-09-10
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class ConcurrenceSettingServiceTest {
    
    @Autowired
    private ConcurrenceSettingService concurrenceSettingService;
    @Autowired
    private CustomerResourceService customerResourceService;
    @Resource
    private CustomerService customerService;
    
    @Resource
    private GroupAccountConfigRepository groupAccountConfigRepository;
    
    @Resource
    private DingWarnRobot dingWarnRobot;
    
    @Resource
    private LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository;
    
    @Test
    public void testTranscendRemind() {
        // long current = System.currentTimeMillis();
        // List<ConcurrenceOverstepMessageDTO> trDTOList = this.concurrenceSettingService.concurrenceOverstepRemind();
        // System.out.println(JSON.toJSON(trDTOList));
        // System.out.printf("spend time[%d]", System.currentTimeMillis() - current);
    }
    
    @Test
    public void testGetMaxConcurrence() {
        CustomerMaxConcurrenceInputVO inputVO = new CustomerMaxConcurrenceInputVO();
        inputVO.setUnionId("d8c6faded1");
        inputVO.setResourceCode("concurrence");
        inputVO.setLiveStartDate("2021-01-01");
        inputVO.setLiveEndDate("2022-04-30");
        CustomerMaxConcurrenceVO maxConcurrence = customerResourceService.getCustomerMaxConcurrence(inputVO);
        System.out.println(maxConcurrence);
    }
    
    @Test
    public void testConcurrenceExpire() {
        LiveCustomerConcurrenceSetting setting =
                liveCustomerConcurrenceSettingRepository.getLatestConcurrenceByCustomerId(
                "3a8cf04cff");
        if (!isNeedDepositDuration(setting)) {
            dingWarnRobot.sendWarnMsg("【并发套餐过期，由于当前计费方式为分钟数，无须打回分钟数套餐】",
                    String.format("客户id为：%s", setting.getCustomerId()));
        }
    }
    
    /**
     * 当前计费方式如果为分钟数计费，则不需要打回分钟数套餐
     * @param setting
     * @return
     */
    private boolean isNeedDepositDuration(LiveCustomerConcurrenceSetting setting) {
        boolean isMinutesBilling = false;
        //集团2.0主账号
        if (AccountTypeEnum.GROUP2.getCode().equals(setting.getAccountType())) {
            GroupAccountConfig groupAccountConfig = groupAccountConfigRepository.findByGroupId(setting.getCustomerId())
                    .orElse(null);
            if (Objects.nonNull(groupAccountConfig)) {
                isMinutesBilling = GroupLiveChargeType.MINUTES.getValue()
                        .equals(groupAccountConfig.getGroupLiveBillingPlan());
            }
        }
        //普通账号
        else {
            GetCustomerStateInputVO inputVO = new GetCustomerStateInputVO();
            inputVO.setUnionId(setting.getCustomerId());
            //从业务系统获取普通账号直播计费方式
            UserMsgVO userMsgVO = customerService.getUserMsgVo(inputVO);
            if (Objects.nonNull(userMsgVO)) {
                CustomerStateVO result = UserPackageClient.getUsersPackageByEmail(userMsgVO.getEmail());
                if (Objects.nonNull(result)) {
                    LivePackageState livePackageState = result.getLivePackageState();
                    String billingPlan = livePackageState.getBillingPlan();
                    isMinutesBilling = LiveBillingPlanConst.duration.name().equals(billingPlan);
                }
            }
        }
        //分钟数计费不需要打回分钟数套餐
        return !isMinutesBilling;
    }
}
