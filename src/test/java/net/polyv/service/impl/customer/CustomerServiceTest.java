package net.polyv.service.impl.customer;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.examinationDonate.BusinessTypeEnum;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.AccountRechargeRecordRepository;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.BatchGetResourceRequest;
import net.polyv.modules.pcs.api.req.DepositAmountRequest;
import net.polyv.modules.pcs.api.req.DepositChannelsRequest;
import net.polyv.modules.pcs.api.req.DepositFlowPackageRequest;
import net.polyv.modules.pcs.api.req.DepositGuideDurationRequest;
import net.polyv.modules.pcs.api.req.DepositLiveConcurrenceRequest;
import net.polyv.modules.pcs.api.req.DepositLiveDurationRequest;
import net.polyv.modules.pcs.api.req.DepositMicDurationRequest;
import net.polyv.modules.pcs.api.req.DepositResourceRequest;
import net.polyv.modules.pcs.api.req.DepositTempSpaceRequest;
import net.polyv.modules.pcs.api.req.ResourceDepositListGetRequest;
import net.polyv.modules.pcs.api.vo.BatchGetResourceResultVO;
import net.polyv.service.CustomizedAccountDepositDetailService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.account.GroupService;
import net.polyv.service.item.ItemService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.JsonMapper;
import net.polyv.web.model.account.CustomerItemStateDetailVO;
import net.polyv.web.model.account.GetCustomerStateInputVO;
import net.polyv.web.model.account.item.CurrentCustomerItemStateDetailInputVO;
import net.polyv.web.model.user.CustomerStateVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/10 15:27
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CustomerServiceTest {
    @Resource
    private CustomerService customerService;
    @Resource
    private ItemService itemService;
    @Resource
    private CustomerResourceService customerResourceService;
    
    @Resource
    private AccountRechargeRecordRepository accountRechargeRecordRepository;
    
    @Resource
    private CustomizedAccountDepositDetailService customizedAccountDepositDetailService;
    
    @Resource
    private GroupService groupService;
    
    @Test
    public void info() {
        GetCustomerStateInputVO getCustomerStateInputVO = new GetCustomerStateInputVO();
        
        //  getCustomerStateInputVO.setLiveUserId("ed5b248130");
        getCustomerStateInputVO.setLiveUserId("0924a99ae2");
        CustomerStateVO customerState = this.customerService.getCustomerState(getCustomerStateInputVO);
        log.info("1111 {}", JsonUtil.beanToString(customerState));
//        getCustomerStateInputVO.setLiveUserId("a71cfb1ea2");
//         customerState = this.customerService.getCustomerState(getCustomerStateInputVO);
//        log.info("1111 {}", JsonUtil.beanToString(customerState));
//        getCustomerStateInputVO.setLiveUserId("61fe77b1cb");
//         customerState = this.customerService.getCustomerState(getCustomerStateInputVO);
//        log.info("1111 {}", JsonUtil.beanToString(customerState));
//        getCustomerStateInputVO.setLiveUserId("fd93d0bae1");
//         customerState = this.customerService.getCustomerState(getCustomerStateInputVO);
//        log.info("1111 {}", JsonUtil.beanToString(customerState));
//        getCustomerStateInputVO.setLiveUserId("887df1fc72");
//         customerState = this.customerService.getCustomerState(getCustomerStateInputVO);
//        log.info("1111 {}", JsonUtil.beanToString(customerState));
        
    }

    @Test
    public void itemStateDetailTest(){
        CurrentCustomerItemStateDetailInputVO input = new CurrentCustomerItemStateDetailInputVO();
        input.setCustomerIds("c427b80288");
        input.setResourceCode(ResourceCodeConst.traffic.name());
        List<CustomerItemStateDetailVO> list = itemService.batchGetCustomerItemStateDetail(input);
        log.info("stateList == {}",list);
    }

    @Test
    public void updateGroupUserExpireDateTest(){
        String groupId = "0a564a07fb";
        Date date = DateFormatUtil.parseDateNormal("2022-10-01");
        BusinessTypeEnum businessTypeEnum2 = BusinessTypeEnum.VOD_PACKAGE;
        customerService.updateGroupUserExpireDate(groupId, businessTypeEnum2,date);
    }
    @Test
    public void updateAmountExpireDateTest(){
        List<String> list = Lists.newArrayList("**********","03a94b9fca");
        try{
            int count = accountRechargeRecordRepository.updateUserExpireDate(list,DateUtil.getDateAfterDays(30));
            log.info("count == {}",count);
        }
        catch (Exception e){
            log.error(e.getMessage());
            log.info("end...");
        }
    }
    @Test
    public void batchDepositTest(){
        DepositResourceRequest request = new DepositResourceRequest();
        request.setGroupId("19686a631a");
        request.setCustomerId("bccc9962da");
        request.setAmountRequest(buildAmountRequest());
        request.setGuideDurationRequest(buildGuideDurationRequest());
        request.setMicDurationRequest(buildMicDurationRequest());
        request.setFlowPackageRequest(buildFlowPackageRequest());
        request.setTempSpaceRequest(buildTempSpaceRequest());
        request.setLiveDurationRequest(buildLiveDurationRequest());
        request.setLiveConcurrenceRequest(buildLiveConcurrenceRequest());
        request.setChannelsRequest(buildChannelsRequest());
        log.info("batch deposit request == {}", JsonMapper.jsonToString(request));
        try{
           customerResourceService.batchDeposit(request);
        }
        catch (Exception e){
            log.error(e.getMessage());
            log.info("end...");
        }
    }

    private DepositAmountRequest buildAmountRequest(){
        DepositAmountRequest request = DepositAmountRequest.builder()
                .amountExpireDate(DateUtil.getDateAfterDays(30))
                .amountExpireType(2)
                .donate(30000000L)
                .build();
        return request;
    }

    private DepositGuideDurationRequest buildGuideDurationRequest(){
        DepositGuideDurationRequest request = new DepositGuideDurationRequest();
        request.setDuration(10);
        request.setEndDate(DateUtil.getDateAfterDays(60));
        request.setStartDate(DateUtil.getDateAfterDays(1));
        return request;
    }
    private DepositMicDurationRequest buildMicDurationRequest(){
        DepositMicDurationRequest request = new DepositMicDurationRequest();
        request.setDuration(22);
        request.setEndDate(DateUtil.getDateAfterDays(60));
        request.setStartDate(DateUtil.getDateAfterDays(1));
        return request;
    }
    private DepositFlowPackageRequest buildFlowPackageRequest(){
        DepositFlowPackageRequest request = new DepositFlowPackageRequest();
        request.setFlow(BigDecimal.valueOf(10));
        request.setEndDate(DateUtil.getDateAfterDays(100));
        request.setStartDate(DateUtil.getDateAfterDays(1));
        return request;
    }
    private DepositTempSpaceRequest buildTempSpaceRequest(){
        DepositTempSpaceRequest request = new DepositTempSpaceRequest();
        request.setSpace(BigDecimal.valueOf(20));
        request.setEndDate(DateUtil.getDateAfterDays(100));
        request.setStartDate(DateUtil.getDateAfterDays(1));
        return request;
    }

    private DepositLiveDurationRequest buildLiveDurationRequest(){
        DepositLiveDurationRequest request = new DepositLiveDurationRequest();
        request.setDuration(77);
        request.setEndDate(DateUtil.getDateAfterDays(90));
        request.setStartDate(DateUtil.getCurrentDay());
        return request;
    }

    private DepositLiveConcurrenceRequest buildLiveConcurrenceRequest(){
        DepositLiveConcurrenceRequest request = new DepositLiveConcurrenceRequest();
        request.setConcurrences(99);
        request.setEndDate(DateUtil.getDateAfterDays(180));
        request.setStartDate(DateUtil.getDateAfterDays(10));
        return request;
    }
    private DepositChannelsRequest buildChannelsRequest(){
        DepositChannelsRequest request = new DepositChannelsRequest();
        request.setChannels(10);
        return request;
    }
    
    @Test
    public void depositListTest() {
        ResourceDepositListGetRequest request = new ResourceDepositListGetRequest();
        request.setCustomerIds("baf4ad57f6,bccc9962da");
        request.setBusinessType(3);
        customizedAccountDepositDetailService.depositList(request);
    }
    
    @Test
    public void groupInfoTest() {
        GetCustomerStateInputVO inputVO = new GetCustomerStateInputVO();
        inputVO.setAccountType(AccountTypeEnum.GROUP2.getCode());
        inputVO.setGroupId("0a564a07fb");
        CustomerStateVO groupAccountState = groupService.getGroupAccountState(inputVO);
        log.info("groupAccountState == {}", groupAccountState);
    }

    @Test
    public void getResourceAvailableListTest(){
        BatchGetResourceRequest request = new BatchGetResourceRequest();
        List<String> unionIds = Lists.newArrayList();
        for (int i = 0; i < 1; i++) {
            unionIds.add("**********");
        }
        request.setUnionIds(unionIds);
        ResponseVO<Map<String, BatchGetResourceResultVO>> responseVO = customerResourceService.getResourceAvailableList(
                request);
        log.info("responseVO == {}", responseVO);
    }

    @Test
    public void initBatchItemCodeConfigTest() {
        this.customerService.initBatchItemCodeConfig(Lists.newArrayList( "bcb6ef082c"), "distributeBillingPlanDaily");
    }
}
