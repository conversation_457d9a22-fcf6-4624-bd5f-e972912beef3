package net.polyv.service.impl.user;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.user.api.service.CrmApi;
import net.polyv.modules.user.api.vo.CustomerEntityVO;
import net.polyv.rest.client.dmp.RtasClient;
import net.polyv.service.CacheService;
import net.polyv.service.account.CustomerService;
import net.polyv.util.DingWarnRobot;
import net.polyv.web.model.WrappedResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: Neo
 * @date: 2022-04-11
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class UserServiceTest {

    @Resource
    private CrmApi crmApi;
    
    @Resource
    private CacheService cacheService;
    
    @Resource
    private DingWarnRobot dingWarnRobot;
    
    @Autowired
    private CustomerService customerService;
    
    @Resource
    private RtasClient rtasClient;
    
    @Test
    public void testUserServiceLog() {
        String email = "<EMAIL>";
        ResponseVO<CustomerEntityVO> emailResult = null;
        try {
            emailResult = this.crmApi.getCustomerByEmail(email);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (Objects.nonNull(emailResult) && emailResult.isSuccess()) {
            CustomerEntityVO customerEntity = emailResult.getData();
            System.out.println(JSON.toJSONString(customerEntity));
        }
    }

    @Test
    public void testCleanVodCache(){

        String customerId = "a63bce7787";
        try{

            WrappedResponse wrappedResponse = cacheService.cleanVodUserCache(customerId);

            log.info("cleanVodUserCache response "+wrappedResponse);
        }
        catch (Exception e){
            log.error("cleanVodUserCache error",e);
            dingWarnRobot.sendWarnMsg("【告警，清理点播缓存异常】",
                    String.format("customerId=%s, exception=%s",customerId, e.getMessage()));
        }
    }
    
    @Test
    public void testGroupSubAccount() {
        String subCustomerId = "2865a2a2dd";
        System.out.println(customerService.isGroupSubAccount(subCustomerId));
        
        String mainCustomerId = "2c840be1db";
        System.out.println(customerService.isGroupSubAccount(mainCustomerId));
    }
    
    @Test
    public void testGetRealtimeViewerCountForUser() {
        String liveUserId = "2779521b45";
        rtasClient.getRealtimeViewerCountForUser(liveUserId);
    }
}
