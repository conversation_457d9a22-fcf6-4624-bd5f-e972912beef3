package net.polyv.service.impl.marketing;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.marketing.ClueSourceFourConstant;
import net.polyv.constant.marketing.ClueSourceThreeConstant;
import net.polyv.constant.marketing.ClueSourceTwoConstant;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.entity.primary.market.TraceClueData;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.third.req.fxiaoke.FXiaoKeCustomerQueryReq;
import net.polyv.modules.third.req.marketing.QingniaoAuthRequest;
import net.polyv.modules.third.req.marketing.QingniaoClueListRequest;
import net.polyv.modules.third.req.marketing.TencentClueListRequest;
import net.polyv.modules.third.service.crm.FXiaoKeService;
import net.polyv.modules.third.service.marketing.QingniaoClueService;
import net.polyv.modules.third.service.marketing.TencentClueService;
import net.polyv.modules.third.stereotype.FXiaoKeApiNameEnum;
import net.polyv.modules.third.vo.fxiaoke.FXiaoKeBaseResponse;
import net.polyv.modules.third.vo.marketing.QingniaoAuthResponse;
import net.polyv.modules.third.vo.marketing.QingniaoClueResponse;
import net.polyv.modules.third.vo.marketing.TencentClueListResponse;
import net.polyv.service.marketing.MarketingClueService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description:
 * @author: Neo
 * @date: 2022-04-26
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("test")
public class MarketingClueServiceTest {

    @Autowired
    private QingniaoClueService qingniaoClueService;

    @Autowired
    private TencentClueService tencentClueService;

    @Autowired
    private MarketingClueService marketingClueService;

    @Autowired
    private FXiaoKeService fXiaoKeService;

    @Test
    public void testQingniaoGetAccessToken() {
        QingniaoAuthRequest req = new QingniaoAuthRequest();
        req.setAuth_code("da1513e7e34d875339714a08a6d95d922c2727ca");
        ResponseVO<QingniaoAuthResponse> accessTokenResponse = qingniaoClueService.getAccessToken(req);
    }

    @Test
    public void testQingniaoRefreshAccessToken() {
        QingniaoAuthRequest req = new QingniaoAuthRequest();
        req.setAuth_code("da1513e7e34d875339714a08a6d95d922c2727ca");
        req.setRefresh_token("");
        ResponseVO<QingniaoAuthResponse> accessTokenResponse = qingniaoClueService.refreshToken(req);
    }

    @Test
    public void testTencentGetClueList() {
        TencentClueListRequest request = new TencentClueListRequest();
        request.setStart_time("2022-04-20");
        request.setEnd_time("2022-04-26");
        ResponseVO<TencentClueListResponse> clueList = this.tencentClueService.getClueList(request, TencentClueService.WECHAT_PLATFORM);
        System.out.println(clueList);
    }

    @Test
    public void testGetClueList() {
        QingniaoClueListRequest req = new QingniaoClueListRequest();
        req.setAdvertiser_ids(Lists.newArrayList("61073352893"));
        // req.setAdvertiser_ids(Lists.newArrayList("1668386212932615"));
        req.setStart_time("2022-04-01");
        req.setEnd_time("2022-04-30");
        req.setPage("1");
        req.setPage_size("50");
        ResponseVO<QingniaoClueResponse> clubList = qingniaoClueService.getClueList(req,
                "6f18db309dd96db9eb19a704bb96d8f7329e952f", 1);
        System.out.println(clubList);
    }

    @Test
    public void testSaveQingniao2Fxk() {
        try {
            this.marketingClueService.syncQingniaoClue2Fxk();
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSaveTencent2Fxk() {
        try {
            this.marketingClueService.syncTencentClue2Fxk();
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testRefreshToken() {
        try {
            this.marketingClueService.refreshQingniaoTokenSchedule();
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testTransferJson() {
        // String text = "47baf0bb6b,1e7e7f802f,e76abba4b5,43bdf588e5,02b5fbdb55,13d09ba1fb,b7d5d303d9,ed802a0b80,a8d6b72390,5b084ad7e1,fe1b3ef745,95848dbf39,e7670ed91f,2c756da657,7fb4c853ce,a1ac683169,887df1fc72,bbfa933818,241513d62f,baf4ad57f6,a54965aff5,146b754735,0cd258d37d,ac59aff03d,f43d15b05d,53d9f1471f,e7537777a3,ed5b248130,a54965aff5,146b754735,48a566f06f,8841131796,54390a07db,457dae5228,d8c6faded1,a2dc4f2517,3660485aa9,37e4d0ed2c,833584471d,ee03840cfd,81aef8fd84,3a52610b49,09b6757f43,d03e90cf4f,e86c72c66e,98184182e6";
        // List<String> textList = Lists.newArrayList(text.split(","));
        // StringBuilder sb = textList.stream().reduce(new StringBuilder(), (v1, v2) -> v1.append(v2).append(","), StringBuilder::append);
        // System.out.println(sb.substring(0,sb.length()-1));


        String existsTestAllow = "47baf0bb6b,1e7e7f802f";

        List<String> existsList = Lists.newArrayList(existsTestAllow.split(","));
        if (existsList.contains("47baf0bb6b")) {
            existsList.removeIf(f -> f.equals("47baf0bb6b"));
            StringBuilder afterRemove = existsList.stream().reduce(new StringBuilder(), (v1, v2) -> v1.append(v2).append(","), StringBuilder::append);
            System.out.println(afterRemove.substring(0, afterRemove.length() - 1));
        }
    }

    @Test
    public void testQueryClue() {
        FXiaoKeCustomerQueryReq clueRequest = new FXiaoKeCustomerQueryReq();
        Map<String, Object> searchQueryInfoMap = new HashMap<>();

        // 结果组装（key:客户电话,value:线索或联系人id）
        Map<String, Object> cluePoolFilterMap = new HashMap<>();
        cluePoolFilterMap.put("field_values", "47c8b61f9d");
        cluePoolFilterMap.put("field_name", "field_S175u__c");
        cluePoolFilterMap.put("operator", "EQ");
        searchQueryInfoMap.put("filters", Lists.newArrayList(cluePoolFilterMap));
        searchQueryInfoMap.put("fieldProjection", Lists.newArrayList("_id", "create_time", "field_S175u__c", "field_k3R1p__c"));
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("dataObjectApiName", FXiaoKeApiNameEnum.CLUE.getValue());
        clueRequest.setData(dataMap);
        ResponseVO<FXiaoKeBaseResponse> fXiaoKeClueResponse = null;
        int totalCount = 0;
        int currentOffset = 0;
        int currentLimit = 100;
        int maxSafeLoopCount = 1000;
        do {
            List<TraceClueData> saveEntityList = new ArrayList<>();
            searchQueryInfoMap.put("offset", currentOffset);
            searchQueryInfoMap.put("limit", currentLimit);
            dataMap.put("search_query_info", searchQueryInfoMap);
            fXiaoKeClueResponse = this.fXiaoKeService.queryCluePools(clueRequest);
            if (fXiaoKeClueResponse.isSuccess()) {
                FXiaoKeBaseResponse data = fXiaoKeClueResponse.getData();
                Map<String, Object> resultMap = data.getData();
                if (Objects.isNull(resultMap) || Objects.isNull(resultMap.get("dataList"))) break;
                totalCount = (Integer) resultMap.get("total");
                List<JSONObject> jsonObjectList = JSONObject.parseArray(String.valueOf(resultMap.get("dataList")), JSONObject.class);
                if (CollectionUtils.isEmpty(jsonObjectList)) break;
                for (JSONObject jo : jsonObjectList) {
                    TraceClueData currentTcd = new TraceClueData();
                    String _id = String.valueOf(jo.get("_id"));
                    String create_time = String.valueOf(jo.get("create_time")); // 时间戳
                    if (Objects.nonNull(jo.get("field_k3R1p__c"))) {
                        String _ip = String.valueOf(jo.get("field_k3R1p__c"));
                        currentTcd.setClueIp(_ip);
                    }
                    currentTcd.setClueId(_id);
                    currentTcd.setClueCreateTime(Long.valueOf(create_time));
                    saveEntityList.add(currentTcd);
                }
                currentOffset += currentLimit;
            }
        } while (currentOffset < totalCount && maxSafeLoopCount-- > 0);
    }

    @Test
    public void testUpdateLock() {
        Map<String, Object> data_map = new HashMap<>();
        Map<String, Object> object_data_map = new HashMap<>();
        object_data_map.put("_id", "62bd63e9ed5185000132297f");
        object_data_map.put("dataObjectApiName", "LeadsObj");
        object_data_map.put("field_wkZv1__c", "trace_keyword_tail");
        data_map.put("object_data", object_data_map);
        Map<String, Object> outerMap = new HashMap<>();
        outerMap.put("data", data_map);
        ResponseVO<FXiaoKeBaseResponse> updateClueResponse = this.fXiaoKeService.updateClueCustomizeField(outerMap);
        if (updateClueResponse.getData().getErrorMessage().contains("数据已被锁定")) {
            System.out.println("====================");
        }

    }

    @Test
    public void testLockClue() {
        Map<String, Object> outerMap = new HashMap<>();
        Map<String, Object> object_data_map = new HashMap<>();
        object_data_map.put("dataObjectApiName", "LeadsObj");
        object_data_map.put("dataIds", Lists.newArrayList("62bd63e9ed5185000132297f"));
        object_data_map.put("detailObjStrategy", 0);
        outerMap.put("data", object_data_map);
        ResponseVO<FXiaoKeBaseResponse> updateClueResponse = this.fXiaoKeService.lockCustomizeField(outerMap);
    }

    @Test
    public void testUnLockClue() {
        Map<String, Object> outerMap = new HashMap<>();
        Map<String, Object> object_data_map = new HashMap<>();
        object_data_map.put("dataObjectApiName", "LeadsObj");
        object_data_map.put("dataIds", Lists.newArrayList("62bd63e9ed5185000132297f"));
        object_data_map.put("detailObjStrategy", 0);
        outerMap.put("data", object_data_map);
        ResponseVO<FXiaoKeBaseResponse> updateClueResponse = this.fXiaoKeService.unlockCustomizeField(outerMap);
    }

    @Test
    public void testUpdateJudge() {
        String fxk_source_2 = "1";
        String fxk_source_3 = "";
        String fxk_source_4 = "9";
        String trace_source_2 = "市场获取-付费推广";
        String trace_source_4 = "";
        boolean need2Override = StringUtils.isEmpty(fxk_source_2) ||
                (fxk_source_2.equals(ClueSourceTwoConstant.TWO_1.getCode()) && trace_source_2.equals(ClueSourceTwoConstant.TWO_2.getDesc())) ||
                (fxk_source_2.equals(ClueSourceTwoConstant.TWO_1.getCode()) && StringUtils.isEmpty(fxk_source_4) && trace_source_2.equals(ClueSourceTwoConstant.TWO_1.getDesc()) && StringUtils.isNotEmpty(trace_source_4)) ||
                (fxk_source_2.equals(ClueSourceTwoConstant.TWO_2.getCode()) && fxk_source_4.equals(ClueSourceFourConstant.FOUR_10.getCode()) && trace_source_2.equals(ClueSourceTwoConstant.TWO_2.getDesc())) ||
                (fxk_source_2.equals(ClueSourceTwoConstant.TWO_2.getCode()) && fxk_source_4.equals(ClueSourceFourConstant.FOUR_11.getCode()) && trace_source_2.equals(ClueSourceTwoConstant.TWO_2.getDesc()) && !trace_source_4.equals(ClueSourceFourConstant.FOUR_10.getDesc())) ||
                (fxk_source_2.equals(ClueSourceTwoConstant.TWO_3.getCode()) && StringUtils.isEmpty(fxk_source_4) && !trace_source_2.equals(ClueSourceTwoConstant.TWO_3.getDesc()));
        String override_source_1 = "";
        String override_source_2 = "";
        String override_source_3 = "";
        String override_source_4 = "";
        if (need2Override) {
            override_source_2 = StringUtils.isNotEmpty(trace_source_2) ? ClueSourceTwoConstant.getByDesc(trace_source_2) : null;
            override_source_4 = StringUtils.isNotEmpty(trace_source_4) ? ClueSourceFourConstant.getByDesc(trace_source_4) : null;
        }
        if (StringUtils.isEmpty(fxk_source_3)) {
            override_source_3 = ClueSourceThreeConstant.THREE_1.getCode();
        }
        System.out.println("");
    }

}
