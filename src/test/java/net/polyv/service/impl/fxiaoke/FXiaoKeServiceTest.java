package net.polyv.service.impl.fxiaoke;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.business.FXiaoKeOrderStatusEnum;
import net.polyv.constant.schdule.ScheduleJobTypeConst;
import net.polyv.constant.schdule.ScheduleStatusConst;
import net.polyv.dao.primary.CustomerEffectiveScheduleJobRepository;
import net.polyv.dao.primary.SalesOpportunitiesRepository;
import net.polyv.dao.primary.business.BusinessPaymentRepository;
import net.polyv.exception.ClearingSystemRuntimeException;
import net.polyv.model.data.salesopportunities.ExtObjectDO;
import net.polyv.model.data.salesopportunities.UserFunctionDO;
import net.polyv.model.dto.ReceivableMessageDTO;
import net.polyv.model.entity.primary.CustomerEffectiveScheduleJob;
import net.polyv.model.entity.primary.SalesOpportunities;
import net.polyv.model.entity.primary.business.BusinessPayment;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.third.req.fxiaoke.FXiaoKeAddSpecReq;
import net.polyv.modules.third.req.fxiaoke.FXiaoKeClueCreateReq;
import net.polyv.modules.third.req.fxiaoke.FXiaoKeOrderOpenInfoUpdateReq;
import net.polyv.modules.third.req.fxiaoke.FXiaoKeOrderReq;
import net.polyv.modules.third.service.crm.FXiaoKeService;
import net.polyv.modules.third.stereotype.FXiaoKeApiNameEnum;
import net.polyv.modules.third.stereotype.FXiaoKeClueOriginEnum;
import net.polyv.modules.third.stereotype.FXiaoKeObjectTypeEnum;
import net.polyv.modules.third.vo.fxiaoke.FXiaoKeAuthResponse;
import net.polyv.modules.third.vo.fxiaoke.FXiaoKeBaseResponse;
import net.polyv.rest.client.live.GroupAccountClient;
import net.polyv.rest.client.live.LiveFunctEnableClient;
import net.polyv.rest.client.live.LivePackageClient;
import net.polyv.rest.client.live.business.LiveInnerFinanceApiClient;
import net.polyv.rest.client.vod.business.VodBusinessOperationClient;
import net.polyv.rest.client.vod.user.UnionUserClient;
import net.polyv.rest.model.live.GroupAccount2IncreaseChannelReq;
import net.polyv.rest.model.live.GroupAccount2VO;
import net.polyv.rest.model.live.UserFunctionVO;
import net.polyv.rest.model.vod.user.VodSetPackageVO;
import net.polyv.rest.util.ResponseUtil;
import net.polyv.service.FXiaoKeCrmService;
import net.polyv.service.account.CustomerService;
import net.polyv.service.schedule.ScheduleJobExecutor;
import net.polyv.service.schedule.ScheduleJobExecutorSimpleFactory;
import net.polyv.service.so.BusinessOperationService;
import net.polyv.service.so.SalesOpportEstablishBusinessService;
import net.polyv.service.subscribe.FXiaoKeReceivableCreateSubscribeService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.JsonMapper;
import net.polyv.util.SpecialSignUtils;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.salesopportunities.input.BusinessOperationInputVO;
import net.polyv.web.model.salesopportunities.input.SalesOpportEstablishInputVO;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: Neo
 * @date: 2022-02-24
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class FXiaoKeServiceTest {
    
    @Resource
    private FXiaoKeCrmService fXiaoKeCrmService;
    @Autowired
    private SalesOpportEstablishBusinessService salesOpportEstablishBusinessService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private FXiaoKeService fXiaoKeService;
    @Autowired
    private VodBusinessOperationClient vodBusinessOperationClient;
    @Autowired
    private LivePackageClient livePackageClient;
    @Autowired
    private LiveInnerFinanceApiClient liveInnerApiClient;
    
    @Autowired
    private BusinessOperationService businessOperationService;
    @Autowired
    private UnionUserClient unionUserClient;
    @Autowired
    private GroupAccountClient groupAccountClient;
    @Autowired
    private SalesOpportunitiesRepository salesOpportunitiesRepository;
    @Autowired
    private LiveFunctEnableClient liveFunctEnableClient;
    @Autowired
    private CustomerEffectiveScheduleJobRepository customerEffectiveScheduleJobRepository;
    
    @Autowired
    private ScheduleJobExecutorSimpleFactory scheduleJobExecutorSimpleFactory;
    
    @Autowired
    private BusinessPaymentRepository businessPaymentRepository;
    
    
    @Resource
    private FXiaoKeReceivableCreateSubscribeService fXiaoKeReceivableCreateSubscribeService;
    
    @Test
    public void testGetAuth() {
        FXiaoKeAuthResponse accessToken = this.fXiaoKeService.getAccessToken();
        System.out.println(JsonMapper.jsonToString(accessToken));
    }
    
    @Test
    public void testVodAddPackagePro() {
        String userId = "00000c1553";
        String packageId = "8";
        String expirationTime = DateFormatUtil.formatDateTimeNormal(new Date());
        String contractId = "123";
        String flow = "1024";
        String space = "1024";
        String isCreateShortVideoCate = "0";
        String period = "1";
        long ptime = System.currentTimeMillis();
        String sign = SpecialSignUtils.getAddVodPackageSign(ptime);

        try {

            ResponseEntity<WrappedResponse<?>> responseEntity = vodBusinessOperationClient.addPackagePro(
                    userId, sign, packageId, expirationTime,
                    period, String.valueOf(ptime), space, flow,
                    contractId, isCreateShortVideoCate,null
            );
            System.out.println(responseEntity);
        } catch (Exception e) {
            System.out.println(e.getLocalizedMessage());
        }


    }

    @Test
    public void testVodAddFlowOrSpace() {
        String userId = "00000c1553";
        String contractId = "123";
        long ptime = System.currentTimeMillis();
        String type = "2";
        String startDate = null;
        String endDate = null;
        String flowOrSpace = "1024";

        String sign = SpecialSignUtils.getAddVodPackageSign(ptime);
        try {
            ResponseEntity<WrappedResponse<?>> responseEntity = vodBusinessOperationClient.addFlowOrSpace(
                    userId, sign, flowOrSpace, type, startDate, String.valueOf(ptime), endDate, contractId,null
            );
            log.info("test addFlowOrSpace response == {}", responseEntity);
        } catch (Exception e) {
            log.error("test addFlowOrSpace error", e);
        }
    }

    @Test
    public void testLiveAddPackage() {
        String userId = "d8c6faded1";
        long ptime = System.currentTimeMillis();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = SpecialSignUtils.getAddPackageSign(ptime);
        String type = "daily";
        String duration = "";
        String concurrences = "10";
        String financeConcurrences = "10";
        String startDate = "2022-03-01";
        String endDate = "2022-03-31";
        String packageId = "8";
        String isUpdateStats = "Y";
        String maxChannel = "1";
        String useDailyTest = "1";
        String amount = "0.01";

        try {
            ResponseEntity<WrappedResponse<?>> responseEntity = livePackageClient.setPackagePro(
                    userId, timestamp, sign, type, duration, concurrences,
                    financeConcurrences, startDate, endDate, packageId,
                    isUpdateStats, maxChannel, useDailyTest, amount,null);
            log.info("test addFlowOrSpace response == {}", responseEntity);
        } catch (Exception e) {
            log.error("test addFlowOrSpace error", e);
        }
    }

    @Test
    public void testLiveAddChannels() {
        String userId = "d8c6faded1";
        String addChannels = "1";
        try {
            ResponseEntity<WrappedResponse<?>> responseEntity = liveInnerApiClient.addChannels(userId, addChannels);
            log.info("test addChannels response == {}", responseEntity);
        } catch (Exception e) {
            log.error("test addChannels error", e);
        }
    }


    @Test
    public void testLiveAddPptPackages() {
        String userId = "d8c6faded1";
        String pptPageCount = "1";
        String pptLineCount = "1";
        String liveDays = "1";
        String liveBillingPlan = "line";
        String amount = "999";

        String chargeType = "";
        String line = "";
        String days = "";
        String page = "";
        try {
            ResponseEntity<WrappedResponse<?>> responseEntity = liveInnerApiClient.addPptPackages(
                    userId, pptPageCount, pptLineCount, liveDays, liveBillingPlan, amount,
                    chargeType, line, days, page);
            log.info("testLiveAddPptPackages response == {}", responseEntity);
        } catch (Exception e) {
            log.error("test AddPptPackages error", e);
        }
    }

    @Test
    public void testLiveAddMicPackages() {
        String userId = "d8c6faded1";
        String liveMinutes = "1000";
        String liveDonated = "10";
        String concurrentType = null;
        String amount = "999";
        String depositType = "minutes";
        String duration = "";
        String donated = "";
        try {
            ResponseEntity<WrappedResponse<?>> responseEntity = liveInnerApiClient.addMicPackages(
                    userId, liveMinutes, liveDonated, concurrentType, amount, depositType,
                    duration, donated,null);
            log.info("test addMicPackages response == {}", responseEntity);
        } catch (Exception e) {
            log.error("test addMicPackages error", e);
        }
    }

    @Test
    public void testLiveAddGuideDurations() {
        String userId = "d8c6faded1";
        String liveMinutes = "1000";
        String amount = "999";
        try {
            ResponseEntity<WrappedResponse<?>> responseEntity = liveInnerApiClient.addGuideDurations(
                    userId, liveMinutes, amount,null);
            log.info("test addGuideDurations response == {}", responseEntity);
        } catch (Exception e) {
            log.error("test addGuideDurations error", e);
        }
    }

    /*@Test
    public void testAddContract() {
        BusinessOperationContractVO vo = new BusinessOperationContractVO();
        BusinessOperationInputVO req = new BusinessOperationInputVO();
        req.setOrderNo("UCFnAkM3o8xv3JZz15t2");
        BusinessOperation businessOperation = businessOperationService.makeBusinessOperation(req);
        vo.setBusinessOperationJson(JsonMapper.jsonToString(businessOperation));
        vo.setContractNo("***************");
        vo.setAccount("中国银行");
        vo.setContractAmount(100.00);
        vo.setContractName("测试合同开通");
        vo.setContractEndTime("2023-03-15");
        vo.setContractType("普通合同");
        vo.setCompany("测试公司0001");
        vo.setChanceName("测试销售机会名字");
        vo.setContractStartTime("2022-03-15");
        vo.setCollectedTime("2022-03-16");
        vo.setEncodedId("UCFnAkM3o8xv3JZz15t5");
        vo.setInvoiceType("增值税普通发票");
        vo.setNote("备注");
        vo.setEmail("<EMAIL>");
        vo.setOperationType("点播");
        vo.setPackageContent("");
        vo.setOperationName("LIVECUSTOM");
        vo.setUnionId("baf4ad57f6");
        vo.setIsAlreadyCreate("N");
        vo.setLoginIp("127.0.0.1");
        vo.setOperatorId("123456");
        vo.setSaleUserId("R15fSxM=");
        vo.setSaleUserName("刘志强");
        vo.setContractPaybackStatus(1);

        try {
            ResponseEntity<WrappedResponse<?>> responseEntity = contractClient.businessOperationAddContract(vo);
            log.info("test addContract response == {}", responseEntity);
        } catch (Exception e) {
            log.error("test addContract error", e);
        }
    }*/

    @Test
    public void enabledFunctionTest(){
        SalesOpportunities so = salesOpportunitiesRepository.findBySoId("631a2385e450f70001401763");
        //设置点播，直播功能开关
        ExtObjectDO extObjectDO = JSON.parseObject(so.getExt(), ExtObjectDO.class);
        SalesOpportEstablishInputVO funcEnableInputVO = new SalesOpportEstablishInputVO();
        funcEnableInputVO.setSalesOpportunitiesId(so.getId());
        funcEnableInputVO.setCustomerId(so.getCustomerId());
        List<UserFunctionDO> snapshotFunctionList = extObjectDO.getSnapshotFunctionList();
        List<UserFunctionVO> userFunctionVOList = snapshotFunctionList == null ? Collections.emptyList() :
                snapshotFunctionList.stream().map(UserFunctionVO::new).collect(Collectors.toList());
        funcEnableInputVO.setFunctionList(userFunctionVOList);
        funcEnableInputVO.setSo(so);
        funcEnableInputVO.setExtObjectDO(extObjectDO);
        salesOpportEstablishBusinessService.enabledFunctionAccess(funcEnableInputVO);
    }
    @Test
    public void testBusinessOperation(){
        BusinessOperationInputVO req = new BusinessOperationInputVO();
        req.setOrderNo("631a2385e450f70001401763");
        req.setLoginUserIp("127.0.0.1");
        req.setLoginUserId("123456");
        req.setPaybackStatus(1);
        businessOperationService.businessOperation(req);
    }
    @Test
    public void testBusinessOperationSendDingDing(){
        BusinessOperationInputVO req = new BusinessOperationInputVO();
        req.setOpenUrl("http%3A%2F%2F172.18.204.128%3A8004%2F");
        req.setOrderNo("6244322c5e7aad00012bf5f9");
        req.setLoginUserIp("127.0.0.1");
        req.setLoginUserId("123456");
        req.setPaybackStatus(1);
        businessOperationService.sendDingDingOpenBusiness(req);
    }
    @Test
    public void testUpdateFxkOrder(){
        String contractId = "123456";
        String orderNo = "6242a7ea119a57000123100c";
        String orderStatus = FXiaoKeOrderStatusEnum.ESTABLISHED.getCode();
        Date effectiveTime = new Date() ;
        Date businessEndTime = DateUtil.tomorrow();
        CommonResult result = businessOperationService.updateBusinessOpenInfo(contractId, orderNo, effectiveTime,businessEndTime,orderStatus);
        log.info("testUpdateFxkOrder result == {}",result);
    }
    @Test
    public void testAddVodPackageId(){
        VodSetPackageVO vodSetPackageVO = VodSetPackageVO.builder()
                .packageId(34)
                .unionId("146b754735")
                .build();
        try{

            WrappedResponse<?> wrappedResponse = unionUserClient.setPackage(vodSetPackageVO);
            log.info("testAddVodPackageId result  = {}",wrappedResponse);
        }
        catch (Exception e){
            log.error("testAddVodPackageId error",e);
        }
    }

    @Test
    public void testSetLivePackageId(){
        //设置直播，点播套餐id
        String livePackageId = "2";
        String liveUserId = "baf4ad57f6";
        if(StringUtils.isNotBlank(livePackageId) && StringUtils.isNumeric(livePackageId)){
            WrappedResponse<?> setEEPackageResponse = liveFunctEnableClient.setEEPackage(liveUserId, Integer.parseInt(livePackageId));
            if (ResponseUtil.isErrorResponse(setEEPackageResponse)) {
                throw new ClearingSystemRuntimeException(setEEPackageResponse.getMessage());
            }
        }
    }

    @Test
    public void testGetGroupAccountByEmail(){

        try{
//            WrappedResponse<GroupAccount2VO> wrappedResponse = groupAccountClient.getByEmail(
//                    "<EMAIL>");
//            log.info("testGetGroupAccountByEmail result  = {}", wrappedResponse);
        }
        catch (Exception e){
            log.error("testGetGroupAccountByEmail error",e);
        }
    }

    @Test
    public void testIncreaseGroupAccountChannels(){

        GroupAccount2IncreaseChannelReq req = GroupAccount2IncreaseChannelReq.builder()
                .channels(1)
                .groupId("5d9f1ab3fb")
                .build();

        try{
            WrappedResponse<?> wrappedResponse = groupAccountClient.increaseChannels(req);
            log.info("testIncreaseGroupAccountChannels result  = {}",wrappedResponse);
        }
        catch (Exception e){
            log.error("testGetGroupAccountByEmail error",e);
        }
    }

    @Test
    public void testGetCustomer() {
        //根据名字查询用户id
        String userId ="123";
        FXiaoKeClueCreateReq clueCreateReq = new FXiaoKeClueCreateReq();
        FXiaoKeClueCreateReq.Data data = clueCreateReq.new Data();
        data.setApiName(FXiaoKeApiNameEnum.CLUE.getValue());
        data.setOrigin(FXiaoKeClueOriginEnum.COMPANY_RESOURCE.getCode()); //默认公司资源
        //邀请人存在，则设置为负责人
        if (StringUtils.isNotBlank(userId)) {
            data.getOwner().add(userId);
        }
        Map<String, FXiaoKeClueCreateReq.Data> dataMap = Maps.newHashMap();
        dataMap.put("object_data", data);
        clueCreateReq.setData(dataMap);
        ResponseVO<FXiaoKeBaseResponse> clue = fXiaoKeService.createClue(clueCreateReq);
        System.out.println(clue);
    }

    @Test
    public void testGetOrder() {
        String order_no = "625532417ccf0b00017d6fa8";
        FXiaoKeOrderReq fxkOrderRequest = new FXiaoKeOrderReq();
        FXiaoKeOrderReq.ObjectData fxkOrderRequestDetail = fxkOrderRequest.new ObjectData();
        fxkOrderRequestDetail.setOrderId(order_no);
        fxkOrderRequestDetail.setDataObjectApiName(FXiaoKeApiNameEnum.ORDER.getValue());
        fxkOrderRequest.setData(fxkOrderRequestDetail);
        ResponseVO<FXiaoKeBaseResponse> fxkOrderResponse = this.fXiaoKeService.getOrderById(fxkOrderRequest);
        if (fxkOrderResponse.isSuccess()) {
            FXiaoKeBaseResponse fxkResponseData = fxkOrderResponse.getData();
            if (Objects.nonNull(fxkResponseData)) {
                Map<String, Object> orderDataMap = fxkResponseData.getData();
                String[] orderOwnerArray = JSON.parseObject(String.valueOf(orderDataMap.get("owner")), String[].class);
                if (Objects.nonNull(orderOwnerArray) && orderOwnerArray.length > 0) {
                    System.out.println(orderOwnerArray[0]);
                }
            }
        }
    }

    @Test
    public void testAddSpec() {
        FXiaoKeAddSpecReq fxkAddSpecReq = new FXiaoKeAddSpecReq();
        FXiaoKeAddSpecReq.ObjectData fxkAddSpecDetail = fxkAddSpecReq.new ObjectData();
        FXiaoKeAddSpecReq.OuterData outerData = fxkAddSpecReq.new OuterData();
        fxkAddSpecDetail.setDataObjectApiName(FXiaoKeApiNameEnum.FUNCTION_MATCHING.getValue());
        fxkAddSpecDetail.setBusinessAuditType(1);
        fxkAddSpecDetail.setEmail("<EMAIL>");
        fxkAddSpecDetail.setOrderNo("6239448ecfb819000150b572");
        String[] owners = new String[]{"FSUID_3B61121FB3B6C67E98A74F04C299E7DC"};
        fxkAddSpecDetail.setOwner(owners);
        fxkAddSpecDetail.setUnionID("baf4ad57f6");
        fxkAddSpecDetail.setViewUrl("http://172.18.204.128:8004/#/open");
        fxkAddSpecDetail.setAuditCheck(0);
        outerData.setObject_data(fxkAddSpecDetail);
        fxkAddSpecReq.setData(outerData);
        ResponseVO<FXiaoKeBaseResponse> addFxkSpecResult = this.fXiaoKeService.createFunctionMatching(fxkAddSpecReq);
    }


    @Test
    public void testUpdateOrderOpenInfo() {
        FXiaoKeOrderOpenInfoUpdateReq fxkOrderOpenUpdate = new FXiaoKeOrderOpenInfoUpdateReq();
        FXiaoKeOrderOpenInfoUpdateReq.ObjectData objD = fxkOrderOpenUpdate.new ObjectData();
        objD.setApiName(FXiaoKeApiNameEnum.ORDER.getValue());
        Map<String, FXiaoKeOrderOpenInfoUpdateReq.ObjectData> dataMap = Maps.newHashMap();
        objD.setOrderNo("62451206d6be790001418db2");
        objD.setOrderStatus("kUv26r3xI");
        objD.setContractId("123987");
        objD.setBusinessStartTime((long) LocalDateTime.now().getNano());
        objD.setBusinessEndTime(DateUtil.tomorrow().getTime());
        dataMap.put("object_data", objD);
        fxkOrderOpenUpdate.setData(dataMap);
        ResponseVO<FXiaoKeBaseResponse> orderOpenInfoResponse = this.fXiaoKeService.updateOrderOpenInfo(fxkOrderOpenUpdate);
        log.info("updateOrderOpenInfo result == {}",orderOpenInfoResponse);
    }

    /*@Test
    public void testDealThawBills() {
        List<BillListDataVO> billListDataVOList = new ArrayList<>();
        BillListDataVO bldv = new BillListDataVO();
        bldv.setCreatedTime(new Date());
        bldv.setConsumeMonth("2022-04");
        bldv.setAccountPeriod("2022-04");
        bldv.setProduction("直播");
        bldv.setItemCategory("导播台");
        bldv.setTradeType("金额结算");
        bldv.setCost(-3130552L);
        bldv.setUnpaid(0L);
        bldv.setStatus("待支付");
        bldv.setBillId(2000185L);
        bldv.setItemName("导播时长");
        bldv.setUnivalence(110000L);
        bldv.setUnivalenceUnit("元/分钟");
        bldv.setItemConsumed(new BigDecimal("-28"));
        bldv.setItemConsumedUnit("分钟");
        bldv.setIsSettledAccount(false);
        bldv.setIsSettledAccountInt(0);
        bldv.setCustomerId("**********");
        try {
            bldv.setConsumeStartDate(DateUtils.parseDate("2022-04-03 00:00:00", DateFormatUtil.FORMAT_DATETIME_NORMAL));
            bldv.setConsumeEndDate(DateUtils.parseDate("2022-04-03 00:00:00", DateFormatUtil.FORMAT_DATETIME_NORMAL));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        billListDataVOList.add(bldv);
        try {
            Map<String, String> params = new HashMap<>();
            String timeStamp = String.valueOf(System.currentTimeMillis());
            params.put("timestamp", timeStamp);
            String sign = SignUtil.getSign(params, ContractClient.SECRET_KEY, null);
            Map<String, Object> paramBody = new HashMap<>();
            paramBody.put("billList", billListDataVOList);
            ResponseEntity<WrappedResponse<?>> responseEntity = contractClient.dealThawBills(paramBody, timeStamp, sign);
            log.info("test addContract response == {}", responseEntity);
        } catch (Exception e) {
            log.error("test addContract error", e);
        }
    }*/

    /*@Test
    public void testGetPackageInfo() {
        try {
            Map<String, String> params = new HashMap<>();
            String timeStamp = String.valueOf(System.currentTimeMillis());
            params.put("encodedIds", "paramBody");
            params.put("timestamp", timeStamp);
            String sign = SignUtil.getSign(params, ContractClient.SECRET_KEY, null);
            Map<String, Object> paramBody = new HashMap<>();
            ResponseEntity<WrappedResponse<?>> responseEntity = contractClient.getPackageInfo("paramBody", timeStamp, sign);
            log.info("test getPackageInfo response == {}", responseEntity);
        } catch (Exception e) {
            log.error("test getPackageInfo error", e);
        }
    }*/

    //测试定时业务开通
    @Test
    public void testBusinessFutureOpen() {

        List<CustomerEffectiveScheduleJob> jobList;
        Date date = DateUtil.tomorrow();
        int count = 0;
        for (; ; count++) {


            // 获取待执行任务
            jobList = customerEffectiveScheduleJobRepository.findByStatusAndEffectiveTimeOrderByPriorityAsc(ScheduleStatusConst.WAIT,
                    DateUtil.beginOfDay(date), PageRequest.of(count, 10));
            if (CollectionUtils.isEmpty(jobList)) {
                break ;
            }

            // 执行任务
            jobList.forEach(job -> {
                ScheduleJobExecutor executor =
                        scheduleJobExecutorSimpleFactory.get(ScheduleJobTypeConst.getByType(job.getType()));

                log.info("执行定时生效job，类型：{}，任务id：{}", ScheduleJobTypeConst.getByType(job.getType()), job.getId());

                int status = ScheduleStatusConst.EXECUTED;
                try {
                    boolean success = executor.process(job);
                    if (!success) {
                        log.error("执行任务id: {}, 任务执行失败", job.getId());
                        status = ScheduleStatusConst.ERROR;
                    }
                } catch (Exception e) {
                    log.error("任务执行失败, 任务id:"+job.getId(), e);
                    status = ScheduleStatusConst.ERROR;
                }

                // 实时保存，确保不会因为后面的报错导致回滚
                job.setStatus(status);
                job.setUpdateTime(new Date());
                customerEffectiveScheduleJobRepository.save(job);
            });
        }

        log.info("用户定时任务生效执行完毕，本次执行记录数: {}", count);

    }

   /* @Test
    public void updateContractPayBackInfo(){

        String soId = "625fdf61b4f8470001e7503c";
        String account = "中国银行";
        String collectedTime = "2022-04-21";
        SalesOpportunities so = this.salesOpportunitiesRepository.findBySoId(soId);
        if (Objects.isNull(so)) {
            log.error(String.format("updateSoPaymentInfo 找不到对应销售机会[%s]", soId));
        }
        ContractPayBackUpdateVO contractPayBackUpdateVO = ContractPayBackUpdateVO.builder()
                .encodedId(soId).account(account)
                .contractAmount(new BigDecimal(so.getContractAmount())
                        .divide(new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION),2, RoundingMode.HALF_UP).doubleValue())
                .collectedTime(collectedTime)
                .build();
        ResponseEntity<WrappedResponse<?>> wrappedResponseResponseEntity = contractClient.updateContractPayBackInfo(contractPayBackUpdateVO);
        if(ResponseUtil.isErrorResponse(wrappedResponseResponseEntity)){
            log.error(String.format("updateContractPayBackInfo失败，错误原因:%s",wrappedResponseResponseEntity.getBody().getMessage()));
        }
    }*/

    @Test
    public void testFilterGroupAccount(){
        List<String> customerIds = Lists.newArrayList();
        customerIds.add("e76cc3e4dc");
        customerIds.add("ed802a0b80");
        customerIds.add("f2df7033e9");
        //过滤掉集团主账号
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(customerIds)){
            customerIds = customerIds.stream().filter(customerId ->
                    AccountTypeEnum.NORMAL.getCode().equals(customerService.getAccountType(customerId)))
                    .collect(Collectors.toList());
            log.info("customerIds == {}", customerIds);
        }
    }
    
    @Test
    public void testGetOrderPayment() {
        String soId = "62662bb5d9dc160001c50fea";
        BusinessPayment businessPayment = businessPaymentRepository.findFirstBySoIdOrderByCreateTimeDesc(soId);
        log.info("testGetOrderPayment result == {}", businessPayment);
    }
    
    /**
     * 测试创建应收记录
     */
    @Test
    public void testCreateReceivableRecord() {
        Map<String, Object> outerMap = Maps.newHashMap();
        Map<String, Object> dataMap = Maps.newHashMap();
        Map<String, Object> objectMap = Maps.newHashMap();
        List<String> ownerList = (List<String>) fXiaoKeCrmService.getSingleValueByCondition("field_7Hf4C__c", "1011",
                "owner", FXiaoKeApiNameEnum.PERSONAL.getValue(), FXiaoKeObjectTypeEnum.DEFAULT.getCode());
        objectMap.put("dataObjectApiName", "accounts_receivable_Obj__c");
        objectMap.put("receivable_type__c", 1);
        objectMap.put("owner", ownerList);
        objectMap.put("customer_name__c", "测试公司");
        objectMap.put("unionid__c", "**********");
        objectMap.put("payment_plan__c", null);
        objectMap.put("receivable_amount__c", 100);
        objectMap.put("receivable_status__c", 1);
        objectMap.put("payment_amount__c", 100);
        objectMap.put("unpaid_amount__c", 10);
    
        objectMap.put("latest_return_date__c", System.currentTimeMillis()); //日期格式必须传时间戳
        objectMap.put("is_overdue__c", 1);
        objectMap.put("overdue_day__c", 9);
    
        dataMap.put("object_data", objectMap);
        outerMap.put("data", dataMap);
        try {
            ResponseVO<FXiaoKeBaseResponse> responseVO = fXiaoKeService.createCustomObjCustomizeField(outerMap);
            log.info("testCreateReceivableRecord result == {}", responseVO);
        } catch (Exception e) {
            log.error("testCreateReceivableRecord error", e);
        }
    
        /**
         * {
         * 	"corpAccessToken": "677AF8B04598ADD2C8A2E7930976E550",
         * 	"currentOpenUserId": "FSUID_6ECF128FF9236B5604716FF338877F1F",
         * 	"data": {
         * 		"object_data": {
         * 			"owner": ["1011"],
         * 			"latest_return_date__c": "2023-04-14",
         * 			"receivable_status__c": 1,
         * 			"overdue_day__c": 9,
         * 			"customer_name__c": "测试公司",
         * 			"unionid__c": "**********",
         * 			"dataObjectApiName": "accounts_receivable_Obj__c",
         * 			"unpaid_amount__c": 10,
         * 			"receivable_amount__c": 100,
         * 			"is_overdue__c": 1,
         * 			"receivable_type__c": 1,
         * 			"payment_amount__c": 100
         *                }* 	},
         * 	"corpId": "FSCID_305BBC9040155754AC9DAE1989057BFC"
         * }
         */
    }
    
    @Test
    public void testQuerySingleValue() {
        Object ownerId = fXiaoKeCrmService.getSingleValueByCondition("field_7Hf4C__c", "1011", "owner",
                FXiaoKeApiNameEnum.PERSONAL.getValue(), FXiaoKeObjectTypeEnum.DEFAULT.getCode());
        log.info("ownerId = {}", ownerId);
    }
    
    @Test
    public void testPushReceivable() {
        for (int i = 0; i < 10; i++) {
            ReceivableMessageDTO messageDTO = new ReceivableMessageDTO();
            messageDTO.setCreateTime(new Date());
            messageDTO.setCustomerId("4dc29dc3f5");
            messageDTO.setReceivableType(3);
            messageDTO.setReceivableAmount(10000);
            messageDTO.setBeyondConcurrenceId(11L);
    
            fXiaoKeReceivableCreateSubscribeService.pushReceivableToRedis(messageDTO);
        }
    }
    
    @Test
    public void testGetLivePackage() {
        try {
            
            ResponseEntity<WrappedResponse<?>> depositPackages = liveInnerApiClient.getDepositPackages();
            log.info("depositPackages == {}", depositPackages);
        } catch (Exception e) {
            log.error("getDepositPackages error", e);
        }
    }
}

