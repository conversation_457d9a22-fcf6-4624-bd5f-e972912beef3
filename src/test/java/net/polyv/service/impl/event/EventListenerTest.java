package net.polyv.service.impl.event;

import net.polyv.constant.event.ClearingEventEnum;
import net.polyv.event.CleanCacheEvent;
import net.polyv.model.event.ClearingEvent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class EventListenerTest {
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Test
    public void clearEventTest(){

        ClearingEvent clearingEvent = new ClearingEvent(ClearingEvent.class, ClearingEventEnum.FINISH,null,null);
        this.applicationEventPublisher.publishEvent(clearingEvent);
        System.out.println("begin to push clear event======");
    }

    @Test
    public void cleanCacheEventTest() {
        CleanCacheEvent event = new CleanCacheEvent(CleanCacheEvent.class, "vod","123456test");
        this.applicationEventPublisher.publishEvent(event);
    }
}
