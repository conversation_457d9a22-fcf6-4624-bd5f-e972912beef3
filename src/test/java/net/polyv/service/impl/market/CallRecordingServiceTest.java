package net.polyv.service.impl.market;

import com.alibaba.excel.util.DateUtils;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.Callback;
import com.aliyun.oss.model.PutObjectRequest;
import net.polyv.service.market.CallRecordingService;
import net.polyv.service.market.FXiaoKeClueSyncService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.web.model.market.CallRecordsEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: Neo
 * @date: 2022-05-20
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CallRecordingServiceTest {

    @Autowired
    private CallRecordingService callRecordingService;

    @Autowired
    private FXiaoKeClueSyncService fXiaoKeClueSyncService;



    @Test
    public void testSaveRecords() {
        List<CallRecordsEntity> creList = new ArrayList<>();
        CallRecordsEntity cre = new CallRecordsEntity();
        cre.setCallType("呼入");
        cre.setCallDuration("47秒");
        cre.setOtherNumber("565");
        cre.setSelfNumber("18620748010");
        cre.setStartTime(1644110826526L);
        creList.add(cre);
        this.callRecordingService.addCallRecords(creList);
    }

    @Test
    public void testUpload() {
        String endpoint = "oss-cn-shenzhen-internal.aliyuncs.com";
        // 填写步骤五获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
        String accessKeyId = "STS.NTEH9G9yxj9oSjTFwqN4e1sgB";
        String accessKeySecret = "FfvWMPiFCSGfwfMQB4RSQu5gJt3QsBZkZjMfwResRjvY";
        // 填写步骤五获取的安全令牌SecurityToken。
        String securityToken = "CAISgwJ1q6Ft5B2yfSjIr5fwA4Pz1KZZ3fuEUUzlonckQrtJ3rbMoDz2IHlEfHdrAuEWtvQ0lW1W7fcalqQqFcIZHRObN5Uss84OqbZKlxU4457b16cNrbH4M0rxYkeJ8a2/SuH9S8ynCZXJQlvYlyh17KLnfDG5JTKMOoGIjpgVBbZ+HHPPD1x8CcxROxFppeIDKHLVLozNCBPxhXfKB0ca0WgVy0EHsPvlmJbEtkCP3AWrl7BF/76ceMb0M5NeW75kSMqw0eBMca7M7TVd8RAi9t0t1fwcp2yc74HFUwIAvkvdYrLOjdRrLR5kYK8hAKBFqvH6nvBgoOvJkID629qM3Tbu9su0GoABgEoPwuTV/pVywL98hA8Fq9YyYBhFdkByyR43ZFwkHXk1xpt6gWct9FbpGd3t/Ixuv9uMDzg8qyjtZ6zWlI3EvoKZtSER3rsaJE6MRKPJ+HeUU3TrDpuzOgCJnFWAI/qlY60pPpjRs07jmXPjpTFqiSqqjpDG4EQU8cuHKRyLDm8=";
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, securityToken);
        // 将本地文件exampletest.txt上传至目标存储空间examplebucket下的目录exampledir。
        PutObjectRequest putObjectRequest = new PutObjectRequest("polyv-financepro", "uploadvoice/20220601/exampletest.txt", new File("K:\\localpath\\exampletest.txt"));

        // 如果需要上传时设置存储类型与访问权限，请参考以下示例代码。
        // ObjectMetadata metadata = new ObjectMetadata();
        // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
        // metadata.setObjectAcl(CannedAccessControlList.Private);
        // putObjectRequest.setMetadata(metadata);
        Callback callback = new Callback();
        callback.setCallbackUrl("https://fxiaoketest.polyv.net/crm-callback/oss-upload-callback");
        callback.setCallbackHost("fxiaoketest.polyv.net");
        // 设置发起回调时请求body的值。
        callback.setCallbackBody("object=${object}&size=${size}&bucket=${bucket}&etag=${etag}}&customerPhone=18630036313&sellerPhone=18620748010&fileId=9984");
        // 设置发起回调请求的Content-Type。
        callback.setCalbackBodyType(Callback.CalbackBodyType.URL);
        // 设置发起回调请求的自定义参数，由Key和Value组成，Key必须以x:开始。
        // callback.addCallbackVar("x:sellerPhone", "18620748010");
        // callback.addCallbackVar("x:customerPhone", "565");
        // callback.addCallbackVar("x:fileId", "9984");
        putObjectRequest.setCallback(callback);

        ossClient.putObject(putObjectRequest);

        ossClient.shutdown();
    }


    @Test
    public void testSyncClue() {
        try {
            fXiaoKeClueSyncService.syncSpecifyPoolClue();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    @Test
    public void testUpdateClue() {
        try {
            fXiaoKeClueSyncService.matchClueAndUpdate();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    @Test
    public void testDate() {
        try {
            Date queryDate = DateUtil.getDateAfterDays(1, DateUtils.parseDate("2022-06-22", DateFormatUtil.FORMAT_DATE_NORMAL));
            System.out.println(DateUtils.format(queryDate,DateFormatUtil.FORMAT_DATETIME_NORMAL));
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    @Test
    public void upload2Crm() {
        this.callRecordingService.upload2Crm();
    }

    @Test
    public void testComplementMatchIds() {
        this.callRecordingService.complementMatchIds();
    }

}
