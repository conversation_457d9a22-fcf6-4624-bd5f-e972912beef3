package net.polyv.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.polyv.service.CacheService;
import net.polyv.service.NotifySmsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description:
 * @author: Neo
 * @date: 2022-04-18
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CacheTest {

    @Autowired
    private CacheService cacheService;

    @Test
    public void testCleanCache() {
        String customerId = "baf4ad57f6";
        cacheService.cleanLiveRestrictPcsResult(customerId);
    }
}
