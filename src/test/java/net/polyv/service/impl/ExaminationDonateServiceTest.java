package net.polyv.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.constant.GlobalConfigConst;
import net.polyv.constant.ResourceAlterTypeConst;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.constant.examinationDonate.BusinessTypeEnum;
import net.polyv.constant.examinationDonate.LiveDailyBillingPlanEnum;
import net.polyv.constant.resource.ResourceSourceConst;
import net.polyv.dao.primary.custom.ResourceAlterationRecordDao;
import net.polyv.dao.primary.examinationDonate.SaleDepartmentBaseAmountRepository;
import net.polyv.dao.primary.resource.ResourceAlterationRecordRepository;
import net.polyv.model.entity.primary.examinationDonate.SaleDepartmentBaseAmount;
import net.polyv.model.entity.primary.resource.ResourceAlterationRecord;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.AddChannelsRequest;
import net.polyv.modules.pcs.api.req.AddResourceRequest;
import net.polyv.modules.pcs.api.req.AmountCalculateRequest;
import net.polyv.modules.pcs.api.req.AmountListRequest;
import net.polyv.modules.pcs.api.req.AmountRequest;
import net.polyv.modules.pcs.api.req.AmountUseDetailRequest;
import net.polyv.modules.pcs.api.req.ExtraAmountAddRequest;
import net.polyv.modules.pcs.api.req.GuideDurationRequest;
import net.polyv.modules.pcs.api.req.LiveDailyConcurrenceRequest;
import net.polyv.modules.pcs.api.req.LiveDurationRequest;
import net.polyv.modules.pcs.api.req.LivePeakConcurrenceRequest;
import net.polyv.modules.pcs.api.req.MicDurationRequest;
import net.polyv.modules.pcs.api.req.PackageSpecificationGetRequest;
import net.polyv.modules.pcs.api.req.PrtcRequest;
import net.polyv.modules.pcs.api.req.VodFlowRequest;
import net.polyv.modules.pcs.api.req.VodPackageRequest;
import net.polyv.modules.pcs.api.req.VodSpaceRequest;
import net.polyv.modules.pcs.api.req.testdonate.FunctionRequest;
import net.polyv.modules.pcs.api.req.testdonate.NumberRequest;
import net.polyv.modules.pcs.api.vo.AmountCalculateResultVO;
import net.polyv.modules.pcs.api.vo.AmountListResultVO;
import net.polyv.modules.pcs.api.vo.AmountUseDetailResultVO;
import net.polyv.modules.pcs.api.vo.PackageSpecificationGetResultVO;
import net.polyv.modules.pcs.api.vo.ResourceAndBusinessTypeGetResultVO;
import net.polyv.service.CacheService;
import net.polyv.service.examinationDonate.AmountManageService;
import net.polyv.service.examinationDonate.ResourceSpecificationService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.util.JsonMapper;
import net.polyv.web.controller.examinationDonate.ResourceSpecificationController;
import net.polyv.web.model.common.PageDataVO;

/**
 * 测试赠送资源管理需求单元测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class ExaminationDonateServiceTest {

    @Value("${spring.redis.host}")
    private String redisIp = "";

    @Value("${spring.redis.port}")
    private String redisPort = "";
    @Autowired
    private AmountManageService amountManageService;
    @Autowired
    private SaleDepartmentBaseAmountRepository saleDepartmentBaseAmountRepository;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private ResourceSpecificationService resourceSpecificationService;

    @Autowired
    ResourceAlterationRecordRepository resourceAlterationRecordRepository;

    @Autowired
    private ResourceAlterationRecordDao resourceAlterationRecordDao;
    
    @Resource
    private ResourceSpecificationController resourceSpecificationController;
    @Test
    public void testSyncBaseAmountToRedis(){
        try {
            //先删除原来hash数据
            redisTemplate.delete(GlobalConfigConst.SALE_DEPARTMENT_BASE_AMOUNT_KEY);
            //批量同步mysql数据到redis
            Pageable pageable = PageRequest.of(0, 1);
            Page<SaleDepartmentBaseAmount> saleDepartmentBaseAmountPage = saleDepartmentBaseAmountRepository.findAll(pageable);
            while(!saleDepartmentBaseAmountPage.isEmpty()){
                syncDbToRedis(saleDepartmentBaseAmountPage.getContent());
                pageable = PageRequest.of(pageable.getPageNumber() + 1, pageable.getPageSize());
                saleDepartmentBaseAmountPage = saleDepartmentBaseAmountRepository.findAll(pageable);
            }

        } catch (Exception e) {
            log.error(String.format("sync baseAmount exception...%s", e.getMessage()), e);
        } finally {
            log.info("end to sync baseAmount....");
        }
    }

    @Test
    public void testGetBaseAmount(){

        String saleUserId = "1011";

        try{
            SaleDepartmentBaseAmount baseAmount = cacheService.getBySaleUserId(saleUserId);
            HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
            opsForHash.delete(GlobalConfigConst.SALE_DEPARTMENT_BASE_AMOUNT_KEY,saleUserId);
            baseAmount = cacheService.getBySaleUserId(saleUserId);

        }
        catch (Exception e){
            log.error("testGetBaseAmount error...",e);
        }
    }

    @Test
    public void testGetResourceAndBusinessType(){
        CommonResult<ResourceAndBusinessTypeGetResultVO> typeList =
                resourceSpecificationService.getResourceAndBusinessTypeList(
                "examination.donate.resource.type", AccountTypeEnum.GROUP2.getCode());
        log.info("testGetResourceAndBusinessType result == {}", JsonMapper.jsonToString(typeList));
    
        CommonResult<ResourceAndBusinessTypeGetResultVO> typeList2 =
                resourceSpecificationService.getResourceAndBusinessTypeList(
                "examination.donate.resource.type", AccountTypeEnum.NORMAL.getCode());
        log.info("testGetResourceAndBusinessType result == {}", JsonMapper.jsonToString(typeList2));
    }

    @Test
    public void testGetPackageSpecification(){
        PackageSpecificationGetRequest Request = new PackageSpecificationGetRequest();
        Request.setResourceType(1);
        Request.setBusinessType(5);
        Request.setAccountType(AccountTypeEnum.GROUP2.getCode());
        Request.setCustomerId("baf4ad57f6");  //普通账号
        //Request.setCustomerId("b36f07ef0f"); //集团主账号
        CommonResult<PackageSpecificationGetResultVO> commonResult = resourceSpecificationService.getPackageSpecification(Request);
        log.info("testGetPackageSpecification result == {}",commonResult);
    }

    /**
     * 获取额度详情列表
     */
    @Test
    public void testGetAmountDetail(){
        AmountCalculateRequest Request = new AmountCalculateRequest();
        Request.setSaleUserId("1011");
        Request.setBusinessType(9);
        Request.setAmount("100");
        CommonResult result = resourceSpecificationService.calculateAmount(Request);
        log.info("getAmountDetail result == {}",result);
    }

    @Test
    public void testAddVodPackageResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("baf4ad57f6");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.NORMAL.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.VOD_PACKAGE.getCode());
        Request.setRemark("VOD_PACKAGE");
        Request.setVodPackageRequest(getVodPackageInput());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testAddVodPackageResource error",e);
        }
    }

    @Test
    public void testAddVodSpaceResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("b36f07ef0f");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.GROUP2.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.VOD_SPACE.getCode());
        Request.setRemark("VOD_SPACE");
        Request.setVodSpaceRequest(getVodSpaceInput());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testAddVodSpaceResource error",e);
        }
    }

    @Test
    public void testAddVodFlowResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("b36f07ef0f");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.GROUP2.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.VOD_FLOW.getCode());
        Request.setRemark("VOD_FLOW");
        Request.setVodFlowRequest(getVodFlowRequest());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testAddVodFlowResource error",e);
        }
    }
    @Test
    public void testAddLiveDurationResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("baf4ad57f6");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.NORMAL.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.LIVE_DURATION.getCode());
        Request.setRemark("LIVE_DURATION");
        Request.setLiveDurationRequest(getLiveDurationInput());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testAddLiveDurationResource error",e);
        }
    }
    @Test
    public void testAddGuideDurationResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("baf4ad57f6");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.NORMAL.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.GUIDE_DURATION.getCode());
        Request.setRemark("GUIDE_DURATION");
        Request.setGuideDurationRequest(getGuideDurationInput());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testAddGuideDurationResource error",e);
        }
    }

    @Test
    public void testAddMicDurationResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("baf4ad57f6");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.NORMAL.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.MIC_DURATION.getCode());
        Request.setRemark("MIC_DURATION");
        Request.setMicDurationRequest(getMicDurationInput());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testAddMicDurationResource error",e);
        }
    }

    @Test
    public void testAddLiveDailyResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("b36f07ef0f");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.GROUP2.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.LIVE_DAILY_TEST.getCode());
        Request.setRemark("LIVE_DAILY");
        Request.setLiveDailyConcurrenceRequest(getLiveDailyInput());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testAddLiveDailyResource error",e);
        }
    }


    @Test
    public void testAddLivePeakResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("b36f07ef0f");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.GROUP2.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.LIVE_PEAK_CONCURRENCE.getCode());
        Request.setRemark("LIVE_PEAK_CONCURRENCE");
        Request.setLivePeakConcurrenceRequest(getLivePeakInput());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testAddLivePeakResource error",e);
        }
    }


    @Test
    public void testAddChannelsResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("baf4ad57f6");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.NORMAL.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.ADD_CHANNELS.getCode());
        Request.setRemark("ADD_CHANNELS");
        Request.setAddChannelsRequest(getAddChannelsInput());
        try {

                resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("getAddChannelsInput error",e);
        }
    }
    @Test
    public void testPrtcDurationResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("baf4ad57f6");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.NORMAL.getCode());
        Request.setResourceType(2);
        Request.setBusinessType(BusinessTypeEnum.PRTC_AMOUNT.getCode());
        Request.setRemark("PRTC_AMOUNT");
        Request.setPrtcRequest(getPrtcInput());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testPrtcDurationResource error",e);
        }
    }
    @Test
    public void testAmountResource(){
        AddResourceRequest Request = new AddResourceRequest();
        Request.setCustomerId("baf4ad57f6");
        Request.setSaleUserId("1011");
        Request.setAccountType(AccountTypeEnum.NORMAL.getCode());
        Request.setResourceType(1);
        Request.setBusinessType(BusinessTypeEnum.AMOUNT.getCode());
        Request.setRemark("AMOUNT");
        Request.setAmountRequest(getAmountInput());
        try {

            resourceSpecificationService.addResource(Request);
        }
        catch (Exception e){
            log.error("testAmountResource error",e);
        }
    }
    private PrtcRequest getPrtcInput(){
        PrtcRequest prtcRequest = new PrtcRequest();
        prtcRequest.setAmount("10");
        prtcRequest.setExpireDate(DateUtil.getDateAfterDays(30));
        return prtcRequest;
    }
    private AmountRequest getAmountInput(){
        AmountRequest amountRequest = new AmountRequest();
        amountRequest.setAmount("10");
        amountRequest.setExpireDate(DateUtil.getDateAfterDays(30));
        return amountRequest;
    }
    private VodPackageRequest getVodPackageInput(){
        VodPackageRequest vodPackageRequest = new VodPackageRequest();
        vodPackageRequest.setPackageSpace("10");
        vodPackageRequest.setExpireDate(DateUtil.getDateAfterDays(30));
        vodPackageRequest.setTestPackageId(34);
        vodPackageRequest.setPackageMonthFlow("10");
        vodPackageRequest.setIsOpenShortVideo("Y");
        vodPackageRequest.setTestPackageName("测试套餐名字");
        vodPackageRequest.setTestPackageDesc("测试套餐描述");
        return vodPackageRequest;

     }

     private AddChannelsRequest getAddChannelsInput(){
        AddChannelsRequest addChannelsRequest = new AddChannelsRequest();
        addChannelsRequest.setChannels(10);
        return addChannelsRequest;
     }
    private VodSpaceRequest getVodSpaceInput(){
        VodSpaceRequest vodSpaceRequest = new VodSpaceRequest();
        vodSpaceRequest.setVodSpace("10");
        vodSpaceRequest.setSpaceExpireDate(DateUtil.getDateAfterDays(30));
        return vodSpaceRequest;

    }

    private LivePeakConcurrenceRequest getLivePeakInput(){
        LivePeakConcurrenceRequest livePeakConcurrenceRequest = new LivePeakConcurrenceRequest();
        livePeakConcurrenceRequest.setConcurrence(200);
        livePeakConcurrenceRequest.setLiveStartDate(DateUtil.getDateAfterDays(1));
        livePeakConcurrenceRequest.setLiveEndDate(DateUtil.getDateAfterDays(30));
        return livePeakConcurrenceRequest;
    }
    private LiveDurationRequest getLiveDurationInput(){
        LiveDurationRequest liveDurationRequest = new LiveDurationRequest();
        liveDurationRequest.setDuration(200);
        liveDurationRequest.setExpireDate(DateUtil.getDateAfterDays(30));
        liveDurationRequest.setIsCustomizePackage("Y");
        liveDurationRequest.setLiveTestPackageId(4);
        return liveDurationRequest;

    }

    private GuideDurationRequest getGuideDurationInput(){
        GuideDurationRequest guideDurationRequest = new GuideDurationRequest();
        guideDurationRequest.setDuration(200);
        guideDurationRequest.setExpireDate(DateUtil.getDateAfterDays(30));
        return guideDurationRequest;

    }

    private MicDurationRequest getMicDurationInput(){
        MicDurationRequest micDurationRequest = new MicDurationRequest();
        micDurationRequest.setDuration(300);
        micDurationRequest.setExpireDate(DateUtil.getDateAfterDays(30));
        return micDurationRequest;

    }
    private LiveDailyConcurrenceRequest getLiveDailyInput(){
        LiveDailyConcurrenceRequest Request = new LiveDailyConcurrenceRequest();
        Request.setConcurrence(100);
        Request.setLiveStartDate(DateFormatUtil.parseDateNormal("2022-06-24"));
        Request.setLiveEndDate(DateFormatUtil.parseDateNormal("2022-06-24"));
        Request.setRealLimit("Y");
        Request.setBillingPlanType(LiveDailyBillingPlanEnum.TEST.getCode());
        return Request;
    }
    private VodFlowRequest getVodFlowRequest(){
        VodFlowRequest vodFlowRequest = new VodFlowRequest();
        vodFlowRequest.setVodFlow("10");
        vodFlowRequest.setFlowExpireDate(DateUtil.getDateAfterDays(60));
        vodFlowRequest.setTrafficType(4);
        return vodFlowRequest;
    }
    public void syncDbToRedis(List<SaleDepartmentBaseAmount> list){
        if(!CollectionUtils.isEmpty(list)){
            HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
            Map<String,SaleDepartmentBaseAmount> map = list.stream().collect(Collectors.toMap(
                    SaleDepartmentBaseAmount::getSaleUserId, Function.identity(), (key1, key2) -> key2));
            opsForHash.putAll(GlobalConfigConst.SALE_DEPARTMENT_BASE_AMOUNT_KEY,map);
        }
    }

    @Test
    public void testGetSales(){
        List<SaleDepartmentBaseAmount> list = amountManageService.getBySaleUserName("黄");

        log.info("getBySaleUserName result :{}",list);
    }

    @Test
    public void testGetAmountList(){
        AmountListRequest request = new AmountListRequest();
        request.setDate("2022-06");
        //Request.setSaleUserId("1011");
        request.setSaleDepartmentType("DS");
        request.setPage(1);
        request.setPageSize(10);
        PageDataVO<AmountListResultVO> list = amountManageService.pageAmountList(request);

        log.info("testGetAmountList result :{}",list);
    }


    @Test
    public void testGetAmountUseDetailList(){
        AmountUseDetailRequest request = new AmountUseDetailRequest();
     /*   request.setDate("2022-06");
        request.setSaleUserId("1011");
        request.setSaleDepartmentType("DS");
     
        request.setEmail("<EMAIL>");*/
        request.setCustomerId("cec4219165");
        request.setPage(1);
        request.setPageSize(20);
        PageDataVO<AmountUseDetailResultVO> list = amountManageService.pageAmountUseDetail(request);

        log.info("testGetAmountUseDetailList result :{}",list);
    }


    @Test
    public void addExtraAmount(){
        ExtraAmountAddRequest request = new ExtraAmountAddRequest();
        request.setSaleUserId("1011");
        request.setExtraAmount("100");
        request.setOperator("刘鉴涛");
        amountManageService.addExtraAmount(request);
    }

    @Test
    public void testClearExpireResource(){
        String expireDate = DateFormatUtil.formatDateNormal(DateUtil.getDateAfterDays(-1)); //到期时间是前一天
        Date now = DateUtil.getCurrentDay();
        List<ResourceAlterationRecord> expireResourceRecords
                = resourceAlterationRecordRepository.getExpireResourceRecord(expireDate);
        if(!CollectionUtils.isEmpty(expireResourceRecords)){
            for(ResourceAlterationRecord record : expireResourceRecords){
                log.info("TestAndDonateResourceExpireClearingScheduleRunner clear resource == {}",record);
                resourceAlterationRecordDao.saveResourceAlteration(now, record.getCustomerId(), record.getResourceCode(),
                        ResourceAlterTypeConst.expire_clean, -(record.getAlteration()),
                        ResourceSourceConst.PERMANENT,null,record.getResourceType(),null);
            }
        }

        
     
    }

    @Test
    public void testGetRedis(){
        log.info("redisIp == {},redisPort =={}",redisIp,redisPort);
        String key = "ESTABLISH_PACKAGE_BUSINESS_KEY:1cd4d0e985e94d2e9a78feab2c2f0cd0";
        Object businessInfo = redisTemplate.opsForValue().get(key);
        log.info("info == {}",businessInfo);
    }
    @Test
    public void testBillingItemCalculateAmount(){
        //次数类
        AmountCalculateRequest amountCalculateRequest = new AmountCalculateRequest();
        amountCalculateRequest.setBusinessType(475);
        amountCalculateRequest.setResourceType(1);
        amountCalculateRequest.setNumber(30);
        amountCalculateRequest.setSaleUserId("1163");
        amountCalculateRequest.setExpireDate("2022-09-29");
        ResponseVO<AmountCalculateResultVO> amountDetail = this.resourceSpecificationController.getAmountDetail(
                amountCalculateRequest);
    log.info("number calculate result=>{}",amountDetail);
    /*         amountCalculateRequest = new AmountCalculateRequest();
        amountCalculateRequest.setBusinessType(265);
        amountCalculateRequest.setResourceType(1);
        amountCalculateRequest.setExpireDate("2022-12-03");
        amountCalculateRequest.setCode("liveCloudMusic");
        amountCalculateRequest.setSaleUserId("1011");
        amountDetail = this.resourceSpecificationController.getAmountDetail(amountCalculateRequest);
        log.info("function calculate result=>{}",amountDetail);*/
    }
    @Test
    public void testFunctionResource(){
        AddResourceRequest req = new AddResourceRequest();
        req.setCustomerId("baf4ad57f6");
        req.setSaleUserId("1011");
        req.setAccountType(AccountTypeEnum.NORMAL.getCode());
        req.setResourceType(1);
        req.setBusinessType(266);
        req.setRemark("测试开通限定使用时长计费项");
        req.setFunctionRequest(getFunctionRequest());
        String businessId = UUID.randomUUID().toString().replaceAll("-","");
        req.setBusinessId(businessId);
        try {
            resourceSpecificationService.billingItemConfigPackage(req);
            log.info("生成套餐信息成功！");
        }
        catch (Exception e){
            log.error("testAddVodPackageResource error",e);
        }
    }
    
    @Test
    public void testNumberResource() {
        AddResourceRequest req = new AddResourceRequest();
        req.setCustomerId("baf4ad57f6");
        req.setSaleUserId("1011");
        req.setAccountType(AccountTypeEnum.NORMAL.getCode());
        req.setRemark("测试开通次数计费项");
        //开通赠送套餐
        req.setResourceType(2);
        req.setBusinessType(399);
        req.setNumberRequest(getNumberRequest());
        String businessId = UUID.randomUUID().toString().replaceAll("-", "");
        req.setBusinessId(businessId);
        try {
            resourceSpecificationService.billingItemConfigPackage(req);
            log.info("生成套餐信息成功！");
        } catch (Exception e) {
            log.error("testAddVodPackageResource error", e);
        }
        //开通测试套餐
        req.setResourceType(1);
        req.setBusinessType(399);
        req.setNumberRequest(getNumberRequest());
        businessId = UUID.randomUUID().toString().replaceAll("-", "");
        req.setBusinessId(businessId);
        try {
            resourceSpecificationService.billingItemConfigPackage(req);
            log.info("生成套餐信息成功！");
        } catch (Exception e) {
            log.error("testAddVodPackageResource error", e);
        }
    }
    
    private NumberRequest getNumberRequest() {
        NumberRequest numberRequest = new NumberRequest();
        numberRequest.setNumber(12);
        numberRequest.setExpireDate("2022-10-09");
        return numberRequest;
    }
    
    private FunctionRequest getFunctionRequest() {
        FunctionRequest functionRequest = new FunctionRequest();
        functionRequest.setCode("liveCloudMusic");
        functionRequest.setExpireDate("2022-09-30");
        return functionRequest;
    }
    
}
