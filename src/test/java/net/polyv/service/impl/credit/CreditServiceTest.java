package net.polyv.service.impl.credit;

import com.alibaba.fastjson.JSON;
import net.polyv.model.data.balance.BalanceInsufficientNotifyResult;
import net.polyv.model.data.credit.CreditInsufficientNotifyResult;
import net.polyv.service.CreditService;
import net.polyv.web.model.concurrence.AccountInsufficientRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description:
 * @author: Neo
 * @date: 2021-09-15
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CreditServiceTest {

    /**
     * 授信服务
     */
    @Autowired
    private CreditService creditService;


    @Test
    public void testCreditInsufficientNotify() {
        CreditInsufficientNotifyResult cinr = this.creditService.creditInsufficientRemind(
                new AccountInsufficientRequest(null,"10000", null));
        System.out.println(JSON.toJSON(cinr));
    }

    @Test
    public void testBalanceInsufficientNotify() {
        AccountInsufficientRequest req = new AccountInsufficientRequest();
        req.setAmountThreshold("1000");
        BalanceInsufficientNotifyResult binr = this.creditService.balanceInsufficientRemind(req);
        System.out.println(JSON.toJSON(binr));
    }
}
