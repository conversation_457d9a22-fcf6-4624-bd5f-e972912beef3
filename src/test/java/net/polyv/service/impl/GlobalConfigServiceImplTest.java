package net.polyv.service.impl;

import net.polyv.service.GlobalConfigService;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

/**
 * 全局配置获取单测
 * <AUTHOR>
 * @since 08/05/2020
 */
@Ignore("该配置无需每次都跑，调试时候跑就OK")
@RunWith(SpringRunner.class)
@SpringBootTest
public class GlobalConfigServiceImplTest {

    @Autowired
    GlobalConfigService globalConfigService;

    @Test
    public void getByKey() {
        Long minConsumption = globalConfigService.getByKey("min_consumption", Long.class);
        Long advancedDepositAmount = globalConfigService.getByKey("advanced_deposit_amount", Long.class);

        Assert.notNull(minConsumption, "配置加载失败");
        Assert.notNull(advancedDepositAmount, "配置加载失败");
    }
}