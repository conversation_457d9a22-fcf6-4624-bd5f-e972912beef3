package net.polyv.service.impl.export;

import lombok.extern.slf4j.Slf4j;
import net.polyv.modules.common.util.DateFormatUtil;
import net.polyv.modules.pcs.api.req.GroupUserDataExportRequest;
import net.polyv.service.export.ExportService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class ExportServiceTest {

    @Autowired
    private ExportService exportService;
    @Test
    public void exportGroupDataTest(){
        GroupUserDataExportRequest request = new GroupUserDataExportRequest();
        request.setStartDate(DateFormatUtil.parseDateTimeNormal("2022-07-01 00:00:00"));
        request.setEndDate(DateFormatUtil.parseDateTimeNormal("2022-09-01 00:00:00"));
        request.setResourceCode("duration");
        request.setGroupId("f993b42d41");
        request.setPageNumber(1);
        request.setPageSize(1000);
        exportService.getGroupUserExportData(request);
    }
}
