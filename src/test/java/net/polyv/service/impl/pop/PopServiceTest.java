package net.polyv.service.impl.pop;

import lombok.extern.slf4j.Slf4j;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pop.api.service.PopCustomerFunctionApi;
import net.polyv.modules.pop.api.vo.DefaultFunctionListVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @description:
 * @author: Neo
 * @date: 2022-10-27
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class PopServiceTest {

    @Autowired
    private PopCustomerFunctionApi popCustomerFunctionApi;

    @Test
    public void testListFunction() {
        ResponseVO<List<DefaultFunctionListVO>> dfList = this.popCustomerFunctionApi.defaultFunctionList();
        System.out.println(dfList.getData());
    }
}
