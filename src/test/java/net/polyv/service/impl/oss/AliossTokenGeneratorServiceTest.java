package net.polyv.service.impl.oss;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.service.oss.AliossTokenGeneratorService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: Neo
 * @date: 2022-05-19
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class AliossTokenGeneratorServiceTest {

    @Autowired
    private AliossTokenGeneratorService aliossTokenGeneratorService;

    @Test
    public void testGeneratorToken() {
        Map<String, String> callbackParam = new HashMap<>();
        Map<String, Object> stringObjectMap = null;
        try {
            stringObjectMap = aliossTokenGeneratorService.generateUploadStsTokenWithCallBack(callbackParam);
        } catch (ClientException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(stringObjectMap));
    }

    @Test
    public void testGetAccessRights() {
        Map<String, String> callbackParam = new HashMap<>();
        Map<String, Object> stringObjectMap = null;
        CommonResult<String> accessRightsResult = aliossTokenGeneratorService.getAccessRightsUrl("uploadvoice/20221017/18064666968(18064666968)_20221016163956.mp3");
        System.out.println(accessRightsResult.getData());
    }

    @Test
    public void testDate() {
        System.out.println(DateUtil.getDiffDays(DateUtil.getFirstDayOfMonth(DateFormatUtil.parseDateNormal("2022-11-14")), DateUtil.getFirstDayOfXMonth(DateFormatUtil.parseDateNormal("2022-11-14"),1)));
    }
}
