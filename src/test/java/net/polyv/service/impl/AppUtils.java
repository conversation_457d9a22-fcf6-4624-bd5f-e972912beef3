package net.polyv.service.impl;

import net.polyv.util.MyApiSignUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @description: 生成app_secret
 * @author: Neo
 * @date: 2022-02-24
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AppUtils {
    //生成 app_secret 密钥
    private final static String SERVER_NAME = "fxiaoke";
    private final static String[] chars = new String[]{"a", "b", "c", "d", "e", "f",
            "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
            "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9"};

    /**
     * @Description: <p>
     * 短8位UUID思想其实借鉴微博短域名的生成方式，但是其重复概率过高，而且每次生成4个，需要随即选取一个。
     * 本算法利用62个可打印字符，通过随机生成32位UUID，由于UUID都为十六进制，所以将UUID分成8组，每4个为一组，然后通过模62操作，结果作为索引取出字符，
     * 这样重复率大大降低。
     * 经测试，在生成一千万个数据也没有出现重复，完全满足大部分需求。
     * </p>
     * <AUTHOR>
     * @date 2019/8/27 16:16
     */
    private String getAppId() {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 10; i++) {
            String str = uuid.substring(i * 3, i * 3 + 2);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % 0x1E]);
        }
        return shortBuffer.toString();

    }

    /**
     * <p>
     * 通过appId和内置关键词生成APP Secret
     * </P>
     *
     * <AUTHOR>
     * @date 2019/8/27 16:32
     */
    private String getAppSecret(String appId) {
        try {
            String[] array = new String[]{appId, SERVER_NAME};
            StringBuffer sb = new StringBuffer();
            // 字符串排序
            Arrays.sort(array);
            for (int i = 0; i < array.length; i++) {
                sb.append(array[i]);
            }
            String str = sb.toString();
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            md.update(str.getBytes());
            byte[] digest = md.digest();

            StringBuffer hexstr = new StringBuffer();
            String shaHex = "";
            for (int i = 0; i < digest.length; i++) {
                shaHex = Integer.toHexString(digest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexstr.append(0);
                }
                hexstr.append(shaHex);
            }
            return hexstr.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            throw new RuntimeException();
        }
    }

    @Test
    public void generateAppId() {
        String appId = getAppId();
        String appSecret = getAppSecret("qn3fqjqus0");
        System.out.println("appId: " + appId);
        System.out.println("appSecret: " + appSecret);
        System.out.println("9088e3ba9958f9fd38edc6b0a80d50e5".length());
    }

    @Test
    public void testMyApiSign() {
        Map<String, String> requestMap = new HashMap<>();
        String appSecret = "4e3c480e83044aa75ac02addcf6f5108";
        requestMap.put("timestamp", "1646795791000");
        requestMap.put("appId", "qn3fqjqus0");
        requestMap.put("sign", "qn3fqjqus0");
        if (!MyApiSignUtil.checkSign(requestMap, appSecret)) {
        }
    }
}
