package net.polyv.service.impl.resource;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.business.BusinessPackageSpecificationRepository;
import net.polyv.model.data.resource.ContractResourceAvailableDO;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.service.GroupResourceAssignableService;
import net.polyv.service.VodUnlimitedFlowPackageService;
import net.polyv.service.business.BusinessPackageSpecificationService;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateUtil;
import net.polyv.web.controller.account.AccountController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 测试查询其他计费项逻辑
 * <AUTHOR>
 * @date 2022/11/26 0:51
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CustomerResourceServiceImplTest extends TestCase {
    @Resource
    private BusinessPackageSpecificationService businessPackageSpecificationService;
    @Resource
    private CustomerResourceService customerResourceService;
    @Resource
    private AccountController accountController;
    @Resource
    private GroupResourceAssignableService groupResourceAssignableService;
    @Resource
    private BusinessPackageSpecificationRepository businessPackageSpecificationRepository;
    @Resource
    private VodUnlimitedFlowPackageService vodUnlimitedFlowPackageService;
    
    @Test
    public void testGetSumAvailableNumber() {
        /*CustomerSumNumberVO sumAvailableNumber2 = this.customerResourceService.getSumAvailableNumber("a54965aff5",
                "distributeBillingPlanDaily");
        CustomerSumNumberVO sumAvailableNumber1 = this.customerResourceService.getSumAvailableNumber("a54965aff5",
                "distributeModifyEnabled");
        CustomerSumNumberVO sumAvailableNumber = this.customerResourceService.getSumAvailableNumber("a54965aff5",
                "SubtitleTask");*/
/*        System.out.println(JsonUtil.beanToString(sumAvailableNumber2));
        System.out.println(JsonUtil.beanToString(sumAvailableNumber1));
        System.out.println(JsonUtil.beanToString(sumAvailableNumber));*/
        
        // ResponseEntity<WrappedResponse<CustomerStateVO>> userInfo = accountController.getUserInfo();
    }
    
    @Test
    public void testRecalculateGroupResourceAssignable() {
        String groupId = "0a564a07fb";
        Integer businessType = 1;
        groupResourceAssignableService.recalculateGroupResource(groupId, businessType);
    
        businessType = 2;
        groupResourceAssignableService.recalculateGroupResource(groupId, businessType);
    
        businessType = 3;
        groupResourceAssignableService.recalculateGroupResource(groupId, businessType);
    
        businessType = 4;
        groupResourceAssignableService.recalculateGroupResource(groupId, businessType);
    
        businessType = 5;
        groupResourceAssignableService.recalculateGroupResource(groupId, businessType);
    
        businessType = 6;
        groupResourceAssignableService.recalculateGroupResource(groupId, businessType);
    
        businessType = 7;
        groupResourceAssignableService.recalculateGroupResource(groupId, businessType);
        //groupResourceAssignableService.recalculateGroupResource(null, null);
    }
    
    @Test
    public void testListContractResourceAvailableWithVodUnlimitedPackage() {
        String customerId = "a489c23516";
        String resourceCode = ResourceCodeConst.traffic.name();
        long reduceAmount = 429496729600000L;
        BusinessPackageSpecification unlimitedFlowPackage =
                businessPackageSpecificationService.findVodUnlimitedFlowPackageByCustomerId(
                customerId, DateUtil.getCurrentDay());
        boolean isVodUnlimitedFlowPackage = Objects.nonNull(unlimitedFlowPackage) ? true : false;
        if (isVodUnlimitedFlowPackage) {
            List<ContractResourceAvailableDO> resourceAvailableList =
                    customerResourceService.listContractResourceAvailableWithVodUnlimitedPackage(unlimitedFlowPackage,
                            resourceCode, DateUtil.getDateAfterDays(-1), reduceAmount);
            log.info("resourceAvailableList == {}", resourceAvailableList);
    
        }
    }
    
    @Test
    public void testPeriodFlowWarnList() {
        log.info("result == {}", vodUnlimitedFlowPackageService.periodFlowWarnNotifyList());
    }
    
    @Test
    public void testPermanentFlowWarnList() {
        log.info("result == {}", vodUnlimitedFlowPackageService.permanentFlowWarnNotifyList());
    }

    @Test
    public void testAddExpireBilling() {
        this.customerResourceService.generateGroupUserAlterationRecord("5c19383311", "duration", 1000, 1, 2);
    }
}