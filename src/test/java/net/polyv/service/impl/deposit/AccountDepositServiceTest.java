package net.polyv.service.impl.deposit;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;

import net.polyv.common.CommonResult;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.deposit.DepositApproachingExpireMessageDTO;
import net.polyv.modules.pcs.api.vo.packages.RechargeMaturityDateVO;
import net.polyv.service.AccountDepositService;
import net.polyv.service.impl.business.BusinessPackageService;
import net.polyv.web.model.account.AccountDepositExpireRequest;
import net.polyv.web.model.account.deposit.AccountDepositWithValidRequest;

/**
 * @description:
 * @author: Neo
 * @date: 2021-09-17
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class AccountDepositServiceTest {

    @Autowired
    private AccountDepositService accountDepositService;

    
    @Resource
    private BusinessPackageService businessPackageService;
    
    @Test
    public   void xx(){
        RechargeMaturityDateVO rmdList = this.businessPackageService.getCustomerAmountList("d8c6faded1");
        System.out.println(JSON.toJSON(rmdList));
    
    }
    @Test
    public void testAccountRechargeExpireDateExhibit() {
        RechargeMaturityDateVO rmdList = this.accountDepositService.accountRechargeExpireDateExhibit("d8c6faded1");
        System.out.println(JSON.toJSON(rmdList));
    }

    @Test
    public void testDepositApproachingExpireNotify() {
        List<DepositApproachingExpireMessageDTO> messageList = this.accountDepositService
                .depositApproachingExpireNotify(new AccountDepositExpireRequest("2001-09-22",15));
        System.out.println(JSON.toJSON(messageList));
    }

    @Test
    public void testExpiredCleanup() {
        try {
            this.accountDepositService.executeExpiredCleanup(null);
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDepositRegistrationGift() {
        List<AccountDepositWithValidRequest> requestList = new ArrayList<>();
        AccountDepositWithValidRequest request = new AccountDepositWithValidRequest();
        request.setCustomerId("527859a304");
        request.setAmount("100");
        request.setExpireDate("2023-12-31");
        requestList.add(request);
        try {
            System.out.println("testDepositRegistrationGift request == " + JSON.toJSONString(requestList));
            CommonResult doGiftResult = this.accountDepositService.depositRegistrationGift(requestList);
            System.out.println(doGiftResult);
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }
    }
}
