package net.polyv.service.impl.bill;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;
import net.polyv.model.data.bill.UnpaidBillMessageDTO;
import net.polyv.modules.common.util.JacksonUtil;
import net.polyv.service.bill.BillingConsumeDataService;
import net.polyv.service.bill.BillingDataService;
import net.polyv.service.bill.BillingService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.web.model.bill.BillSearchInputVO;
import net.polyv.web.model.bill.UnpaidBillingRemindRequest;
import net.polyv.web.model.bill.data.BillPageDataVO;
import net.polyv.web.model.consume.input.UserConsumeInfoGetVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: Neo
 * @date: 2021-10-12
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class BillingServiceTest {
    
    @Autowired
    private BillingService billingService;
    
    @Autowired
    private BillingConsumeDataService billingConsumeService;
    
    @Autowired
    private BillingDataService billingDataService;
    
    @Test
    public void testUnpaidBillNotify() {
        UnpaidBillingRemindRequest request = new UnpaidBillingRemindRequest();
        request.setDaysThreshold(15);
        List<UnpaidBillMessageDTO> ubmList = this.billingService.unpaidBillNotify(request);
        System.out.println(JSON.toJSON(ubmList));
    }
    
    @Test
    public void testBillingPage() {
        UserConsumeInfoGetVO req = new UserConsumeInfoGetVO() ;
//        req.setGroupId("123aaa");
        req.setStartDate(DateFormatUtil.parseDateNormal("2000-01-01"));
        req.setEndDate(DateFormatUtil.parseDateNormal("2030-01-01"));
//        req.setResourceSourceList(Collections.singletonList(1));
//        req.setCustomerIdList(Arrays.asList("03a94b9fca"));
        req.setCustomerIdListJson(JacksonUtil.writeAsString(Collections.singletonList("03a94b9fca")));
        req.setItemIdListJson(JacksonUtil.writeAsString(Collections.singletonList(1)));
        Object o = billingConsumeService.getGroupOrCustomerConsumeInfo(req);
        System.out.println(o);
    }
    
    @Test
    public void testBillingAgg() {
        BillSearchInputVO inputVO = new BillSearchInputVO();
        inputVO.setCustomerId("0a564a07fb");
        inputVO.setConsumeStartDate(DateUtil.getDateAfterDays(-210));
        inputVO.setConsumeEndDate(DateUtil.getDateAfterDays(-180));
        BillPageDataVO overview = billingDataService.overview(inputVO);
        log.info("testBillingAgg result == {}", overview);
        
    }
}
