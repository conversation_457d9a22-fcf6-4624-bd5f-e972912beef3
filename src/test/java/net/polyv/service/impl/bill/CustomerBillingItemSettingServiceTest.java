package net.polyv.service.impl.bill;

import net.polyv.common.CommonResult;
import net.polyv.constant.item.ItemCodeConst;
import net.polyv.exception.ClearingSystemException;
import net.polyv.service.CustomerBillingItemSettingService;
import net.polyv.web.model.itemsetting.input.CustomerBillingItemRatioRequest;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description: 用户-计费项-设置表操作的service接口测试类
 * @author: Neo
 * @date: 2021-10-15
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CustomerBillingItemSettingServiceTest {

    @Autowired
    private CustomerBillingItemSettingService customerBillingItemSettingService;

    @Test
    public void testUpdateCustomerBillingItemRatio() {
        CustomerBillingItemRatioRequest cbrRequest = new CustomerBillingItemRatioRequest();
        cbrRequest.setCustomerIdList(Lists.newArrayList("8beae89601","08b204568b"));
        cbrRequest.setItemCode(ItemCodeConst.inter_pd.getCode());
        cbrRequest.setRatio(250);
        try {
            CommonResult updateCbirResult = this.customerBillingItemSettingService.updateCustomerBillingItemRatio(cbrRequest);
            System.out.println(updateCbirResult);
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }
    }
}
