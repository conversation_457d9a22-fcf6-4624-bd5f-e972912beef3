package net.polyv.service.impl.bill.processor.common;

import net.polyv.constant.item.ItemCodeConst;
import net.polyv.model.data.bill.BillClearingResultDO;
import net.polyv.web.model.bill.BillClearingInputVO;
import org.junit.Assert;
import org.junit.Test;

/**
 * 金额结算器 单测
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/9
 */
public class AbstractBalanceClearingProcessorTest{

    @Test
    public void testClearingBalanceAmount() {

        BillClearingInputVO inputVO = new BillClearingInputVO();

        BillClearingResultDO resultDO = new SimpleBalanceClearingProcessor().testClearingBalanceAmount(inputVO);

        Assert.assertNotNull(resultDO);
        Assert.assertEquals(new Long(0).longValue(), resultDO.getBillClearingReduceStateDO().getReduceBaseItemAmount());
        Assert.assertEquals(new Long(10000), resultDO.getBillClearingReduceStateDO().getTotalBalanceAmount());
    }

    private static class SimpleBalanceClearingProcessor extends AbstractBalanceClearingProcessor {

        @Override
        protected BillClearingResultDO doBillClearing(BillClearingInputVO inputVO) {
            return clearingBalanceAmount(inputVO);
        }

        @Override
        public String getProcessItemCode() {
            return ItemCodeConst.small_class_duration.getCode();
        }

        @Override
        protected long getUseItemAmount(BillClearingInputVO inputVO) {
            return 10L;
        }

        @Override
        protected int getBillingItemRatio(BillClearingInputVO inputVO) {
            return 100;
        }

        @Override
        protected long getBaseItemUnitPrice(String customerId, String itemCode, String scaleCode, long consumed) {
            return 1000L;
        }

        public BillClearingResultDO testClearingBalanceAmount(BillClearingInputVO inputVO) {
            return this.clearingBalanceAmount(inputVO);
        }
    }
}