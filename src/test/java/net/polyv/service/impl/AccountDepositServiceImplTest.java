package net.polyv.service.impl;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import cn.hutool.core.date.DateUtil;
import net.polyv.dao.primary.AccountRechargeRecordRepository;
import net.polyv.dao.primary.AccountRechargeRecordResourcePointRepository;
import net.polyv.model.data.deposit.DepositPeriodDO;
import net.polyv.model.entity.primary.AccountRechargeRecord;
import net.polyv.service.AccountDepositLogicService;
import net.polyv.service.AccountDepositService;
import net.polyv.service.GlobalConfigService;
import net.polyv.web.model.account.DepositInputVO;

/**
 * 账户储值单测
 * <AUTHOR>
 * @since 09/05/2020
 */
public class AccountDepositServiceImplTest {
    
    private static final int DEFAULT_RECHARGE_PERIOD_DAYS = 365;
    
    private final AccountDepositService accountDepositService;
    
    private AccountDepositLogicService accountDepositLogicService;
    
    private final AccountRechargeRecordRepository accountRechargeRecordRepository;
    private final AccountRechargeRecordResourcePointRepository accountRechargeRecordResourcePointRepository;
    private final GlobalConfigService globalConfigService;
    
    public AccountDepositServiceImplTest() {
        
        this.accountRechargeRecordRepository = Mockito.mock(AccountRechargeRecordRepository.class);
        this.globalConfigService = Mockito.mock(GlobalConfigService.class);
        this.accountRechargeRecordResourcePointRepository = Mockito.mock(AccountRechargeRecordResourcePointRepository.class);
        
        this.accountDepositService = new AccountDepositServiceImpl(globalConfigService, null, null, null,
                accountRechargeRecordRepository, null, null, null, null, null, null,
                null, null, null, accountRechargeRecordResourcePointRepository);
    }

    // 首次充值计算周期
    @SuppressWarnings("unchecked")
    @Test
    public void calcCurrentPeriodFirstTimeRecharge() {
        DepositInputVO inputVO = new DepositInputVO();
        inputVO.setCustomerId("123");

        when(accountRechargeRecordRepository.findFirstByCustomerIdOrderByCreateTimeDesc(inputVO.getCustomerId()))
                .thenReturn(null);
        when(globalConfigService.getByKey(anyString(), Mockito.any(Class.class)))
                .thenReturn(DEFAULT_RECHARGE_PERIOD_DAYS);
    
        DepositPeriodDO depositPeriodDO = accountDepositLogicService.calcCurrentPeriod(inputVO);

        Assert.assertEquals("周期计算错误", 0,
                DateUtil.compare(depositPeriodDO.getPeriodStartTime(),
                        DateUtil.beginOfDay(DateUtil.date())));
        Assert.assertEquals("周期计算错误", 0,
                DateUtil.compare(depositPeriodDO.getPeriodEndTime(),
                        DateUtil.offsetDay(DateUtil.beginOfDay(DateUtil.date()), DEFAULT_RECHARGE_PERIOD_DAYS)));
    }

    // 在上一次的充值周期中
    @Test
    public void calcCurrentPeriodNotFirstTimeRecharge() {
        DepositInputVO inputVO = new DepositInputVO();
        inputVO.setCustomerId("123");

        AccountRechargeRecord lastRecord = new AccountRechargeRecord();
        lastRecord.setPeriodStartTime(DateUtil.offsetDay(DateUtil.date(), -128));
        lastRecord.setPeriodEndTime(DateUtil.offsetDay(DateUtil.date(), 228));
        when(accountRechargeRecordRepository.findFirstByCustomerIdOrderByCreateTimeDesc(inputVO.getCustomerId()))
                .thenReturn(lastRecord);
    
        DepositPeriodDO depositPeriodDO = accountDepositLogicService.calcCurrentPeriod(inputVO);

        Assert.assertEquals("周期计算错误", 0,
                DateUtil.compare(depositPeriodDO.getPeriodStartTime(),
                        lastRecord.getPeriodStartTime()));
        Assert.assertEquals("周期计算错误", 0,
                DateUtil.compare(depositPeriodDO.getPeriodEndTime(), lastRecord.getPeriodEndTime()));
    }

    // 启动新的充值周期
    @SuppressWarnings("unchecked")
    @Test
    public void calcCurrentPeriodStartNewRechargePeriod() {
        DepositInputVO inputVO = new DepositInputVO();
        inputVO.setCustomerId("123");

        AccountRechargeRecord lastRecord = new AccountRechargeRecord();
        lastRecord.setPeriodStartTime(DateUtil.offsetDay(DateUtil.date(), -128));
        lastRecord.setPeriodEndTime(DateUtil.offsetDay(DateUtil.date(), -1));
        when(accountRechargeRecordRepository.findFirstByCustomerIdOrderByCreateTimeDesc(inputVO.getCustomerId()))
                .thenReturn(lastRecord);
        when(globalConfigService.getByKey(anyString(), Mockito.any(Class.class)))
                .thenReturn(DEFAULT_RECHARGE_PERIOD_DAYS);
    
        DepositPeriodDO depositPeriodDO = accountDepositLogicService.calcCurrentPeriod(inputVO);

        Assert.assertEquals("周期计算错误", 0,
                DateUtil.compare(depositPeriodDO.getPeriodStartTime(),
                        DateUtil.beginOfDay(DateUtil.date())));
        Assert.assertEquals("周期计算错误", 0,
                DateUtil.compare(depositPeriodDO.getPeriodEndTime(),
                        DateUtil.offsetDay(DateUtil.beginOfDay(DateUtil.date()), DEFAULT_RECHARGE_PERIOD_DAYS)));
    }
}