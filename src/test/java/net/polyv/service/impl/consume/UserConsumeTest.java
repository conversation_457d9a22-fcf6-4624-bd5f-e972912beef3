package net.polyv.service.impl.consume;

import lombok.extern.slf4j.Slf4j;
import net.polyv.modules.pcs.api.req.GroupMicDurationGetRequest;
import net.polyv.modules.pcs.api.vo.GroupMicDurationResultVO;
import net.polyv.service.consume.ItemConsumeService;
import net.polyv.util.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class UserConsumeTest {

    @Resource
    private ItemConsumeService itemConsumeService;

    @Test
    public void testGetGroupMicDuration(){
        GroupMicDurationGetRequest request = new GroupMicDurationGetRequest();
        request.setGroupId("6e05afa327");
        request.setEndDate(String.valueOf(DateUtil.getDateAfterDays(-240).getTime()));
        request.setStartDate(String.valueOf(DateUtil.getDateAfterDays(-270).getTime()));
        try {
        
            List<GroupMicDurationResultVO> groupMicDuration = itemConsumeService.getGroupMicDuration(request);
            log.info("list == {}", groupMicDuration);
        } catch (Exception e) {
            log.error("testGetGroupMicDuration error", e);
        }
    }
}
