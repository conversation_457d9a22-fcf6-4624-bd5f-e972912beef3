package net.polyv.service.impl.billingConfig;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;

/**
 * 关闭计费想
 * <AUTHOR>
 * @date 2022/11/11 15:00
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class BillingItemCloseServiceTest extends TestCase {
    @Resource
    private BillingItemCloseService billingItemCloseService;
    
    @Test
    public void testCloseFunction() {
        this.billingItemCloseService.closeFunction(new Date());
    }
}