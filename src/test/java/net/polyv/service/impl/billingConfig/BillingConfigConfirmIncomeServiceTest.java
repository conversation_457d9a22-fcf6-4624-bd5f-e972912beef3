package net.polyv.service.impl.billingConfig;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;

/**
 * <AUTHOR>
 * @date 2022/11/8 16:38
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class BillingConfigConfirmIncomeServiceTest extends TestCase {
    @Resource
    private BillingConfigConfirmIncomeService billingConfigConfirmIncomeService;
    
    @Test
    public void testConfirmIncome() {
        billingConfigConfirmIncomeService.confirmIncome("", new Date());
    }
}