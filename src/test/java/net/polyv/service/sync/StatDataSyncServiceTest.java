package net.polyv.service.sync;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.web.model.stat.StatQueryRequest;

/**
 * 数据同步
 * <AUTHOR>
 * @date 2022/9/26 17:46
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class StatDataSyncServiceTest extends TestCase {
    @Resource
    private StatDataSyncService statContractSyncService;
    @Test
    public void testIncrementalDataSync() {
/*        Date dateBeforeDays = DateUtil.getYesterday();
        StatQueryRequest statQueryRequest = new StatQueryRequest();
        statQueryRequest.setStatTime(dateBeforeDays);
        statQueryRequest.setStartTime(DateUtil.getDateStart(DateFormatUtil.parseDateNormal("2022-09-20")));
        statQueryRequest.setEndTime(DateUtil.getDateEnd(DateFormatUtil.parseDateNormal("2022-09-20")));
        statQueryRequest.setPageSize(200);*/
    
        StatQueryRequest statQueryRequest = new StatQueryRequest();
        statQueryRequest.setContractId("ZqMVbE7d6M");
        statContractSyncService.incrementalDataSync(statQueryRequest);
    }
    
    @Test
    public  void testAllData(){
        Date dateBeforeDays = DateUtil.getCurrentDay();
        StatQueryRequest statQueryRequest = new StatQueryRequest();
        statQueryRequest.setStatTime(dateBeforeDays);
        statQueryRequest.setEndTime(DateUtil.getDateEnd(DateFormatUtil.parseDateNormal("2022-10-07")));
        statQueryRequest.setNotIncrementalDataSync(true);
        statContractSyncService.initDataSync(statQueryRequest);
    }
}