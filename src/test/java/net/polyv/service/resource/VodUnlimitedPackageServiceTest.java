package net.polyv.service.resource;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.model.entity.primary.business.BusinessPackageSpecification;
import net.polyv.service.business.BusinessPackageSpecificationService;
import net.polyv.util.DateUtil;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class VodUnlimitedPackageServiceTest extends TestCase {
    @Resource
    private BusinessPackageSpecificationService businessPackageSpecificationService;
    
    @Test
    public void testQueryAllData() {
        
        try {
            List<BusinessPackageSpecification> vodUnlimitedFlowPackage =
                    this.businessPackageSpecificationService.findVodUnlimitedFlowPackage();
            log.info("vodUnlimitedFlowPackage == {}" + vodUnlimitedFlowPackage);
        } catch (Exception e) {
            log.error("findVodUnlimitedFlowPackage error", e);
        }
    }
    
    @Test
    public void testQueryCustomerData() {
        
        try {
            String customerId = "77d76b3884";
            Date date = DateUtil.getCurrentDay();
            BusinessPackageSpecification vodUnlimitedFlowPackage =
                    this.businessPackageSpecificationService.findVodUnlimitedFlowPackageByCustomerId(
                    customerId, date);
            log.info("vodUnlimitedFlowPackage == {}" + vodUnlimitedFlowPackage);
        } catch (Exception e) {
            log.error("findVodUnlimitedFlowPackageByCustomerId error", e);
        }
    }
}