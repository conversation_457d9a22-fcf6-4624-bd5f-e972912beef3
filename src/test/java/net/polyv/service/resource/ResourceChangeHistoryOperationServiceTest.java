package net.polyv.service.resource;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class ResourceChangeHistoryOperationServiceTest extends TestCase {
    @Resource
    private ResourceChangeHistoryOperationService resourceChangeHistoryOperationService;
    @Test
    public void testDoActiveData() {
        this.resourceChangeHistoryOperationService.doActiveData("");
    }
}