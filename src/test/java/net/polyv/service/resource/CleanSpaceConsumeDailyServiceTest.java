package net.polyv.service.resource;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.service.CleanSpaceConsumeDailyService;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CleanSpaceConsumeDailyServiceTest extends TestCase {
    
    @Resource
    private CleanSpaceConsumeDailyService cleanSpaceConsumeDailyService;
    
    @Test
    public void testCleanSpaceConsumeDaily() {
        cleanSpaceConsumeDailyService.cleanSpaceBackupConsumeDaily();
    }
    
    @Test
    public void testMigrateItemConsumeDailyToBackup() {
        cleanSpaceConsumeDailyService.migrateItemConsumeDailyToBackup();
    }
    
    @Test
    public void testCleanBeforeDaysItemConsumeDaily() {
        cleanSpaceConsumeDailyService.cleanBeforeDaysItemConsumeDaily();
    }
}
