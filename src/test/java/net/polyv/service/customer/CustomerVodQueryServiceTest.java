package net.polyv.service.customer;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.modules.pcs.api.req.crm.GetUserVodPackageRequest;
import net.polyv.modules.pcs.api.vo.crm.GetUserVodPackageResponse;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CustomerVodQueryServiceTest extends TestCase {
    @Resource
    private CustomerVodQueryService customerVodQueryService;
    @Test
    public void testGetVodPackageExpireList() {
        GetUserVodPackageRequest request = new GetUserVodPackageRequest();
        request.setDay(30);
        List<GetUserVodPackageResponse> vodPackageExpireList = customerVodQueryService.getVodPackageExpireList(
                request );
        System.out.println(vodPackageExpireList);
    }
}