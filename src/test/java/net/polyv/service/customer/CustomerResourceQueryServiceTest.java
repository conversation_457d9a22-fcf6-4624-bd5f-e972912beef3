package net.polyv.service.customer;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import net.polyv.dao.primary.resource.CustomerPeriodResourceSettingRepository;
import net.polyv.model.entity.primary.resource.CustomerPeriodResourceSetting;
import net.polyv.modules.pcs.api.stereotype.ResourceEnum;
import net.polyv.modules.pcs.api.vo.crm.GetGrayUserResourceByZeroResponse;
import net.polyv.service.impl.resource.available.BaseAvailableCalcServiceImpl;
import net.polyv.service.impl.resource.available.TrafficAvailableCalcServiceImpl;
import net.polyv.service.resource.CustomerResourceService;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CustomerResourceQueryServiceTest extends TestCase {
    @Resource
    BaseAvailableCalcServiceImpl baseAvailableCalcService;
    @Resource
    TrafficAvailableCalcServiceImpl trafficAvailableCalcService;
    @Resource
    private CustomerPeriodResourceSettingRepository customerPeriodResourceSettingRepository;
    @Resource
    private CustomerResourceService customerResourceService;
    @Test
    public void test() {
        
        String customerId = "63abcfce96";
        String resource = ResourceEnum.traffic.name();
        Date date = new Date();
        long tempTotal = trafficAvailableCalcService.getTempAvailable(customerId, resource, date);
        long available = baseAvailableCalcService.getAvailable(customerId, resource, date);
        CustomerPeriodResourceSetting setting =
                this.customerPeriodResourceSettingRepository.getCurrentEffectivePeriodSetting(
                customerId, resource);
        if (tempTotal == 0 && available == 0) {
            GetGrayUserResourceByZeroResponse result = GetGrayUserResourceByZeroResponse.builder()
                    .available(trafficAvailableCalcService.getPeriodAvailable(customerId, resource, date))
                    .recharge(setting.getPeriodAmount())
                    .customerId(customerId)
                    .build();
            System.out.println(result);
        }
    }
}