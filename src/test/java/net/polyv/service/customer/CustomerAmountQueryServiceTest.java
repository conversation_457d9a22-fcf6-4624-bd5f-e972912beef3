package net.polyv.service.customer;

import java.util.List;

import javax.annotation.Resource;

import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import cn.hutool.core.date.DateUtil;
import junit.framework.TestCase;
import net.polyv.model.dto.CustomerLastRechargeTimeDTO;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.pcs.api.vo.crm.GetLastRechargeToNowResourceResponse;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CustomerAmountQueryServiceTest extends TestCase {
    
    @Resource
    private CustomerAmountQueryService customerAmountQueryService;
    @Test
    public void testGetDonateAndTestLastNormalRecharge() {
        CustomerLastRechargeTimeDTO dto = new CustomerLastRechargeTimeDTO();
        dto.setCustomerId("48a566f06f");
        dto.setAlteration(10000000L);
        dto.setDepositTime(DateUtil.parse("2023-03-07 18:49:53", Constant.DATE_TIME_FORMAT_yyyy_MM_dd_HH_mm_ss));
        List<GetLastRechargeToNowResourceResponse> donateAndTestLastNormalRecharge =
                this.customerAmountQueryService.getDonateAndTestLastNormalRecharge(Lists.newArrayList("48a566f06f"));
        System.out.println(donateAndTestLastNormalRecharge);
    }
}