package net.polyv.service.handler;

import net.polyv.modules.common.exception.BizException;
import net.polyv.modules.common.util.JacksonUtil;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenCommonPackageExtInfo;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenRequest;
import net.polyv.modules.pcs.api.req.onlinebusiness.OnlineBusinessOpenResource;
import net.polyv.modules.pcs.api.stereotype.OnlineBusinessOpenResourceCodeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * OnlineBusinessCommonPackageOpenHandler 单元测试类
 * 专注于测试主要的业务逻辑和验证方法
 */
@RunWith(MockitoJUnitRunner.class)
public class OnlineBusinessCommonPackageOpenHandlerTest {

    @InjectMocks
    private OnlineBusinessCommonPackageOpenHandler handler;

    private OnlineBusinessOpenRequest testRequest;
    private OnlineBusinessOpenCommonPackageExtInfo testExtInfo;

    @Before
    public void setUp() {
        setupTestData();
    }

    private void setupTestData() {
        // 初始化扩展信息
        testExtInfo = new OnlineBusinessOpenCommonPackageExtInfo();
        testExtInfo.setAmount("1000.00");
        testExtInfo.setProductCode("TEST_PRODUCT");
        testExtInfo.setPayedAccountCode("GUANGZHOU_ALIPAY");
        testExtInfo.setPackageId("test_package_id");
        testExtInfo.setPackageName("测试套餐");
        testExtInfo.setPackageDesc("测试套餐描述");
        testExtInfo.setVodSpace(1024L * 1024L * 1024L); // 1GB
        testExtInfo.setVodFlow(10L * 1024L * 1024L * 1024L); // 10GB
        testExtInfo.setVodPackageIsAnnualFlow((short) 0);
        testExtInfo.setStartDate("2024-01-01");
        testExtInfo.setEndDate("2024-12-31");
        testExtInfo.setValidityUnit(12);
        testExtInfo.setQuantity(1L);
        testExtInfo.setCooperationSubject(0); // 0-广州

        // 初始化开通资源
        OnlineBusinessOpenResource openResource = new OnlineBusinessOpenResource();
        openResource.setResourceCode(OnlineBusinessOpenResourceCodeEnum.VOD_PACKAGE);
        openResource.setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // 初始化开通请求
        testRequest = new OnlineBusinessOpenRequest();
        testRequest.setUnionId("test_union_id");
        testRequest.setOpenResource(openResource);
    }

    @Test
    public void testCanOpen_WithLivePackage_ShouldReturnTrue() {
        // Given
        OnlineBusinessOpenResource openResource = new OnlineBusinessOpenResource();
        openResource.setResourceCode(OnlineBusinessOpenResourceCodeEnum.LIVE_PACKAGE);
        testRequest.setOpenResource(openResource);

        // When
        boolean result = handler.canOpen(testRequest);

        // Then
        assertTrue("应该支持直播套餐开通", result);
    }

    @Test
    public void testCanOpen_WithVodPackage_ShouldReturnTrue() {
        // Given
        OnlineBusinessOpenResource openResource = new OnlineBusinessOpenResource();
        openResource.setResourceCode(OnlineBusinessOpenResourceCodeEnum.VOD_PACKAGE);
        testRequest.setOpenResource(openResource);

        // When
        boolean result = handler.canOpen(testRequest);

        // Then
        assertTrue("应该支持点播套餐开通", result);
    }

    @Test
    public void testFillOnlineBusinessOpenParam_WithVodPackage_ShouldNotChangeExistingData() {
        // Given
        testExtInfo.setPackageName("已有套餐名称");
        testExtInfo.setPackageDesc("已有套餐描述");
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When
        handler.fillOnlineBusinessOpenParam(testRequest);

        // Then
        OnlineBusinessOpenCommonPackageExtInfo resultExtInfo = JacksonUtil.readValue(
                testRequest.getOpenResource().getExtInfo(), OnlineBusinessOpenCommonPackageExtInfo.class);
        assertEquals("已有套餐名称", resultExtInfo.getPackageName());
        assertEquals("已有套餐描述", resultExtInfo.getPackageDesc());
    }

    @Test
    public void testFillOnlineBusinessOpenParam_WithNullOpenResource_ShouldNotThrowException() {
        // Given
        testRequest.setOpenResource(null);

        // When & Then
        try {
            handler.fillOnlineBusinessOpenParam(testRequest);
        } catch (Exception e) {
            fail("当开通资源为null时不应该抛出异常");
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithValidData_ShouldNotThrowException() {
        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
        } catch (Exception e) {
            fail("有效数据不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithBlankAmount_ShouldThrowException() {
        // Given
        testExtInfo.setAmount("");
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("合同金额为空时应该抛出异常");
        } catch (BizException e) {
            assertEquals("合同金额不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithBlankProductCode_ShouldThrowException() {
        // Given
        testExtInfo.setProductCode("");
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("产品编码为空时应该抛出异常");
        } catch (BizException e) {
            assertEquals("产品编码不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithBlankPayedAccountCode_ShouldThrowException() {
        // Given
        testExtInfo.setPayedAccountCode("");
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("收款账户代码为空时应该抛出异常");
        } catch (BizException e) {
            assertEquals("收款账户代码不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithVodPackageBlankPackageId_ShouldThrowException() {
        // Given
        testExtInfo.setPackageId("");
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("套餐ID为空时应该抛出异常");
        } catch (BizException e) {
            assertEquals("套餐ID不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithVodPackageNullVodSpace_ShouldThrowException() {
        // Given
        testExtInfo.setVodSpace(null);
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("点播空间为null时应该抛出异常");
        } catch (BizException e) {
            assertEquals("点播空间不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithVodPackageNullVodFlow_ShouldThrowException() {
        // Given
        testExtInfo.setVodFlow(null);
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("点播流量为null时应该抛出异常");
        } catch (BizException e) {
            assertEquals("点播流量不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithAnnualFlowButNoMonths_ShouldThrowException() {
        // Given
        testExtInfo.setVodPackageIsAnnualFlow((short) 1);
        testExtInfo.setVodPackageFlowMonthsOfYear("");
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("年流量套餐但月数为空时应该抛出异常");
        } catch (BizException e) {
            assertEquals("点播套餐年流量月数不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithBlankStartDate_ShouldThrowException() {
        // Given
        testExtInfo.setStartDate("");
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("开始时间为空时应该抛出异常");
        } catch (BizException e) {
            assertEquals("套餐开始时间不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithBlankEndDate_ShouldThrowException() {
        // Given
        testExtInfo.setEndDate("");
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("结束时间为空时应该抛出异常");
        } catch (BizException e) {
            assertEquals("套餐结束时间不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithNullValidityUnit_ShouldThrowException() {
        // Given
        testExtInfo.setValidityUnit(null);
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("有效期单位为null时应该抛出异常");
        } catch (BizException e) {
            assertEquals("有效期单位不能为空", e.getMessage());
        }
    }

    @Test
    public void testPreValidateOnlineBusinessOpenParam_WithInvalidPayedAccountCode_ShouldThrowException() {
        // Given
        testExtInfo.setPayedAccountCode("INVALID_ACCOUNT");
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When & Then
        try {
            handler.preValidateOnlineBusinessOpenParam(testRequest);
            fail("无效收款账户代码时应该抛出异常");
        } catch (BizException e) {
            assertTrue("错误信息应该包含无效的收款账户代码", 
                    e.getMessage().contains("无效的收款账户代码"));
        }
    }

    @Test
    public void testOverrideOnlineBusinessOpenParam_WithPastStartDate_ShouldAdjustToToday() {
        // Given
        String pastDate = "2023-01-01";
        String pastEndDate = "2023-12-31";
        testExtInfo.setStartDate(pastDate);
        testExtInfo.setEndDate(pastEndDate);
        testRequest.getOpenResource().setExtInfo(JacksonUtil.writeAsString(testExtInfo));

        // When
        handler.overrideOnlineBusinessOpenParam(testRequest);

        // Then
        OnlineBusinessOpenCommonPackageExtInfo resultExtInfo = JacksonUtil.readValue(
                testRequest.getOpenResource().getExtInfo(), OnlineBusinessOpenCommonPackageExtInfo.class);
        
        // 过去的开始日期应该被调整为今天
        assertNotEquals("过去开始时间应该被调整", pastDate, resultExtInfo.getStartDate());
        assertNotEquals("过去结束时间应该被调整", pastEndDate, resultExtInfo.getEndDate());
    }

    @Test
    public void testOverrideOnlineBusinessOpenParam_WithNullOpenResource_ShouldThrowException() {
        // Given
        testRequest.setOpenResource(null);

        // When & Then
        try {
            handler.overrideOnlineBusinessOpenParam(testRequest);
            fail("开通资源为null时应该抛出异常");
        } catch (Exception e) {
            // 期望抛出异常
            assertNotNull("应该抛出异常", e);
        }
    }
} 