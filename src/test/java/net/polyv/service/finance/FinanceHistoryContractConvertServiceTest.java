package net.polyv.service.finance;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.dao.oldFinance.BusinessOperationRepository;
import net.polyv.model.entity.oldFinance.BusinessOperation;
import net.polyv.model.entity.primary.definition.ContractEnumDefinition;

/**
 * 测试获取数据
 * <AUTHOR>
 * @date 2022/10/11 17:45
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class FinanceHistoryContractConvertServiceTest extends TestCase {
    @Resource
    private FinanceHistoryContractConvertService financeHistoryContractConvertService;
    @Resource
    private BusinessOperationRepository businessOperationRepository;
    
    @Test
    public void testFindContractEnumByOperationName() {
        /** ZZMZGrKtSy
         GBOHOohewK
         zmCsR5xaSz
         bx88lTZyLJ
         aPyKRjTOII
         7RY2BKYPKf
         gX6pHscVMH
         W6zyrRIcFO
         1XbeJO4nxJ
         4Xd6CHsQkU
         YXQv8rAkIx
         JcblMtRbJO
         CQQNiRqVqn
         2q4O9fzJEu
         E1hcNune8A
         fRnNqcGgPW*/
        ArrayList<String> list = Lists.newArrayList("ZZMZGrKtSy", "GBOHOohewK", "zmCsR5xaSz", "bx88lTZyLJ",
                "aPyKRjTOII", "7RY2BKYPKf", "gX6pHscVMH", "W6zyrRIcFO", "1XbeJO4nxJ", "4Xd6CHsQkU", "YXQv8rAkIx",
                "JcblMtRbJO", "CQQNiRqVqn", "2q4O9fzJEu", "E1hcNune8A", "fRnNqcGgPW");
        List<BusinessOperation> list1 = businessOperationRepository.findByContractIdIn(list);
        list1.forEach(v->{
            ContractEnumDefinition opens =
                    financeHistoryContractConvertService.findContractEnumByOperationName(v.getOperationName(),v.getTrafficType()
                    ,v.getOperationType(),v.getVodIsYearly());
            System.out.println("---->"+ JsonUtil.beanToString(opens).orElse(""));
        });
    
    }
}