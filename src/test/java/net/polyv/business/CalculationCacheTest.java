package net.polyv.business;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;

import net.polyv.config.PcsConfig;
import net.polyv.service.GrayTestService;
import net.polyv.service.customer.CustomerFlowCalculationService;
import net.polyv.service.customer.CustomerResourceQueryService;
import net.polyv.service.customer.CustomerSpaceCalculationService;

/**
 * 业务开通测试
 * <AUTHOR>
 * @date 2022/8/11 10:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class CalculationCacheTest {
    @Resource
    private CustomerResourceQueryService customerResourceQueryService;
    
    @Resource
    private CustomerSpaceCalculationService customerSpaceCalculationService;
    @Resource
    private CustomerFlowCalculationService customerFlowCalculationService;
    @Resource
    private GrayTestService grayTestService;
    
    @Resource
    private PcsConfig pcsConfig;
    
    
    //七点半获取灰度用户数据，处理点播空间为0的进入缓存
    @Test
    public void execute() {
        List<String> userIds = Lists.newArrayList("3660485aa9");
        customerSpaceCalculationService.calculationSpaceIsZeroUser(userIds);
    }
    
    //七点半获取灰度用户数据，处理点播空间为0的进入缓存
    @Test
    public void executeFlow() {
       /* //获取灰度用户从账单查询近七天消耗的数据
        LocalDateTime now = LocalDateTime.now();
        GetUserLast7DayConsumedRequest build = GetUserLast7DayConsumedRequest.builder()
                .queryTimeEnd(LocalDateTimeUtil.format(now, Constant.DATE_FORMAT_yyyy_MM_dd))
                .queryTimeStart(LocalDateTimeUtil.format(now.minusDays(7).plusDays(1), Constant.DATE_FORMAT_yyyy_MM_dd))
                .resourceCode(ResourceCodeConst.traffic.name())
                .needQueryGray(true)
                .grayUserIds(this.grayTestService.getUserIdList())
                .build();
        List<GetUserLast7DayConsumedResponse> responseList =
                this.customerResourceQueryService.getUserConsumedByLastDay7(
                        build, false);
        if (CollectionUtils.isEmpty(responseList)){
            return;
        }
        List<String> customerIds = responseList.stream()
                .map(GetUserLast7DayConsumedResponse::getCustomerId)
                .collect(Collectors.toList());
       customerFlowCalculationService.calculationFlowIsZeroUser(customerIds);*/
        //   customerFlowCalculationService.calculationPackageFlowUser(responseList);
    }
}
