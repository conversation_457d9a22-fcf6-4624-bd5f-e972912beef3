package net.polyv.schedule.clearing;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.modules.common.stereotype.SwitchEnum;
import net.polyv.service.clearing.ItemClearingService;

/**
 * 测试触发计费项结算
 * <AUTHOR>
 * @date 2022/11/25 10:29
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class ItemConsumeDailySyncAndClearingScheduleRunnerTest extends TestCase {
    @Resource
    private ItemClearingService itemClearingService;
    
    @Test
    public void testExecute() {
        List<ResourceCodeConst> resourceCodeConsts = Arrays.asList(ResourceCodeConst.ppt_composite_duration,
                ResourceCodeConst.small_class_record_duration, ResourceCodeConst.seminar_record_duration);
        itemClearingService.clearingWithResourceCodes(resourceCodeConsts, SwitchEnum.N.getCode());
        
    }
}