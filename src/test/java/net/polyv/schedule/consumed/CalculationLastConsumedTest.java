package net.polyv.schedule.consumed;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.pcs.api.req.crm.GetUserLast7DayConsumedRequest;
import net.polyv.modules.pcs.api.stereotype.ResourceEnum;
import net.polyv.modules.pcs.api.vo.crm.GetUserLast7DayConsumedResponse;
import net.polyv.service.GrayTestService;
import net.polyv.service.customer.CustomerFlowCalculationService;
import net.polyv.service.customer.CustomerLastConsumedService;
import net.polyv.util.DingWarnRobot;

/**
 * <AUTHOR>
 * @date 2023/4/20
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class CalculationLastConsumedTest {
    @Resource
    private CustomerLastConsumedService customerLastConsumedService;
    
    @Resource
    private ThreadPoolExecutor threadPoolExecutor;
    @Resource
    private DingWarnRobot dingWarnRobot;
    @Resource
    private GrayTestService grayTestService;

    @Resource
    private CustomerFlowCalculationService customerFlowCalculationService;

    @Test
    public void test() {
        //获取灰度用户从账单查询近七天消耗的数据
        LocalDateTime now = LocalDateTime.now();
        
        List<ResourceEnum> resourceCodeList = Lists.newArrayList(ResourceEnum.traffic, ResourceEnum.duration,
                ResourceEnum.mic_duration, ResourceEnum.guide_duration, ResourceEnum.amount);
        for (ResourceEnum resourceEnum : resourceCodeList) {
            GetUserLast7DayConsumedRequest build = GetUserLast7DayConsumedRequest.builder()
                    .queryTimeEnd(LocalDateTimeUtil.format(now, Constant.DATE_FORMAT_yyyy_MM_dd))
                    .queryTimeStart(
                            LocalDateTimeUtil.format(now.minusDays(7).plusDays(1), Constant.DATE_FORMAT_yyyy_MM_dd))
                    .build();
            if (ResourceEnum.live_flow.equals(resourceEnum)) {
                build.setNeedQuerySumRecharge(true);
            }
            build.setResourceCode(resourceEnum.name());
            try {
                this.customerLastConsumedService.setCacheDataByLastConsumed(build);
            } catch (Exception e) {
                log.error("无法计算消耗量数据：{}", build, e);
                dingWarnRobot.sendWarnMsg("【预计算告警消息失败】", Lists.newArrayList("参数：", JsonUtil.beanToString(build).get()));
            }
        }


        try {
            TimeUnit.SECONDS.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testS() {
        log.info("start to execute CalculationFlowGrayUserScheduleRunner....");
        //获取灰度用户从账单查询近七天消耗的数据
        LocalDateTime now = LocalDateTime.now();
        GetUserLast7DayConsumedRequest build = GetUserLast7DayConsumedRequest.builder()
                .queryTimeEnd(LocalDateTimeUtil.format(now, Constant.DATE_FORMAT_yyyy_MM_dd))
                .queryTimeStart(LocalDateTimeUtil.format(now.minusDays(7).plusDays(1), Constant.DATE_FORMAT_yyyy_MM_dd))
                .resourceCode(ResourceCodeConst.traffic.name())
                .needQueryGray(true)
                .grayUserIds(new ArrayList<>((this.grayTestService.getGrayUserIdList())))
                .build();
        List<GetUserLast7DayConsumedResponse> responseList = this.customerLastConsumedService.listByLastConsumedInCache(
                build);
        if (CollectionUtils.isEmpty(responseList)) {
            log.info("近七天消耗流量客户为空");
            return;
        }
        List<String> customerIds = responseList.stream()
                .map(GetUserLast7DayConsumedResponse::getCustomerId)
                .collect(Collectors.toList());
        customerFlowCalculationService.calculationFlowIsZeroUser(customerIds);
        customerFlowCalculationService.calculationPackageFlowUser(responseList);
        log.info("end execute CalculationFlowGrayUserScheduleRunner....");
    }
}
