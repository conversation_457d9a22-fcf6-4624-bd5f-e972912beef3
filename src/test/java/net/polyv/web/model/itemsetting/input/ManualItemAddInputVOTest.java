package net.polyv.web.model.itemsetting.input;

import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;

/**
 * 手工账单添加入参测试
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/21
 */
public class ManualItemAddInputVOTest {


    @Test
    public void setFieldTrim() {

        String content = "你好";

        ManualItemAddInputVO inputVO = new ManualItemAddInputVO();
        inputVO.setName(content + "\t");
        inputVO.setCategory(content + "\n");
        inputVO.setItemConsumedUnit(content + "\r");
        inputVO.setProduction(content + "\n\r\t");
        inputVO.setUnivalenceUnit(content + "\t\t");

        Arrays.asList(inputVO.getName(), inputVO.getCategory(),
                inputVO.getItemConsumedUnit(), inputVO.getProduction(), inputVO.getUnivalenceUnit())
                .forEach(item -> Assert.assertEquals(content, item));
    }
}