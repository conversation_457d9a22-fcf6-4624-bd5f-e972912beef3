package net.polyv.web.model.itemsetting.input;

import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;

/**
 * 手工账单更新入参测试
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/21
 */
public class ManualItemUpdateInputVOTest {

    @Test
    public void setFieldTrim() {

        String content = "你好";

        ManualItemUpdateInputVO inputVO = new ManualItemUpdateInputVO();
        inputVO.setName(content + "\t");
        inputVO.setCategory(content + "\n");
        inputVO.setItemConsumedUnit(content + "\r");
        inputVO.setProduction(content + "\n\r\t");
        inputVO.setUnivalenceUnit(content + "\t\t");

        Arrays.asList(inputVO.getName(), inputVO.getCategory(),
                inputVO.getItemConsumedUnit(), inputVO.getProduction(), inputVO.getUnivalenceUnit())
                .forEach(item -> Assert.assertEquals(content, item));
    }
}