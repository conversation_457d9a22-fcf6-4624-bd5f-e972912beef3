package net.polyv.web.controller.billingconfig;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.PcsEnumConst;
import net.polyv.modules.common.vo.Pager;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.billingconfig.PageBillingItemRequest;
import net.polyv.modules.pcs.api.req.billingconfig.SaveBillingItemRequest;
import net.polyv.modules.pcs.api.vo.billingconfig.BillingItemPageVO;

/**
 * <AUTHOR>
 * @date 2022/11/10 10:42
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class BillingConfigControllerTest extends TestCase {
    @Resource
    private BillingConfigController billingConfigController;
    
    @Test
    public void testBillingItemPageList() {
        PageBillingItemRequest request = new PageBillingItemRequest();
        request.setPage(1);
        request.setPageSize(10);
        ResponseVO<Pager<BillingItemPageVO>> pagerResponseVO = this.billingConfigController.billingItemPageList(
                request);
        System.out.println("1   " + pagerResponseVO);
        
        request.setPage(2);
        request.setPageSize(10);
        pagerResponseVO = this.billingConfigController.billingItemPageList(request);
        System.out.println("2   " + pagerResponseVO);
        
        request.setPage(3);
        request.setPageSize(10);
        pagerResponseVO = this.billingConfigController.billingItemPageList(request);
        System.out.println("3   " + pagerResponseVO);
        
        request.setPage(4);
        request.setPageSize(10);
        pagerResponseVO = this.billingConfigController.billingItemPageList(request);
        System.out.println("4   " + pagerResponseVO);
    }
    
    @Test
    public  void  testSave(){
        SaveBillingItemRequest request = new SaveBillingItemRequest();
        request.setItemCode("testDosageCode");
        request.setItemName("测试计费项-用量");
        request.setBillingWayId(5);
        request.setBillingWayName("按用量计费");
        request.setProduction("直播");
        request.setDefaultUnivalence(new BigDecimal("1"));
        request.setDetail("用量项目明细");
        request.setUnivalenceUnit("元/G");
        request.setScaleCode("chain");
        request.setDosageUnit(null);
        request.setSubCategory("用量细分");
        request.setIsExpireReset(null);
        request.setLongestOpenTimeId(null);
        request.setLongestOpenTimeName(null);
        request.setProductCode("Live");
        request.setFunctionCode("showApiSetting");
        request.setClassifyCode("LiveValueAdd");
        ResponseVO<Object> objectResponseVO = this.billingConfigController.saveBillingItem(request  );
        
        try {
            TimeUnit.SECONDS.sleep(100L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
    @Test
    public  void  testTimeSave(){
        SaveBillingItemRequest request = new SaveBillingItemRequest();
        request.setItemCode("testTimeCode");
        request.setItemName("测试计费项-时长");
        request.setBillingWayId(3);
        request.setBillingWayName("按限定使用时长");
        request.setProduction("直播");
        request.setDefaultUnivalence(new BigDecimal("1"));
        request.setDetail("时长项目明细");
        request.setUnivalenceUnit("元/G");
        request.setScaleCode("chain");
        request.setSubCategory("时长细分");
        ResponseVO<Object> objectResponseVO = this.billingConfigController.saveBillingItem(request  );
        
        try {
            TimeUnit.SECONDS.sleep(100L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
     @Test
    public  void  testResourceSave(){
        SaveBillingItemRequest request = new SaveBillingItemRequest();
        request.setItemCode("testResourceCode");
        request.setItemName("测试计费项-资源");
        request.setBillingWayId(1);
        request.setBillingWayName("按使用资源量计费");
        request.setProduction("直播");
        request.setDefaultUnivalence(new BigDecimal("1"));
        request.setDetail("资源项目明细");
        request.setUnivalenceUnit("元/G");
        request.setScaleCode("chain");
        request.setSubCategory("资源细分");
        request.setIsExpireReset(PcsEnumConst.YES.getValue());
        request.setLongestOpenTimeName("半年");
        request.setLongestOpenTimeId(1);
        request.setDosageUnit("GB");
        ResponseVO<Object> objectResponseVO = this.billingConfigController.saveBillingItem(request  );
        
        try {
            TimeUnit.SECONDS.sleep(100L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    } @Test
    public  void  testResource2Save(){
        SaveBillingItemRequest request = new SaveBillingItemRequest();
        request.setItemCode("testResourceCode");
        request.setItemName("测试计费项-资源海外");
        request.setBillingWayId(1);
        request.setBillingWayName("按使用资源量计费");
        request.setProduction("直播");
        request.setDefaultUnivalence(new BigDecimal("1"));
        request.setDetail("资源项目明细");
        request.setUnivalenceUnit("元/G");
        request.setScaleCode("overseas");
        request.setSubCategory("资源细分");
        request.setIsExpireReset(PcsEnumConst.YES.getValue());
        request.setLongestOpenTimeName("半年");
        request.setLongestOpenTimeId(1);
        request.setDosageUnit("GB");
        ResponseVO<Object> objectResponseVO = this.billingConfigController.saveBillingItem(request  );
        
        try {
            TimeUnit.SECONDS.sleep(100L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
    
}