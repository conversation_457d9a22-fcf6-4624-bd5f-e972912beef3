package net.polyv.web.controller.examinationDonate;

import java.util.Optional;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.AddResourceRequest;
import net.polyv.modules.pcs.api.req.AmountCalculateRequest;
import net.polyv.modules.pcs.api.vo.AmountCalculateResultVO;

/**
 * <AUTHOR>
 * @date 2022/11/9 16:32
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class ResourceSpecificationControllerTest extends TestCase {
    @Resource
    private ResourceSpecificationController resourceSpecificationController;
    
    @Test
    public void testAddResource() {
        String str = "{\"accountType\":\"normal\",\"customerId\":\"8733ee6df2\",\"resourceType\":\"2\"," +
                "\"businessType\":265,\"functionRequest\":{\"code\":\"smallClassMenuEnabled\"," +
                "\"expireDate\":\"2022-11-10\"},\"remark\":\"\"}";
        Optional<AddResourceRequest> addResourceRequest = JsonUtil.stringToBean(str, AddResourceRequest.class);
        AddResourceRequest request = addResourceRequest.get();
        request.setSaleUserId("1254");
        ResponseVO<Object> vo = resourceSpecificationController.addResource(request);
    
    }
    
    @Test
    public void testAmount() {
        AmountCalculateRequest request = new AmountCalculateRequest();
        //businessType=265&resourceType=2&expireDate=&code=VideoBarrage
        AmountCalculateRequest q = new AmountCalculateRequest();
        q.setResourceType(2);
        q.setBusinessType(265);
        q.setExpireDate("2022-11-15");
        q.setCode("VideoBarrage");
        ResponseVO<AmountCalculateResultVO> amountDetail = this.resourceSpecificationController.getAmountDetail(q);
        System.out.println(amountDetail);
    }
}