package net.polyv.web.controller.account;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.account.CustomerItemStateResultVO;
import net.polyv.web.model.account.GetCustomerStateInputVO;
import net.polyv.web.model.account.item.CustomerItemInfoGetInputVO;

/**
 * 获取计费项 可用量接口 测试
 * <AUTHOR>
 * @date 2022/11/1 10:44
 */

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class AccountControllerTest extends TestCase {
    @Resource
    private AccountController accountController;
    
    @Test
    public void testItemState() {
    
        CustomerItemInfoGetInputVO vo = new CustomerItemInfoGetInputVO();
        vo.setItemCode("portalAccessEnabled");
        vo.setResourceCode("portalAccessEnabled");
        vo.setBaseDate(new Date());
        vo.setCustomerId("baf4ad57f6");
        ResponseEntity<WrappedResponse<CustomerItemStateResultVO>> entity = accountController.itemState(vo);
        System.out.println(entity);
    }
    
    @Test
    public void testInfo() {
        GetCustomerStateInputVO getCustomerStateInputVO = new GetCustomerStateInputVO();
        getCustomerStateInputVO.setLiveUserId("2b3ccac944");
        this.accountController.getUserInfo(getCustomerStateInputVO);
    }
    
}