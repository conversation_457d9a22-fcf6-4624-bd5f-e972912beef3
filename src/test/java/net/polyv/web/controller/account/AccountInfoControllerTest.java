package net.polyv.web.controller.account;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import cn.hutool.core.date.LocalDateTimeUtil;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.req.crm.GetGrayUserResourceByZeroRequest;
import net.polyv.modules.pcs.api.req.crm.GetLastRechargeToNowResourceRequest;
import net.polyv.modules.pcs.api.req.crm.GetUserLast7DayConsumedRequest;
import net.polyv.modules.pcs.api.stereotype.ResourceEnum;
import net.polyv.modules.pcs.api.vo.crm.GetGrayUserResourceByZeroResponse;
import net.polyv.modules.pcs.api.vo.crm.GetLastRechargeToNowResourceResponse;
import net.polyv.modules.pcs.api.vo.crm.GetUserLast7DayConsumedResponse;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("prod")
public class AccountInfoControllerTest extends TestCase {
    @Resource
    private AccountInfoController accountInfoController;
    
    @Test
    public void testGetGrayUserResourceByZero() {
        GetGrayUserResourceByZeroRequest getGrayUserResourceByZeroRequest = new GetGrayUserResourceByZeroRequest();
        getGrayUserResourceByZeroRequest.setResourceCode("traffic");
        getGrayUserResourceByZeroRequest.setQueryByZero(false);
        ResponseVO<List<GetGrayUserResourceByZeroResponse>> sss = this.accountInfoController.getGrayUserResourceByZero(
                getGrayUserResourceByZeroRequest);
    
        try {
            TimeUnit.MINUTES.sleep(20);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
    @Test
    public void testGetLastRechargeToNewResource(){
        LocalDateTime now = LocalDateTime.now();
        GetUserLast7DayConsumedRequest request = new GetUserLast7DayConsumedRequest();
        request.setQueryTimeEnd(LocalDateTimeUtil.format(now, Constant.DATE_FORMAT_yyyy_MM_dd));
        //时间计算为7天(含) 所以查询记录实际会是8条数据
        request.setQueryTimeStart(LocalDateTimeUtil.format(now.minusDays(7).plusDays(1), Constant.DATE_FORMAT_yyyy_MM_dd));
        request.setResourceCode(ResourceEnum.amount.name());
        request.setNeedQueryGray(false);
        ResponseVO<List<GetUserLast7DayConsumedResponse>> userConsumedByLastDay7 =
                this.accountInfoController.getUserConsumedByLastDay7(
                request);
        List<GetUserLast7DayConsumedResponse> data = userConsumedByLastDay7.getData();
        List<String> collect = data.stream()
                .map(GetUserLast7DayConsumedResponse::getCustomerId)
                .collect(Collectors.toList());
        
        GetLastRechargeToNowResourceRequest requestss =
                new GetLastRechargeToNowResourceRequest();
        requestss.setResourceCode("amount");
        requestss.setCustomerIds(StringUtils.join(collect,Constant.COMMA));
        ResponseVO<List<GetLastRechargeToNowResourceResponse>> lastRechargeToNewResource =
                accountInfoController.getLastRechargeToNewResource(requestss );
        System.out.println(JsonUtil.beanToString(lastRechargeToNewResource).get());
    }
    
}