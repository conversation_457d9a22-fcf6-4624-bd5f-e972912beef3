package net.polyv.web.controller.business;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.common.CommonResult;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.service.so.BusinessOperationService;
import net.polyv.web.model.salesopportunities.input.BusinessOperationInputVO;

/**
 * TOD
 * <AUTHOR>
 * @date 2022/8/11 10:43
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class BusinessOperationControllerTest extends TestCase {
    @Resource
    private BusinessOperationService businessOperationService;
    @Resource
    private BusinessOrderController businessOrderController;
    
    @Test
    public void testBusinessOperation() {
        BusinessOperationInputVO vo = new BusinessOperationInputVO();
        vo.setOpenUrl("http://**************:8004/#/open?orderNo=636a2206b0c2ae0001cd4eb7");
        vo.setLoginUserId("chenhao");
        vo.setLoginUserIp("131.23.1131.153");
        vo.setPaybackStatus(0);
        vo.setOrderNo("636a2206b0c2ae0001cd4eb7");
        
        CommonResult result = businessOperationService.businessOperation(vo);
        log.info("开通业务：{}", JsonUtil.beanToString(result).orElse(""));
    }
}