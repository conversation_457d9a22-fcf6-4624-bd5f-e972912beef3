package net.polyv.web.controller.business;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.web.model.WrappedResponse;

/**
 * 查询
 * <AUTHOR>
 * @date 2022/8/11 10:43
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class BusinessBillControllerTest extends TestCase {
    @Resource
    private  BusinessBillController businessBillController;
    private  static String  soId ="12342342342342342";
    
 
    @Test
    public void testGetOtherItemList() {
        ResponseEntity<WrappedResponse<Object>> list = this.businessBillController.getOtherItemList(soId);
        log.info("查询其他计费项列表：{}", JsonUtil.beanToString(list).orElse("")); 
    }
}