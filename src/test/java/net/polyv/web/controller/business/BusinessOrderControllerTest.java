package net.polyv.web.controller.business;

import java.util.Optional;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import cn.hutool.json.JSONUtil;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.model.data.business.BusinessOrderAddSpecDTO;
import net.polyv.web.model.WrappedResponse;

/**
 * 业务开通测试
 * <AUTHOR>
 * @date 2022/9/15 16:06
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
@Slf4j
public class BusinessOrderControllerTest extends TestCase {
    @Resource
    private  BusinessOrderController businessOrderController;
    @Test
    public void testAddPackageSpecInfo() {
        String  str ="{\"soId\":\"63229d82cf152f00019c8967\",\"accountType\":\"group_v1\",\"email\":\"test0003@polyv" +
                ".com\",\"validPeriodStartTime\":\"2022-09-15\",\"validPeriodEndTime\":\"2022-10-31\"," +
                "\"isAlreadyActivated\":0,\"vodSpace\":null,\"vodFlow\":null,\"vodPackageType\":null," +
                "\"vodPackageIsAnnualFlow\":null,\"packageIsCustomize\":0,\"vodPackageFlowMonthsOfYear\":null," +
                "\"vodFlowType\":null,\"liveChannelQuantity\":null,\"livePackageType\":\"18\"," +
                "\"livePackageMinutesQuantity\":\"50000\",\"livePackageMinutesQuantityGift\":null," +
                "\"liveConcurrentType\":null,\"liveConcurrentQuantity\":null,\"liveConcurrentIsLimit\":null," +
                "\"liveConcurrentDuration\":null,\"liveConcurrentDurationUnit\":\"天\"," +
                "\"liveConcurrentBillingType\":null,\"liveMicMinutesQuantity\":null," +
                "\"liveDirectorMinutesQuantity\":null,\"packageName\":\"专享套餐（5万分钟数）\",\"packageDesc\":\"功能同基础版\"," +
                "\"billingItemJson\":\"[]\",\"functionList\":\"[]\",\"company\":\"test_group 测试\"," +
                "\"unionId\":\"2ecbd24acd\",\"remark\":\"\"}";
        Optional<BusinessOrderAddSpecDTO> businessOrderAddSpecDTO = JsonUtil.stringToBean(str,
                BusinessOrderAddSpecDTO.class);
        this.businessOrderController.addPackageSpecInfo(businessOrderAddSpecDTO.get());
    }
    
    @Test
    public  void  addSpec(){
        String  str ="{\n" + "    \"soId\": \"632aaa60a8617e00013f490c\",\n" + "    \"accountType\": \"normal\",\n" +
                "    \"email\": \"<EMAIL>\",\n" +
                "    \"validPeriodStartTime\": \"2022-10-09\",\n" + "    \"validPeriodEndTime\": \"2022-10-27\",\n" +
                "    \"isAlreadyActivated\": 0,\n" + "    \"vodSpace\": \"20\",\n" + "    \"vodFlow\": \"60\",\n" +
                "    \"vodPackageType\": \"8\",\n" + "    \"vodPackageIsAnnualFlow\": 1,\n" +
                "    \"packageIsCustomize\": 0,\n" + "    \"vodPackageFlowMonthsOfYear\": \"13\",\n" +
                "    \"vodFlowType\": null,\n" + "    \"liveChannelQuantity\": null,\n" +
                "    \"livePackageType\": null,\n" + "    \"livePackageMinutesQuantity\": null,\n" +
                "    \"livePackageMinutesQuantityGift\": null,\n" + "    \"liveConcurrentType\": null,\n" +
                "    \"liveConcurrentQuantity\": null,\n" + "    \"liveConcurrentIsLimit\": null,\n" +
                "    \"liveConcurrentDuration\": null,\n" + "    \"liveConcurrentDurationUnit\": \"天\",\n" +
                "    \"liveConcurrentBillingType\": null,\n" + "    \"liveMicMinutesQuantity\": null,\n" +
                "    \"liveDirectorMinutesQuantity\": null,\n" + "    \"billingItemJson\": \"[]\",\n" +
                "    \"functionList\": \"[]\",\n" + "    \"company\": \"保利威内测\",\n" +
                "    \"unionId\": \"c139fd9513\",\n" + "    \"remark\": \"\"\n" + "}";
        Optional<BusinessOrderAddSpecDTO> businessOrderAddSpecDTO = JsonUtil.stringToBean(str,
                BusinessOrderAddSpecDTO.class);
        this.businessOrderController.addPackageSpecInfo(businessOrderAddSpecDTO.get());
    }
    
    private static   String str ="[{\"code\":\"liveCloudMusic\",\"name\":\"云音乐（限定使用时长）\",\"expireTimeStart\":\"2022-08-19\"," +
            "\"expireTimeEnd\":\"2022-08-30\",\"sys\":\"Live\",\"type\":1},{\"code\":\"liveCloudSplit\"," +
            "\"name\":\"云剪辑（次数）\",\"expireTimeStart\":\"2022-08-19\",\"expireTimeEnd\":\"2022-08-30\"," +
            "\"sys\":\"Live\",\"value\":\"12\",\"type\":0}]";
    @Test
    public void testGetSpec() {
        String stests ="{\"soId\":\"62eb80fb0185f30001c5576b\",\"accountType\":\"normal\",\"email\":\"<EMAIL>\",\"validPeriodStartTime\":\"2022-08-11\",\"validPeriodEndTime\":\"2022-08-26\"," +
                "\"isAlreadyActivated\":0,\"liveChannelQuantity\":\"12\",\"liveConcurrentType\":\"monthly\"," +
                "\"liveConcurrentQuantity\":\"11\",\"liveConcurrentIsLimit\":0,\"liveConcurrentDuration\":\"1\"," +
                "\"liveConcurrentDurationUnit\":\"月\",\"billingItemJson\":\"[]\",\"functionList\":\"[]\"," +
                "\"company\":\"广州易方信息科技股份有限公司\",\"unionId\":\"cacb60fd09\",\"remark\":\"配置\",\"otherBillItems\":\"[]\"}";
        BusinessOrderAddSpecDTO dto = JSONUtil.toBean(stests,BusinessOrderAddSpecDTO.class);
        // TODO: 2022/8/11
        dto.setOtherBillItems(str);
        ResponseEntity<WrappedResponse<Object>> entity = this.businessOrderController.addPackageSpecInfo(dto);
        log.info("开通规格配置：{}", JsonUtil.beanToString(entity).orElse(""));
    }
}