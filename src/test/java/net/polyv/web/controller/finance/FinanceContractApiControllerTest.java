package net.polyv.web.controller.finance;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.finance.StatusValueEnum;
import net.polyv.modules.common.vo.ResponseVO;

/**
 * 打印日志
 * <AUTHOR>
 * @date 2022/10/19 14:33
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class FinanceContractApiControllerTest extends TestCase {
    @Resource
    private FinanceContractApiController financeContractApiController;
    
    @Test
    public void testAssociationContractOperation() {
        String contractId = "vPV8IZOn32";
        String soId = "630ef3ac5ed27c0001f81757";
        ResponseVO<String> stringResponseVO = financeContractApiController.associationContractOperation(contractId,
                soId, StatusValueEnum.YES.getValue());
        System.out.println(stringResponseVO);
        try {
            TimeUnit.SECONDS.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}