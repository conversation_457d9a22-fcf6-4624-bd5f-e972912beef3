package net.polyv.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;
import net.polyv.modules.common.constant.Constant;
import net.polyv.modules.common.vo.ResponseVO;
import net.polyv.modules.pcs.api.stereotype.DictionaryCodeEnum;
import net.polyv.modules.pcs.api.vo.DictDefinitionVO;
import net.polyv.web.controller.common.SystemDictController;

/**
 * 系统字典
 * <AUTHOR>
 * @date 2022/8/16 18:04
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class SystemDictControllerTest {

    
    @Resource
    private SystemDictController systemDictController;
    
    @Test
    public void byCode() {
        String str = DictionaryCodeEnum.BUSINESS_OPPORTUNITY_BILLING_METHOD.getCode() + Constant.COMMA +
                DictionaryCodeEnum.BUSINESS_OPPORTUNITY_BUSINESS_TYPE.getCode();
        ResponseVO<Map<String, List<DictDefinitionVO>>> dictByType = systemDictController.getDictByType(str);
        System.out.println(dictByType);
    }
  
    @Test
    public  void  byPid(){
    
    }
  
}
