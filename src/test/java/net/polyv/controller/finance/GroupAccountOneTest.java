package net.polyv.controller.finance;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;
import net.polyv.exception.ClearingSystemException;
import net.polyv.model.data.business.BusinessOrderAddSpecDTO;
import net.polyv.model.dto.GroupAccount;
import net.polyv.model.dto.GroupAccountOnePackageDTO;
import net.polyv.modules.pcs.api.req.finance.FinanceBusinessOrderOpenDTO;
import net.polyv.rest.client.group.GroupOneAccountClient;
import net.polyv.rest.client.group.GroupOneOtherPackageClient;
import net.polyv.rest.model.dmp.Paginator;
import net.polyv.service.GroupAccountOnePreOpenService;
import net.polyv.service.GroupAccountOneService;
import net.polyv.util.JsonMapper;
import net.polyv.web.controller.business.BusinessOperationController;
import net.polyv.web.controller.business.BusinessOrderController;
import net.polyv.web.model.WrappedResponse;
import net.polyv.web.model.salesopportunities.input.BusinessOperationInputVO;

/**
 * 集团账号 测试
 * <AUTHOR>
 * @date 2022/8/9 9:53
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class GroupAccountOneTest {
    
    @Resource
    private GroupAccountOneService groupAccountOneService;
    @Resource
    private GroupAccountOnePreOpenService groupAccountOnePreOpenService;
    @Resource
    private BusinessOperationController businessOperationController;
    @Resource
    private BusinessOrderController businessOrderController;
    
    @Resource
    private GroupOneOtherPackageClient groupOneOtherPackageClient;
    @Resource
    private GroupOneAccountClient groupOneAccountClient;
    public FinanceBusinessOrderOpenDTO createPreVodPackage() {
        String msg = "{\n" + "    \"soId\":\"\",\n" + "    \"accountType\":\"group_v1\",\n" +
                "    \"email\":\"<EMAIL>\",\n" + "    \"validPeriodStartTime\":\"2022-08-09\",\n" +
                "    \"validPeriodEndTime\":\"2022-08-25\",\n" + "    \"vodSpace\":\"20\",\n" +
                "    \"vodFlow\":\"60\",\n" + "    \"vodPackageType\":\"10\",\n" +
                "    \"vodPackageIsAnnualFlow\":1,\n" + "    \"vodPackageFlowMonthsOfYear\":\"12\",\n" +
                "    \"packageName\":\"个人版\",\n" + "    \"packageDesc\":\"空间：20G，流量：60G/月，自定义播放器\",\n" +
                "    \"company\":\"广州易方信息科技股份优先公司\",\n" + "    \"unionId\":\"4dc29dc3f5\",\n" + "\n" +
                "  \"saleUserId\":\"R15fSxM=\",\n" + "  \"saleUserName\":\"张智勇\",\n" + " \"contractAmount\": 1231,\n" +
                " \"billingPlanCode\": \"vod_package\",\n" + " \"businessType\" :1,\n" + "    \"remark\":\"测试开通\"\n" +
                "}";
        return JsonMapper.stringToBean(msg, FinanceBusinessOrderOpenDTO.class);
    }
    
    public BusinessOrderAddSpecDTO createLiveDuration() {
        String msg = "{\"soId\":\"62df85981fa7d4000121a5c3\",\"accountType\":\"group_v1\"," +
                "\"email\":\"<EMAIL>\",\"validPeriodStartTime\":\"2022-08-15\"," +
                "\"validPeriodEndTime\":\"2022-08-30\",\"isAlreadyActivated\":null,\"vodSpace\":null," +
                "\"vodFlow\":null,\"vodPackageType\":null,\"vodPackageIsAnnualFlow\":null," +
                "\"packageIsCustomize\":null,\"vodPackageFlowMonthsOfYear\":null,\"vodFlowType\":null," +
                "\"liveChannelQuantity\":\"1\",\"livePackageType\":\"2\",\"livePackageMinutesQuantity\":\"1000000\"," +
                "\"livePackageMinutesQuantityGift\":\"1\",\"liveConcurrentType\":null," +
                "\"liveConcurrentQuantity\":null,\"liveConcurrentIsLimit\":null,\"liveConcurrentDuration\":null," +
                "\"liveConcurrentDurationUnit\":\"天\",\"liveConcurrentBillingType\":null," +
                "\"liveMicMinutesQuantity\":null,\"liveDirectorMinutesQuantity\":null," +
                "\"packageName\":\"企业版（100万分钟）\",\"packageDesc\":\"最高可赠送20万分钟\",\"billingItemJson\":\"[]\"," +
                "\"functionList\":\"[]\",\"company\":\"保利威\",\"unionId\":\"4dc29dc3f5\",\"remark\":\"\"}";
        
        return JsonMapper.stringToBean(msg, BusinessOrderAddSpecDTO.class);
    }
    
    public BusinessOrderAddSpecDTO createLiveCurrent() {
        String msg = "{\"soId\":\"62df9d8a7cd91f00014894ba\",\"accountType\":\"group_v1\"," +
                "\"email\":\"<EMAIL>\",\"validPeriodStartTime\":\"2022-08-15\"," +
                "\"validPeriodEndTime\":\"2022-08-31\",\"isAlreadyActivated\":null,\"vodSpace\":null," +
                "\"vodFlow\":null,\"vodPackageType\":null,\"vodPackageIsAnnualFlow\":null," +
                "\"packageIsCustomize\":null,\"vodPackageFlowMonthsOfYear\":null,\"vodFlowType\":null," +
                "\"liveChannelQuantity\":\"5\",\"livePackageType\":null,\"livePackageMinutesQuantity\":null," +
                "\"livePackageMinutesQuantityGift\":null,\"liveConcurrentType\":\"monthly\"," +
                "\"liveConcurrentQuantity\":\"2\",\"liveConcurrentIsLimit\":1,\"liveConcurrentDuration\":\"1\"," +
                "\"liveConcurrentDurationUnit\":\"天\",\"liveConcurrentBillingType\":null," +
                "\"liveMicMinutesQuantity\":null,\"liveDirectorMinutesQuantity\":null,\"billingItemJson\":\"[]\"," +
                "\"functionList\":\"[]\",\"company\":\"保利威\",\"unionId\":\"4dc29dc3f5\",\"remark\":\"\"}";
        
        return JsonMapper.stringToBean(msg, BusinessOrderAddSpecDTO.class);
    }
    
    public BusinessOrderAddSpecDTO createVodPackage() {
        String msg = "{\"soId\":\"62df858c104b4b0001f11291\",\"accountType\":\"group_v1\"," +
                "\"email\":\"<EMAIL>\",\"validPeriodStartTime\":\"2022-08-16\"," +
                "\"validPeriodEndTime\":\"2022-08-31\",\"isAlreadyActivated\":null,\"vodSpace\":\"200\"," +
                "\"vodFlow\":\"1024\",\"vodPackageType\":\"109\",\"vodPackageIsAnnualFlow\":0," +
                "\"packageIsCustomize\":null,\"vodPackageFlowMonthsOfYear\":null,\"vodFlowType\":null," +
                "\"liveChannelQuantity\":null,\"livePackageType\":null,\"livePackageMinutesQuantity\":null," +
                "\"livePackageMinutesQuantityGift\":null,\"liveConcurrentType\":null,\"liveConcurrentQuantity\":null," +
                "\"liveConcurrentIsLimit\":null,\"liveConcurrentDuration\":null,\"liveConcurrentDurationUnit\":null," +
                "\"liveConcurrentBillingType\":null,\"liveMicMinutesQuantity\":null," +
                "\"liveDirectorMinutesQuantity\":null,\"packageName\":\"--教育版-标准型\"," +
                "\"packageDesc\":\"新版教育版，200G空间、1T流量\",\"billingItemJson\":\"[]\",\"functionList\":\"[]\"," +
                "\"company\":\"保利威\",\"unionId\":\"4dc29dc3f5\",\"remark\":\"测试点播开通\"}";
        
        return JsonMapper.stringToBean(msg, BusinessOrderAddSpecDTO.class);
    }
    public BusinessOrderAddSpecDTO createMicDuration() {
        String msg = "{\"soId\":\"6283be025b37f5000144bb37\",\"accountType\":\"group_v1\"," +
                "\"email\":\"<EMAIL>\",\"validPeriodStartTime\":\"2022-08-16\"," +
                "\"validPeriodEndTime\":null,\"isAlreadyActivated\":0,\"vodSpace\":null,\"vodFlow\":null," +
                "\"vodPackageType\":null,\"vodPackageIsAnnualFlow\":null,\"packageIsCustomize\":null," +
                "\"vodPackageFlowMonthsOfYear\":null,\"vodFlowType\":null,\"liveChannelQuantity\":null," +
                "\"livePackageType\":null,\"livePackageMinutesQuantity\":null," +
                "\"livePackageMinutesQuantityGift\":null,\"liveConcurrentType\":null,\"liveConcurrentQuantity\":null," +
                "\"liveConcurrentIsLimit\":null,\"liveConcurrentDuration\":null,\"liveConcurrentDurationUnit\":\"天\"," +
                "\"liveConcurrentBillingType\":null,\"liveMicMinutesQuantity\":\"1\"," +
                "\"liveDirectorMinutesQuantity\":null,\"billingItemJson\":\"[]\",\"functionList\":\"[]\"," +
                "\"company\":\"保利威\",\"unionId\":\"4dc29dc3f5\",\"remark\":\"集团1.0直播连麦分钟数测试\"}";
        
        return JsonMapper.stringToBean(msg, BusinessOrderAddSpecDTO.class);
    }
    
    @Test
    public  void queryAccount(){
        WrappedResponse<Paginator<GroupAccount>> userListByEmail = groupOneAccountClient.getUserListByEmail(
                "<EMAIL>", null, null, 1, 2);
         log.info("输出:{}",userListByEmail);
    }
    
    @Test
    public void preOpen() {
        FinanceBusinessOrderOpenDTO base = createPreVodPackage();
        try {
            this.groupAccountOnePreOpenService.preOpenLogic(base);
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }
    }
    
    @Test
    public void openLiveDuration() {
        BusinessOrderAddSpecDTO dto = createLiveDuration();
        
        ResponseEntity<WrappedResponse<Object>> addResult = this.businessOrderController.addPackageSpecInfo(dto);
        log.info("保存业务规格result:{})", addResult);
        
        BusinessOperationInputVO vo = new BusinessOperationInputVO();
        vo.setOrderNo("62df85981fa7d4000121a5c3");
        vo.setPaybackStatus(1);
        vo.setLoginUserIp("***************");
        vo.setLoginUserId("123123");
        vo.setOpenUrl("http://**************:8004/#/open?orderNo=62df85981fa7d4000121a5c3");
        
        ResponseEntity<WrappedResponse<Object>> entity = this.businessOperationController.businessOperation(vo);
        log.info("业务开通result:{}", entity);
    }
    
    @Test
    public void openLiveCurrent() {
        BusinessOrderAddSpecDTO dto = createLiveCurrent();
        
        ResponseEntity<WrappedResponse<Object>> addResult = this.businessOrderController.addPackageSpecInfo(dto);
        log.info("保存业务规格result:{})", addResult);
        
        BusinessOperationInputVO vo = new BusinessOperationInputVO();
        vo.setOrderNo("62df9d8a7cd91f00014894ba");
        vo.setPaybackStatus(1);
        vo.setLoginUserIp("***************");
        vo.setLoginUserId("123123");
        vo.setOpenUrl("http://**************:8004/#/open?orderNo=62df9d8a7cd91f00014894ba");
        ResponseEntity<WrappedResponse<Object>> entity = this.businessOperationController.businessOperation(vo);
        log.info("业务开通result:{}", entity);
    }
    
    @Test
    public void addVodPackage() {
        BusinessOrderAddSpecDTO dto = createVodPackage();
        
        ResponseEntity<WrappedResponse<Object>> addResult = this.businessOrderController.addPackageSpecInfo(dto);
        log.info("保存业务规格result:{})", addResult);
        
    }
    
    @Test
    public void openVodPackage() {
        BusinessOperationInputVO vo = new BusinessOperationInputVO();
        vo.setOrderNo("62df858c104b4b0001f11291");
        vo.setPaybackStatus(1);
        vo.setLoginUserIp("***************");
        vo.setLoginUserId("123123");
        vo.setOpenUrl("http://**************:8004/#/open?orderNo=62df858c104b4b0001f11291");
        ResponseEntity<WrappedResponse<Object>> entity = this.businessOperationController.businessOperation(vo);
        log.info("业务开通result:{}", entity);
    }
      @Test
    public void addMicDuration() {
        BusinessOrderAddSpecDTO dto = createMicDuration();
        
        ResponseEntity<WrappedResponse<Object>> addResult = this.businessOrderController.addPackageSpecInfo(dto);
        log.info("保存业务规格result:{})", addResult);
        
    }
    
    @Test
    public void openMicDuration() {
        BusinessOperationInputVO vo = new BusinessOperationInputVO();
        vo.setOrderNo("6283be025b37f5000144bb37");
        vo.setPaybackStatus(1);
        vo.setLoginUserIp("***************");
        vo.setLoginUserId("123123");
        vo.setOpenUrl("http://**************:8004/#/open?orderNo=6283be025b37f5000144bb37");
        ResponseEntity<WrappedResponse<Object>> entity = this.businessOperationController.businessOperation(vo);
        log.info("业务开通result:{}", entity);
    }
    
    
    @Test
    public  void testSendData(){
        GroupAccountOnePackageDTO packageDTO = new GroupAccountOnePackageDTO();
        packageDTO.setSpace(1L);
        packageDTO.setGroupId("4dc29dc3f5");
        packageDTO.setFlowSize(1L);
        packageDTO.setChannels(1L);
        packageDTO.setConcurrency(1L);
        packageDTO.setDuration(1L);
        packageDTO.setLiveEnd("2022-09-02");
        packageDTO.setVodEnd("2022-09-02");
        WrappedResponse<String> stringWrappedResponse = this.groupOneOtherPackageClient.addGroupPackage(packageDTO);
        log.info("输出：{}", stringWrappedResponse);
    }
    
    @Test
    public void testGetUnionIdsByGroupId() {
        List<String> list = groupAccountOneService.getUnionIdsByGroupId("05c96fe798");
        System.out.println(list);
    }
}
