package net.polyv.util;

import com.google.common.collect.Lists;
import net.polyv.model.data.resource.ContractResourceAvailableDO;
import net.polyv.model.entity.primary.resource.ResourceAlterationRecord;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.util.DigestUtils;

import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 签名工具类单测
 * <AUTHOR>
 * @since 07/05/2020
 */
public class SignUtilTest {

    /**
     * {
     * 	'customerId': '527859a304',
     * 	'summary': '1230',
     * 	'contractType': '无合同',
     * 	'contractId': '',
     * 	'saleUserId': 'TVhQQA==',
     * 	'saleUserName': '黄裕丰',
     * 	'opportunitiesSaleUserId': '',
     * 	'opportunitiesSaleUserName': '',
     * 	'name': '',
     * 	'code': '',
     * 	'company': '个人_15902032665',
     * 	'email': '<EMAIL>',
     * 	'businessType': '13',
     * 	'billingPlan': '',
     * 	'amountGained': '',
     * 	'contractAmount': '',
     * 	'invoiceType': '',
     * 	'acceptanceAccount': '',
     * 	'customerFrom': '',
     * 	'industryDistribution': '',
     * 	'type': '',
     * 	'functionPackage': '',
     * 	'introduction': '000',
     * 	'fileJson': '[]',
     * 	'updateAmountGainedDate': 'true',
     * 	'updateContractTime': 'true',
     * 	'updateFileJson': 'true',
     * 	'isGroupUser': '0',
     * 	'periodType': '',
     * 	'uid': 'TVhQQA==',
     * 	'uip': '**********',
     * 	'sign': 'ACB99291650FD2734C16ED772C25E665',
     * 	'timestamp': '*************'
     * }
     */
    @Test
    public void checkSignV2() {
        MockHttpServletRequest mockRequest = new MockHttpServletRequest();
//        mockRequest.addParameter("customerId", "527859a304");
//        mockRequest.addParameter("summary", "1230");
//        mockRequest.addParameter("contractType", "无合同");
//        mockRequest.addParameter("contractId", "");
//        mockRequest.addParameter("saleUserId", "TVhQQA==");
//        mockRequest.addParameter("saleUserName", "黄裕丰");
//        mockRequest.addParameter("opportunitiesSaleUserId", "");
//        mockRequest.addParameter("opportunitiesSaleUserName", "");
//        mockRequest.addParameter("name", "");
//        mockRequest.addParameter("code", "");
//
//        mockRequest.addParameter("company", "个人_15902032665");
//        mockRequest.addParameter("email", "<EMAIL>");
//        mockRequest.addParameter("businessType", "13");
//        mockRequest.addParameter("billingPlan", "");
//        mockRequest.addParameter("amountGained", "");
//        mockRequest.addParameter("contractAmount", "");
//        mockRequest.addParameter("invoiceType", "");
//        mockRequest.addParameter("acceptanceAccount", "");
//
//
//        mockRequest.addParameter("customerFrom", "");
//        mockRequest.addParameter("industryDistribution", "");
//        mockRequest.addParameter("type", "");
//        mockRequest.addParameter("functionPackage", "");
//        mockRequest.addParameter("introduction", "000");
//        mockRequest.addParameter("fileJson", "[]");
//        mockRequest.addParameter("updateAmountGainedDate", "true");
//        mockRequest.addParameter("updateContractTime", "true");
//        mockRequest.addParameter("updateFileJson", "true");
//        mockRequest.addParameter("isGroupUser", "0");
//        mockRequest.addParameter("periodType", "");
//        mockRequest.addParameter("uid", "TVhQQA==");
//        mockRequest.addParameter("uip", "**********");
        mockRequest.addParameter("userId","baf4ad57f6");
        mockRequest.addParameter("packageId","2");
        mockRequest.addParameter("timestamp","*************");



        mockRequest.addParameter("sign", "E631C2A3E56ED34FA4267E912EEB4FF9");

        boolean isSignCheckValid = SignUtil.checkSignV2(mockRequest, "polyv_calculate_api_innor", null);
        Assert.assertTrue("参数校验失败，请检查签名工具类", isSignCheckValid);
    }
    @Test
    public void test(){

        boolean isCurrentDay = DateFormatUtil.parseDateNormal("2022-07-21").getTime()
                == DateUtil.getCurrentDay().getTime();


        List<ContractResourceAvailableDO> list = Lists.newArrayList();
        ContractResourceAvailableDO contractResourceAvailableDO1 =
                ContractResourceAvailableDO.builder()
                .expireDate(DateFormatUtil.parseDateNormal("2022-07-01"))
                .createTime(DateFormatUtil.parseDateTimeNormal("2022-07-01 12:00:00"))
                        .build();
        ContractResourceAvailableDO contractResourceAvailableDO2 =
                ContractResourceAvailableDO.builder()
                        .expireDate(DateFormatUtil.parseDateNormal("2022-07-01"))
                        .createTime(DateFormatUtil.parseDateTimeNormal("2022-07-01 11:00:20"))
                        .build();
        ContractResourceAvailableDO contractResourceAvailableDO3 =
                ContractResourceAvailableDO.builder()
                        .expireDate(DateFormatUtil.parseDateNormal("2022-06-30"))
                        .createTime(DateFormatUtil.parseDateTimeNormal("2022-07-01 20:00:01"))
                        .build();
        list.add(contractResourceAvailableDO1);
        list.add(contractResourceAvailableDO2);
        list.add(contractResourceAvailableDO3);
        list = list.stream().sorted(Comparator.comparing(ContractResourceAvailableDO:: getExpireDate)
                .thenComparing(ContractResourceAvailableDO :: getCreateTime)).collect(Collectors.toList());
        System.out.println(list);
    }

    @Test
    public void test2(){
        List<ResourceAlterationRecord> depositList = Lists.newArrayList();
        ResourceAlterationRecord one = new ResourceAlterationRecord();
        one.setExpireDate(DateUtil.getDateAfterDays(3));
        ResourceAlterationRecord two = new ResourceAlterationRecord();
        two.setExpireDate(DateUtil.getDateAfterDays(2));
        ResourceAlterationRecord three = new ResourceAlterationRecord();
        three.setExpireDate(null);

        depositList.add(one);
        depositList.add(two);
        depositList.add(three);
        Optional<ResourceAlterationRecord> maxExpireDateRecordOptional = depositList.stream()
                .filter(deposit -> Objects.nonNull(deposit.getExpireDate()))
                .max(Comparator.comparing(ResourceAlterationRecord::getExpireDate));
        ResourceAlterationRecord maxExpireDateRecord = null;
        if (maxExpireDateRecordOptional.isPresent()) {
            maxExpireDateRecord = maxExpireDateRecordOptional.get();
        }
        System.out.println("===============");
    }
    
    @Test
    public void testCalendar() {
        Date currentDay = DateUtil.getBeginOfDay(
                DateUtil.getDateAfterDays(-1, DateUtil.getDateAfterDays(-1)));    // 今天生效要扣除一天
        
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentDay);
        cal.add(Calendar.YEAR, 1);//增加一个自然年
        System.out.println(cal.getTime());
    }
}