package net.polyv.util;

import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;

/**
 * 文件测试
 * <AUTHOR>
 * @date 2022/10/27 15:09
 */
public class FileTest {
    public static void main(String[] args) {
       /* FileTest fileTest = new FileTest();
        fileTest.readFileAsList();*/
        boolean empty = StringUtils.isBlank("      ");
        System.out.println(empty);
    }
    
    private void readFileAsList() {
        String fileName = "C:\\Users\\<USER>\\Desktop\\组装套餐信息失败.txt";
        
        try {
            String pattern = "contractId=(.*?)(?=, cont)";
            List<String> lines = Files.readAllLines(Paths.get(fileName), Charset.defaultCharset());
            List<String> contractIds = new ArrayList<>();
            for (String line : lines) {
                Pattern r = Pattern.compile(pattern);
                Matcher m = r.matcher(line);
                while (m.find())//matcher.find()用于查找是否有这个字符，有的话返回true
                {
                    //start()返回上一个匹配项的起始索引
                    //end()返回上一个匹配项的末尾索引。
                    String substring = line.substring(m.start(), m.end());
                    contractIds.add(StringUtils.replace(substring, "contractId=", ""));
                }
            }
            String join = StringUtils.join(contractIds, ",");
            System.out.println(join);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
}
