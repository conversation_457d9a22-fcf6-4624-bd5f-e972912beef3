package net.polyv.util.converter;

import net.polyv.constant.common.PrecisionConst;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;

import org.junit.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/5/31
 */
public class UnitConverterUtilTest {

    @Test
    public void bytes2GB() {
        Date startDate = DateFormatUtil.parseDateNormal("2024-04-10");
        System.out.println(startDate.compareTo(DateUtil.getCurrentDay()) != 0);
        // 获取清理日期范围
        Date cleanEndDate = DateUtil.getDateAfterDays(-7 - 1);
        Date cleanStartDate = DateUtil.getDateAfterDays(-7 + 1, cleanEndDate);
        System.out.println(cleanStartDate);
        System.out.println(cleanEndDate);
    
        Date migrateEndDate = DateUtil.getDateAfterDays(-1);
        Date migrateStartDate = DateUtil.getDateAfterDays(-13, migrateEndDate);
    
        System.out.println(migrateStartDate);
        System.out.println(migrateEndDate);
        long s = 153 * 125 / 100;
        System.out.println(s);
        long bytes = 1073741824;
        BigDecimal personalLeftAmount = new BigDecimal(0.00);
        System.out.println(new BigDecimal(10).divide(new BigDecimal(3.4), 0, BigDecimal.ROUND_UP).intValue());
        System.out.println(new BigDecimal(10).divide(new BigDecimal(3), 2, RoundingMode.HALF_UP)
                .setScale(0, BigDecimal.ROUND_UP)
                .intValue());        //BigDecimal _1GB = BigDecimal.valueOf(1).setScale(2, BigDecimal.ROUND_HALF_UP);
        //Assert.assertEquals(_1GB, UnitConverterUtil.bytes2GB(bytes));
        personalLeftAmount = personalLeftAmount.add(
                new BigDecimal(200000000000L).divide(new BigDecimal(PrecisionConst.DEFAULT_MONEY_EXPAND_PRECISION), 2,
                        RoundingMode.HALF_UP));
        System.out.println(personalLeftAmount);
    }
    
    
}