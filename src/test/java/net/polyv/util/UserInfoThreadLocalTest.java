package net.polyv.util;

import net.polyv.model.data.GlobalUserInfoDO;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Test;

/**
 * 多线程用户信息保存单测
 * <AUTHOR>
 * @since 07/05/2020
 */
public class UserInfoThreadLocalTest {

    // 验证threadlocal在多线程模式下工作良好
    @Test
    public void multiThreadLocalParamWorkNormalTest() {
        for (int i = 0; i < 10; i++) {
            new Thread(new DemoRunner()).start();
        }

        // junit不支持多线程，控制main线程不要太快退出
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Assert.fail("休眠失败," + e.getMessage());
        }
    }

    static class DemoRunner implements Runnable {

        @Override
        public void run() {
            String threadId = String.valueOf(Thread.currentThread().getId());

            GlobalUserInfoDO userInfoDO = new GlobalUserInfoDO();
            userInfoDO.setUserId(threadId);
            userInfoDO.setUserIp(threadId);
            UserInfoThreadLocal.setGlobalUserInfoDO(userInfoDO);

            try {
                Thread.sleep(RandomUtils.nextInt(0, 100));
            } catch (InterruptedException e) {
                Assert.fail("休眠失败," + e.getMessage());
            }


            String userId = UserInfoThreadLocal.getUserId();
            String userIp = UserInfoThreadLocal.getUserIp();
            Assert.assertEquals("多线程并发错误", threadId, userId);
            Assert.assertEquals("多线程并发错误", threadId, userIp);
        }
    }
}