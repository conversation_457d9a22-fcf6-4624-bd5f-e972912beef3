package net.polyv.util;

import java.util.List;

import org.springframework.util.CollectionUtils;

import cn.hutool.json.JSONUtil;
import net.polyv.model.data.finance.BillObject;

/**
  测试收入数据
 
 * <AUTHOR>
 * @date 2022/10/12 17:01
 */

public class IncomeTest {
    public static void main(String[] args) {
        String str  ="[{\"cost\":389200,\"billId\":2617819,\"status\":\"待支付\",\"itemName\":\"重制视频时长\"," +
                "\"realMonth\":\"2022-06\",\"tradeType\":\"金额结算\",\"customerId\":\"84602d62e8\"," +
                "\"production\":\"直播\",\"univalence\":2800,\"createdTime\":*************,\"itemCategory\":\"重制课件\"," +
                "\"itemConsumed\":139,\"accountPeriod\":\"2022-06\",\"settledAccount\":false," +
                "\"univalenceUnit\":\"元/千分钟\",\"itemConsumedUnit\":\"分钟\",\"isSettledAccountInt\":0}," +
                "{\"cost\":960400,\"billId\":2631775,\"status\":\"待支付\",\"itemName\":\"重制视频时长\"," +
                "\"realMonth\":\"2022-06\",\"tradeType\":\"金额结算\",\"customerId\":\"84602d62e8\"," +
                "\"production\":\"直播\",\"univalence\":2800,\"createdTime\":*************,\"itemCategory\":\"重制课件\"," +
                "\"itemConsumed\":343,\"accountPeriod\":\"2022-06\",\"settledAccount\":false," +
                "\"univalenceUnit\":\"元/千分钟\",\"itemConsumedUnit\":\"分钟\",\"isSettledAccountInt\":0}," +
                "{\"cost\":137200,\"billId\":2667121,\"status\":\"待支付\",\"itemName\":\"重制视频时长\"," +
                "\"realMonth\":\"2022-06\",\"tradeType\":\"金额结算\",\"customerId\":\"84602d62e8\"," +
                "\"production\":\"直播\",\"univalence\":2800,\"createdTime\":*************,\"itemCategory\":\"重制课件\"," +
                "\"itemConsumed\":49,\"accountPeriod\":\"2022-06\",\"settledAccount\":false," +
                "\"univalenceUnit\":\"元/千分钟\",\"itemConsumedUnit\":\"分钟\",\"isSettledAccountInt\":0}," +
                "{\"cost\":6546400,\"billId\":2678485,\"status\":\"待支付\",\"itemName\":\"重制视频时长\"," +
                "\"realMonth\":\"2022-06\",\"tradeType\":\"金额结算\",\"customerId\":\"84602d62e8\"," +
                "\"production\":\"直播\",\"univalence\":2800,\"createdTime\":*************,\"itemCategory\":\"重制课件\"," +
                "\"itemConsumed\":2338,\"accountPeriod\":\"2022-06\",\"settledAccount\":false," +
                "\"univalenceUnit\":\"元/千分钟\",\"itemConsumedUnit\":\"分钟\",\"isSettledAccountInt\":0},{\"cost\":56000," +
                "\"billId\":2692195,\"status\":\"待支付\",\"itemName\":\"重制视频时长\",\"realMonth\":\"2022-06\"," +
                "\"tradeType\":\"金额结算\",\"customerId\":\"84602d62e8\",\"production\":\"直播\",\"univalence\":2800," +
                "\"createdTime\":*************,\"itemCategory\":\"重制课件\",\"itemConsumed\":20," +
                "\"accountPeriod\":\"2022-06\",\"settledAccount\":false,\"univalenceUnit\":\"元/千分钟\"," +
                "\"itemConsumedUnit\":\"分钟\",\"isSettledAccountInt\":0},{\"cost\":1187200,\"billId\":2720953," +
                "\"status\":\"待支付\",\"itemName\":\"重制视频时长\",\"realMonth\":\"2022-06\",\"tradeType\":\"金额结算\"," +
                "\"customerId\":\"84602d62e8\",\"production\":\"直播\",\"univalence\":2800," +
                "\"createdTime\":*************,\"itemCategory\":\"重制课件\",\"itemConsumed\":424," +
                "\"accountPeriod\":\"2022-06\",\"settledAccount\":false,\"univalenceUnit\":\"元/千分钟\"," +
                "\"itemConsumedUnit\":\"分钟\",\"isSettledAccountInt\":0},{\"cost\":736400,\"billId\":2735645," +
                "\"status\":\"待支付\",\"itemName\":\"重制视频时长\",\"realMonth\":\"2022-06\",\"tradeType\":\"金额结算\"," +
                "\"customerId\":\"84602d62e8\",\"production\":\"直播\",\"univalence\":2800," +
                "\"createdTime\":*************,\"itemCategory\":\"重制课件\",\"itemConsumed\":263," +
                "\"accountPeriod\":\"2022-06\",\"settledAccount\":false,\"univalenceUnit\":\"元/千分钟\"," +
                "\"itemConsumedUnit\":\"分钟\",\"isSettledAccountInt\":0},{\"cost\":1302000,\"billId\":2772767," +
                "\"status\":\"待支付\",\"itemName\":\"重制视频时长\",\"realMonth\":\"2022-06\",\"tradeType\":\"金额结算\"," +
                "\"customerId\":\"84602d62e8\",\"production\":\"直播\",\"univalence\":2800," +
                "\"createdTime\":*************,\"itemCategory\":\"重制课件\",\"itemConsumed\":465," +
                "\"accountPeriod\":\"2022-06\",\"settledAccount\":false,\"univalenceUnit\":\"元/千分钟\"," +
                "\"itemConsumedUnit\":\"分钟\",\"isSettledAccountInt\":0}]";
        List<BillObject> objects = JSONUtil.toList(str, BillObject.class);
        Long num = calculateCumulativeConsumeByItem(objects, "2022-06", "重制视频时长");
        Long aLong = calculateIncomeByMonth(objects, "2022-06");
        Long ew = calculateIncomeByMonthAndItem(objects, "2022-06","重制视频时长");
        System.out.println("calculateCumulativeConsumeByItem "+num);
        System.out.println("calculateIncomeByMonth "+aLong);
        System.out.println("calculateIncomeByMonthAndItem "+ew);
        
    }
    
    
    private static  Long calculateIncomeByMonthAndItem(List<BillObject> billObjects, String month, String itemName) {
        if (CollectionUtils.isEmpty(billObjects)) {
            return 0L;
        }
        return billObjects.stream()
                .filter(obj -> month.equals(obj.getRealMonth()))
                .filter(obj -> itemName.equals(obj.getItemName()))
                .mapToLong(BillObject::getCost)
                .sum();
    }
    private static Long calculateCumulativeConsumeByItem(List<BillObject> billObjects, String month, String itemName) {
        if (CollectionUtils.isEmpty(billObjects)) {
            return 0L;
        }
        return billObjects.stream()
                .filter(obj -> itemName.equals(obj.getItemName()))
                .filter(obj -> month.compareTo(obj.getRealMonth())<=0)
                .mapToLong(BillObject::getItemConsumed)
                .sum();
    }
    
    private static Long calculateIncomeByMonth(List<BillObject> billObjects, String month) {
        if (CollectionUtils.isEmpty(billObjects)) {
            return 0L;
        }
        return billObjects.stream()
                .filter(obj -> month.equals(obj.getRealMonth()))
                .mapToLong(BillObject::getCost)
                .sum();
    }
}
