package net.polyv.util;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> href="mailto:<EMAIL>">zhuangmingnan</a>
 * created by 2021/4/8
 */
@Ignore("测试用")
@ActiveProfiles("dev")
@SpringBootTest
@RunWith(SpringRunner.class)
public class DingWarnRobotTest {

    @Autowired
    DingWarnRobot dingWarnRobot;

    @Test
    public void testSendWarnMsg() {
        dingWarnRobot.sendWarnMsg("zhuangmingnan-test", "测试告警消息增加机器信息");
    }
}