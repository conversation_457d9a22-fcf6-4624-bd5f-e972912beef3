package net.polyv.rest.client.vod.resource;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import net.polyv.constant.business.AccountTypeEnum;
import net.polyv.modules.pcs.api.vo.VodPackageState;
import net.polyv.rest.client.live.UserPackageClient;
import net.polyv.service.account.CustomerService;
import net.polyv.util.converter.UnitConverterUtil;
import net.polyv.web.model.user.CustomerStateVO;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class VodResourceServiceTest extends TestCase {
    @Resource
    private  VodResourceService vodResourceService;
    @Resource
    private VodResourceClient vodResourceClient;
    @Resource
    private CustomerService customerService ;
    @Test
    public void testGetVodSyncData() {
     /* List<VodPackageVo> vodSyncData = this.vodResourceService.getVodSyncData("48a566f06f");
        System.out.println(JsonUtil.beanToString(vodSyncData).get());
*/
   
    }
    
    @Test
    public  void  test(){
        CustomerStateVO usersPackageByEmail = UserPackageClient.getUsersPackageByEmail("<EMAIL>");
    
        System.out.println(usersPackageByEmail.getVodPackageState());
       // System.out.println(JsonUtil.beanToString(usersPackageByEmail).get());
    
    }
    @Test
    public  void testInfo(){
        VodPackageState vodPackage = customerService.getVodPackage(AccountTypeEnum.NORMAL.getCode(), "48a566f06f");
        System.out.println(UnitConverterUtil.bytes2GB(vodPackage.getValidTraffic()));
        System.out.println(UnitConverterUtil.bytes2GB(vodPackage.getValidTrafficPackages()));
        System.out.println(UnitConverterUtil.bytes2GB(vodPackage.getValidTrafficTemp()));
        //10.00
        //712.00
        //0.00
        
        
    }
}