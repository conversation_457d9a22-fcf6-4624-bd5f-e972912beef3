package net.polyv.rest.client.vod.resource;

import net.polyv.util.converter.UnitConverterUtil;

/**
 * <AUTHOR>
 * @date 2023/4/23
 */

public class Test {
    
    public static void main(String[] args) {
        long leftFlow = 773847880040448l - 0L;
        long leftPackageFlow = 0l;
        
        leftPackageFlow = 764504178688l;
        leftFlow -=764504178688l;
        long mainSize =944892805120000l;
        System.out.println("flowSize "+UnitConverterUtil.bytes2GB(leftFlow));
        System.out.println("流量包 "+UnitConverterUtil.bytes2GB(leftPackageFlow));
        System.out.println("tb_temporarytraffic "+UnitConverterUtil.bytes2GB(mainSize));
        System.out.println("tb_temporarytraffic/12 "+UnitConverterUtil.bytes2GB(mainSize/12));
            System.out.println("available "+UnitConverterUtil.bytes2GB(775241596928L));
        System.out.println("buyflow "+UnitConverterUtil.bytes2GB(1032867693985792l));
    }

}
