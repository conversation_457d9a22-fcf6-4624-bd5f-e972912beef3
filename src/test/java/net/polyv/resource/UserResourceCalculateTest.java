package net.polyv.resource;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import net.polyv.constant.item.ResourceCodeConst;
import net.polyv.dao.primary.LiveCustomerConcurrenceSettingRepository;
import net.polyv.dao.primary.LiveFlowPackageInfoRepository;
import net.polyv.dao.primary.PcsFunctionConfigRepository;
import net.polyv.model.entity.primary.LiveCustomerConcurrenceSetting;
import net.polyv.model.entity.primary.LiveFlowPackageInfo;
import net.polyv.model.entity.primary.PcsFunctionConfig;
import net.polyv.service.resource.CustomerResourceService;
import net.polyv.util.DateFormatUtil;
import net.polyv.util.DateUtil;
import net.polyv.web.model.bill.BillClearingInputVO;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class UserResourceCalculateTest {
    @Autowired
    private CustomerResourceService customerResourceService;
    
    @Autowired
    protected LiveCustomerConcurrenceSettingRepository liveCustomerConcurrenceSettingRepository;
    
    @Resource
    private LiveFlowPackageInfoRepository liveFlowPackageInfoRepository;
    
    @Resource
    private PcsFunctionConfigRepository pcsFunctionConfigRepository;
    
    @Test
    public void listContractTest() {
        String customerId = "baf4ad57f6";
        ResourceCodeConst resourceCode = ResourceCodeConst.duration;
        Date baseDate = DateFormatUtil.parseDateNormal("2022-10-11");
/*        List<ContractResourceAvailableDO> list = customerResourceService.listContractResourceAvailable(customerId,
resourceCode, baseDate);
        System.out.println(list);*/
    }
    
    @Test
    public void testResourceClear() {
        BillClearingInputVO inputVO = new BillClearingInputVO();
        inputVO.setConsumeEndDate(DateUtil.getDateAfterDays(-1, new Date()));
        inputVO.setCustomerId("baf4ad57f6");
        LiveCustomerConcurrenceSetting setting =
                liveCustomerConcurrenceSettingRepository.findUnTestCustomerCurrentEffectiveSetting(
                inputVO.getCustomerId(), inputVO.getConsumeEndDate());
        
        if (setting != null) {
            log.info("当前顾客使用了并发包天，无需出直播流量账单, customerId={}, itemCode={}", inputVO.getCustomerId(),
                    inputVO.getItemCode());
            return;
        }
        
        //有生效中直播流量套餐的情况
        List<LiveFlowPackageInfo> flowPackageList =
                liveFlowPackageInfoRepository.findByCustomerIdAndIsLiveFlowBillingAndStartDateLessThanEqualAndEndDateGreaterThanEqual(
                        inputVO.getCustomerId(), 1, inputVO.getConsumeEndDate(), inputVO.getConsumeEndDate());
        
        //结算方式默认是直播流量的情况
        PcsFunctionConfig pcsFunctionConfig = pcsFunctionConfigRepository.findByCustomerIdAndCodeAndCreateTimeLessThan(
                inputVO.getCustomerId(), ResourceCodeConst.live_flow.name(),
                DateUtil.getDateAfterDays(1, inputVO.getConsumeEndDate()));
        //如果直播流量套餐不存在 && 默认计费方式不是直播流量，则不需要出账
        if (CollectionUtils.isEmpty(flowPackageList) && Objects.isNull(pcsFunctionConfig)) {
            log.info(
                    "当前顾客没有直播流量套餐 && 默认不是直播流量计费，无须出直播流量账单, customerId={}, consumeEndDate={}",
                    inputVO.getCustomerId(), inputVO.getConsumeEndDate());
            return;
        }
    }
}
