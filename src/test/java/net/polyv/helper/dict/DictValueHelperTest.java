package net.polyv.helper.dict;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import junit.framework.TestCase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.polyv.commons.javautils.util.JsonUtil;
import net.polyv.exception.ClearingSystemException;
import net.polyv.modules.pcs.api.annotation.DictValue;
import net.polyv.modules.pcs.api.stereotype.DictionaryCodeEnum;

/**
 *  字典注解测试
 * <AUTHOR>
 * @date 2022/9/29 16:50
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class DictValueHelperTest extends TestCase {
    @Resource
    private  DictValueHelper dictValueHelper;
    @Test
    public   void   testObj(){
        try {
            ObjectTest objectTest = new ObjectTest("1");
            dictValueHelper.convertDict(objectTest);
            System.out.println(JsonUtil.beanToString(objectTest).orElse(""));
        } catch (ClearingSystemException e) {
            e.printStackTrace();
        }
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class  ObjectTest{
        @DictValue(dictType = DictionaryCodeEnum.RELATION_STATUS)
        private  String  name;
    }
}