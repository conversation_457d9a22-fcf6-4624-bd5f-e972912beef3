server:
  port: 8600

spring:
  application:
    name: pcs
  profiles:
    active: test
  main:
    allow-bean-definition-overriding: true
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    primary:
      driver-class-name: com.mysql.jdbc.Driver
      url: ***************************************************************************************************************************************************************************
      username: pcs_root
      password: pcs_913.Net

    dmp:
      driver-class-name: com.mysql.jdbc.Driver
      url: ********************************************************************************************************************************************************************************************************************************
      username: dmp_test
      password: dmp_test_763!^
      # 初始化大小，最小，最大
      initialSize: 5
      minIdle: 5
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 30000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 28800
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 40000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: true
      testOnReturn: false
      type: com.alibaba.druid.pool.DruidDataSource


    druid:
      # 连接池的配置信息
      # 初始化大小，最小，最大
      initial-size: 5
      min-idle: 5
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall

  jpa:
    database: mysql
    hibernate.ddl-auto: none
    show-sql: true
    generate-ddl: false
    properties:
      hibernate:
        format_sql: true
#        generate_statistics: true
        physical_naming_strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
        jdbc:
          batch_versioned_data: true
          batch_size: 500
        order_inserts: true
        order_updates: true
        dialect: net.polyv.config.PcsMysqlDialect


  redis:
    database: 0
    host: **************
    port: 6379
    password:
    timeout: 6000ms
    jedis:
      pool:
        max-active: 1000
        max-idle: 10
        min-idle: 5
        max-wait: -1ms


eureka:
  instance:
    prefer-ip-address: true
    instanceId: ${spring.cloud.client.ip-address}:${server.port}
  client:
    serviceUrl:
      defaultZone: http://test-registry.polyv.net:18761/eureka/
    #是否将自己注册到eureka服务注册中心，默认为true
    register-with-eureka: false
    #是否从服务注册中心获取可用的服务清单，默认为true
    fetch-registry: true


feign:
  hystrix:
    enabled: true
  client:
    config:
      feignName:
        connectTimeout: 5000
        readTimeout: 5000
hystrix:
  #这样将会自动配置一个 Hystrix 并发策略插件的 hook，这个 hook 会将 SecurityContext 从主线程传输到 Hystrix 的命令。
  #因为 Hystrix 不允许注册多个 Hystrix 策略，所以可以声明 HystrixConcurrencyStrategy
  #为一个 Spring bean 来实现扩展。Spring Cloud 会在 Spring 的上下文中查找你的实现，并将其包装在自己的插件中。
  shareSecurityContext: true
  command:
    fallback:
      enabled: true
    default:
      circuitBreaker:
        requestVolumeThreshold: 1
        sleepWindowInMilliseconds: 15000
        #强制打开熔断器，如果打开这个开关，那么拒绝所有request，默认false
        forceOpen: false
        #强制关闭熔断器 如果这个开关打开，circuit将一直关闭且忽略，默认false
        forceClosed: false
      execution:
        isolation:
          thread:
            # 熔断器超时时间，默认：1000/毫秒
            timeoutInMilliseconds: 50000

# 保利威自定义参数
polyv:
  sign:
    secretKey: polyv_pcs_api_innor
  secrekey:
    inner:
      live:
        cal: polyv_calculate_api_innor
      vod:
        cal: polyv_calculate_api_inner
      finance:
        cal: polyv_finance_api_innor

  dingding:
    mini-program:
      appKey: dingiaxl99isw4qo4fxi
      appSecret: iT7Et3te5QoQdnDisnXrmG3hBGRTDfe8rouKh50RT9V3N2cfPrBPVlS2R0_wwMil
    approval:
      so:
        processCode: PROC-889E0BCA-46E1-4BE4-8D83-C9DBDCECDE65
      manual-bill:
        processCode: PROC-0417465F-3A64-4757-8912-F4327EB5C7E1
    robot:
      system:
        access-token: 3668c280d5361430b52380ad3a122f21ce72837745b74c7b26210e3baea1a0f5
      notify:
        balance:
          access-token: 3668c280d5361430b52380ad3a122f21ce72837745b74c7b26210e3baea1a0f5
        order:
          access-token: 15d6ae44e5ec781e868cf93736e26868b24bd6a5e0a98914b56a70f5e6b0ec7d

  crm:
    appId: 5c2187f59a7e3a55fe8c8cd5143886fc
    token: 9c40d315898f989c0c475083664e30a6
  customized:
    group-id: 19686a631a
  redis:
    live:
      host: **************
      port: 6379

  service-provider:
    live-api:
      name: live-api
      url:
    group-account:
      url: https://live.polyv.net/group-account/

  pcs:
    test:
      # 测试环境允许名单(unionId)
      allow-list: 887df1fc72,bbfa933818,241513d62f,baf4ad57f6,a54965aff5,146b754735,0cd258d37d,ac59aff03d,f43d15b05d,53d9f1471f

  # 健康检查
  health:
    token: 36b58a62-808d-4a64-96e9-c3a1c0f7c255

logging:
  level:
    org.hibernate.type.descriptor.sql.BasicBinder: trace
    net.polyv.rest.client: debug
    com:
      alibaba:
        druid:
          pool: trace
    root: info

business:
  spec:
    view:
      url: http://**************:8004/

fxiaoke:
  appid: FSAID_131abac
  appsecret: 2f3c1bd5a9c84f88868760484064d66e
  permanentCode: E83D379EE507DAEB2C2B84901CD4E874
  currentOpenUserId: FSUID_6ECF128FF9236B5604716FF338877F1F


alioss:
  clearing:
    name: <EMAIL>
    accesskeyid: LTAI5tMpzkjUx7yr3w6E28Hq
    accesskeysecret: ******************************
    upload:
      domain: polyv-financepro.oss-cn-shenzhen-internal.aliyuncs.com
      endpoint: oss-cn-shenzhen-internal.aliyuncs.com
      bucket: polyv-financepro
      roleArn: acs:ram::1722304298095780:role/aliyunossfinanceprorole
    callback:
      body: object=$(object)&size=$(size)&bucket=$(bucket)&etag=$(etag)
      bodyType: application/x-www-form-urlencoded
      host: fxiaoketest.polyv.net
      url: https://fxiaoketest.polyv.net/aliyunoss/callback