info:
  projectName: pcs
  port: 8600
  jarName: target/clearing-system-1.0.0.jar
  serviceDiscoverTimeout: 30
  filebeatIndex: dbg-bo-pop-app
  sideCarFilebeat:
    filebeatConfigMapName: filebeat-sidecar-pop-service-config
data:
  # 测试环境
  test:
    # Dockerfile & Jenkinsfile
    cicd:
      baseImage: registry-vpc.cn-shenzhen.aliyuncs.com/polyv/jdk:alpine-8u212-micro-v1.0.1.1
      multiVersion: "Y"
      namespace: "polyv-test"
      registry: "registry-vpc.cn-shenzhen.aliyuncs.com"
      replicas: 1
      delay: 30
      # 额外 java 运行参数
      javaOthersParams:
        # debug
        - "-Xdebug -Xrunjdwp:server=y,transport=dt_socket,address=29001,suspend=n"
    resources:
      limits:
        cpu: 2000m
        memory: 4096Mi
      requests:
        cpu: 1000m
        memory: 2048Mi
    host:
      - ip: "************"
        hostnames:
          - 'apichat.polyv.net'
      - ip: "**************"
        hostnames:
          - 'api.polyv.net'
          - 'api-internal.polyv.net'
          - 'my.polyv.net'
    env:
      - name: ENV
        value: fat
      - name: SERVICE_BRANCH_NAME
        value: $BRANCH_NAME
      - name: JAVA_OPTIONS_XMS
        value: '-Xms2048m'
      - name: JAVA_OPTIONS_XMX
        value: '-Xmx2048m -XX:MaxDirectMemorySize=512m'
    skywalking:
      swUse: Y
      swAgentName: clearing-system
      swAgentCollectorBackendServices: skywalking-oap.comp-tools.svc.cluster.local:11800
    file:
      k8sDeployment: "test-default.yaml"
      k8sService: "test-svc-default.yaml"
      dockerFile: "Dockerfile"
  test-alone:
    # Dockerfile & Jenkinsfile
    cicd:
      baseImage: registry-vpc.cn-shenzhen.aliyuncs.com/polyv/jdk:alpine-8u212-micro-v1.0.1.1
      multiVersion: "Y"
      namespace: "polyv-alone"
      registry: "registry-vpc.cn-shenzhen.aliyuncs.com"
      replicas: 1
      delay: 5
      # 额外 java 运行参数
      javaOthersParams:
        - ""
    resources:
      limits:
        cpu: 2000m
        memory: 4096Mi
      requests:
        cpu: 1000m
        memory: 2048Mi
    host:
      - ip: "************"
        hostnames:
          - 'apichat.polyv.net'
      - ip: "**************"
        hostnames:
          - 'api.polyv.net'
          - 'api.internal.polyv.net'
          - 'my.polyv.net'
      - ip: "**************"
        hostnames:
          - 'pcs.internal.polyv.net'
      - ip: "**************"
        hostnames:
          - 'skywalking-oap.comp-tools.svc.cluster.local'
    env:
      - name: ENV
        value: fat
      - name: IDC
        value: test-alone
      - name: SERVICE_BRANCH_NAME
        value: fat
      - name: JAVA_OPTIONS_XMS
        value: '-Xms2048m'
      - name: JAVA_OPTIONS_XMX
        value: '-Xmx2048m -XX:MaxDirectMemorySize=512m'
    skywalking:
      swUse: Y
      swAgentName: clearing-system
      swAgentCollectorBackendServices: skywalking-oap.comp-tools.svc.cluster.local:11800
    file:
      k8sDeployment: "test-default.yaml"
      k8sService: "test-svc-default.yaml"
      dockerFile: "Dockerfile"
  # 预发布环境
  pre:
    cicd:
      baseImage: registry-vpc.cn-shenzhen.aliyuncs.com/polyv/jdk:alpine-8u212-micro-v1.0.1.1
      multiVersion: "Y"
      namespace: "polyv-pre"
      registry: "registry-vpc.cn-shenzhen.aliyuncs.com"
      replicas: 1
      delay: 0
      # 额外 java 运行参数
      javaOthersParams:
        - ""
    resources:
      limits:
        cpu: 1000m
        memory: 1024Mi
      requests:
        cpu: 1000m
        memory: 1024Mi
    host:
      - ip: "************"
        hostnames:
          - "apichat.polyv.net"
      - ip: "**************"
        hostnames:
          - "api.polyv.net"
          - "api.internal.polyv.net"
          - "api-internal.polyv.net"
          - "my.polyv.net"
    env:
      - name: ENV
        value: uat
      - name: JAVA_OPTIONS_XMS
        value: '-Xms512m'
      - name: JAVA_OPTIONS_XMX
        value: '-Xmx512m -XX:MaxDirectMemorySize=128m'
    skywalking:
      swUse: Y
      swAgentName: clearing-system
      swAgentCollectorBackendServices: skywalking-oap.comp-tools.svc.cluster.local:11800
    file:
      k8sDeployment: "pre-default.yaml"
      k8sService: "pre-svc-default.yaml"
      dockerFile: "Dockerfile"
  # 生产环境
  prod:
    cicd:
      baseImage: registry-vpc.cn-shenzhen.aliyuncs.com/polyv/jdk:alpine-8u212-micro-v1.0.1.1
      multiVersion: "N"
      namespace: "polyv-common"
      registry: "registry-vpc.cn-shenzhen.aliyuncs.com"
      replicas: 2
      delay: 0
      # 额外 java 运行参数
      javaOthersParams:
        - ""
    resources:
      limits:
        cpu: 2000m
        memory: 6Gi
      requests:
        cpu: 1000m
        memory: 4Gi
    host: []
    env:
      - name: ENV
        value: pro
      - name: JAVA_OPTIONS_XMS
        value: '-Xms4096m'
      - name: JAVA_OPTIONS_XMX
        value: '-Xmx4096m -XX:MaxDirectMemorySize=1024m'
    skywalking:
      swUse: Y
      swAgentName: clearing-system
      swAgentCollectorBackendServices: skywalking-oap.comp-tools.svc.cluster.local:11800
    file:
      k8sDeployment: "prod-default.yaml"
      k8sService: "prod-svc-default.yaml"
      dockerFile: "Dockerfile"
